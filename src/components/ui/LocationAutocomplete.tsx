'use client'

import React, { useState, useEffect, useRef, useCallback } from 'react'
import { cn } from '@/lib/utils'
import { 
  LocationSearchResult, 
  PlaceDetails, 
  LocationAutocompleteProps,
  DEFAULT_LOCATION_CONFIG 
} from '@/lib/types/location'
import { getLocationService } from '@/lib/services/location'
import { LoadingSpinner } from './LoadingSpinner'

/**
 * LocationAutocomplete component for Google Places integration
 * Provides address autocomplete with South African location focus
 */
export const LocationAutocomplete: React.FC<LocationAutocompleteProps> = ({
  value = '',
  onLocationSelect,
  onInputChange,
  placeholder = 'Enter location...',
  countryRestriction = DEFAULT_LOCATION_CONFIG.COUNTRY_RESTRICTION,
  types = [],
  className,
  disabled = false,
  required = false,
  error
}) => {
  const [inputValue, setInputValue] = useState(value)
  const [suggestions, setSuggestions] = useState<LocationSearchResult[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [isOpen, setIsOpen] = useState(false)
  const [selectedIndex, setSelectedIndex] = useState(-1)
  const [sessionToken] = useState(() => crypto.randomUUID())

  const inputRef = useRef<HTMLInputElement>(null)
  const dropdownRef = useRef<HTMLDivElement>(null)
  const debounceRef = useRef<NodeJS.Timeout>()

  const locationService = getLocationService()

  // Debounced search function
  const debouncedSearch = useCallback(async (query: string) => {
    if (query.length < 2) {
      setSuggestions([])
      setIsOpen(false)
      return
    }

    setIsLoading(true)
    
    try {
      const response = await locationService.getPlacePredictions(query, {
        types: types.length > 0 ? types : undefined,
        sessionToken
      })

      if (response.success && response.data) {
        setSuggestions(response.data.slice(0, DEFAULT_LOCATION_CONFIG.MAX_AUTOCOMPLETE_RESULTS))
        setIsOpen(true)
      } else {
        setSuggestions([])
        setIsOpen(false)
      }
    } catch (error) {
      console.error('Location search error:', error)
      setSuggestions([])
      setIsOpen(false)
    } finally {
      setIsLoading(false)
    }
  }, [locationService, types, sessionToken])

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value
    setInputValue(newValue)
    setSelectedIndex(-1)
    
    onInputChange?.(newValue)

    // Clear existing debounce
    if (debounceRef.current) {
      clearTimeout(debounceRef.current)
    }

    // Set new debounce
    debounceRef.current = setTimeout(() => {
      debouncedSearch(newValue)
    }, DEFAULT_LOCATION_CONFIG.DEBOUNCE_DELAY)
  }

  // Handle suggestion selection
  const handleSuggestionSelect = async (suggestion: LocationSearchResult) => {
    setInputValue(suggestion.description)
    setIsOpen(false)
    setSuggestions([])
    setIsLoading(true)

    try {
      const response = await locationService.getLocationData(suggestion.placeId)
      
      if (response.success && response.data) {
        onLocationSelect(response.data)
      }
    } catch (error) {
      console.error('Error getting place details:', error)
    } finally {
      setIsLoading(false)
    }
  }

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!isOpen || suggestions.length === 0) return

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault()
        setSelectedIndex(prev => 
          prev < suggestions.length - 1 ? prev + 1 : prev
        )
        break
      case 'ArrowUp':
        e.preventDefault()
        setSelectedIndex(prev => prev > 0 ? prev - 1 : prev)
        break
      case 'Enter':
        e.preventDefault()
        if (selectedIndex >= 0 && selectedIndex < suggestions.length) {
          handleSuggestionSelect(suggestions[selectedIndex])
        }
        break
      case 'Escape':
        setIsOpen(false)
        setSelectedIndex(-1)
        inputRef.current?.blur()
        break
    }
  }

  // Handle click outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        !inputRef.current?.contains(event.target as Node)
      ) {
        setIsOpen(false)
        setSelectedIndex(-1)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  // Update input value when prop changes
  useEffect(() => {
    setInputValue(value)
  }, [value])

  // Cleanup debounce on unmount
  useEffect(() => {
    return () => {
      if (debounceRef.current) {
        clearTimeout(debounceRef.current)
      }
    }
  }, [])

  return (
    <div className={cn('relative w-full', className)}>
      <div className="relative">
        <input
          ref={inputRef}
          type="text"
          value={inputValue}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          onFocus={() => {
            if (suggestions.length > 0) {
              setIsOpen(true)
            }
          }}
          placeholder={placeholder}
          disabled={disabled}
          required={required}
          className={cn(
            `
            w-full px-4 py-2 pr-10 border-2 border-[var(--medium-gray)] 
            rounded-[var(--radius-md)] font-[var(--font-ui)] 
            transition-colors duration-300
            focus:outline-none focus:border-[var(--primary-brown)] 
            focus:ring-0 focus:shadow-[0_0_0_3px_rgba(139,69,19,0.1)]
            placeholder:text-[var(--medium-gray)]
            disabled:bg-gray-100 disabled:cursor-not-allowed
            `,
            error && 'border-red-500 focus:border-red-500'
          )}
        />
        
        {/* Loading spinner */}
        {isLoading && (
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
            <LoadingSpinner size="sm" />
          </div>
        )}

        {/* Location icon */}
        {!isLoading && (
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-[var(--medium-gray)]">
            <svg
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z" />
              <circle cx="12" cy="10" r="3" />
            </svg>
          </div>
        )}
      </div>

      {/* Dropdown */}
      {isOpen && suggestions.length > 0 && (
        <div
          ref={dropdownRef}
          className="absolute z-50 w-full mt-1 bg-white border border-[var(--medium-gray)] rounded-[var(--radius-md)] shadow-lg max-h-60 overflow-y-auto"
        >
          {suggestions.map((suggestion, index) => (
            <div
              key={suggestion.placeId}
              onClick={() => handleSuggestionSelect(suggestion)}
              className={cn(
                'px-4 py-3 cursor-pointer border-b border-gray-100 last:border-b-0 transition-colors',
                'hover:bg-[var(--light-brown)] hover:bg-opacity-10',
                selectedIndex === index && 'bg-[var(--light-brown)] bg-opacity-20'
              )}
            >
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 mt-1 text-[var(--medium-gray)]">
                  <svg
                    width="14"
                    height="14"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z" />
                    <circle cx="12" cy="10" r="3" />
                  </svg>
                </div>
                <div className="flex-1 min-w-0">
                  <div className="font-medium text-[var(--primary-brown)] truncate">
                    {suggestion.mainText}
                  </div>
                  {suggestion.secondaryText && (
                    <div className="text-sm text-[var(--medium-gray)] truncate">
                      {suggestion.secondaryText}
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Error message */}
      {error && (
        <p className="mt-1 text-sm text-red-600">{error}</p>
      )}
    </div>
  )
}

/**
 * Simple location display component
 */
export const LocationDisplay: React.FC<{
  locationData: any
  showFullAddress?: boolean
  className?: string
}> = ({ locationData, showFullAddress = false, className }) => {
  if (!locationData) return null

  const displayText = showFullAddress
    ? locationData.formattedAddress
    : `${locationData.addressComponents?.locality || ''}, ${locationData.addressComponents?.administrativeAreaLevel1 || ''}`.replace(/^,\s*/, '')

  return (
    <div className={cn('flex items-center space-x-2 text-[var(--medium-gray)]', className)}>
      <svg
        width="14"
        height="14"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      >
        <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z" />
        <circle cx="12" cy="10" r="3" />
      </svg>
      <span className="text-sm">{displayText}</span>
    </div>
  )
}

/**
 * Distance filter component for location-based searches
 */
export const DistanceFilter: React.FC<{
  value?: number
  onChange: (radius: number) => void
  options?: number[]
  className?: string
  label?: string
}> = ({
  value,
  onChange,
  options = DEFAULT_LOCATION_CONFIG.DEFAULT_RADIUS_OPTIONS,
  className,
  label = 'Distance'
}) => {
  return (
    <div className={cn('w-full', className)}>
      {label && (
        <label className="block mb-2 font-semibold text-[var(--primary-brown)] font-[var(--font-ui)]">
          {label}
        </label>
      )}
      <select
        value={value || ''}
        onChange={(e) => onChange(Number(e.target.value))}
        className={cn(
          `
          w-full px-4 py-2 border-2 border-[var(--medium-gray)]
          rounded-[var(--radius-md)] font-[var(--font-ui)]
          transition-colors duration-300
          focus:outline-none focus:border-[var(--primary-brown)]
          focus:ring-0 focus:shadow-[0_0_0_3px_rgba(139,69,19,0.1)]
          bg-white
          `
        )}
      >
        <option value="">Any distance</option>
        {options.map(distance => (
          <option key={distance} value={distance}>
            Within {distance}km
          </option>
        ))}
      </select>
    </div>
  )
}
