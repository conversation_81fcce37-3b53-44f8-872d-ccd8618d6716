import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const placeId = searchParams.get('place_id')
  const sessionToken = searchParams.get('sessiontoken')
  
  if (!placeId) {
    return NextResponse.json({ error: 'place_id parameter is required' }, { status: 400 })
  }

  const apiKey = process.env.GOOGLE_PLACES_API_KEY_SERVER
  if (!apiKey) {
    return NextResponse.json({ error: 'Google Places API key not configured' }, { status: 500 })
  }

  try {
    const params = new URLSearchParams({
      place_id: placeId,
      key: apiKey,
      fields: 'place_id,formatted_address,geometry,address_components,name,types',
      language: 'en',
    })

    if (sessionToken) {
      params.append('sessiontoken', sessionToken)
    }

    const response = await fetch(
      `https://maps.googleapis.com/maps/api/place/details/json?${params}`,
      {
        headers: {
          'Accept': 'application/json',
        },
      }
    )

    if (!response.ok) {
      throw new Error(`Google Places API error: ${response.status}`)
    }

    const data = await response.json()

    if (data.status !== 'OK') {
      throw new Error(`Google Places API error: ${data.status} - ${data.error_message || 'Unknown error'}`)
    }

    const place = data.result
    if (!place) {
      throw new Error('No place data returned')
    }

    // Transform address components
    const addressComponents: any = {}
    place.address_components?.forEach((component: any) => {
      const types = component.types
      if (types.includes('locality')) {
        addressComponents.locality = component.long_name
      }
      if (types.includes('administrative_area_level_1')) {
        addressComponents.administrativeAreaLevel1 = component.long_name
      }
      if (types.includes('administrative_area_level_2')) {
        addressComponents.administrativeAreaLevel2 = component.long_name
      }
      if (types.includes('country')) {
        addressComponents.country = component.long_name
        addressComponents.countryCode = component.short_name
      }
      if (types.includes('postal_code')) {
        addressComponents.postalCode = component.long_name
      }
      if (types.includes('route')) {
        addressComponents.route = component.long_name
      }
      if (types.includes('street_number')) {
        addressComponents.streetNumber = component.long_name
      }
    })

    // Transform the response to match our expected format
    const placeDetails = {
      placeId: place.place_id,
      formattedAddress: place.formatted_address,
      name: place.name,
      coordinates: {
        latitude: place.geometry?.location?.lat || 0,
        longitude: place.geometry?.location?.lng || 0
      },
      addressComponents,
      types: place.types || [],
      viewport: place.geometry?.viewport ? {
        northeast: {
          lat: place.geometry.viewport.northeast.lat,
          lng: place.geometry.viewport.northeast.lng
        },
        southwest: {
          lat: place.geometry.viewport.southwest.lat,
          lng: place.geometry.viewport.southwest.lng
        }
      } : undefined
    }

    return NextResponse.json({
      success: true,
      data: placeDetails
    })

  } catch (error) {
    console.error('Places details API error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error occurred' 
      },
      { status: 500 }
    )
  }
}
