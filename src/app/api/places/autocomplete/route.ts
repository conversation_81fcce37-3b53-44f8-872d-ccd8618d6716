import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const input = searchParams.get('input')
  const sessionToken = searchParams.get('sessiontoken')
  const types = searchParams.get('types')
  
  if (!input) {
    return NextResponse.json({ error: 'Input parameter is required' }, { status: 400 })
  }

  const apiKey = process.env.GOOGLE_PLACES_API_KEY_SERVER
  if (!apiKey) {
    return NextResponse.json({ error: 'Google Places API key not configured' }, { status: 500 })
  }

  try {
    const params = new URLSearchParams({
      input,
      key: apiKey,
      language: 'en',
      components: 'country:za', // Restrict to South Africa
    })

    if (sessionToken) {
      params.append('sessiontoken', sessionToken)
    }

    if (types) {
      params.append('types', types)
    }

    const response = await fetch(
      `https://maps.googleapis.com/maps/api/place/autocomplete/json?${params}`,
      {
        headers: {
          'Accept': 'application/json',
        },
      }
    )

    if (!response.ok) {
      throw new Error(`Google Places API error: ${response.status}`)
    }

    const data = await response.json()

    if (data.status !== 'OK' && data.status !== 'ZERO_RESULTS') {
      throw new Error(`Google Places API error: ${data.status} - ${data.error_message || 'Unknown error'}`)
    }

    // Transform the response to match our expected format
    const predictions = data.predictions?.map((prediction: any) => ({
      placeId: prediction.place_id,
      description: prediction.description,
      mainText: prediction.structured_formatting?.main_text || prediction.description,
      secondaryText: prediction.structured_formatting?.secondary_text || '',
      types: prediction.types || [],
      matchedSubstrings: prediction.matched_substrings || []
    })) || []

    return NextResponse.json({
      success: true,
      data: predictions,
      status: data.status
    })

  } catch (error) {
    console.error('Places autocomplete API error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error occurred' 
      },
      { status: 500 }
    )
  }
}
