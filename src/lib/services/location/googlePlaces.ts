/**
 * Google Places API service for BVR Safaris
 * Handles autocomplete, place details, and geocoding functionality
 */

import {
  LocationSearchResult,
  PlaceDetails,
  GeocodeResult,
  LocationServiceConfig,
  LocationError,
  LocationErrorType,
  ServiceResponse,
  DEFAULT_LOCATION_CONFIG
} from '@/lib/types/location'

export class GooglePlacesService {
  private apiKey: string
  private config: LocationServiceConfig
  private cache = new Map<string, any>()
  private requestQueue = new Map<string, Promise<any>>()

  constructor(apiKey: string, config?: Partial<LocationServiceConfig>) {
    this.apiKey = apiKey
    this.config = {
      apiKey,
      countryRestriction: DEFAULT_LOCATION_CONFIG.COUNTRY_RESTRICTION,
      language: DEFAULT_LOCATION_CONFIG.LANGUAGE,
      region: DEFAULT_LOCATION_CONFIG.REGION,
      ...config
    }
  }

  /**
   * Get place predictions for autocomplete
   */
  async getPlacePredictions(
    input: string,
    options?: {
      types?: string[]
      componentRestrictions?: { country: string }
      locationBias?: { lat: number; lng: number; radius: number }
      sessionToken?: string
    }
  ): Promise<ServiceResponse<LocationSearchResult[]>> {
    if (!input || input.length < 2) {
      return { success: true, data: [] }
    }

    const cacheKey = `predictions_${input}_${JSON.stringify(options)}`
    
    // Check cache first
    const cached = this.getFromCache(cacheKey)
    if (cached) {
      return { success: true, data: cached, cached: true }
    }

    // Check if request is already in progress
    if (this.requestQueue.has(cacheKey)) {
      try {
        const result = await this.requestQueue.get(cacheKey)!
        return { success: true, data: result }
      } catch (error) {
        return this.handleError(error)
      }
    }

    // Create new request
    const requestPromise = this.fetchPlacePredictions(input, options)
    this.requestQueue.set(cacheKey, requestPromise)

    try {
      const results = await requestPromise
      this.setCache(cacheKey, results)
      this.requestQueue.delete(cacheKey)
      return { success: true, data: results }
    } catch (error) {
      this.requestQueue.delete(cacheKey)
      return this.handleError(error)
    }
  }

  /**
   * Get detailed place information
   */
  async getPlaceDetails(
    placeId: string,
    fields?: string[]
  ): Promise<ServiceResponse<PlaceDetails>> {
    const cacheKey = `details_${placeId}_${fields?.join(',') || 'default'}`
    
    // Check cache first
    const cached = this.getFromCache(cacheKey)
    if (cached) {
      return { success: true, data: cached, cached: true }
    }

    try {
      const details = await this.fetchPlaceDetails(placeId, fields)
      this.setCache(cacheKey, details)
      return { success: true, data: details }
    } catch (error) {
      return this.handleError(error)
    }
  }

  /**
   * Geocode an address to coordinates
   */
  async geocodeAddress(address: string): Promise<ServiceResponse<GeocodeResult>> {
    const cacheKey = `geocode_${address}`
    
    // Check cache first
    const cached = this.getFromCache(cacheKey)
    if (cached) {
      return { success: true, data: cached, cached: true }
    }

    try {
      const result = await this.fetchGeocode(address)
      this.setCache(cacheKey, result)
      return { success: true, data: result }
    } catch (error) {
      return this.handleError(error)
    }
  }

  /**
   * Reverse geocode coordinates to address
   */
  async reverseGeocode(
    lat: number, 
    lng: number
  ): Promise<ServiceResponse<PlaceDetails>> {
    const cacheKey = `reverse_${lat}_${lng}`
    
    // Check cache first
    const cached = this.getFromCache(cacheKey)
    if (cached) {
      return { success: true, data: cached, cached: true }
    }

    try {
      const result = await this.fetchReverseGeocode(lat, lng)
      this.setCache(cacheKey, result)
      return { success: true, data: result }
    } catch (error) {
      return this.handleError(error)
    }
  }

  /**
   * Private method to fetch place predictions from Google API
   */
  private async fetchPlacePredictions(
    input: string,
    options?: any
  ): Promise<LocationSearchResult[]> {
    const params = new URLSearchParams({
      input,
      key: this.apiKey,
      language: this.config.language!,
      components: `country:${options?.componentRestrictions?.country || this.config.countryRestriction}`,
    })

    if (options?.types) {
      params.append('types', options.types.join('|'))
    }

    if (options?.sessionToken) {
      params.append('sessiontoken', options.sessionToken)
    }

    const response = await fetch(
      `https://maps.googleapis.com/maps/api/place/autocomplete/json?${params}`
    )

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const data = await response.json()

    if (data.status !== 'OK' && data.status !== 'ZERO_RESULTS') {
      throw new Error(`Google Places API error: ${data.status} - ${data.error_message || 'Unknown error'}`)
    }

    return (data.predictions || []).map((prediction: any) => ({
      placeId: prediction.place_id,
      description: prediction.description,
      mainText: prediction.structured_formatting?.main_text || prediction.description,
      secondaryText: prediction.structured_formatting?.secondary_text || '',
      types: prediction.types || [],
      structuredFormatting: prediction.structured_formatting
    }))
  }

  /**
   * Private method to fetch place details from Google API
   */
  private async fetchPlaceDetails(
    placeId: string,
    fields?: string[]
  ): Promise<PlaceDetails> {
    const defaultFields = [
      'place_id',
      'formatted_address',
      'geometry',
      'address_components',
      'types',
      'name',
      'business_status'
    ]

    const params = new URLSearchParams({
      place_id: placeId,
      key: this.apiKey,
      language: this.config.language!,
      fields: (fields || defaultFields).join(',')
    })

    const response = await fetch(
      `https://maps.googleapis.com/maps/api/place/details/json?${params}`
    )

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const data = await response.json()

    if (data.status !== 'OK') {
      throw new Error(`Google Places API error: ${data.status} - ${data.error_message || 'Unknown error'}`)
    }

    const place = data.result
    return {
      placeId: place.place_id,
      formattedAddress: place.formatted_address,
      coordinates: {
        lat: place.geometry.location.lat,
        lng: place.geometry.location.lng
      },
      addressComponents: (place.address_components || []).map((component: any) => ({
        longName: component.long_name,
        shortName: component.short_name,
        types: component.types
      })),
      types: place.types || [],
      viewport: place.geometry.viewport ? {
        northeast: {
          lat: place.geometry.viewport.northeast.lat,
          lng: place.geometry.viewport.northeast.lng
        },
        southwest: {
          lat: place.geometry.viewport.southwest.lat,
          lng: place.geometry.viewport.southwest.lng
        }
      } : undefined,
      name: place.name,
      businessStatus: place.business_status
    }
  }

  /**
   * Private method to geocode address
   */
  private async fetchGeocode(address: string): Promise<GeocodeResult> {
    const params = new URLSearchParams({
      address,
      key: this.apiKey,
      language: this.config.language!,
      region: this.config.region!
    })

    const response = await fetch(
      `https://maps.googleapis.com/maps/api/geocode/json?${params}`
    )

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const data = await response.json()

    if (data.status !== 'OK') {
      throw new Error(`Geocoding API error: ${data.status} - ${data.error_message || 'Unknown error'}`)
    }

    const result = data.results[0]
    return {
      coordinates: {
        lat: result.geometry.location.lat,
        lng: result.geometry.location.lng
      },
      formattedAddress: result.formatted_address,
      addressComponents: (result.address_components || []).map((component: any) => ({
        longName: component.long_name,
        shortName: component.short_name,
        types: component.types
      })),
      placeId: result.place_id,
      types: result.types || []
    }
  }

  /**
   * Private method to reverse geocode coordinates
   */
  private async fetchReverseGeocode(lat: number, lng: number): Promise<PlaceDetails> {
    const params = new URLSearchParams({
      latlng: `${lat},${lng}`,
      key: this.apiKey,
      language: this.config.language!
    })

    const response = await fetch(
      `https://maps.googleapis.com/maps/api/geocode/json?${params}`
    )

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const data = await response.json()

    if (data.status !== 'OK') {
      throw new Error(`Reverse geocoding API error: ${data.status} - ${data.error_message || 'Unknown error'}`)
    }

    const result = data.results[0]
    return {
      placeId: result.place_id,
      formattedAddress: result.formatted_address,
      coordinates: { lat, lng },
      addressComponents: (result.address_components || []).map((component: any) => ({
        longName: component.long_name,
        shortName: component.short_name,
        types: component.types
      })),
      types: result.types || []
    }
  }

  /**
   * Cache management
   */
  private getFromCache(key: string): any {
    const entry = this.cache.get(key)
    if (!entry) return null

    const { data, timestamp, ttl } = entry
    if (Date.now() - timestamp > ttl) {
      this.cache.delete(key)
      return null
    }

    return data
  }

  private setCache(key: string, data: any, ttl = DEFAULT_LOCATION_CONFIG.CACHE_TTL): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    })

    // Clean up old entries periodically
    if (this.cache.size > 100) {
      this.cleanupCache()
    }
  }

  private cleanupCache(): void {
    const now = Date.now()
    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > entry.ttl) {
        this.cache.delete(key)
      }
    }
  }

  /**
   * Error handling
   */
  private handleError(error: any): ServiceResponse<any> {
    let locationError: LocationError

    if (error.message?.includes('quota') || error.message?.includes('OVER_QUERY_LIMIT')) {
      locationError = {
        type: LocationErrorType.QUOTA_EXCEEDED,
        message: 'API quota exceeded. Please try again later.',
        originalError: error
      }
    } else if (error.message?.includes('PERMISSION_DENIED')) {
      locationError = {
        type: LocationErrorType.PERMISSION_DENIED,
        message: 'Permission denied. Please check API key configuration.',
        originalError: error
      }
    } else if (error.message?.includes('INVALID_REQUEST')) {
      locationError = {
        type: LocationErrorType.INVALID_REQUEST,
        message: 'Invalid request parameters.',
        originalError: error
      }
    } else if (error.name === 'TypeError' || error.message?.includes('fetch')) {
      locationError = {
        type: LocationErrorType.NETWORK_ERROR,
        message: 'Network error. Please check your internet connection.',
        originalError: error
      }
    } else {
      locationError = {
        type: LocationErrorType.UNKNOWN_ERROR,
        message: error.message || 'An unknown error occurred.',
        originalError: error
      }
    }

    return { success: false, error: locationError }
  }

  /**
   * Clear cache
   */
  clearCache(): void {
    this.cache.clear()
  }
}
