/**
 * Location services entry point for BVR Safaris
 * Provides a unified interface for all location-related functionality
 */

import { GooglePlacesService } from './googlePlaces'
import { DistanceService, GeoUtils } from './distance'
import {
  LocationSearchResult,
  PlaceDetails,
  GeocodeResult,
  LocationData,
  LocationError,
  LocationErrorType,
  ServiceResponse,
  BrowserLocationResult,
  BrowserLocationOptions,
  DEFAULT_LOCATION_CONFIG,
  PROVINCE_MAPPING
} from '@/lib/types/location'
import { GeoPoint } from 'firebase/firestore'
import { SouthAfricanProvince } from '@/lib/constants'

/**
 * Main location service class that orchestrates all location functionality
 */
export class LocationService {
  private googlePlaces: GooglePlacesService
  private static instance: LocationService

  constructor(apiKey: string) {
    this.googlePlaces = new GooglePlacesService(apiKey)
  }

  /**
   * Get singleton instance
   */
  static getInstance(): LocationService {
    if (!LocationService.instance) {
      const apiKey = process.env.NEXT_PUBLIC_GOOGLE_PLACES_API_KEY
      if (!apiKey) {
        throw new Error('Google Places API key not configured')
      }
      LocationService.instance = new LocationService(apiKey)
    }
    return LocationService.instance
  }

  /**
   * Get place predictions for autocomplete
   */
  async getPlacePredictions(
    input: string,
    options?: {
      types?: string[]
      sessionToken?: string
    }
  ): Promise<ServiceResponse<LocationSearchResult[]>> {
    return this.googlePlaces.getPlacePredictions(input, {
      ...options,
      componentRestrictions: { country: DEFAULT_LOCATION_CONFIG.COUNTRY_RESTRICTION }
    })
  }

  /**
   * Get detailed place information and convert to LocationData
   */
  async getLocationData(placeId: string): Promise<ServiceResponse<LocationData>> {
    const response = await this.googlePlaces.getPlaceDetails(placeId)
    
    if (!response.success || !response.data) {
      return response as ServiceResponse<LocationData>
    }

    const placeDetails = response.data
    const locationData = this.convertToLocationData(placeDetails)
    
    return { success: true, data: locationData, cached: response.cached }
  }

  /**
   * Geocode an address and return LocationData
   */
  async geocodeToLocationData(address: string): Promise<ServiceResponse<LocationData>> {
    const response = await this.googlePlaces.geocodeAddress(address)
    
    if (!response.success || !response.data) {
      return response as ServiceResponse<LocationData>
    }

    const geocodeResult = response.data
    
    // Get place details if we have a place ID
    if (geocodeResult.placeId) {
      return this.getLocationData(geocodeResult.placeId)
    }

    // Create LocationData from geocode result
    const locationData: LocationData = {
      placeId: geocodeResult.placeId || '',
      formattedAddress: geocodeResult.formattedAddress,
      coordinates: new GeoPoint(geocodeResult.coordinates.lat, geocodeResult.coordinates.lng),
      addressComponents: this.parseAddressComponents(geocodeResult.addressComponents),
      placeTypes: geocodeResult.types
    }

    return { success: true, data: locationData, cached: response.cached }
  }

  /**
   * Get user's current location using browser geolocation
   */
  async getCurrentLocation(
    options?: BrowserLocationOptions
  ): Promise<ServiceResponse<BrowserLocationResult>> {
    return new Promise((resolve) => {
      if (!navigator.geolocation) {
        resolve({
          success: false,
          error: {
            type: LocationErrorType.PERMISSION_DENIED,
            message: 'Geolocation is not supported by this browser'
          }
        })
        return
      }

      const defaultOptions: BrowserLocationOptions = {
        enableHighAccuracy: true,
        timeout: DEFAULT_LOCATION_CONFIG.GEOLOCATION_TIMEOUT,
        maximumAge: DEFAULT_LOCATION_CONFIG.GEOLOCATION_MAX_AGE,
        ...options
      }

      navigator.geolocation.getCurrentPosition(
        (position) => {
          const result: BrowserLocationResult = {
            coordinates: {
              lat: position.coords.latitude,
              lng: position.coords.longitude
            },
            accuracy: position.coords.accuracy,
            timestamp: position.timestamp
          }
          resolve({ success: true, data: result })
        },
        (error) => {
          let errorType: LocationErrorType
          let message: string

          switch (error.code) {
            case error.PERMISSION_DENIED:
              errorType = LocationErrorType.PERMISSION_DENIED
              message = 'Location access denied by user'
              break
            case error.POSITION_UNAVAILABLE:
              errorType = LocationErrorType.API_ERROR
              message = 'Location information unavailable'
              break
            case error.TIMEOUT:
              errorType = LocationErrorType.NETWORK_ERROR
              message = 'Location request timed out'
              break
            default:
              errorType = LocationErrorType.UNKNOWN_ERROR
              message = 'Unknown location error'
          }

          resolve({
            success: false,
            error: { type: errorType, message, originalError: error }
          })
        },
        defaultOptions
      )
    })
  }

  /**
   * Convert PlaceDetails to LocationData format
   */
  private convertToLocationData(placeDetails: PlaceDetails): LocationData {
    return {
      placeId: placeDetails.placeId,
      formattedAddress: placeDetails.formattedAddress,
      coordinates: new GeoPoint(placeDetails.coordinates.lat, placeDetails.coordinates.lng),
      addressComponents: this.parseAddressComponents(placeDetails.addressComponents),
      placeTypes: placeDetails.types,
      viewport: placeDetails.viewport ? {
        northeast: new GeoPoint(placeDetails.viewport.northeast.lat, placeDetails.viewport.northeast.lng),
        southwest: new GeoPoint(placeDetails.viewport.southwest.lat, placeDetails.viewport.southwest.lng)
      } : undefined,
      name: placeDetails.name
    }
  }

  /**
   * Parse address components into structured format
   */
  private parseAddressComponents(components: any[]): LocationData['addressComponents'] {
    const parsed: LocationData['addressComponents'] = {
      country: 'South Africa',
      administrativeAreaLevel1: ''
    }

    for (const component of components) {
      const types = component.types

      if (types.includes('street_number')) {
        parsed.streetNumber = component.longName
      } else if (types.includes('route')) {
        parsed.route = component.longName
      } else if (types.includes('locality')) {
        parsed.locality = component.longName
      } else if (types.includes('sublocality') || types.includes('sublocality_level_1')) {
        parsed.sublocality = component.longName
      } else if (types.includes('administrative_area_level_1')) {
        parsed.administrativeAreaLevel1 = component.longName
      } else if (types.includes('administrative_area_level_2')) {
        parsed.administrativeAreaLevel2 = component.longName
      } else if (types.includes('postal_code')) {
        parsed.postalCode = component.longName
      } else if (types.includes('country')) {
        parsed.country = component.longName
      }
    }

    return parsed
  }

  /**
   * Map location data to South African province
   */
  static mapToSouthAfricanProvince(locationData: LocationData): SouthAfricanProvince | null {
    const provinceName = locationData.addressComponents.administrativeAreaLevel1
    
    // Direct mapping
    if (provinceName && PROVINCE_MAPPING[provinceName]) {
      return PROVINCE_MAPPING[provinceName] as SouthAfricanProvince
    }

    // Fuzzy matching for common variations
    const normalizedProvince = provinceName?.toLowerCase().trim()
    
    const provinceMap: Record<string, SouthAfricanProvince> = {
      'eastern cape': 'Eastern Cape',
      'free state': 'Free State',
      'gauteng': 'Gauteng',
      'kwazulu-natal': 'KwaZulu-Natal',
      'kzn': 'KwaZulu-Natal',
      'limpopo': 'Limpopo',
      'mpumalanga': 'Mpumalanga',
      'northern cape': 'Northern Cape',
      'north west': 'North West',
      'northwest': 'North West',
      'western cape': 'Western Cape'
    }

    return normalizedProvince ? provinceMap[normalizedProvince] || null : null
  }

  /**
   * Create searchable location string
   */
  static createSearchableLocation(locationData: LocationData): string {
    return GeoUtils.createSearchableLocation(locationData.addressComponents)
  }

  /**
   * Generate geohash for location
   */
  static generateGeoHash(coordinates: { lat: number; lng: number }): string {
    return GeoUtils.generateSimpleGeoHash(coordinates.lat, coordinates.lng)
  }

  /**
   * Clear all caches
   */
  clearCache(): void {
    this.googlePlaces.clearCache()
  }
}

// Export distance utilities
export { DistanceService, GeoUtils }

// Export types
export type {
  LocationSearchResult,
  PlaceDetails,
  LocationData,
  GeocodeResult,
  LocationError,
  ServiceResponse,
  BrowserLocationResult
}

// Export error types
export { LocationErrorType }

// Create and export default instance
let defaultLocationService: LocationService | null = null

export function getLocationService(): LocationService {
  if (!defaultLocationService) {
    defaultLocationService = LocationService.getInstance()
  }
  return defaultLocationService
}

// Utility functions for common operations
export const locationUtils = {
  /**
   * Format coordinates for display
   */
  formatCoordinates(lat: number, lng: number): string {
    return `${lat.toFixed(6)}, ${lng.toFixed(6)}`
  },

  /**
   * Validate South African coordinates
   */
  isValidSouthAfricanLocation(lat: number, lng: number): boolean {
    return DistanceService.isValidCoordinate({ lat, lng }) && 
           DistanceService.isWithinSouthAfrica({ lat, lng })
  },

  /**
   * Get display name for location
   */
  getLocationDisplayName(locationData: LocationData): string {
    const { addressComponents } = locationData
    
    if (addressComponents.locality && addressComponents.administrativeAreaLevel1) {
      return `${addressComponents.locality}, ${addressComponents.administrativeAreaLevel1}`
    } else if (addressComponents.administrativeAreaLevel2 && addressComponents.administrativeAreaLevel1) {
      return `${addressComponents.administrativeAreaLevel2}, ${addressComponents.administrativeAreaLevel1}`
    } else if (addressComponents.administrativeAreaLevel1) {
      return addressComponents.administrativeAreaLevel1
    } else {
      return locationData.formattedAddress
    }
  },

  /**
   * Extract city/town from location data
   */
  getCityFromLocation(locationData: LocationData): string | null {
    return locationData.addressComponents.locality || 
           locationData.addressComponents.sublocality || 
           locationData.addressComponents.administrativeAreaLevel2 || 
           null
  }
}
