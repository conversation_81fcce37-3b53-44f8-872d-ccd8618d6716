/**
 * Distance calculation utilities for BVR Safaris geolocation
 * Provides accurate distance calculations and geographic utilities
 */

import { DistanceResult } from '@/lib/types/location'

export class DistanceService {
  /**
   * Calculate distance between two points using Haversine formula
   * Returns distance in kilometers
   */
  static calculateDistance(
    point1: { lat: number; lng: number },
    point2: { lat: number; lng: number }
  ): number {
    const R = 6371 // Earth's radius in kilometers
    const dLat = this.toRadians(point2.lat - point1.lat)
    const dLng = this.toRadians(point2.lng - point1.lng)
    
    const a = 
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(this.toRadians(point1.lat)) * Math.cos(this.toRadians(point2.lat)) *
      Math.sin(dLng / 2) * Math.sin(dLng / 2)
    
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
    const distance = R * c
    
    return Math.round(distance * 100) / 100 // Round to 2 decimal places
  }

  /**
   * Check if a point is within a specified radius of a center point
   */
  static isWithinRadius(
    center: { lat: number; lng: number },
    point: { lat: number; lng: number },
    radiusKm: number
  ): boolean {
    const distance = this.calculateDistance(center, point)
    return distance <= radiusKm
  }

  /**
   * Calculate bounding box for efficient geographic queries
   * Returns northeast and southwest corners of a box around a center point
   */
  static getBoundingBox(
    center: { lat: number; lng: number },
    radiusKm: number
  ): {
    northeast: { lat: number; lng: number }
    southwest: { lat: number; lng: number }
  } {
    const R = 6371 // Earth's radius in kilometers
    const lat = this.toRadians(center.lat)
    const lng = this.toRadians(center.lng)
    
    // Angular distance in radians on a great circle
    const angular = radiusKm / R
    
    const minLat = lat - angular
    const maxLat = lat + angular
    
    let minLng: number
    let maxLng: number
    
    if (minLat > this.toRadians(-90) && maxLat < this.toRadians(90)) {
      const deltaLng = Math.asin(Math.sin(angular) / Math.cos(lat))
      minLng = lng - deltaLng
      maxLng = lng + deltaLng
      
      if (minLng < this.toRadians(-180)) minLng += 2 * Math.PI
      if (maxLng > this.toRadians(180)) maxLng -= 2 * Math.PI
    } else {
      // A pole is within the distance
      minLat = Math.max(minLat, this.toRadians(-90))
      maxLat = Math.min(maxLat, this.toRadians(90))
      minLng = this.toRadians(-180)
      maxLng = this.toRadians(180)
    }
    
    return {
      southwest: {
        lat: this.toDegrees(minLat),
        lng: this.toDegrees(minLng)
      },
      northeast: {
        lat: this.toDegrees(maxLat),
        lng: this.toDegrees(maxLng)
      }
    }
  }

  /**
   * Sort an array of points by distance from a center point
   */
  static sortByDistance<T extends { lat: number; lng: number }>(
    points: T[],
    center: { lat: number; lng: number }
  ): (T & { distance: number })[] {
    return points
      .map(point => ({
        ...point,
        distance: this.calculateDistance(center, point)
      }))
      .sort((a, b) => a.distance - b.distance)
  }

  /**
   * Filter points within a specified radius
   */
  static filterByRadius<T extends { lat: number; lng: number }>(
    points: T[],
    center: { lat: number; lng: number },
    radiusKm: number
  ): (T & { distance: number })[] {
    return points
      .map(point => ({
        ...point,
        distance: this.calculateDistance(center, point)
      }))
      .filter(point => point.distance <= radiusKm)
      .sort((a, b) => a.distance - b.distance)
  }

  /**
   * Get the center point (centroid) of multiple coordinates
   */
  static getCenterPoint(
    points: { lat: number; lng: number }[]
  ): { lat: number; lng: number } {
    if (points.length === 0) {
      throw new Error('Cannot calculate center of empty points array')
    }

    if (points.length === 1) {
      return points[0]
    }

    let x = 0
    let y = 0
    let z = 0

    for (const point of points) {
      const lat = this.toRadians(point.lat)
      const lng = this.toRadians(point.lng)

      x += Math.cos(lat) * Math.cos(lng)
      y += Math.cos(lat) * Math.sin(lng)
      z += Math.sin(lat)
    }

    const total = points.length
    x = x / total
    y = y / total
    z = z / total

    const centralLng = Math.atan2(y, x)
    const centralSquareRoot = Math.sqrt(x * x + y * y)
    const centralLat = Math.atan2(z, centralSquareRoot)

    return {
      lat: this.toDegrees(centralLat),
      lng: this.toDegrees(centralLng)
    }
  }

  /**
   * Calculate the bearing (direction) from one point to another
   * Returns bearing in degrees (0-360)
   */
  static calculateBearing(
    from: { lat: number; lng: number },
    to: { lat: number; lng: number }
  ): number {
    const dLng = this.toRadians(to.lng - from.lng)
    const lat1 = this.toRadians(from.lat)
    const lat2 = this.toRadians(to.lat)

    const y = Math.sin(dLng) * Math.cos(lat2)
    const x = Math.cos(lat1) * Math.sin(lat2) - Math.sin(lat1) * Math.cos(lat2) * Math.cos(dLng)

    let bearing = this.toDegrees(Math.atan2(y, x))
    return (bearing + 360) % 360 // Normalize to 0-360
  }

  /**
   * Format distance for display
   */
  static formatDistance(distanceKm: number): string {
    if (distanceKm < 1) {
      return `${Math.round(distanceKm * 1000)}m`
    } else if (distanceKm < 10) {
      return `${distanceKm.toFixed(1)}km`
    } else {
      return `${Math.round(distanceKm)}km`
    }
  }

  /**
   * Check if a point is within South Africa's approximate bounds
   */
  static isWithinSouthAfrica(point: { lat: number; lng: number }): boolean {
    // Approximate bounds of South Africa
    const bounds = {
      north: -22.0,
      south: -35.0,
      east: 33.0,
      west: 16.0
    }

    return (
      point.lat >= bounds.south &&
      point.lat <= bounds.north &&
      point.lng >= bounds.west &&
      point.lng <= bounds.east
    )
  }

  /**
   * Get approximate travel time based on distance (rough estimation)
   * Assumes average speed of 80 km/h for highway travel
   */
  static estimateTravelTime(distanceKm: number): DistanceResult {
    const averageSpeedKmh = 80
    const durationMinutes = Math.round((distanceKm / averageSpeedKmh) * 60)
    
    return {
      distance: distanceKm,
      duration: durationMinutes
    }
  }

  /**
   * Format travel time for display
   */
  static formatTravelTime(minutes: number): string {
    if (minutes < 60) {
      return `${minutes} min`
    } else {
      const hours = Math.floor(minutes / 60)
      const remainingMinutes = minutes % 60
      if (remainingMinutes === 0) {
        return `${hours}h`
      } else {
        return `${hours}h ${remainingMinutes}m`
      }
    }
  }

  /**
   * Validate coordinates
   */
  static isValidCoordinate(point: { lat: number; lng: number }): boolean {
    return (
      typeof point.lat === 'number' &&
      typeof point.lng === 'number' &&
      point.lat >= -90 &&
      point.lat <= 90 &&
      point.lng >= -180 &&
      point.lng <= 180 &&
      !isNaN(point.lat) &&
      !isNaN(point.lng)
    )
  }

  /**
   * Convert degrees to radians
   */
  private static toRadians(degrees: number): number {
    return degrees * (Math.PI / 180)
  }

  /**
   * Convert radians to degrees
   */
  private static toDegrees(radians: number): number {
    return radians * (180 / Math.PI)
  }
}

/**
 * Utility functions for working with geographic data
 */
export class GeoUtils {
  /**
   * Generate a simple geohash for efficient geographic indexing
   * This is a simplified version - for production, consider using a proper geohash library
   */
  static generateSimpleGeoHash(
    lat: number,
    lng: number,
    precision: number = 6
  ): string {
    const latRange = [-90, 90]
    const lngRange = [-180, 180]
    let hash = ''
    let isEven = true
    let bit = 0
    let ch = 0

    while (hash.length < precision) {
      if (isEven) {
        const mid = (lngRange[0] + lngRange[1]) / 2
        if (lng >= mid) {
          ch |= (1 << (4 - bit))
          lngRange[0] = mid
        } else {
          lngRange[1] = mid
        }
      } else {
        const mid = (latRange[0] + latRange[1]) / 2
        if (lat >= mid) {
          ch |= (1 << (4 - bit))
          latRange[0] = mid
        } else {
          latRange[1] = mid
        }
      }

      isEven = !isEven
      bit++

      if (bit === 5) {
        hash += this.base32[ch]
        bit = 0
        ch = 0
      }
    }

    return hash
  }

  private static base32 = '0123456789bcdefghjkmnpqrstuvwxyz'

  /**
   * Create a searchable location string from address components
   */
  static createSearchableLocation(addressComponents: any): string {
    const components = []
    
    if (addressComponents.route) components.push(addressComponents.route)
    if (addressComponents.locality) components.push(addressComponents.locality)
    if (addressComponents.sublocality) components.push(addressComponents.sublocality)
    if (addressComponents.administrativeAreaLevel2) components.push(addressComponents.administrativeAreaLevel2)
    if (addressComponents.administrativeAreaLevel1) components.push(addressComponents.administrativeAreaLevel1)
    if (addressComponents.postalCode) components.push(addressComponents.postalCode)
    
    return components.join(' ').toLowerCase()
  }
}
