{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__14115bab._.js", "server/edge/chunks/edge-wrapper_1a855b9f.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*){(\\\\.json)}?", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "7rEDkKo0B4pBKmoVUjF2NsDAxtzTHoZ1CXj+2mWPgnY=", "__NEXT_PREVIEW_MODE_ID": "38ed8cd3c5ce51533ce778fb3c9a2728", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "7d9cabb63f93c94f023df21ceaaea80a601f6257296bed2f4464145d40484ace", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "c98cfd6b673fa0d790694f88e204996f9c291385da27f69bdce2ece04401f985"}}}, "instrumentation": null, "functions": {}}