"use strict";exports.id=546,exports.ids=[546],exports.modules={474:(e,t,a)=>{a.d(t,{C2:()=>o,QQ:()=>i,eg:()=>n,zi:()=>l});var r=a(75535),d=a(50944);function s(e){if(!e.exists())return null;let t=e.data();return{id:e.id,...t,createdAt:t?.createdAt?.toDate?.()||t?.createdAt,updatedAt:t?.updatedAt?.toDate?.()||t?.updatedAt}}let i={async get(e){let t=(0,r.H9)(d.db,"users",e);return s(await (0,r.x7)(t))},async create(e,t){let a=(0,r.H9)(d.db,"users",e),s=new Date;await (0,r.BN)(a,{...t,createdAt:r.Dc.fromDate(s),updatedAt:r.Dc.fromDate(s)})},async update(e,t){let a=(0,r.H9)(d.db,"users",e);await (0,r.mZ)(a,{...t,updatedAt:r.Dc.now()})}},n={async getAll(e){let t=(0,r.rJ)(d.db,"farms");return e?.isActive!==void 0&&(t=(0,r.P)(t,(0,r._M)("isActive","==",e.isActive))),e?.featured!==void 0&&(t=(0,r.P)(t,(0,r._M)("featured","==",e.featured))),e?.province&&(t=(0,r.P)(t,(0,r._M)("province","==",e.province))),e?.activityType&&(t=(0,r.P)(t,(0,r._M)("activityTypes","==",e.activityType))),t=(0,r.P)(t,(0,r.My)("createdAt","desc")),e?.limit&&(t=(0,r.P)(t,(0,r.AB)(e.limit))),(await (0,r.GG)(t)).docs.map(e=>s(e)).filter(Boolean)},async get(e){let t=(0,r.H9)(d.db,"farms",e);return s(await (0,r.x7)(t))},async getByOwner(e){let t=(0,r.P)((0,r.rJ)(d.db,"farms"),(0,r._M)("ownerId","==",e),(0,r.My)("createdAt","desc"));return(await (0,r.GG)(t)).docs.map(e=>s(e)).filter(Boolean)},async getActive(e){let t=(0,r.P)((0,r.rJ)(d.db,"farms"),(0,r._M)("isActive","==",!0),(0,r.My)("createdAt","desc"));return e&&(t=(0,r.P)(t,(0,r.AB)(e))),(await (0,r.GG)(t)).docs.map(e=>s(e)).filter(Boolean)},async create(e){let t=new Date;return(await (0,r.gS)((0,r.rJ)(d.db,"farms"),{...e,createdAt:r.Dc.fromDate(t),updatedAt:r.Dc.fromDate(t)})).id},async update(e,t){let a=(0,r.H9)(d.db,"farms",e);await (0,r.mZ)(a,{...t,updatedAt:r.Dc.now()})},async delete(e){let t=(0,r.H9)(d.db,"farms",e);await (0,r.kd)(t)},async addImage(e,t){let a=new Date;return(await (0,r.gS)((0,r.rJ)(d.db,"farms",e,"images"),{...t,createdAt:r.Dc.fromDate(a),updatedAt:r.Dc.fromDate(a)})).id},async getImages(e){let t=(0,r.P)((0,r.rJ)(d.db,"farms",e,"images"),(0,r.My)("displayOrder","asc"),(0,r.My)("createdAt","asc"));return(await (0,r.GG)(t)).docs.map(e=>s(e)).filter(Boolean)},async deleteImage(e,t){let a=(0,r.H9)(d.db,"farms",e,"images",t);await (0,r.kd)(a)}},l={async getAll(e){let t=(0,r.rJ)(d.db,"bookings");return e?.hunterId&&(t=(0,r.P)(t,(0,r._M)("hunterId","==",e.hunterId))),e?.farmId&&(t=(0,r.P)(t,(0,r._M)("farmId","==",e.farmId))),e?.status&&(t=(0,r.P)(t,(0,r._M)("status","==",e.status))),t=(0,r.P)(t,(0,r.My)("createdAt","desc")),e?.limit&&(t=(0,r.P)(t,(0,r.AB)(e.limit))),(await (0,r.GG)(t)).docs.map(e=>s(e)).filter(Boolean)},async get(e){let t=(0,r.H9)(d.db,"bookings",e);return s(await (0,r.x7)(t))},async create(e){let t=new Date,a=`BVR-${t.getFullYear()}${(t.getMonth()+1).toString().padStart(2,"0")}${t.getDate().toString().padStart(2,"0")}-${Math.random().toString(36).substring(2,8).toUpperCase()}`;return(await (0,r.gS)((0,r.rJ)(d.db,"bookings"),{...e,bookingReference:a,createdAt:r.Dc.fromDate(t),updatedAt:r.Dc.fromDate(t)})).id},async update(e,t){let a=(0,r.H9)(d.db,"bookings",e);await (0,r.mZ)(a,{...t,updatedAt:r.Dc.now()})}},o={async getByFarm(e){let t=(0,r.P)((0,r.rJ)(d.db,"farms",e,"reviews"),(0,r._M)("isPublic","==",!0),(0,r.My)("createdAt","desc"));return(await (0,r.GG)(t)).docs.map(e=>s(e)).filter(Boolean)},async create(e,t){let a=new Date;return(await (0,r.gS)((0,r.rJ)(d.db,"farms",e,"reviews"),{...t,createdAt:r.Dc.fromDate(a),updatedAt:r.Dc.fromDate(a)})).id},async update(e,t,a){let s=(0,r.H9)(d.db,"farms",e,"reviews",t);await (0,r.mZ)(s,{...a,updatedAt:r.Dc.now()})}}},9776:(e,t,a)=>{a.d(t,{k:()=>s});var r=a(60687),d=a(4780);function s({size:e="md",className:t}){return(0,r.jsx)("div",{className:(0,d.cn)("flex items-center justify-center",t),children:(0,r.jsx)("div",{className:(0,d.cn)("animate-spin rounded-full border-2 border-white border-t-transparent",{sm:"w-4 h-4",md:"w-6 h-6",lg:"w-12 h-12"}[e])})})}},28749:(e,t,a)=>{a.d(t,{MH:()=>m,Wu:()=>l,ZB:()=>c,Zp:()=>n,aR:()=>o});var r=a(60687),d=a(43210),s=a(30474),i=a(4780);let n=(0,d.forwardRef)(({className:e,hover:t=!0,children:a,...d},s)=>(0,r.jsx)("div",{ref:s,className:(0,i.cn)(`
          bg-white rounded-[var(--radius-lg)] shadow-[var(--shadow-md)] 
          overflow-hidden transition-all duration-300
          `,t&&"hover:transform hover:-translate-y-1 hover:shadow-[var(--shadow-xl)]",e),...d,children:a}));n.displayName="Card";let l=(0,d.forwardRef)(({className:e,...t},a)=>(0,r.jsx)("div",{ref:a,className:(0,i.cn)("p-[var(--space-lg)]",e),...t}));l.displayName="CardContent";let o=(0,d.forwardRef)(({className:e,...t},a)=>(0,r.jsx)("div",{ref:a,className:(0,i.cn)("px-[var(--space-lg)] pt-[var(--space-lg)]",e),...t}));o.displayName="CardHeader";let c=(0,d.forwardRef)(({className:e,...t},a)=>(0,r.jsx)("h3",{ref:a,className:(0,i.cn)("text-xl font-bold mb-2 text-[var(--primary-brown)] font-[var(--font-display)]",e),...t}));c.displayName="CardTitle";let m=(0,d.forwardRef)(({className:e,src:t,alt:a,children:d,...n},l)=>(0,r.jsx)("div",{ref:l,className:(0,i.cn)("relative w-full h-48 bg-gradient-to-br from-[var(--primary-green)] to-[var(--secondary-sunset)] flex items-center justify-center text-white text-5xl",e),...n,children:t?(0,r.jsx)(s.default,{src:t,alt:a||"",fill:!0,className:"object-cover"}):d}));m.displayName="CardImage"},30474:(e,t,a)=>{a.d(t,{default:()=>d.a});var r=a(31261),d=a.n(r)},31261:(e,t,a)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var a in t)Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}(t,{default:function(){return l},getImageProps:function(){return n}});let r=a(14985),d=a(44953),s=a(46533),i=r._(a(1933));function n(e){let{props:t}=(0,d.getImgProps)(e,{defaultLoader:i.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,a]of Object.entries(t))void 0===a&&delete t[e];return{props:t}}let l=s.Image},51907:(e,t,a)=>{a.d(t,{p:()=>i});var r=a(60687),d=a(43210),s=a(4780);let i=(0,d.forwardRef)(({className:e,type:t,label:a,error:d,...i},n)=>(0,r.jsxs)("div",{className:"w-full",children:[a&&(0,r.jsx)("label",{className:"block mb-2 font-semibold text-[var(--primary-brown)] font-[var(--font-ui)]",children:a}),(0,r.jsx)("input",{type:t,className:(0,s.cn)(`
            w-full px-4 py-2 border-2 border-[var(--medium-gray)] 
            rounded-[var(--radius-md)] font-[var(--font-ui)] 
            transition-colors duration-300
            focus:outline-none focus:border-[var(--primary-brown)] 
            focus:ring-0 focus:shadow-[0_0_0_3px_rgba(139,69,19,0.1)]
            placeholder:text-[var(--medium-gray)]
            `,d&&"border-red-500 focus:border-red-500",e),ref:n,...i}),d&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-600",children:d})]}));i.displayName="Input"},70440:(e,t,a)=>{a.r(t),a.d(t,{default:()=>d});var r=a(31658);let d=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},71170:(e,t,a)=>{a.d(t,{E:()=>i});var r=a(60687),d=a(43210),s=a(4780);let i=(0,d.forwardRef)(({className:e,variant:t="default",...a},d)=>(0,r.jsx)("span",{ref:d,className:(0,s.cn)("inline-flex items-center px-2 py-1 rounded-[var(--radius-sm)] text-sm font-semibold",{hunting:"bg-[var(--hunting-accent)] text-white",photo:"bg-[var(--photo-accent)] text-white",location:"bg-[var(--secondary-stone)] text-white",default:"bg-[var(--medium-gray)] text-white"}[t],e),...a}));i.displayName="Badge"},89173:(e,t,a)=>{a.d(t,{t:()=>c});var r=a(60687),d=a(43210),s=a(30474),i=a(9776);let n=["IMG_6015-min.JPG","IMG_6077-min.JPG","IMG_6207-min.JPG","IMG_6297-min.JPG","IMG_6333-min.JPG","IMG_6395-min.JPG","IMG_6498-min.JPG","IMG_6610-min.JPG","IMG_6632-min.JPG","IMG_6695-min.JPG","IMG_6738-min.JPG","IMG_6744-min.JPG","IMG_6784-min.JPG"],l=`/banner_images/${n[0]}`;var o=a(4780);function c({className:e,children:t,priority:a=!1,onImageLoad:n}){let[c,m]=(0,d.useState)(l),[u,f]=(0,d.useState)(!0);return(0,r.jsxs)("div",{className:(0,o.cn)("relative w-full h-full overflow-hidden",e),children:[u&&(0,r.jsx)("div",{className:"absolute inset-0 z-10 bg-gradient-to-br from-[var(--primary-green)] to-[var(--secondary-sunset)] flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center text-white",children:[(0,r.jsx)(i.k,{size:"lg",className:"mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-sm font-medium",children:"Loading beautiful safari imagery..."})]})}),(0,r.jsx)(s.default,{src:c,alt:"Safari landscape",fill:!0,className:"object-cover",priority:a,onLoad:()=>{f(!1)},onError:()=>{console.error("Failed to load hero image, falling back to default"),m(l),f(!1)},sizes:"100vw"}),(0,r.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-40"}),t&&(0,r.jsx)("div",{className:"absolute inset-0 z-20",children:t})]})}}};