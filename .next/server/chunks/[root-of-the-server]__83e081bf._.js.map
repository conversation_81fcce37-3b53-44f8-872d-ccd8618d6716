{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/src/personal/bvr-safaris/src/app/api/places/autocomplete/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\n\nexport async function GET(request: NextRequest) {\n  const { searchParams } = new URL(request.url)\n  const input = searchParams.get('input')\n  const sessionToken = searchParams.get('sessiontoken')\n  const types = searchParams.get('types')\n  \n  if (!input) {\n    return NextResponse.json({ error: 'Input parameter is required' }, { status: 400 })\n  }\n\n  const apiKey = process.env.GOOGLE_PLACES_API_KEY_SERVER\n  if (!apiKey) {\n    return NextResponse.json({ error: 'Google Places API key not configured' }, { status: 500 })\n  }\n\n  try {\n    const params = new URLSearchParams({\n      input,\n      key: apiKey,\n      language: 'en',\n      components: 'country:za', // Restrict to South Africa\n    })\n\n    if (sessionToken) {\n      params.append('sessiontoken', sessionToken)\n    }\n\n    if (types) {\n      params.append('types', types)\n    }\n\n    const response = await fetch(\n      `https://maps.googleapis.com/maps/api/place/autocomplete/json?${params}`,\n      {\n        headers: {\n          'Accept': 'application/json',\n        },\n      }\n    )\n\n    if (!response.ok) {\n      throw new Error(`Google Places API error: ${response.status}`)\n    }\n\n    const data = await response.json()\n\n    if (data.status !== 'OK' && data.status !== 'ZERO_RESULTS') {\n      throw new Error(`Google Places API error: ${data.status} - ${data.error_message || 'Unknown error'}`)\n    }\n\n    // Transform the response to match our expected format\n    const predictions = data.predictions?.map((prediction: any) => ({\n      placeId: prediction.place_id,\n      description: prediction.description,\n      mainText: prediction.structured_formatting?.main_text || prediction.description,\n      secondaryText: prediction.structured_formatting?.secondary_text || '',\n      types: prediction.types || [],\n      matchedSubstrings: prediction.matched_substrings || []\n    })) || []\n\n    return NextResponse.json({\n      success: true,\n      data: predictions,\n      status: data.status\n    })\n\n  } catch (error) {\n    console.error('Places autocomplete API error:', error)\n    return NextResponse.json(\n      { \n        success: false, \n        error: error instanceof Error ? error.message : 'Unknown error occurred' \n      },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEO,eAAe,IAAI,OAAoB;IAC5C,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;IAC5C,MAAM,QAAQ,aAAa,GAAG,CAAC;IAC/B,MAAM,eAAe,aAAa,GAAG,CAAC;IACtC,MAAM,QAAQ,aAAa,GAAG,CAAC;IAE/B,IAAI,CAAC,OAAO;QACV,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAA8B,GAAG;YAAE,QAAQ;QAAI;IACnF;IAEA,MAAM,SAAS,QAAQ,GAAG,CAAC,4BAA4B;IACvD,IAAI,CAAC,QAAQ;QACX,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAuC,GAAG;YAAE,QAAQ;QAAI;IAC5F;IAEA,IAAI;QACF,MAAM,SAAS,IAAI,gBAAgB;YACjC;YACA,KAAK;YACL,UAAU;YACV,YAAY;QACd;QAEA,IAAI,cAAc;YAChB,OAAO,MAAM,CAAC,gBAAgB;QAChC;QAEA,IAAI,OAAO;YACT,OAAO,MAAM,CAAC,SAAS;QACzB;QAEA,MAAM,WAAW,MAAM,MACrB,CAAC,6DAA6D,EAAE,QAAQ,EACxE;YACE,SAAS;gBACP,UAAU;YACZ;QACF;QAGF,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,yBAAyB,EAAE,SAAS,MAAM,EAAE;QAC/D;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,IAAI,KAAK,MAAM,KAAK,QAAQ,KAAK,MAAM,KAAK,gBAAgB;YAC1D,MAAM,IAAI,MAAM,CAAC,yBAAyB,EAAE,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,aAAa,IAAI,iBAAiB;QACtG;QAEA,sDAAsD;QACtD,MAAM,cAAc,KAAK,WAAW,EAAE,IAAI,CAAC,aAAoB,CAAC;gBAC9D,SAAS,WAAW,QAAQ;gBAC5B,aAAa,WAAW,WAAW;gBACnC,UAAU,WAAW,qBAAqB,EAAE,aAAa,WAAW,WAAW;gBAC/E,eAAe,WAAW,qBAAqB,EAAE,kBAAkB;gBACnE,OAAO,WAAW,KAAK,IAAI,EAAE;gBAC7B,mBAAmB,WAAW,kBAAkB,IAAI,EAAE;YACxD,CAAC,MAAM,EAAE;QAET,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;YACN,QAAQ,KAAK,MAAM;QACrB;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD,GACA;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}