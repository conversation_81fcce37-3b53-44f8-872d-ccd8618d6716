"use strict";exports.id=574,exports.ids=[574],exports.modules={474:(e,t,a)=>{a.d(t,{C2:()=>c,QQ:()=>i,eg:()=>l,zi:()=>n});var r=a(75535),d=a(50944);function s(e){if(!e.exists())return null;let t=e.data();return{id:e.id,...t,createdAt:t?.createdAt?.toDate?.()||t?.createdAt,updatedAt:t?.updatedAt?.toDate?.()||t?.updatedAt}}let i={async get(e){let t=(0,r.H9)(d.db,"users",e);return s(await (0,r.x7)(t))},async create(e,t){let a=(0,r.H9)(d.db,"users",e),s=new Date;await (0,r.BN)(a,{...t,createdAt:r.Dc.fromDate(s),updatedAt:r.Dc.fromDate(s)})},async update(e,t){let a=(0,r.H9)(d.db,"users",e);await (0,r.mZ)(a,{...t,updatedAt:r.Dc.now()})}},l={async getAll(e){let t=(0,r.rJ)(d.db,"farms");return e?.isActive!==void 0&&(t=(0,r.P)(t,(0,r._M)("isActive","==",e.isActive))),e?.featured!==void 0&&(t=(0,r.P)(t,(0,r._M)("featured","==",e.featured))),e?.province&&(t=(0,r.P)(t,(0,r._M)("province","==",e.province))),e?.activityType&&(t=(0,r.P)(t,(0,r._M)("activityTypes","==",e.activityType))),t=(0,r.P)(t,(0,r.My)("createdAt","desc")),e?.limit&&(t=(0,r.P)(t,(0,r.AB)(e.limit))),(await (0,r.GG)(t)).docs.map(e=>s(e)).filter(Boolean)},async get(e){let t=(0,r.H9)(d.db,"farms",e);return s(await (0,r.x7)(t))},async getByOwner(e){let t=(0,r.P)((0,r.rJ)(d.db,"farms"),(0,r._M)("ownerId","==",e),(0,r.My)("createdAt","desc"));return(await (0,r.GG)(t)).docs.map(e=>s(e)).filter(Boolean)},async getActive(e){let t=(0,r.P)((0,r.rJ)(d.db,"farms"),(0,r._M)("isActive","==",!0),(0,r.My)("createdAt","desc"));return e&&(t=(0,r.P)(t,(0,r.AB)(e))),(await (0,r.GG)(t)).docs.map(e=>s(e)).filter(Boolean)},async create(e){let t=new Date;return(await (0,r.gS)((0,r.rJ)(d.db,"farms"),{...e,createdAt:r.Dc.fromDate(t),updatedAt:r.Dc.fromDate(t)})).id},async update(e,t){let a=(0,r.H9)(d.db,"farms",e);await (0,r.mZ)(a,{...t,updatedAt:r.Dc.now()})},async delete(e){let t=(0,r.H9)(d.db,"farms",e);await (0,r.kd)(t)},async addImage(e,t){let a=new Date;return(await (0,r.gS)((0,r.rJ)(d.db,"farms",e,"images"),{...t,createdAt:r.Dc.fromDate(a),updatedAt:r.Dc.fromDate(a)})).id},async getImages(e){let t=(0,r.P)((0,r.rJ)(d.db,"farms",e,"images"),(0,r.My)("displayOrder","asc"),(0,r.My)("createdAt","asc"));return(await (0,r.GG)(t)).docs.map(e=>s(e)).filter(Boolean)},async deleteImage(e,t){let a=(0,r.H9)(d.db,"farms",e,"images",t);await (0,r.kd)(a)}},n={async getAll(e){let t=(0,r.rJ)(d.db,"bookings");return e?.hunterId&&(t=(0,r.P)(t,(0,r._M)("hunterId","==",e.hunterId))),e?.farmId&&(t=(0,r.P)(t,(0,r._M)("farmId","==",e.farmId))),e?.status&&(t=(0,r.P)(t,(0,r._M)("status","==",e.status))),t=(0,r.P)(t,(0,r.My)("createdAt","desc")),e?.limit&&(t=(0,r.P)(t,(0,r.AB)(e.limit))),(await (0,r.GG)(t)).docs.map(e=>s(e)).filter(Boolean)},async get(e){let t=(0,r.H9)(d.db,"bookings",e);return s(await (0,r.x7)(t))},async create(e){let t=new Date,a=`BVR-${t.getFullYear()}${(t.getMonth()+1).toString().padStart(2,"0")}${t.getDate().toString().padStart(2,"0")}-${Math.random().toString(36).substring(2,8).toUpperCase()}`;return(await (0,r.gS)((0,r.rJ)(d.db,"bookings"),{...e,bookingReference:a,createdAt:r.Dc.fromDate(t),updatedAt:r.Dc.fromDate(t)})).id},async update(e,t){let a=(0,r.H9)(d.db,"bookings",e);await (0,r.mZ)(a,{...t,updatedAt:r.Dc.now()})}},c={async getByFarm(e){let t=(0,r.P)((0,r.rJ)(d.db,"farms",e,"reviews"),(0,r._M)("isPublic","==",!0),(0,r.My)("createdAt","desc"));return(await (0,r.GG)(t)).docs.map(e=>s(e)).filter(Boolean)},async create(e,t){let a=new Date;return(await (0,r.gS)((0,r.rJ)(d.db,"farms",e,"reviews"),{...t,createdAt:r.Dc.fromDate(a),updatedAt:r.Dc.fromDate(a)})).id},async update(e,t,a){let s=(0,r.H9)(d.db,"farms",e,"reviews",t);await (0,r.mZ)(s,{...a,updatedAt:r.Dc.now()})}}},9776:(e,t,a)=>{a.d(t,{k:()=>s});var r=a(60687),d=a(4780);function s({size:e="md",className:t}){return(0,r.jsx)("div",{className:(0,d.cn)("flex items-center justify-center",t),children:(0,r.jsx)("div",{className:(0,d.cn)("animate-spin rounded-full border-2 border-white border-t-transparent",{sm:"w-4 h-4",md:"w-6 h-6",lg:"w-12 h-12"}[e])})})}},13861:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},28749:(e,t,a)=>{a.d(t,{MH:()=>m,Wu:()=>n,ZB:()=>o,Zp:()=>l,aR:()=>c});var r=a(60687),d=a(43210),s=a(30474),i=a(4780);let l=(0,d.forwardRef)(({className:e,hover:t=!0,children:a,...d},s)=>(0,r.jsx)("div",{ref:s,className:(0,i.cn)(`
          bg-white rounded-[var(--radius-lg)] shadow-[var(--shadow-md)] 
          overflow-hidden transition-all duration-300
          `,t&&"hover:transform hover:-translate-y-1 hover:shadow-[var(--shadow-xl)]",e),...d,children:a}));l.displayName="Card";let n=(0,d.forwardRef)(({className:e,...t},a)=>(0,r.jsx)("div",{ref:a,className:(0,i.cn)("p-[var(--space-lg)]",e),...t}));n.displayName="CardContent";let c=(0,d.forwardRef)(({className:e,...t},a)=>(0,r.jsx)("div",{ref:a,className:(0,i.cn)("px-[var(--space-lg)] pt-[var(--space-lg)]",e),...t}));c.displayName="CardHeader";let o=(0,d.forwardRef)(({className:e,...t},a)=>(0,r.jsx)("h3",{ref:a,className:(0,i.cn)("text-xl font-bold mb-2 text-[var(--primary-brown)] font-[var(--font-display)]",e),...t}));o.displayName="CardTitle";let m=(0,d.forwardRef)(({className:e,src:t,alt:a,children:d,...l},n)=>(0,r.jsx)("div",{ref:n,className:(0,i.cn)("relative w-full h-48 bg-gradient-to-br from-[var(--primary-green)] to-[var(--secondary-sunset)] flex items-center justify-center text-white text-5xl",e),...l,children:t?(0,r.jsx)(s.default,{src:t,alt:a||"",fill:!0,className:"object-cover"}):d}));m.displayName="CardImage"},30474:(e,t,a)=>{a.d(t,{default:()=>d.a});var r=a(31261),d=a.n(r)},31261:(e,t,a)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var a in t)Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}(t,{default:function(){return n},getImageProps:function(){return l}});let r=a(14985),d=a(44953),s=a(46533),i=r._(a(1933));function l(e){let{props:t}=(0,d.getImgProps)(e,{defaultLoader:i.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,a]of Object.entries(t))void 0===a&&delete t[e];return{props:t}}let n=s.Image},41312:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(62688).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},53411:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(62688).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},62688:(e,t,a)=>{a.d(t,{A:()=>m});var r=a(43210);let d=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),s=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,a)=>a?a.toUpperCase():t.toLowerCase()),i=e=>{let t=s(e);return t.charAt(0).toUpperCase()+t.slice(1)},l=(...e)=>e.filter((e,t,a)=>!!e&&""!==e.trim()&&a.indexOf(e)===t).join(" ").trim(),n=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let o=(0,r.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:a=2,absoluteStrokeWidth:d,className:s="",children:i,iconNode:o,...m},u)=>(0,r.createElement)("svg",{ref:u,...c,width:t,height:t,stroke:e,strokeWidth:d?24*Number(a)/Number(t):a,className:l("lucide",s),...!i&&!n(m)&&{"aria-hidden":"true"},...m},[...o.map(([e,t])=>(0,r.createElement)(e,t)),...Array.isArray(i)?i:[i]])),m=(e,t)=>{let a=(0,r.forwardRef)(({className:a,...s},n)=>(0,r.createElement)(o,{ref:n,iconNode:t,className:l(`lucide-${d(i(e))}`,`lucide-${e}`,a),...s}));return a.displayName=i(e),a}},63143:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(62688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},64398:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(62688).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},70440:(e,t,a)=>{a.r(t),a.d(t,{default:()=>d});var r=a(31658);let d=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},96474:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},97992:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(62688).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])}};