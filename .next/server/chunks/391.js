exports.id=391,exports.ids=[391],exports.modules={720:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,16444,23)),Promise.resolve().then(t.t.bind(t,16042,23)),Promise.resolve().then(t.t.bind(t,88170,23)),Promise.resolve().then(t.t.bind(t,49477,23)),Promise.resolve().then(t.t.bind(t,29345,23)),Promise.resolve().then(t.t.bind(t,12089,23)),Promise.resolve().then(t.t.bind(t,46577,23)),Promise.resolve().then(t.t.bind(t,31307,23))},2643:(e,r,t)=>{"use strict";t.d(r,{$:()=>n});var s=t(60687),a=t(43210),o=t(4780);let n=(0,a.forwardRef)(({className:e,variant:r="primary",size:t="md",isLoading:a,children:n,disabled:i,...l},c)=>{let d=`
      inline-flex items-center justify-center rounded-[var(--radius-md)] 
      font-[var(--font-ui)] font-semibold transition-all duration-300 
      focus:outline-none focus:ring-2 focus:ring-offset-2
      disabled:opacity-50 disabled:cursor-not-allowed
      hover:transform hover:-translate-y-0.5 hover:shadow-[var(--shadow-lg)]
    `,h={primary:`
        bg-[var(--primary-brown)] text-white 
        hover:bg-[#A0522D] focus:ring-[var(--primary-brown)]
      `,secondary:`
        bg-[var(--secondary-sky)] text-white 
        hover:bg-[#357AE0] focus:ring-[var(--secondary-sky)]
      `,outline:`
        bg-transparent border-2 border-[var(--primary-brown)] text-[var(--primary-brown)]
        hover:bg-[var(--primary-brown)] hover:text-white focus:ring-[var(--primary-brown)]
      `,hunting:`
        bg-[var(--hunting-accent)] text-white 
        hover:bg-[#A67C00] focus:ring-[var(--hunting-accent)]
      `,photo:`
        bg-[var(--photo-accent)] text-white 
        hover:bg-[#3557C7] focus:ring-[var(--photo-accent)]
      `};return(0,s.jsxs)("button",{className:(0,o.cn)(d,h[r],{sm:"px-3 py-1.5 text-sm",md:"px-6 py-2 text-base",lg:"px-8 py-3 text-lg"}[t],a&&"cursor-wait",e),disabled:i||a,ref:c,...l,children:[a&&(0,s.jsxs)("svg",{className:"mr-2 h-4 w-4 animate-spin",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,s.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,s.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),n]})});n.displayName="Button"},4780:(e,r,t)=>{"use strict";t.d(r,{Y:()=>i,Z:()=>n,cn:()=>o});var s=t(49384),a=t(82348);function o(...e){return(0,a.QP)((0,s.$)(e))}function n(e){return e.toString().replace(/\B(?=(\d{3})+(?!\d))/g," ")}function i(e){let r=new Date(e);return`${r.getDate()} ${["January","February","March","April","May","June","July","August","September","October","November","December"][r.getMonth()]} ${r.getFullYear()}`}},10501:(e,r,t)=>{"use strict";t.d(r,{Navbar:()=>h});var s=t(60687),a=t(43210),o=t(85814),n=t.n(o),i=t(16189),l=t(2643),c=t(4780),d=t(57445);function h(){let[e,r]=(0,a.useState)(!1),[t,o]=(0,a.useState)(!1),{user:h,loading:m,signOut:u}=(0,d.As)(),x=(0,i.useRouter)(),f=async()=>{await u(),x.push("/")};return(0,s.jsx)("nav",{className:"bg-white shadow-[var(--shadow-sm)] sticky top-0 z-50",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,s.jsx)(n(),{href:"/",className:"flex items-center font-bold",style:{fontFamily:"var(--font-display)",fontSize:"28px",fontWeight:"700",color:"var(--deep-brown)",textDecoration:"none"},children:"BvR Safaris"}),(0,s.jsxs)("div",{className:"hidden md:flex items-center space-x-8",children:[(0,s.jsx)(n(),{href:"/farms",className:"nav-text transition-colors hover:opacity-80",style:{fontFamily:"var(--font-body)",fontSize:"var(--text-secondary-body)",fontWeight:"400",color:"var(--dark-text)",textDecoration:"none"},children:"Farms"}),(0,s.jsx)(n(),{href:"/farms?activity=hunting",className:"nav-text transition-colors hover:opacity-80",style:{fontFamily:"var(--font-body)",fontSize:"var(--text-secondary-body)",fontWeight:"400",color:"var(--dark-text)",textDecoration:"none"},children:"Hunt"}),(0,s.jsx)(n(),{href:"/farms?activity=photo_safari",className:"nav-text transition-colors hover:opacity-80",style:{fontFamily:"var(--font-body)",fontSize:"var(--text-secondary-body)",fontWeight:"400",color:"var(--dark-text)",textDecoration:"none"},children:"Photo Safari"}),(0,s.jsx)(n(),{href:"/about",className:"nav-text transition-colors hover:opacity-80",style:{fontFamily:"var(--font-body)",fontSize:"var(--text-secondary-body)",fontWeight:"400",color:"var(--dark-text)",textDecoration:"none"},children:"About"})]}),(0,s.jsx)("div",{className:"hidden md:flex items-center space-x-4",children:m?(0,s.jsx)("div",{className:"animate-pulse bg-gray-200 h-8 w-20 rounded"}):h?(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsxs)("button",{onClick:()=>o(!t),className:"flex items-center space-x-2 text-[var(--dark-gray)] hover:text-[var(--primary-brown)] font-medium transition-colors",children:[(0,s.jsx)("div",{className:"w-8 h-8 bg-[var(--primary-brown)] text-white rounded-full flex items-center justify-center text-sm font-semibold",children:h.displayName?.[0]||h.email?.[0]?.toUpperCase()||"U"}),(0,s.jsx)("span",{className:"hidden lg:block",children:h.displayName||"Account"}),(0,s.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})]}),t&&(0,s.jsxs)("div",{className:"absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 border",children:[(0,s.jsx)(n(),{href:"/dashboard",className:"block px-4 py-2 text-sm text-[var(--dark-gray)] hover:bg-[var(--earth-100)] hover:text-[var(--primary-brown)]",onClick:()=>o(!1),children:"Dashboard"}),(0,s.jsx)(n(),{href:"/profile",className:"block px-4 py-2 text-sm text-[var(--dark-gray)] hover:bg-[var(--earth-100)] hover:text-[var(--primary-brown)]",onClick:()=>o(!1),children:"Profile Settings"}),(0,s.jsx)("hr",{className:"my-1"}),(0,s.jsx)("button",{onClick:()=>{o(!1),f()},className:"block w-full text-left px-4 py-2 text-sm text-[var(--dark-gray)] hover:bg-[var(--earth-100)] hover:text-[var(--primary-brown)]",children:"Sign Out"})]})]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(n(),{href:"/auth/login",children:(0,s.jsx)(l.$,{variant:"outline",size:"sm",style:{fontFamily:"var(--font-body)",fontSize:"var(--text-labels)",borderColor:"var(--deep-brown)",color:"var(--deep-brown)",borderRadius:"8px"},children:"Sign In"})}),(0,s.jsx)(n(),{href:"/auth/register",children:(0,s.jsx)(l.$,{variant:"primary",size:"sm",style:{fontFamily:"var(--font-body)",fontSize:"var(--text-labels)",backgroundColor:"var(--deep-brown)",color:"var(--cream)",borderRadius:"8px"},children:"Sign Up"})})]})}),(0,s.jsx)("div",{className:"md:hidden",children:(0,s.jsx)("button",{onClick:()=>r(!e),className:"text-[var(--dark-gray)] hover:text-[var(--primary-brown)] p-2",children:(0,s.jsx)("svg",{className:"h-6 w-6",fill:"none",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",viewBox:"0 0 24 24",stroke:"currentColor",children:e?(0,s.jsx)("path",{d:"M6 18L18 6M6 6l12 12"}):(0,s.jsx)("path",{d:"M4 6h16M4 12h16M4 18h16"})})})})]}),(0,s.jsx)("div",{className:(0,c.cn)("md:hidden transition-all duration-300 overflow-hidden",e?"max-h-96 pb-4":"max-h-0"),children:(0,s.jsxs)("div",{className:"px-2 pt-2 pb-3 space-y-1",children:[(0,s.jsx)(n(),{href:"/farms",className:"block px-3 py-2 text-[var(--dark-gray)] hover:text-[var(--primary-brown)] font-medium",children:"Farms"}),(0,s.jsx)(n(),{href:"/farms?activity=hunting",className:"block px-3 py-2 text-[var(--dark-gray)] hover:text-[var(--primary-brown)] font-medium",children:"Hunt"}),(0,s.jsx)(n(),{href:"/farms?activity=photo_safari",className:"block px-3 py-2 text-[var(--dark-gray)] hover:text-[var(--primary-brown)] font-medium",children:"Photo Safari"}),(0,s.jsx)(n(),{href:"/about",className:"block px-3 py-2 text-[var(--dark-gray)] hover:text-[var(--primary-brown)] font-medium",children:"About"}),(0,s.jsx)("div",{className:"pt-4 space-y-2",children:m?(0,s.jsx)("div",{className:"animate-pulse bg-gray-200 h-8 w-full rounded"}):h?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(n(),{href:"/dashboard",className:"block",children:(0,s.jsx)(l.$,{variant:"outline",size:"sm",className:"w-full",children:"Dashboard"})}),(0,s.jsx)(n(),{href:"/profile",className:"block",children:(0,s.jsx)(l.$,{variant:"outline",size:"sm",className:"w-full",children:"Profile"})}),(0,s.jsx)(l.$,{variant:"primary",size:"sm",className:"w-full",onClick:f,children:"Sign Out"})]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(n(),{href:"/auth/login",className:"block",children:(0,s.jsx)(l.$,{variant:"outline",size:"sm",className:"w-full",children:"Sign In"})}),(0,s.jsx)(n(),{href:"/auth/register",className:"block",children:(0,s.jsx)(l.$,{variant:"primary",size:"sm",className:"w-full",children:"Sign Up"})})]})})]})})]})})}},11265:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,4536,23)),Promise.resolve().then(t.bind(t,93319)),Promise.resolve().then(t.bind(t,49567))},24417:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,85814,23)),Promise.resolve().then(t.bind(t,10501)),Promise.resolve().then(t.bind(t,57445))},49567:(e,r,t)=>{"use strict";t.d(r,{AuthProvider:()=>a});var s=t(12907);let a=(0,s.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/src/personal/bvr-safaris/src/hooks/useAuth.tsx","AuthProvider");(0,s.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/src/personal/bvr-safaris/src/hooks/useAuth.tsx","useAuth"),(0,s.registerClientReference)(function(){throw Error("Attempted to call useRequireAuth() from the server but useRequireAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/src/personal/bvr-safaris/src/hooks/useAuth.tsx","useRequireAuth"),(0,s.registerClientReference)(function(){throw Error("Attempted to call useUserRole() from the server but useUserRole is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/src/personal/bvr-safaris/src/hooks/useAuth.tsx","useUserRole")},50944:(e,r,t)=>{"use strict";t.d(r,{IG:()=>s.IG,db:()=>s.db,j2:()=>s.j2});var s=t(71557)},57445:(e,r,t)=>{"use strict";t.d(r,{As:()=>d,AuthProvider:()=>c,Nu:()=>m});var s=t(60687),a=t(43210),o=t(19978),n=t(75535),i=t(50944);let l=(0,a.createContext)(void 0);function c({children:e}){let[r,t]=(0,a.useState)(null),[c,d]=(0,a.useState)(null),[m,u]=(0,a.useState)(!0),[x,f]=(0,a.useState)(null),v=async e=>{try{let r=(0,n.H9)(i.db,"users",e),t=await (0,n.x7)(r);if(t.exists()){let e=t.data();return{id:t.id,...e,createdAt:e.createdAt?.toDate?.()||e.createdAt,updatedAt:e.updatedAt?.toDate?.()||e.updatedAt}}return null}catch(e){return console.error("Error fetching user profile:",e),null}},p=async()=>{r&&d(await v(r.uid))},b=e=>new Promise(r=>{let t=(0,o.hg)(i.j2,s=>{e&&s&&s.uid===e.uid?(t(),r()):e||s||(t(),r())})}),y=async(e,r)=>{try{f(null),u(!0);let t=await (0,o.x9)(i.j2,e,r);await b(t.user),await new Promise(e=>setTimeout(e,100))}catch(e){throw f(h(e instanceof Error&&"code"in e?e.code:"unknown")),e}finally{u(!1)}},g=async(e,r,t)=>{try{f(null),u(!0);let s=(await (0,o.eJ)(i.j2,e,r)).user;await (0,o.r7)(s,{displayName:`${t.firstName} ${t.lastName}`});let a=n.Dc.now(),l={email:e,fullName:`${t.firstName} ${t.lastName}`,firstName:t.firstName,lastName:t.lastName,phone:t.phone||null,role:t.role,languagePreference:"en",createdAt:a,updatedAt:a};await (0,n.BN)((0,n.H9)(i.db,"users",s.uid),l),d({id:s.uid,...l,phone:t.phone||void 0});try{let e=await s.getIdToken(),r=await fetch("/api/auth/set-role",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${e}`},body:JSON.stringify({uid:s.uid,role:t.role})});if(r.ok)console.log("User role set successfully");else{let e=await r.json();console.error("Failed to set user role:",e.error)}}catch(e){console.error("Error setting user role:",e)}await b(s),await new Promise(e=>setTimeout(e,100))}catch(e){throw f(h(e instanceof Error&&"code"in e?e.code:"unknown")),e}finally{u(!1)}},w=async()=>{try{f(null),await (0,o.CI)(i.j2),d(null)}catch(e){throw f(h(e instanceof Error&&"code"in e?e.code:"unknown")),e}},j=async e=>{try{f(null),await (0,o.J1)(i.j2,e)}catch(e){throw f(h(e instanceof Error&&"code"in e?e.code:"unknown")),e}};return(0,s.jsx)(l.Provider,{value:{user:r,userProfile:c,loading:m,error:x,signIn:y,signUp:g,signOut:w,resetPassword:j,clearError:()=>{f(null)},refreshProfile:p},children:e})}function d(){let e=(0,a.useContext)(l);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}function h(e){switch(e){case"auth/user-not-found":case"auth/wrong-password":case"auth/invalid-credential":return"Invalid email or password";case"auth/email-already-in-use":return"An account with this email already exists";case"auth/weak-password":return"Password should be at least 6 characters";case"auth/invalid-email":return"Invalid email address";case"auth/too-many-requests":return"Too many failed attempts. Please try again later";case"auth/network-request-failed":return"Network error. Please check your connection";default:return"An unexpected error occurred. Please try again"}}function m(){let{user:e,loading:r}=d();return{user:e,loading:r,isAuthenticated:!!e}}},58864:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,86346,23)),Promise.resolve().then(t.t.bind(t,27924,23)),Promise.resolve().then(t.t.bind(t,35656,23)),Promise.resolve().then(t.t.bind(t,40099,23)),Promise.resolve().then(t.t.bind(t,38243,23)),Promise.resolve().then(t.t.bind(t,28827,23)),Promise.resolve().then(t.t.bind(t,62763,23)),Promise.resolve().then(t.t.bind(t,97173,23))},61135:()=>{},71557:(e,r,t)=>{"use strict";t.d(r,{IG:()=>d,db:()=>c,j2:()=>l});var s=t(67989),a=t(19978),o=t(75535),n=t(70146);let i=(0,s.Wp)({apiKey:"AIzaSyDdsTm8eifLrb1W0WLK0DwBk4p4XLFZKMw",authDomain:"rvbsafaris.firebaseapp.com",projectId:"rvbsafaris",storageBucket:"rvbsafaris.firebasestorage.app",messagingSenderId:"593781635754",appId:"1:593781635754:web:7a39a3b4451f34f85feed3"}),l=(0,a.xI)(i),c=(0,o.aU)(i),d=(0,n.c7)(i);console.log("Firebase Storage initialized with bucket:","rvbsafaris.firebasestorage.app")},82893:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>x,metadata:()=>u});var s=t(37413),a=t(49078),o=t.n(a),n=t(28831),i=t.n(n);t(61135);var l=t(93319),c=t(4536),d=t.n(c);function h(){return(0,s.jsx)("footer",{className:"bg-[var(--dark-gray)] text-white mt-auto",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[(0,s.jsxs)("div",{className:"col-span-1 md:col-span-2",children:[(0,s.jsxs)("div",{className:"flex items-center mb-4",children:[(0,s.jsx)("span",{className:"text-2xl mr-2",children:"Proudly South African"}),(0,s.jsx)("h3",{className:"text-xl font-bold font-[var(--font-display)]",children:"BvR Safaris"})]}),(0,s.jsx)("p",{className:"text-gray-300 mb-4",children:"South Africa's premier platform for booking authentic hunting and photo safari experiences. Connect with certified game farms and create unforgettable memories in the African wilderness."}),(0,s.jsxs)("div",{className:"flex space-x-4",children:[(0,s.jsxs)("a",{href:"#",className:"text-gray-300 hover:text-white transition-colors",children:[(0,s.jsx)("span",{className:"sr-only",children:"Facebook"}),(0,s.jsx)("svg",{className:"h-6 w-6",fill:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{d:"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"})})]}),(0,s.jsxs)("a",{href:"#",className:"text-gray-300 hover:text-white transition-colors",children:[(0,s.jsx)("span",{className:"sr-only",children:"Instagram"}),(0,s.jsx)("svg",{className:"h-6 w-6",fill:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{d:"M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.014 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.32-1.297C4.198 14.926 3.652 13.18 3.652 11.987c0-1.297.49-2.448 1.297-3.32.865-.865 2.023-1.297 3.32-1.297 1.297 0 2.448.49 3.32 1.297.865.865 1.297 2.023 1.297 3.32 0 1.297-.49 2.448-1.297 3.32-.865.865-2.023 1.297-3.32 1.297zm7.718-9.996c-.33 0-.612-.282-.612-.612s.282-.612.612-.612.612.282.612.612-.282.612-.612.612zm-3.573 2.248c-1.297 0-2.344 1.047-2.344 2.344s1.047 2.344 2.344 2.344 2.344-1.047 2.344-2.344-1.047-2.344-2.344-2.344z"})})]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-semibold mb-4",children:"Quick Links"}),(0,s.jsxs)("ul",{className:"space-y-2",children:[(0,s.jsx)("li",{children:(0,s.jsx)(d(),{href:"/farms",className:"text-gray-300 hover:text-white transition-colors",children:"Browse Farms"})}),(0,s.jsx)("li",{children:(0,s.jsx)(d(),{href:"/farms?activity=hunting",className:"text-gray-300 hover:text-white transition-colors",children:"Hunting Safaris"})}),(0,s.jsx)("li",{children:(0,s.jsx)(d(),{href:"/farms?activity=photo_safari",className:"text-gray-300 hover:text-white transition-colors",children:"Photo Safaris"})}),(0,s.jsx)("li",{children:(0,s.jsx)(d(),{href:"/how-it-works",className:"text-gray-300 hover:text-white transition-colors",children:"How It Works"})}),(0,s.jsx)("li",{children:(0,s.jsx)(d(),{href:"/safety",className:"text-gray-300 hover:text-white transition-colors",children:"Safety Guidelines"})})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-semibold mb-4",children:"Support"}),(0,s.jsxs)("ul",{className:"space-y-2",children:[(0,s.jsx)("li",{children:(0,s.jsx)(d(),{href:"/contact",className:"text-gray-300 hover:text-white transition-colors",children:"Contact Us"})}),(0,s.jsx)("li",{children:(0,s.jsx)(d(),{href:"/faq",className:"text-gray-300 hover:text-white transition-colors",children:"FAQ"})}),(0,s.jsx)("li",{children:(0,s.jsx)(d(),{href:"/help",className:"text-gray-300 hover:text-white transition-colors",children:"Help Center"})}),(0,s.jsx)("li",{children:(0,s.jsx)(d(),{href:"/farm-owners",className:"text-gray-300 hover:text-white transition-colors",children:"For Farm Owners"})}),(0,s.jsx)("li",{children:(0,s.jsx)(d(),{href:"/terms",className:"text-gray-300 hover:text-white transition-colors",children:"Terms of Service"})}),(0,s.jsx)("li",{children:(0,s.jsx)(d(),{href:"/privacy",className:"text-gray-300 hover:text-white transition-colors",children:"Privacy Policy"})})]})]})]}),(0,s.jsxs)("div",{className:"border-t border-gray-700 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center",children:[(0,s.jsxs)("p",{className:"text-gray-300 text-sm",children:["\xa9 ",new Date().getFullYear()," BvR Safaris. All rights reserved."]}),(0,s.jsx)("div",{className:"flex items-center space-x-4 mt-4 md:mt-0",children:(0,s.jsx)("span",{className:"text-gray-300 text-sm",children:"Proudly South African"})})]})]})})}var m=t(49567);let u={title:"BvR Safaris - Hunt & Photo Safari Booking",description:"Book authentic hunting and photo safari experiences at South Africa's premier game farms. Connect with certified lodges and create unforgettable memories."};function x({children:e}){return(0,s.jsx)("html",{lang:"en",children:(0,s.jsx)("body",{className:`${o().variable} ${i().variable} antialiased min-h-screen flex flex-col`,children:(0,s.jsxs)(m.AuthProvider,{children:[(0,s.jsx)(l.Navbar,{}),(0,s.jsx)("main",{className:"flex-1",children:e}),(0,s.jsx)(h,{})]})})})}},93319:(e,r,t)=>{"use strict";t.d(r,{Navbar:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call Navbar() from the server but Navbar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/src/personal/bvr-safaris/src/components/layout/Navbar.tsx","Navbar")}};