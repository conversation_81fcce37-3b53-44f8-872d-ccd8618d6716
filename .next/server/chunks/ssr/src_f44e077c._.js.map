{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/src/personal/bvr-safaris/src/components/ui/Input.tsx"], "sourcesContent": ["import { InputHTMLAttributes, forwardRef } from 'react'\nimport { cn } from '@/lib/utils'\n\nexport interface InputProps extends InputHTMLAttributes<HTMLInputElement> {\n  label?: string\n  error?: string\n}\n\nconst Input = forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, label, error, ...props }, ref) => {\n    return (\n      <div className=\"w-full\">\n        {label && (\n          <label className=\"block mb-2 font-semibold text-[var(--primary-brown)] font-[var(--font-ui)]\">\n            {label}\n          </label>\n        )}\n        <input\n          type={type}\n          className={cn(\n            `\n            w-full px-4 py-2 border-2 border-[var(--medium-gray)] \n            rounded-[var(--radius-md)] font-[var(--font-ui)] \n            transition-colors duration-300\n            focus:outline-none focus:border-[var(--primary-brown)] \n            focus:ring-0 focus:shadow-[0_0_0_3px_rgba(139,69,19,0.1)]\n            placeholder:text-[var(--medium-gray)]\n            `,\n            error && 'border-red-500 focus:border-red-500',\n            className\n          )}\n          ref={ref}\n          {...props}\n        />\n        {error && (\n          <p className=\"mt-1 text-sm text-red-600\">{error}</p>\n        )}\n      </div>\n    )\n  }\n)\n\nInput.displayName = 'Input'\n\nexport { Input }"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAOA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACrB,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE;IAC5C,qBACE,8OAAC;QAAI,WAAU;;YACZ,uBACC,8OAAC;gBAAM,WAAU;0BACd;;;;;;0BAGL,8OAAC;gBACC,MAAM;gBACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,CAAC;;;;;;;YAOD,CAAC,EACD,SAAS,uCACT;gBAEF,KAAK;gBACJ,GAAG,KAAK;;;;;;YAEV,uBACC,8OAAC;gBAAE,WAAU;0BAA6B;;;;;;;;;;;;AAIlD;AAGF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/src/personal/bvr-safaris/src/components/ui/Card.tsx"], "sourcesContent": ["import { HTMLAttributes, forwardRef } from 'react'\nimport Image from 'next/image'\nimport { cn } from '@/lib/utils'\n\nexport interface CardProps extends HTMLAttributes<HTMLDivElement> {\n  hover?: boolean\n}\n\nconst Card = forwardRef<HTMLDivElement, CardProps>(\n  ({ className, hover = true, children, ...props }, ref) => {\n    return (\n      <div\n        ref={ref}\n        className={cn(\n          `\n          bg-white rounded-[var(--radius-lg)] shadow-[var(--shadow-md)] \n          overflow-hidden transition-all duration-300\n          `,\n          hover && 'hover:transform hover:-translate-y-1 hover:shadow-[var(--shadow-xl)]',\n          className\n        )}\n        {...props}\n      >\n        {children}\n      </div>\n    )\n  }\n)\n\nCard.displayName = 'Card'\n\nconst CardContent = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn('p-[var(--space-lg)]', className)}\n      {...props}\n    />\n  )\n)\n\nCardContent.displayName = 'CardContent'\n\nconst CardHeader = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn('px-[var(--space-lg)] pt-[var(--space-lg)]', className)}\n      {...props}\n    />\n  )\n)\n\nCardHeader.displayName = 'CardHeader'\n\nconst CardTitle = forwardRef<HTMLHeadingElement, HTMLAttributes<HTMLHeadingElement>>(\n  ({ className, ...props }, ref) => (\n    <h3\n      ref={ref}\n      className={cn(\n        'text-xl font-bold mb-2 text-[var(--primary-brown)] font-[var(--font-display)]',\n        className\n      )}\n      {...props}\n    />\n  )\n)\n\nCardTitle.displayName = 'CardTitle'\n\nconst CardImage = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement> & {\n  src?: string\n  alt?: string\n  children?: React.ReactNode\n}>(\n  ({ className, src, alt, children, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(\n        'relative w-full h-48 bg-gradient-to-br from-[var(--primary-green)] to-[var(--secondary-sunset)] flex items-center justify-center text-white text-5xl',\n        className\n      )}\n      {...props}\n    >\n      {src ? (\n        <Image src={src} alt={alt || ''} fill className=\"object-cover\" />\n      ) : (\n        children\n      )}\n    </div>\n  )\n)\n\nCardImage.displayName = 'CardImage'\n\nexport { Card, CardContent, CardHeader, CardTitle, CardImage }"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;AACA;;;;;AAMA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACpB,CAAC,EAAE,SAAS,EAAE,QAAQ,IAAI,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IAChD,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,CAAC;;;UAGD,CAAC,EACD,SAAS,wEACT;QAED,GAAG,KAAK;kBAER;;;;;;AAGP;AAGF,KAAK,WAAW,GAAG;AAEnB,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAKf,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAC1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAKf,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACzB,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iFACA;QAED,GAAG,KAAK;;;;;;AAKf,UAAU,WAAW,GAAG;AAExB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAKzB,CAAC,EAAE,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAC5C,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wJACA;QAED,GAAG,KAAK;kBAER,oBACC,8OAAC,6HAAA,CAAA,UAAK;YAAC,KAAK;YAAK,KAAK,OAAO;YAAI,IAAI;YAAC,WAAU;;;;;mBAEhD;;;;;;AAMR,UAAU,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 156, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/src/personal/bvr-safaris/src/lib/firebase/storage.ts"], "sourcesContent": ["import {\n  deleteObject,\n  listAll,\n  getMetadata,\n  updateMetadata,\n  StorageReference,\n  ref,\n  getDownloadURL\n} from 'firebase/storage'\nimport { storage } from './client'\nimport { auth } from './config'\n\nexport interface UploadProgress {\n  bytesTransferred: number\n  totalBytes: number\n  progress: number\n}\n\nexport interface UploadOptions {\n  onProgress?: (progress: UploadProgress) => void\n  onError?: (error: Error) => void\n  onComplete?: (downloadURL: string) => void\n}\n\n// Get the Cloud Function URL based on environment\nfunction getCloudFunctionURL(): string {\n  const projectId = process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID\n\n  if (!projectId) {\n    throw new Error('NEXT_PUBLIC_FIREBASE_PROJECT_ID environment variable is required')\n  }\n\n  if (process.env.NODE_ENV === 'development' && process.env.NEXT_PUBLIC_USE_FIREBASE_EMULATORS === 'true') {\n    return `http://localhost:5001/${projectId}/us-central1/uploadImage`\n  }\n\n  // Production URL\n  return `https://us-central1-${projectId}.cloudfunctions.net/uploadImage`\n}\n\n\n\n// Upload file using Cloud Function\nasync function uploadFileViaCloudFunction(\n  file: File,\n  bucket: 'farm-images' | 'profile-images',\n  farmId?: string,\n  userId?: string,\n  options?: UploadOptions\n): Promise<string> {\n  try {\n    // Get authentication token\n    const user = auth.currentUser\n    if (!user) {\n      throw new Error('User must be authenticated to upload files')\n    }\n\n    const token = await user.getIdToken()\n\n    // Create FormData\n    const formData = new FormData()\n    formData.append('file', file)\n    formData.append('bucket', bucket)\n\n    if (farmId) {\n      formData.append('farmId', farmId)\n    }\n    if (userId) {\n      formData.append('userId', userId)\n    }\n\n    // Simulate progress tracking since we can't get real progress from fetch\n    const simulateProgress = () => {\n      if (!options?.onProgress) return\n\n      let progress = 0\n      const interval = setInterval(() => {\n        progress += Math.random() * 20\n        if (progress >= 90) {\n          clearInterval(interval)\n          return\n        }\n\n        options.onProgress?.({\n          bytesTransferred: Math.floor((progress / 100) * file.size),\n          totalBytes: file.size,\n          progress\n        })\n      }, 200)\n\n      return interval\n    }\n\n    const progressInterval = simulateProgress()\n\n    try {\n      // Upload to Cloud Function\n      const response = await fetch(getCloudFunctionURL(), {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${token}`\n        },\n        body: formData\n      })\n\n      // Clear progress simulation\n      if (progressInterval) {\n        clearInterval(progressInterval)\n      }\n\n      if (!response.ok) {\n        const errorData = await response.json().catch(() => ({}))\n        throw new Error(errorData.error || `Upload failed with status ${response.status}`)\n      }\n\n      const result = await response.json()\n\n      if (!result.success || !result.downloadURL) {\n        throw new Error(result.error || 'Upload failed')\n      }\n\n      // Complete progress\n      options?.onProgress?.({\n        bytesTransferred: file.size,\n        totalBytes: file.size,\n        progress: 100\n      })\n\n      options?.onComplete?.(result.downloadURL)\n\n      return result.downloadURL\n\n    } catch (error) {\n      // Clear progress simulation on error\n      if (progressInterval) {\n        clearInterval(progressInterval)\n      }\n\n      const uploadError = error instanceof Error ? error : new Error('Upload failed')\n      options?.onError?.(uploadError)\n      throw uploadError\n    }\n\n  } catch (error) {\n    console.error('Error uploading file via Cloud Function:', error)\n    const uploadError = error instanceof Error ? error : new Error('Upload failed')\n    options?.onError?.(uploadError)\n    throw uploadError\n  }\n}\n\n// Storage service for Firebase Storage operations (read-only operations)\nexport const storageService = {\n\n  // Delete a file from Firebase Storage\n  async deleteFile(path: string): Promise<void> {\n    try {\n      const storageRef = ref(storage, path)\n      await deleteObject(storageRef)\n    } catch (error) {\n      console.error('Error deleting file:', error)\n      throw error\n    }\n  },\n\n  // Get download URL for a file\n  async getDownloadURL(path: string): Promise<string> {\n    try {\n      const storageRef = ref(storage, path)\n      return await getDownloadURL(storageRef)\n    } catch (error) {\n      console.error('Error getting download URL:', error)\n      throw error\n    }\n  },\n\n  // List all files in a directory\n  async listFiles(path: string): Promise<StorageReference[]> {\n    try {\n      const storageRef = ref(storage, path)\n      const result = await listAll(storageRef)\n      return result.items\n    } catch (error) {\n      console.error('Error listing files:', error)\n      throw error\n    }\n  },\n\n  // Get file metadata\n  async getMetadata(path: string) {\n    try {\n      const storageRef = ref(storage, path)\n      return await getMetadata(storageRef)\n    } catch (error) {\n      console.error('Error getting metadata:', error)\n      throw error\n    }\n  },\n\n  // Update file metadata\n  async updateMetadata(path: string, metadata: Record<string, string>) {\n    try {\n      const storageRef = ref(storage, path)\n      return await updateMetadata(storageRef, metadata)\n    } catch (error) {\n      console.error('Error updating metadata:', error)\n      throw error\n    }\n  }\n}\n\n// Helper functions for specific use cases\nexport const farmImageService = {\n  // Upload farm image using Cloud Function\n  async uploadFarmImage(\n    farmId: string,\n    file: File,\n    options?: UploadOptions\n  ): Promise<string> {\n    console.log('Using Cloud Function for farm image upload')\n    return await uploadFileViaCloudFunction(\n      file,\n      'farm-images',\n      farmId,\n      undefined,\n      options\n    )\n  },\n\n  // Delete farm image\n  async deleteFarmImage(farmId: string, imageUrl: string): Promise<void> {\n    try {\n      // Extract path from Firebase Storage URL\n      const path = extractPathFromURL(imageUrl)\n      if (path && path.startsWith(`farm-images/${farmId}/`)) {\n        await storageService.deleteFile(path)\n      }\n    } catch (error) {\n      console.error('Error deleting farm image:', error)\n      throw error\n    }\n  },\n\n  // List all images for a farm\n  async listFarmImages(farmId: string): Promise<string[]> {\n    try {\n      const files = await storageService.listFiles(`farm-images/${farmId}`)\n      const urls = await Promise.all(\n        files.map(file => getDownloadURL(file))\n      )\n      return urls\n    } catch (error) {\n      console.error('Error listing farm images:', error)\n      throw error\n    }\n  }\n}\n\nexport const profileImageService = {\n  // Upload profile image using Cloud Function\n  async uploadProfileImage(\n    userId: string,\n    file: File,\n    options?: UploadOptions\n  ): Promise<string> {\n    console.log('Using Cloud Function for profile image upload')\n    return await uploadFileViaCloudFunction(\n      file,\n      'profile-images',\n      undefined,\n      userId,\n      options\n    )\n  },\n\n  // Delete profile image\n  async deleteProfileImage(userId: string, imageUrl: string): Promise<void> {\n    try {\n      const path = extractPathFromURL(imageUrl)\n      if (path && path.startsWith(`profile-images/${userId}/`)) {\n        await storageService.deleteFile(path)\n      }\n    } catch (error) {\n      console.error('Error deleting profile image:', error)\n      throw error\n    }\n  }\n}\n\n// Helper function to extract storage path from Firebase Storage URL\nfunction extractPathFromURL(url: string): string | null {\n  try {\n    const urlObj = new URL(url)\n    if (urlObj.hostname === 'firebasestorage.googleapis.com') {\n      const pathMatch = urlObj.pathname.match(/\\/o\\/(.+)\\?/)\n      if (pathMatch) {\n        return decodeURIComponent(pathMatch[1])\n      }\n    }\n    return null\n  } catch (error) {\n    console.error('Error extracting path from URL:', error)\n    return null\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;AAAA;AASA;AAAA;;;;AAeA,kDAAkD;AAClD,SAAS;IACP,MAAM;IAEN,uCAAgB;;IAEhB;IAEA,IAAI,oDAAyB,iBAAiB,QAAQ,GAAG,CAAC,kCAAkC,KAAK,QAAQ;QACvG,OAAO,CAAC,sBAAsB,EAAE,UAAU,wBAAwB,CAAC;IACrE;IAEA,iBAAiB;IACjB,OAAO,CAAC,oBAAoB,EAAE,UAAU,+BAA+B,CAAC;AAC1E;AAIA,mCAAmC;AACnC,eAAe,2BACb,IAAU,EACV,MAAwC,EACxC,MAAe,EACf,MAAe,EACf,OAAuB;IAEvB,IAAI;QACF,2BAA2B;QAC3B,MAAM,OAAO,gIAAA,CAAA,OAAI,CAAC,WAAW;QAC7B,IAAI,CAAC,MAAM;YACT,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,QAAQ,MAAM,KAAK,UAAU;QAEnC,kBAAkB;QAClB,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,QAAQ;QACxB,SAAS,MAAM,CAAC,UAAU;QAE1B,IAAI,QAAQ;YACV,SAAS,MAAM,CAAC,UAAU;QAC5B;QACA,IAAI,QAAQ;YACV,SAAS,MAAM,CAAC,UAAU;QAC5B;QAEA,yEAAyE;QACzE,MAAM,mBAAmB;YACvB,IAAI,CAAC,SAAS,YAAY;YAE1B,IAAI,WAAW;YACf,MAAM,WAAW,YAAY;gBAC3B,YAAY,KAAK,MAAM,KAAK;gBAC5B,IAAI,YAAY,IAAI;oBAClB,cAAc;oBACd;gBACF;gBAEA,QAAQ,UAAU,GAAG;oBACnB,kBAAkB,KAAK,KAAK,CAAC,AAAC,WAAW,MAAO,KAAK,IAAI;oBACzD,YAAY,KAAK,IAAI;oBACrB;gBACF;YACF,GAAG;YAEH,OAAO;QACT;QAEA,MAAM,mBAAmB;QAEzB,IAAI;YACF,2BAA2B;YAC3B,MAAM,WAAW,MAAM,MAAM,uBAAuB;gBAClD,QAAQ;gBACR,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;gBACpC;gBACA,MAAM;YACR;YAEA,4BAA4B;YAC5B,IAAI,kBAAkB;gBACpB,cAAc;YAChB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;gBACvD,MAAM,IAAI,MAAM,UAAU,KAAK,IAAI,CAAC,0BAA0B,EAAE,SAAS,MAAM,EAAE;YACnF;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,CAAC,OAAO,OAAO,IAAI,CAAC,OAAO,WAAW,EAAE;gBAC1C,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI;YAClC;YAEA,oBAAoB;YACpB,SAAS,aAAa;gBACpB,kBAAkB,KAAK,IAAI;gBAC3B,YAAY,KAAK,IAAI;gBACrB,UAAU;YACZ;YAEA,SAAS,aAAa,OAAO,WAAW;YAExC,OAAO,OAAO,WAAW;QAE3B,EAAE,OAAO,OAAO;YACd,qCAAqC;YACrC,IAAI,kBAAkB;gBACpB,cAAc;YAChB;YAEA,MAAM,cAAc,iBAAiB,QAAQ,QAAQ,IAAI,MAAM;YAC/D,SAAS,UAAU;YACnB,MAAM;QACR;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4CAA4C;QAC1D,MAAM,cAAc,iBAAiB,QAAQ,QAAQ,IAAI,MAAM;QAC/D,SAAS,UAAU;QACnB,MAAM;IACR;AACF;AAGO,MAAM,iBAAiB;IAE5B,sCAAsC;IACtC,MAAM,YAAW,IAAY;QAC3B,IAAI;YACF,MAAM,aAAa,CAAA,GAAA,oLAAA,CAAA,MAAG,AAAD,EAAE,gIAAA,CAAA,UAAO,EAAE;YAChC,MAAM,CAAA,GAAA,oLAAA,CAAA,eAAY,AAAD,EAAE;QACrB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,MAAM;QACR;IACF;IAEA,8BAA8B;IAC9B,MAAM,gBAAe,IAAY;QAC/B,IAAI;YACF,MAAM,aAAa,CAAA,GAAA,oLAAA,CAAA,MAAG,AAAD,EAAE,gIAAA,CAAA,UAAO,EAAE;YAChC,OAAO,MAAM,CAAA,GAAA,oLAAA,CAAA,iBAAc,AAAD,EAAE;QAC9B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,MAAM;QACR;IACF;IAEA,gCAAgC;IAChC,MAAM,WAAU,IAAY;QAC1B,IAAI;YACF,MAAM,aAAa,CAAA,GAAA,oLAAA,CAAA,MAAG,AAAD,EAAE,gIAAA,CAAA,UAAO,EAAE;YAChC,MAAM,SAAS,MAAM,CAAA,GAAA,oLAAA,CAAA,UAAO,AAAD,EAAE;YAC7B,OAAO,OAAO,KAAK;QACrB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,MAAM;QACR;IACF;IAEA,oBAAoB;IACpB,MAAM,aAAY,IAAY;QAC5B,IAAI;YACF,MAAM,aAAa,CAAA,GAAA,oLAAA,CAAA,MAAG,AAAD,EAAE,gIAAA,CAAA,UAAO,EAAE;YAChC,OAAO,MAAM,CAAA,GAAA,oLAAA,CAAA,cAAW,AAAD,EAAE;QAC3B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,MAAM;QACR;IACF;IAEA,uBAAuB;IACvB,MAAM,gBAAe,IAAY,EAAE,QAAgC;QACjE,IAAI;YACF,MAAM,aAAa,CAAA,GAAA,oLAAA,CAAA,MAAG,AAAD,EAAE,gIAAA,CAAA,UAAO,EAAE;YAChC,OAAO,MAAM,CAAA,GAAA,oLAAA,CAAA,iBAAc,AAAD,EAAE,YAAY;QAC1C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM;QACR;IACF;AACF;AAGO,MAAM,mBAAmB;IAC9B,yCAAyC;IACzC,MAAM,iBACJ,MAAc,EACd,IAAU,EACV,OAAuB;QAEvB,QAAQ,GAAG,CAAC;QACZ,OAAO,MAAM,2BACX,MACA,eACA,QACA,WACA;IAEJ;IAEA,oBAAoB;IACpB,MAAM,iBAAgB,MAAc,EAAE,QAAgB;QACpD,IAAI;YACF,yCAAyC;YACzC,MAAM,OAAO,mBAAmB;YAChC,IAAI,QAAQ,KAAK,UAAU,CAAC,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC,GAAG;gBACrD,MAAM,eAAe,UAAU,CAAC;YAClC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,MAAM;QACR;IACF;IAEA,6BAA6B;IAC7B,MAAM,gBAAe,MAAc;QACjC,IAAI;YACF,MAAM,QAAQ,MAAM,eAAe,SAAS,CAAC,CAAC,YAAY,EAAE,QAAQ;YACpE,MAAM,OAAO,MAAM,QAAQ,GAAG,CAC5B,MAAM,GAAG,CAAC,CAAA,OAAQ,CAAA,GAAA,oLAAA,CAAA,iBAAc,AAAD,EAAE;YAEnC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,MAAM;QACR;IACF;AACF;AAEO,MAAM,sBAAsB;IACjC,4CAA4C;IAC5C,MAAM,oBACJ,MAAc,EACd,IAAU,EACV,OAAuB;QAEvB,QAAQ,GAAG,CAAC;QACZ,OAAO,MAAM,2BACX,MACA,kBACA,WACA,QACA;IAEJ;IAEA,uBAAuB;IACvB,MAAM,oBAAmB,MAAc,EAAE,QAAgB;QACvD,IAAI;YACF,MAAM,OAAO,mBAAmB;YAChC,IAAI,QAAQ,KAAK,UAAU,CAAC,CAAC,eAAe,EAAE,OAAO,CAAC,CAAC,GAAG;gBACxD,MAAM,eAAe,UAAU,CAAC;YAClC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,MAAM;QACR;IACF;AACF;AAEA,oEAAoE;AACpE,SAAS,mBAAmB,GAAW;IACrC,IAAI;QACF,MAAM,SAAS,IAAI,IAAI;QACvB,IAAI,OAAO,QAAQ,KAAK,kCAAkC;YACxD,MAAM,YAAY,OAAO,QAAQ,CAAC,KAAK,CAAC;YACxC,IAAI,WAAW;gBACb,OAAO,mBAAmB,SAAS,CAAC,EAAE;YACxC;QACF;QACA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 388, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/src/personal/bvr-safaris/src/components/ui/FileUpload.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useRef } from 'react'\nimport Image from 'next/image'\nimport { But<PERSON> } from './Button'\nimport { farmImageService, profileImageService } from '@/lib/firebase/storage'\n\ninterface FileUploadProps {\n  onUpload: (url: string) => void\n  bucket: 'farm-images' | 'profile-images'\n  farmId?: string // Required for farm-images bucket\n  userId?: string // Required for profile-images bucket\n  accept?: string\n  maxSize?: number // in MB\n  className?: string\n  children?: React.ReactNode\n}\n\nexport function FileUpload({\n  onUpload,\n  bucket,\n  farmId,\n  userId,\n  accept = 'image/*',\n  maxSize = 5,\n  className = '',\n  children\n}: FileUploadProps) {\n  const [uploading, setUploading] = useState(false)\n  const [uploadProgress, setUploadProgress] = useState<number>(0)\n  const [error, setError] = useState<string | null>(null)\n  const [uploadMethod, setUploadMethod] = useState<'cloud-function' | 'direct' | null>(null)\n  const fileInputRef = useRef<HTMLInputElement>(null)\n\n  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {\n    const file = event.target.files?.[0]\n    if (!file) return\n\n    setError(null)\n    setUploadProgress(0)\n    setUploadMethod(null)\n\n    // Validate file size\n    if (file.size > maxSize * 1024 * 1024) {\n      setError(`File size must be less than ${maxSize}MB`)\n      return\n    }\n\n    // Validate file type\n    if (accept && !file.type.match(accept.replace('*', '.*'))) {\n      setError('Invalid file type')\n      return\n    }\n\n    // Validate required parameters\n    if (bucket === 'farm-images' && !farmId) {\n      setError('Farm ID is required for farm image uploads')\n      return\n    }\n\n    if (bucket === 'profile-images' && !userId) {\n      setError('User ID is required for profile image uploads')\n      return\n    }\n\n    setUploading(true)\n\n    try {\n      let downloadURL: string\n\n      if (bucket === 'farm-images' && farmId) {\n        console.log('Starting farm image upload for farmId:', farmId)\n\n        // Track which upload method is being used\n        const originalConsoleLog = console.log\n        console.log = (...args) => {\n          if (args[0]?.includes('Using Cloud Function')) {\n            setUploadMethod('cloud-function')\n          } else if (args[0]?.includes('falling back to direct upload')) {\n            setUploadMethod('direct')\n          }\n          originalConsoleLog(...args)\n        }\n\n        downloadURL = await farmImageService.uploadFarmImage(farmId, file, {\n          onProgress: (progress) => {\n            console.log('Upload progress:', progress.progress)\n            setUploadProgress(progress.progress)\n          },\n          onError: (error) => {\n            console.error('Upload error:', error)\n            setError(error.message)\n          }\n        })\n\n        // Restore console.log\n        console.log = originalConsoleLog\n      } else if (bucket === 'profile-images' && userId) {\n        console.log('Starting profile image upload for userId:', userId)\n\n        // Track which upload method is being used\n        const originalConsoleLog = console.log\n        console.log = (...args) => {\n          if (args[0]?.includes('Using Cloud Function')) {\n            setUploadMethod('cloud-function')\n          } else if (args[0]?.includes('falling back to direct upload')) {\n            setUploadMethod('direct')\n          }\n          originalConsoleLog(...args)\n        }\n\n        downloadURL = await profileImageService.uploadProfileImage(userId, file, {\n          onProgress: (progress) => {\n            console.log('Upload progress:', progress.progress)\n            setUploadProgress(progress.progress)\n          },\n          onError: (error) => {\n            console.error('Upload error:', error)\n            setError(error.message)\n          }\n        })\n\n        // Restore console.log\n        console.log = originalConsoleLog\n      } else {\n        throw new Error('Invalid bucket or missing required parameters')\n      }\n\n      onUpload(downloadURL)\n      setUploadProgress(100)\n    } catch (error) {\n      console.error('Upload error:', error)\n      setError(error instanceof Error ? error.message : 'Upload failed')\n    } finally {\n      setUploading(false)\n      // Reset progress after a delay\n      setTimeout(() => setUploadProgress(0), 2000)\n    }\n  }\n\n  const handleClick = () => {\n    fileInputRef.current?.click()\n  }\n\n  return (\n    <div className={className}>\n      <input\n        ref={fileInputRef}\n        type=\"file\"\n        accept={accept}\n        onChange={handleFileSelect}\n        className=\"hidden\"\n      />\n\n      {children ? (\n        <div onClick={handleClick} className=\"cursor-pointer\">\n          {children}\n        </div>\n      ) : (\n        <Button\n          type=\"button\"\n          variant=\"outline\"\n          onClick={handleClick}\n          isLoading={uploading}\n          disabled={uploading}\n        >\n          {uploading ? `Uploading... ${Math.round(uploadProgress)}%` : 'Upload File'}\n        </Button>\n      )}\n\n      {error && (\n        <p className=\"text-red-600 text-sm mt-2\">{error}</p>\n      )}\n\n      {uploading && uploadMethod && (\n        <p className=\"text-blue-600 text-xs mt-1\">\n          {uploadMethod === 'cloud-function'\n            ? '🚀 Using secure server-side upload'\n            : '📁 Using direct upload'}\n        </p>\n      )}\n    </div>\n  )\n}\n\ninterface ImageUploadProps extends Omit<FileUploadProps, 'accept'> {\n  currentImage?: string\n  alt?: string\n}\n\nexport function ImageUpload({\n  currentImage,\n  alt = 'Upload preview',\n  onUpload,\n  bucket,\n  farmId,\n  userId,\n  maxSize = 5,\n  className = ''\n}: ImageUploadProps) {\n  const [uploading, setUploading] = useState(false)\n  const [uploadProgress, setUploadProgress] = useState<number>(0)\n  const [error, setError] = useState<string | null>(null)\n  const [preview, setPreview] = useState<string | null>(currentImage || null)\n  const fileInputRef = useRef<HTMLInputElement>(null)\n\n  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {\n    const file = event.target.files?.[0]\n    if (!file) return\n\n    setError(null)\n    setUploadProgress(0)\n\n    // Validate file size\n    if (file.size > maxSize * 1024 * 1024) {\n      setError(`File size must be less than ${maxSize}MB`)\n      return\n    }\n\n    // Validate file type\n    if (!file.type.startsWith('image/')) {\n      setError('Please select an image file')\n      return\n    }\n\n    // Validate required parameters\n    if (bucket === 'farm-images' && !farmId) {\n      setError('Farm ID is required for farm image uploads')\n      return\n    }\n\n    if (bucket === 'profile-images' && !userId) {\n      setError('User ID is required for profile image uploads')\n      return\n    }\n\n    // Create preview\n    const reader = new FileReader()\n    reader.onload = (e) => {\n      setPreview(e.target?.result as string)\n    }\n    reader.readAsDataURL(file)\n\n    setUploading(true)\n\n    try {\n      let downloadURL: string\n\n      if (bucket === 'farm-images' && farmId) {\n        console.log('Starting farm image upload for farmId:', farmId)\n        downloadURL = await farmImageService.uploadFarmImage(farmId, file, {\n          onProgress: (progress) => {\n            console.log('Upload progress:', progress.progress)\n            setUploadProgress(progress.progress)\n          },\n          onError: (error) => {\n            console.error('Upload error:', error)\n            setError(error.message)\n          }\n        })\n      } else if (bucket === 'profile-images' && userId) {\n        console.log('Starting profile image upload for userId:', userId)\n        downloadURL = await profileImageService.uploadProfileImage(userId, file, {\n          onProgress: (progress) => {\n            console.log('Upload progress:', progress.progress)\n            setUploadProgress(progress.progress)\n          },\n          onError: (error) => {\n            console.error('Upload error:', error)\n            setError(error.message)\n          }\n        })\n      } else {\n        throw new Error('Invalid bucket or missing required parameters')\n      }\n\n      // Update preview to use the uploaded URL\n      setPreview(downloadURL)\n      onUpload(downloadURL)\n      setUploadProgress(100)\n    } catch (error) {\n      console.error('Upload error:', error)\n      setError(error instanceof Error ? error.message : 'Upload failed')\n      // Reset preview on error\n      setPreview(currentImage || null)\n    } finally {\n      setUploading(false)\n      // Reset progress after a delay\n      setTimeout(() => setUploadProgress(0), 2000)\n    }\n  }\n\n  const handleClick = () => {\n    fileInputRef.current?.click()\n  }\n\n  return (\n    <div className={className}>\n      <input\n        ref={fileInputRef}\n        type=\"file\"\n        accept=\"image/*\"\n        onChange={handleFileSelect}\n        className=\"hidden\"\n      />\n\n      <div\n        onClick={handleClick}\n        className=\"relative border-2 border-dashed border-earth-300 rounded-lg p-6 hover:border-accent-600 transition-colors cursor-pointer group\"\n      >\n        {preview ? (\n          <div className=\"relative\">\n            <div className=\"relative w-full h-48 rounded-lg overflow-hidden\">\n              <Image\n                src={preview}\n                alt={alt}\n                fill\n                className=\"object-cover\"\n                sizes=\"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw\"\n              />\n            </div>\n            <div className=\"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all rounded-lg flex items-center justify-center\">\n              <div className=\"opacity-0 group-hover:opacity-100 transition-opacity\">\n                <svg className=\"w-8 h-8 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z\" />\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 13a3 3 0 11-6 0 3 3 0 016 0z\" />\n                </svg>\n              </div>\n            </div>\n            {uploading && (\n              <div className=\"absolute inset-0 bg-black bg-opacity-50 rounded-lg flex items-center justify-center\">\n                <div className=\"text-center text-white\">\n                  <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-2\"></div>\n                  <p className=\"text-sm\">{Math.round(uploadProgress)}%</p>\n                </div>\n              </div>\n            )}\n          </div>\n        ) : (\n          <div className=\"text-center\">\n            <svg className=\"mx-auto h-12 w-12 text-earth-400\" stroke=\"currentColor\" fill=\"none\" viewBox=\"0 0 48 48\">\n              <path d=\"M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02\" strokeWidth={2} strokeLinecap=\"round\" strokeLinejoin=\"round\" />\n            </svg>\n            <div className=\"mt-4\">\n              <p className=\"text-sm text-earth-600\">\n                {uploading ? `Uploading... ${Math.round(uploadProgress)}%` : 'Click to upload an image'}\n              </p>\n              <p className=\"text-xs text-earth-500 mt-1\">\n                PNG, JPG, GIF up to {maxSize}MB\n              </p>\n            </div>\n          </div>\n        )}\n      </div>\n\n      {error && (\n        <p className=\"text-red-600 text-sm mt-2\">{error}</p>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAkBO,SAAS,WAAW,EACzB,QAAQ,EACR,MAAM,EACN,MAAM,EACN,MAAM,EACN,SAAS,SAAS,EAClB,UAAU,CAAC,EACX,YAAY,EAAE,EACd,QAAQ,EACQ;IAChB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC7D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsC;IACrF,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAE9C,MAAM,mBAAmB,OAAO;QAC9B,MAAM,OAAO,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QACpC,IAAI,CAAC,MAAM;QAEX,SAAS;QACT,kBAAkB;QAClB,gBAAgB;QAEhB,qBAAqB;QACrB,IAAI,KAAK,IAAI,GAAG,UAAU,OAAO,MAAM;YACrC,SAAS,CAAC,4BAA4B,EAAE,QAAQ,EAAE,CAAC;YACnD;QACF;QAEA,qBAAqB;QACrB,IAAI,UAAU,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,OAAO,OAAO,CAAC,KAAK,QAAQ;YACzD,SAAS;YACT;QACF;QAEA,+BAA+B;QAC/B,IAAI,WAAW,iBAAiB,CAAC,QAAQ;YACvC,SAAS;YACT;QACF;QAEA,IAAI,WAAW,oBAAoB,CAAC,QAAQ;YAC1C,SAAS;YACT;QACF;QAEA,aAAa;QAEb,IAAI;YACF,IAAI;YAEJ,IAAI,WAAW,iBAAiB,QAAQ;gBACtC,QAAQ,GAAG,CAAC,0CAA0C;gBAEtD,0CAA0C;gBAC1C,MAAM,qBAAqB,QAAQ,GAAG;gBACtC,QAAQ,GAAG,GAAG,CAAC,GAAG;oBAChB,IAAI,IAAI,CAAC,EAAE,EAAE,SAAS,yBAAyB;wBAC7C,gBAAgB;oBAClB,OAAO,IAAI,IAAI,CAAC,EAAE,EAAE,SAAS,kCAAkC;wBAC7D,gBAAgB;oBAClB;oBACA,sBAAsB;gBACxB;gBAEA,cAAc,MAAM,iIAAA,CAAA,mBAAgB,CAAC,eAAe,CAAC,QAAQ,MAAM;oBACjE,YAAY,CAAC;wBACX,QAAQ,GAAG,CAAC,oBAAoB,SAAS,QAAQ;wBACjD,kBAAkB,SAAS,QAAQ;oBACrC;oBACA,SAAS,CAAC;wBACR,QAAQ,KAAK,CAAC,iBAAiB;wBAC/B,SAAS,MAAM,OAAO;oBACxB;gBACF;gBAEA,sBAAsB;gBACtB,QAAQ,GAAG,GAAG;YAChB,OAAO,IAAI,WAAW,oBAAoB,QAAQ;gBAChD,QAAQ,GAAG,CAAC,6CAA6C;gBAEzD,0CAA0C;gBAC1C,MAAM,qBAAqB,QAAQ,GAAG;gBACtC,QAAQ,GAAG,GAAG,CAAC,GAAG;oBAChB,IAAI,IAAI,CAAC,EAAE,EAAE,SAAS,yBAAyB;wBAC7C,gBAAgB;oBAClB,OAAO,IAAI,IAAI,CAAC,EAAE,EAAE,SAAS,kCAAkC;wBAC7D,gBAAgB;oBAClB;oBACA,sBAAsB;gBACxB;gBAEA,cAAc,MAAM,iIAAA,CAAA,sBAAmB,CAAC,kBAAkB,CAAC,QAAQ,MAAM;oBACvE,YAAY,CAAC;wBACX,QAAQ,GAAG,CAAC,oBAAoB,SAAS,QAAQ;wBACjD,kBAAkB,SAAS,QAAQ;oBACrC;oBACA,SAAS,CAAC;wBACR,QAAQ,KAAK,CAAC,iBAAiB;wBAC/B,SAAS,MAAM,OAAO;oBACxB;gBACF;gBAEA,sBAAsB;gBACtB,QAAQ,GAAG,GAAG;YAChB,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;YAEA,SAAS;YACT,kBAAkB;QACpB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD,SAAU;YACR,aAAa;YACb,+BAA+B;YAC/B,WAAW,IAAM,kBAAkB,IAAI;QACzC;IACF;IAEA,MAAM,cAAc;QAClB,aAAa,OAAO,EAAE;IACxB;IAEA,qBACE,8OAAC;QAAI,WAAW;;0BACd,8OAAC;gBACC,KAAK;gBACL,MAAK;gBACL,QAAQ;gBACR,UAAU;gBACV,WAAU;;;;;;YAGX,yBACC,8OAAC;gBAAI,SAAS;gBAAa,WAAU;0BAClC;;;;;qCAGH,8OAAC,kIAAA,CAAA,SAAM;gBACL,MAAK;gBACL,SAAQ;gBACR,SAAS;gBACT,WAAW;gBACX,UAAU;0BAET,YAAY,CAAC,aAAa,EAAE,KAAK,KAAK,CAAC,gBAAgB,CAAC,CAAC,GAAG;;;;;;YAIhE,uBACC,8OAAC;gBAAE,WAAU;0BAA6B;;;;;;YAG3C,aAAa,8BACZ,8OAAC;gBAAE,WAAU;0BACV,iBAAiB,mBACd,uCACA;;;;;;;;;;;;AAKd;AAOO,SAAS,YAAY,EAC1B,YAAY,EACZ,MAAM,gBAAgB,EACtB,QAAQ,EACR,MAAM,EACN,MAAM,EACN,MAAM,EACN,UAAU,CAAC,EACX,YAAY,EAAE,EACG;IACjB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC7D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB,gBAAgB;IACtE,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAE9C,MAAM,mBAAmB,OAAO;QAC9B,MAAM,OAAO,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QACpC,IAAI,CAAC,MAAM;QAEX,SAAS;QACT,kBAAkB;QAElB,qBAAqB;QACrB,IAAI,KAAK,IAAI,GAAG,UAAU,OAAO,MAAM;YACrC,SAAS,CAAC,4BAA4B,EAAE,QAAQ,EAAE,CAAC;YACnD;QACF;QAEA,qBAAqB;QACrB,IAAI,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;YACnC,SAAS;YACT;QACF;QAEA,+BAA+B;QAC/B,IAAI,WAAW,iBAAiB,CAAC,QAAQ;YACvC,SAAS;YACT;QACF;QAEA,IAAI,WAAW,oBAAoB,CAAC,QAAQ;YAC1C,SAAS;YACT;QACF;QAEA,iBAAiB;QACjB,MAAM,SAAS,IAAI;QACnB,OAAO,MAAM,GAAG,CAAC;YACf,WAAW,EAAE,MAAM,EAAE;QACvB;QACA,OAAO,aAAa,CAAC;QAErB,aAAa;QAEb,IAAI;YACF,IAAI;YAEJ,IAAI,WAAW,iBAAiB,QAAQ;gBACtC,QAAQ,GAAG,CAAC,0CAA0C;gBACtD,cAAc,MAAM,iIAAA,CAAA,mBAAgB,CAAC,eAAe,CAAC,QAAQ,MAAM;oBACjE,YAAY,CAAC;wBACX,QAAQ,GAAG,CAAC,oBAAoB,SAAS,QAAQ;wBACjD,kBAAkB,SAAS,QAAQ;oBACrC;oBACA,SAAS,CAAC;wBACR,QAAQ,KAAK,CAAC,iBAAiB;wBAC/B,SAAS,MAAM,OAAO;oBACxB;gBACF;YACF,OAAO,IAAI,WAAW,oBAAoB,QAAQ;gBAChD,QAAQ,GAAG,CAAC,6CAA6C;gBACzD,cAAc,MAAM,iIAAA,CAAA,sBAAmB,CAAC,kBAAkB,CAAC,QAAQ,MAAM;oBACvE,YAAY,CAAC;wBACX,QAAQ,GAAG,CAAC,oBAAoB,SAAS,QAAQ;wBACjD,kBAAkB,SAAS,QAAQ;oBACrC;oBACA,SAAS,CAAC;wBACR,QAAQ,KAAK,CAAC,iBAAiB;wBAC/B,SAAS,MAAM,OAAO;oBACxB;gBACF;YACF,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;YAEA,yCAAyC;YACzC,WAAW;YACX,SAAS;YACT,kBAAkB;QACpB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD,yBAAyB;YACzB,WAAW,gBAAgB;QAC7B,SAAU;YACR,aAAa;YACb,+BAA+B;YAC/B,WAAW,IAAM,kBAAkB,IAAI;QACzC;IACF;IAEA,MAAM,cAAc;QAClB,aAAa,OAAO,EAAE;IACxB;IAEA,qBACE,8OAAC;QAAI,WAAW;;0BACd,8OAAC;gBACC,KAAK;gBACL,MAAK;gBACL,QAAO;gBACP,UAAU;gBACV,WAAU;;;;;;0BAGZ,8OAAC;gBACC,SAAS;gBACT,WAAU;0BAET,wBACC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;gCACJ,KAAK;gCACL,KAAK;gCACL,IAAI;gCACJ,WAAU;gCACV,OAAM;;;;;;;;;;;sCAGV,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;oCAAqB,MAAK;oCAAO,QAAO;oCAAe,SAAQ;;sDAC5E,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;sDACrE,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;wBAI1E,2BACC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAE,WAAU;;4CAAW,KAAK,KAAK,CAAC;4CAAgB;;;;;;;;;;;;;;;;;;;;;;;yCAM3D,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;4BAAmC,QAAO;4BAAe,MAAK;4BAAO,SAAQ;sCAC1F,cAAA,8OAAC;gCAAK,GAAE;gCAAyL,aAAa;gCAAG,eAAc;gCAAQ,gBAAe;;;;;;;;;;;sCAExP,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CACV,YAAY,CAAC,aAAa,EAAE,KAAK,KAAK,CAAC,gBAAgB,CAAC,CAAC,GAAG;;;;;;8CAE/D,8OAAC;oCAAE,WAAU;;wCAA8B;wCACpB;wCAAQ;;;;;;;;;;;;;;;;;;;;;;;;YAOtC,uBACC,8OAAC;gBAAE,WAAU;0BAA6B;;;;;;;;;;;;AAIlD", "debugId": null}}, {"offset": {"line": 850, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/src/personal/bvr-safaris/src/lib/types/location.ts"], "sourcesContent": ["/**\n * Location-related types for BVR Safaris geolocation functionality\n */\n\nimport { GeoPoint } from 'firebase/firestore'\n\n// Google Places API response types\nexport interface LocationSearchResult {\n  placeId: string\n  description: string\n  mainText: string\n  secondaryText: string\n  types: string[]\n  structuredFormatting?: {\n    mainText: string\n    secondaryText?: string\n  }\n}\n\nexport interface AddressComponent {\n  longName: string\n  shortName: string\n  types: string[]\n}\n\nexport interface PlaceDetails {\n  placeId: string\n  formattedAddress: string\n  coordinates: {\n    lat: number\n    lng: number\n  }\n  addressComponents: AddressComponent[]\n  types: string[]\n  viewport?: {\n    northeast: { lat: number; lng: number }\n    southwest: { lat: number; lng: number }\n  }\n  name?: string\n  businessStatus?: string\n}\n\n// Enhanced location data structure for farms\nexport interface LocationData {\n  placeId: string\n  formattedAddress: string\n  coordinates: GeoPoint // Firestore GeoPoint for geo queries\n  addressComponents: {\n    streetNumber?: string\n    route?: string\n    locality?: string // City/Town\n    sublocality?: string // Suburb/Area\n    administrativeAreaLevel1: string // Province\n    administrativeAreaLevel2?: string // District/Municipality\n    postalCode?: string\n    country: string // Should be \"South Africa\"\n  }\n  placeTypes: string[]\n  viewport?: {\n    northeast: GeoPoint\n    southwest: GeoPoint\n  }\n  name?: string // Business name if applicable\n}\n\n// Search parameters for location-based queries\nexport interface LocationSearchParams {\n  center?: { lat: number; lng: number }\n  radius?: number // in kilometers\n  bounds?: {\n    northeast: { lat: number; lng: number }\n    southwest: { lat: number; lng: number }\n  }\n  formattedAddress?: string\n  text?: string // fallback text search\n}\n\n// Distance calculation result\nexport interface DistanceResult {\n  distance: number // in kilometers\n  duration?: number // in minutes (if available)\n}\n\n// Geocoding result\nexport interface GeocodeResult {\n  coordinates: { lat: number; lng: number }\n  formattedAddress: string\n  addressComponents: AddressComponent[]\n  placeId?: string\n  types: string[]\n}\n\n// Location service configuration\nexport interface LocationServiceConfig {\n  apiKey: string\n  countryRestriction?: string // Default: 'za' for South Africa\n  language?: string // Default: 'en'\n  region?: string // Default: 'za'\n}\n\n// Cache entry for location data\nexport interface LocationCacheEntry {\n  data: any\n  timestamp: number\n  ttl: number // time to live in milliseconds\n}\n\n// Error types for location services\nexport enum LocationErrorType {\n  API_ERROR = 'API_ERROR',\n  NETWORK_ERROR = 'NETWORK_ERROR',\n  INVALID_REQUEST = 'INVALID_REQUEST',\n  QUOTA_EXCEEDED = 'QUOTA_EXCEEDED',\n  PERMISSION_DENIED = 'PERMISSION_DENIED',\n  UNKNOWN_ERROR = 'UNKNOWN_ERROR'\n}\n\nexport interface LocationError {\n  type: LocationErrorType\n  message: string\n  originalError?: any\n}\n\n// Utility types for component props\nexport interface LocationAutocompleteProps {\n  value?: string\n  onLocationSelect: (location: PlaceDetails) => void\n  onInputChange?: (input: string) => void\n  placeholder?: string\n  countryRestriction?: string\n  types?: string[]\n  className?: string\n  disabled?: boolean\n  required?: boolean\n  error?: string\n}\n\nexport interface LocationDisplayProps {\n  locationData: LocationData\n  showFullAddress?: boolean\n  showDistance?: boolean\n  userLocation?: { lat: number; lng: number }\n  className?: string\n}\n\nexport interface DistanceFilterProps {\n  value?: number\n  onChange: (radius: number) => void\n  options?: number[] // Available radius options in km\n  className?: string\n}\n\n// Search filter types with location support\nexport interface EnhancedSearchFilters {\n  query?: string\n  location?: LocationSearchParams\n  provinces?: string[]\n  activities?: string[]\n  priceRange?: [number, number]\n  sortBy?: 'distance' | 'rating' | 'created' | 'name' | 'price'\n  limit?: number\n}\n\n// Farm with distance information\nexport interface FarmWithDistance {\n  id: string\n  name: string\n  locationData?: LocationData\n  distance?: number\n  [key: string]: any // Other farm properties\n}\n\n// Browser geolocation types\nexport interface BrowserLocationOptions {\n  enableHighAccuracy?: boolean\n  timeout?: number\n  maximumAge?: number\n}\n\nexport interface BrowserLocationResult {\n  coordinates: { lat: number; lng: number }\n  accuracy: number\n  timestamp: number\n}\n\n// Rate limiting configuration\nexport interface RateLimitConfig {\n  requestsPerSecond: number\n  burstLimit: number\n  windowMs: number\n}\n\n// Service response wrapper\nexport interface ServiceResponse<T> {\n  success: boolean\n  data?: T\n  error?: LocationError\n  cached?: boolean\n}\n\n// Province mapping for South African addresses\nexport const PROVINCE_MAPPING: Record<string, string> = {\n  'Eastern Cape': 'Eastern Cape',\n  'Free State': 'Free State',\n  'Gauteng': 'Gauteng',\n  'KwaZulu-Natal': 'KwaZulu-Natal',\n  'Limpopo': 'Limpopo',\n  'Mpumalanga': 'Mpumalanga',\n  'Northern Cape': 'Northern Cape',\n  'North West': 'North West',\n  'Western Cape': 'Western Cape'\n}\n\n// Common place types for South African locations\nexport const SOUTH_AFRICAN_PLACE_TYPES = {\n  ESTABLISHMENT: 'establishment',\n  GEOCODE: 'geocode',\n  LOCALITY: 'locality',\n  SUBLOCALITY: 'sublocality',\n  ADMINISTRATIVE_AREA_LEVEL_1: 'administrative_area_level_1',\n  ADMINISTRATIVE_AREA_LEVEL_2: 'administrative_area_level_2',\n  COUNTRY: 'country',\n  POSTAL_CODE: 'postal_code'\n} as const\n\n// Default configuration values\nexport const DEFAULT_LOCATION_CONFIG = {\n  COUNTRY_RESTRICTION: 'za',\n  LANGUAGE: 'en',\n  REGION: 'za',\n  CACHE_TTL: 5 * 60 * 1000, // 5 minutes\n  DEBOUNCE_DELAY: 300, // milliseconds\n  DEFAULT_RADIUS_OPTIONS: [5, 10, 25, 50, 100], // kilometers\n  MAX_AUTOCOMPLETE_RESULTS: 5,\n  GEOLOCATION_TIMEOUT: 10000, // 10 seconds\n  GEOLOCATION_MAX_AGE: 60000 // 1 minute\n} as const\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;;AA0GM,IAAA,AAAK,2CAAA;;;;;;;WAAA;;AA6FL,MAAM,mBAA2C;IACtD,gBAAgB;IAChB,cAAc;IACd,WAAW;IACX,iBAAiB;IACjB,WAAW;IACX,cAAc;IACd,iBAAiB;IACjB,cAAc;IACd,gBAAgB;AAClB;AAGO,MAAM,4BAA4B;IACvC,eAAe;IACf,SAAS;IACT,UAAU;IACV,aAAa;IACb,6BAA6B;IAC7B,6BAA6B;IAC7B,SAAS;IACT,aAAa;AACf;AAGO,MAAM,0BAA0B;IACrC,qBAAqB;IACrB,UAAU;IACV,QAAQ;IACR,WAAW,IAAI,KAAK;IACpB,gBAAgB;IAChB,wBAAwB;QAAC;QAAG;QAAI;QAAI;QAAI;KAAI;IAC5C,0BAA0B;IAC1B,qBAAqB;IACrB,qBAAqB,MAAM,WAAW;AACxC", "debugId": null}}, {"offset": {"line": 911, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/src/personal/bvr-safaris/src/lib/services/location/googlePlaces.ts"], "sourcesContent": ["/**\n * Google Places API service for BVR Safaris\n * Handles autocomplete, place details, and geocoding functionality\n */\n\nimport {\n  LocationSearchResult,\n  PlaceDetails,\n  GeocodeResult,\n  LocationServiceConfig,\n  LocationError,\n  LocationErrorType,\n  ServiceResponse,\n  DEFAULT_LOCATION_CONFIG\n} from '@/lib/types/location'\n\nexport class GooglePlacesService {\n  private apiKey: string\n  private config: LocationServiceConfig\n  private cache = new Map<string, any>()\n  private requestQueue = new Map<string, Promise<any>>()\n\n  constructor(apiKey: string, config?: Partial<LocationServiceConfig>) {\n    this.apiKey = apiKey\n    this.config = {\n      apiKey,\n      countryRestriction: DEFAULT_LOCATION_CONFIG.COUNTRY_RESTRICTION,\n      language: DEFAULT_LOCATION_CONFIG.LANGUAGE,\n      region: DEFAULT_LOCATION_CONFIG.REGION,\n      ...config\n    }\n  }\n\n  /**\n   * Get place predictions for autocomplete\n   */\n  async getPlacePredictions(\n    input: string,\n    options?: {\n      types?: string[]\n      componentRestrictions?: { country: string }\n      locationBias?: { lat: number; lng: number; radius: number }\n      sessionToken?: string\n    }\n  ): Promise<ServiceResponse<LocationSearchResult[]>> {\n    if (!input || input.length < 2) {\n      return { success: true, data: [] }\n    }\n\n    const cacheKey = `predictions_${input}_${JSON.stringify(options)}`\n    \n    // Check cache first\n    const cached = this.getFromCache(cacheKey)\n    if (cached) {\n      return { success: true, data: cached, cached: true }\n    }\n\n    // Check if request is already in progress\n    if (this.requestQueue.has(cacheKey)) {\n      try {\n        const result = await this.requestQueue.get(cacheKey)!\n        return { success: true, data: result }\n      } catch (error) {\n        return this.handleError(error)\n      }\n    }\n\n    // Create new request\n    const requestPromise = this.fetchPlacePredictions(input, options)\n    this.requestQueue.set(cacheKey, requestPromise)\n\n    try {\n      const results = await requestPromise\n      this.setCache(cacheKey, results)\n      this.requestQueue.delete(cacheKey)\n      return { success: true, data: results }\n    } catch (error) {\n      this.requestQueue.delete(cacheKey)\n      return this.handleError(error)\n    }\n  }\n\n  /**\n   * Get detailed place information\n   */\n  async getPlaceDetails(\n    placeId: string,\n    fields?: string[]\n  ): Promise<ServiceResponse<PlaceDetails>> {\n    const cacheKey = `details_${placeId}_${fields?.join(',') || 'default'}`\n    \n    // Check cache first\n    const cached = this.getFromCache(cacheKey)\n    if (cached) {\n      return { success: true, data: cached, cached: true }\n    }\n\n    try {\n      const details = await this.fetchPlaceDetails(placeId, fields)\n      this.setCache(cacheKey, details)\n      return { success: true, data: details }\n    } catch (error) {\n      return this.handleError(error)\n    }\n  }\n\n  /**\n   * Geocode an address to coordinates\n   */\n  async geocodeAddress(address: string): Promise<ServiceResponse<GeocodeResult>> {\n    const cacheKey = `geocode_${address}`\n    \n    // Check cache first\n    const cached = this.getFromCache(cacheKey)\n    if (cached) {\n      return { success: true, data: cached, cached: true }\n    }\n\n    try {\n      const result = await this.fetchGeocode(address)\n      this.setCache(cacheKey, result)\n      return { success: true, data: result }\n    } catch (error) {\n      return this.handleError(error)\n    }\n  }\n\n  /**\n   * Reverse geocode coordinates to address\n   */\n  async reverseGeocode(\n    lat: number, \n    lng: number\n  ): Promise<ServiceResponse<PlaceDetails>> {\n    const cacheKey = `reverse_${lat}_${lng}`\n    \n    // Check cache first\n    const cached = this.getFromCache(cacheKey)\n    if (cached) {\n      return { success: true, data: cached, cached: true }\n    }\n\n    try {\n      const result = await this.fetchReverseGeocode(lat, lng)\n      this.setCache(cacheKey, result)\n      return { success: true, data: result }\n    } catch (error) {\n      return this.handleError(error)\n    }\n  }\n\n  /**\n   * Private method to fetch place predictions from Google API\n   */\n  private async fetchPlacePredictions(\n    input: string,\n    options?: any\n  ): Promise<LocationSearchResult[]> {\n    const params = new URLSearchParams({\n      input,\n      key: this.apiKey,\n      language: this.config.language!,\n      components: `country:${options?.componentRestrictions?.country || this.config.countryRestriction}`,\n    })\n\n    if (options?.types) {\n      params.append('types', options.types.join('|'))\n    }\n\n    if (options?.sessionToken) {\n      params.append('sessiontoken', options.sessionToken)\n    }\n\n    const response = await fetch(\n      `https://maps.googleapis.com/maps/api/place/autocomplete/json?${params}`\n    )\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`)\n    }\n\n    const data = await response.json()\n\n    if (data.status !== 'OK' && data.status !== 'ZERO_RESULTS') {\n      throw new Error(`Google Places API error: ${data.status} - ${data.error_message || 'Unknown error'}`)\n    }\n\n    return (data.predictions || []).map((prediction: any) => ({\n      placeId: prediction.place_id,\n      description: prediction.description,\n      mainText: prediction.structured_formatting?.main_text || prediction.description,\n      secondaryText: prediction.structured_formatting?.secondary_text || '',\n      types: prediction.types || [],\n      structuredFormatting: prediction.structured_formatting\n    }))\n  }\n\n  /**\n   * Private method to fetch place details from Google API\n   */\n  private async fetchPlaceDetails(\n    placeId: string,\n    fields?: string[]\n  ): Promise<PlaceDetails> {\n    const defaultFields = [\n      'place_id',\n      'formatted_address',\n      'geometry',\n      'address_components',\n      'types',\n      'name',\n      'business_status'\n    ]\n\n    const params = new URLSearchParams({\n      place_id: placeId,\n      key: this.apiKey,\n      language: this.config.language!,\n      fields: (fields || defaultFields).join(',')\n    })\n\n    const response = await fetch(\n      `https://maps.googleapis.com/maps/api/place/details/json?${params}`\n    )\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`)\n    }\n\n    const data = await response.json()\n\n    if (data.status !== 'OK') {\n      throw new Error(`Google Places API error: ${data.status} - ${data.error_message || 'Unknown error'}`)\n    }\n\n    const place = data.result\n    return {\n      placeId: place.place_id,\n      formattedAddress: place.formatted_address,\n      coordinates: {\n        lat: place.geometry.location.lat,\n        lng: place.geometry.location.lng\n      },\n      addressComponents: (place.address_components || []).map((component: any) => ({\n        longName: component.long_name,\n        shortName: component.short_name,\n        types: component.types\n      })),\n      types: place.types || [],\n      viewport: place.geometry.viewport ? {\n        northeast: {\n          lat: place.geometry.viewport.northeast.lat,\n          lng: place.geometry.viewport.northeast.lng\n        },\n        southwest: {\n          lat: place.geometry.viewport.southwest.lat,\n          lng: place.geometry.viewport.southwest.lng\n        }\n      } : undefined,\n      name: place.name,\n      businessStatus: place.business_status\n    }\n  }\n\n  /**\n   * Private method to geocode address\n   */\n  private async fetchGeocode(address: string): Promise<GeocodeResult> {\n    const params = new URLSearchParams({\n      address,\n      key: this.apiKey,\n      language: this.config.language!,\n      region: this.config.region!\n    })\n\n    const response = await fetch(\n      `https://maps.googleapis.com/maps/api/geocode/json?${params}`\n    )\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`)\n    }\n\n    const data = await response.json()\n\n    if (data.status !== 'OK') {\n      throw new Error(`Geocoding API error: ${data.status} - ${data.error_message || 'Unknown error'}`)\n    }\n\n    const result = data.results[0]\n    return {\n      coordinates: {\n        lat: result.geometry.location.lat,\n        lng: result.geometry.location.lng\n      },\n      formattedAddress: result.formatted_address,\n      addressComponents: (result.address_components || []).map((component: any) => ({\n        longName: component.long_name,\n        shortName: component.short_name,\n        types: component.types\n      })),\n      placeId: result.place_id,\n      types: result.types || []\n    }\n  }\n\n  /**\n   * Private method to reverse geocode coordinates\n   */\n  private async fetchReverseGeocode(lat: number, lng: number): Promise<PlaceDetails> {\n    const params = new URLSearchParams({\n      latlng: `${lat},${lng}`,\n      key: this.apiKey,\n      language: this.config.language!\n    })\n\n    const response = await fetch(\n      `https://maps.googleapis.com/maps/api/geocode/json?${params}`\n    )\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`)\n    }\n\n    const data = await response.json()\n\n    if (data.status !== 'OK') {\n      throw new Error(`Reverse geocoding API error: ${data.status} - ${data.error_message || 'Unknown error'}`)\n    }\n\n    const result = data.results[0]\n    return {\n      placeId: result.place_id,\n      formattedAddress: result.formatted_address,\n      coordinates: { lat, lng },\n      addressComponents: (result.address_components || []).map((component: any) => ({\n        longName: component.long_name,\n        shortName: component.short_name,\n        types: component.types\n      })),\n      types: result.types || []\n    }\n  }\n\n  /**\n   * Cache management\n   */\n  private getFromCache(key: string): any {\n    const entry = this.cache.get(key)\n    if (!entry) return null\n\n    const { data, timestamp, ttl } = entry\n    if (Date.now() - timestamp > ttl) {\n      this.cache.delete(key)\n      return null\n    }\n\n    return data\n  }\n\n  private setCache(key: string, data: any, ttl = DEFAULT_LOCATION_CONFIG.CACHE_TTL): void {\n    this.cache.set(key, {\n      data,\n      timestamp: Date.now(),\n      ttl\n    })\n\n    // Clean up old entries periodically\n    if (this.cache.size > 100) {\n      this.cleanupCache()\n    }\n  }\n\n  private cleanupCache(): void {\n    const now = Date.now()\n    for (const [key, entry] of this.cache.entries()) {\n      if (now - entry.timestamp > entry.ttl) {\n        this.cache.delete(key)\n      }\n    }\n  }\n\n  /**\n   * Error handling\n   */\n  private handleError(error: any): ServiceResponse<any> {\n    let locationError: LocationError\n\n    if (error.message?.includes('quota') || error.message?.includes('OVER_QUERY_LIMIT')) {\n      locationError = {\n        type: LocationErrorType.QUOTA_EXCEEDED,\n        message: 'API quota exceeded. Please try again later.',\n        originalError: error\n      }\n    } else if (error.message?.includes('PERMISSION_DENIED')) {\n      locationError = {\n        type: LocationErrorType.PERMISSION_DENIED,\n        message: 'Permission denied. Please check API key configuration.',\n        originalError: error\n      }\n    } else if (error.message?.includes('INVALID_REQUEST')) {\n      locationError = {\n        type: LocationErrorType.INVALID_REQUEST,\n        message: 'Invalid request parameters.',\n        originalError: error\n      }\n    } else if (error.name === 'TypeError' || error.message?.includes('fetch')) {\n      locationError = {\n        type: LocationErrorType.NETWORK_ERROR,\n        message: 'Network error. Please check your internet connection.',\n        originalError: error\n      }\n    } else {\n      locationError = {\n        type: LocationErrorType.UNKNOWN_ERROR,\n        message: error.message || 'An unknown error occurred.',\n        originalError: error\n      }\n    }\n\n    return { success: false, error: locationError }\n  }\n\n  /**\n   * Clear cache\n   */\n  clearCache(): void {\n    this.cache.clear()\n  }\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAED;;AAWO,MAAM;IACH,OAAc;IACd,OAA6B;IAC7B,QAAQ,IAAI,MAAkB;IAC9B,eAAe,IAAI,MAA2B;IAEtD,YAAY,MAAc,EAAE,MAAuC,CAAE;QACnE,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,MAAM,GAAG;YACZ;YACA,oBAAoB,+HAAA,CAAA,0BAAuB,CAAC,mBAAmB;YAC/D,UAAU,+HAAA,CAAA,0BAAuB,CAAC,QAAQ;YAC1C,QAAQ,+HAAA,CAAA,0BAAuB,CAAC,MAAM;YACtC,GAAG,MAAM;QACX;IACF;IAEA;;GAEC,GACD,MAAM,oBACJ,KAAa,EACb,OAKC,EACiD;QAClD,IAAI,CAAC,SAAS,MAAM,MAAM,GAAG,GAAG;YAC9B,OAAO;gBAAE,SAAS;gBAAM,MAAM,EAAE;YAAC;QACnC;QAEA,MAAM,WAAW,CAAC,YAAY,EAAE,MAAM,CAAC,EAAE,KAAK,SAAS,CAAC,UAAU;QAElE,oBAAoB;QACpB,MAAM,SAAS,IAAI,CAAC,YAAY,CAAC;QACjC,IAAI,QAAQ;YACV,OAAO;gBAAE,SAAS;gBAAM,MAAM;gBAAQ,QAAQ;YAAK;QACrD;QAEA,0CAA0C;QAC1C,IAAI,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,WAAW;YACnC,IAAI;gBACF,MAAM,SAAS,MAAM,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC;gBAC3C,OAAO;oBAAE,SAAS;oBAAM,MAAM;gBAAO;YACvC,EAAE,OAAO,OAAO;gBACd,OAAO,IAAI,CAAC,WAAW,CAAC;YAC1B;QACF;QAEA,qBAAqB;QACrB,MAAM,iBAAiB,IAAI,CAAC,qBAAqB,CAAC,OAAO;QACzD,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,UAAU;QAEhC,IAAI;YACF,MAAM,UAAU,MAAM;YACtB,IAAI,CAAC,QAAQ,CAAC,UAAU;YACxB,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC;YACzB,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAQ;QACxC,EAAE,OAAO,OAAO;YACd,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC;YACzB,OAAO,IAAI,CAAC,WAAW,CAAC;QAC1B;IACF;IAEA;;GAEC,GACD,MAAM,gBACJ,OAAe,EACf,MAAiB,EACuB;QACxC,MAAM,WAAW,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAAE,QAAQ,KAAK,QAAQ,WAAW;QAEvE,oBAAoB;QACpB,MAAM,SAAS,IAAI,CAAC,YAAY,CAAC;QACjC,IAAI,QAAQ;YACV,OAAO;gBAAE,SAAS;gBAAM,MAAM;gBAAQ,QAAQ;YAAK;QACrD;QAEA,IAAI;YACF,MAAM,UAAU,MAAM,IAAI,CAAC,iBAAiB,CAAC,SAAS;YACtD,IAAI,CAAC,QAAQ,CAAC,UAAU;YACxB,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAQ;QACxC,EAAE,OAAO,OAAO;YACd,OAAO,IAAI,CAAC,WAAW,CAAC;QAC1B;IACF;IAEA;;GAEC,GACD,MAAM,eAAe,OAAe,EAA2C;QAC7E,MAAM,WAAW,CAAC,QAAQ,EAAE,SAAS;QAErC,oBAAoB;QACpB,MAAM,SAAS,IAAI,CAAC,YAAY,CAAC;QACjC,IAAI,QAAQ;YACV,OAAO;gBAAE,SAAS;gBAAM,MAAM;gBAAQ,QAAQ;YAAK;QACrD;QAEA,IAAI;YACF,MAAM,SAAS,MAAM,IAAI,CAAC,YAAY,CAAC;YACvC,IAAI,CAAC,QAAQ,CAAC,UAAU;YACxB,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAO;QACvC,EAAE,OAAO,OAAO;YACd,OAAO,IAAI,CAAC,WAAW,CAAC;QAC1B;IACF;IAEA;;GAEC,GACD,MAAM,eACJ,GAAW,EACX,GAAW,EAC6B;QACxC,MAAM,WAAW,CAAC,QAAQ,EAAE,IAAI,CAAC,EAAE,KAAK;QAExC,oBAAoB;QACpB,MAAM,SAAS,IAAI,CAAC,YAAY,CAAC;QACjC,IAAI,QAAQ;YACV,OAAO;gBAAE,SAAS;gBAAM,MAAM;gBAAQ,QAAQ;YAAK;QACrD;QAEA,IAAI;YACF,MAAM,SAAS,MAAM,IAAI,CAAC,mBAAmB,CAAC,KAAK;YACnD,IAAI,CAAC,QAAQ,CAAC,UAAU;YACxB,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAO;QACvC,EAAE,OAAO,OAAO;YACd,OAAO,IAAI,CAAC,WAAW,CAAC;QAC1B;IACF;IAEA;;GAEC,GACD,MAAc,sBACZ,KAAa,EACb,OAAa,EACoB;QACjC,MAAM,SAAS,IAAI,gBAAgB;YACjC;YACA,KAAK,IAAI,CAAC,MAAM;YAChB,UAAU,IAAI,CAAC,MAAM,CAAC,QAAQ;YAC9B,YAAY,CAAC,QAAQ,EAAE,SAAS,uBAAuB,WAAW,IAAI,CAAC,MAAM,CAAC,kBAAkB,EAAE;QACpG;QAEA,IAAI,SAAS,OAAO;YAClB,OAAO,MAAM,CAAC,SAAS,QAAQ,KAAK,CAAC,IAAI,CAAC;QAC5C;QAEA,IAAI,SAAS,cAAc;YACzB,OAAO,MAAM,CAAC,gBAAgB,QAAQ,YAAY;QACpD;QAEA,MAAM,WAAW,MAAM,MACrB,CAAC,6DAA6D,EAAE,QAAQ;QAG1E,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,IAAI,KAAK,MAAM,KAAK,QAAQ,KAAK,MAAM,KAAK,gBAAgB;YAC1D,MAAM,IAAI,MAAM,CAAC,yBAAyB,EAAE,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,aAAa,IAAI,iBAAiB;QACtG;QAEA,OAAO,CAAC,KAAK,WAAW,IAAI,EAAE,EAAE,GAAG,CAAC,CAAC,aAAoB,CAAC;gBACxD,SAAS,WAAW,QAAQ;gBAC5B,aAAa,WAAW,WAAW;gBACnC,UAAU,WAAW,qBAAqB,EAAE,aAAa,WAAW,WAAW;gBAC/E,eAAe,WAAW,qBAAqB,EAAE,kBAAkB;gBACnE,OAAO,WAAW,KAAK,IAAI,EAAE;gBAC7B,sBAAsB,WAAW,qBAAqB;YACxD,CAAC;IACH;IAEA;;GAEC,GACD,MAAc,kBACZ,OAAe,EACf,MAAiB,EACM;QACvB,MAAM,gBAAgB;YACpB;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QAED,MAAM,SAAS,IAAI,gBAAgB;YACjC,UAAU;YACV,KAAK,IAAI,CAAC,MAAM;YAChB,UAAU,IAAI,CAAC,MAAM,CAAC,QAAQ;YAC9B,QAAQ,CAAC,UAAU,aAAa,EAAE,IAAI,CAAC;QACzC;QAEA,MAAM,WAAW,MAAM,MACrB,CAAC,wDAAwD,EAAE,QAAQ;QAGrE,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,IAAI,KAAK,MAAM,KAAK,MAAM;YACxB,MAAM,IAAI,MAAM,CAAC,yBAAyB,EAAE,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,aAAa,IAAI,iBAAiB;QACtG;QAEA,MAAM,QAAQ,KAAK,MAAM;QACzB,OAAO;YACL,SAAS,MAAM,QAAQ;YACvB,kBAAkB,MAAM,iBAAiB;YACzC,aAAa;gBACX,KAAK,MAAM,QAAQ,CAAC,QAAQ,CAAC,GAAG;gBAChC,KAAK,MAAM,QAAQ,CAAC,QAAQ,CAAC,GAAG;YAClC;YACA,mBAAmB,CAAC,MAAM,kBAAkB,IAAI,EAAE,EAAE,GAAG,CAAC,CAAC,YAAmB,CAAC;oBAC3E,UAAU,UAAU,SAAS;oBAC7B,WAAW,UAAU,UAAU;oBAC/B,OAAO,UAAU,KAAK;gBACxB,CAAC;YACD,OAAO,MAAM,KAAK,IAAI,EAAE;YACxB,UAAU,MAAM,QAAQ,CAAC,QAAQ,GAAG;gBAClC,WAAW;oBACT,KAAK,MAAM,QAAQ,CAAC,QAAQ,CAAC,SAAS,CAAC,GAAG;oBAC1C,KAAK,MAAM,QAAQ,CAAC,QAAQ,CAAC,SAAS,CAAC,GAAG;gBAC5C;gBACA,WAAW;oBACT,KAAK,MAAM,QAAQ,CAAC,QAAQ,CAAC,SAAS,CAAC,GAAG;oBAC1C,KAAK,MAAM,QAAQ,CAAC,QAAQ,CAAC,SAAS,CAAC,GAAG;gBAC5C;YACF,IAAI;YACJ,MAAM,MAAM,IAAI;YAChB,gBAAgB,MAAM,eAAe;QACvC;IACF;IAEA;;GAEC,GACD,MAAc,aAAa,OAAe,EAA0B;QAClE,MAAM,SAAS,IAAI,gBAAgB;YACjC;YACA,KAAK,IAAI,CAAC,MAAM;YAChB,UAAU,IAAI,CAAC,MAAM,CAAC,QAAQ;YAC9B,QAAQ,IAAI,CAAC,MAAM,CAAC,MAAM;QAC5B;QAEA,MAAM,WAAW,MAAM,MACrB,CAAC,kDAAkD,EAAE,QAAQ;QAG/D,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,IAAI,KAAK,MAAM,KAAK,MAAM;YACxB,MAAM,IAAI,MAAM,CAAC,qBAAqB,EAAE,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,aAAa,IAAI,iBAAiB;QAClG;QAEA,MAAM,SAAS,KAAK,OAAO,CAAC,EAAE;QAC9B,OAAO;YACL,aAAa;gBACX,KAAK,OAAO,QAAQ,CAAC,QAAQ,CAAC,GAAG;gBACjC,KAAK,OAAO,QAAQ,CAAC,QAAQ,CAAC,GAAG;YACnC;YACA,kBAAkB,OAAO,iBAAiB;YAC1C,mBAAmB,CAAC,OAAO,kBAAkB,IAAI,EAAE,EAAE,GAAG,CAAC,CAAC,YAAmB,CAAC;oBAC5E,UAAU,UAAU,SAAS;oBAC7B,WAAW,UAAU,UAAU;oBAC/B,OAAO,UAAU,KAAK;gBACxB,CAAC;YACD,SAAS,OAAO,QAAQ;YACxB,OAAO,OAAO,KAAK,IAAI,EAAE;QAC3B;IACF;IAEA;;GAEC,GACD,MAAc,oBAAoB,GAAW,EAAE,GAAW,EAAyB;QACjF,MAAM,SAAS,IAAI,gBAAgB;YACjC,QAAQ,GAAG,IAAI,CAAC,EAAE,KAAK;YACvB,KAAK,IAAI,CAAC,MAAM;YAChB,UAAU,IAAI,CAAC,MAAM,CAAC,QAAQ;QAChC;QAEA,MAAM,WAAW,MAAM,MACrB,CAAC,kDAAkD,EAAE,QAAQ;QAG/D,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,IAAI,KAAK,MAAM,KAAK,MAAM;YACxB,MAAM,IAAI,MAAM,CAAC,6BAA6B,EAAE,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,aAAa,IAAI,iBAAiB;QAC1G;QAEA,MAAM,SAAS,KAAK,OAAO,CAAC,EAAE;QAC9B,OAAO;YACL,SAAS,OAAO,QAAQ;YACxB,kBAAkB,OAAO,iBAAiB;YAC1C,aAAa;gBAAE;gBAAK;YAAI;YACxB,mBAAmB,CAAC,OAAO,kBAAkB,IAAI,EAAE,EAAE,GAAG,CAAC,CAAC,YAAmB,CAAC;oBAC5E,UAAU,UAAU,SAAS;oBAC7B,WAAW,UAAU,UAAU;oBAC/B,OAAO,UAAU,KAAK;gBACxB,CAAC;YACD,OAAO,OAAO,KAAK,IAAI,EAAE;QAC3B;IACF;IAEA;;GAEC,GACD,AAAQ,aAAa,GAAW,EAAO;QACrC,MAAM,QAAQ,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;QAC7B,IAAI,CAAC,OAAO,OAAO;QAEnB,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,EAAE,GAAG;QACjC,IAAI,KAAK,GAAG,KAAK,YAAY,KAAK;YAChC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;YAClB,OAAO;QACT;QAEA,OAAO;IACT;IAEQ,SAAS,GAAW,EAAE,IAAS,EAAE,MAAM,+HAAA,CAAA,0BAAuB,CAAC,SAAS,EAAQ;QACtF,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK;YAClB;YACA,WAAW,KAAK,GAAG;YACnB;QACF;QAEA,oCAAoC;QACpC,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,KAAK;YACzB,IAAI,CAAC,YAAY;QACnB;IACF;IAEQ,eAAqB;QAC3B,MAAM,MAAM,KAAK,GAAG;QACpB,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,GAAI;YAC/C,IAAI,MAAM,MAAM,SAAS,GAAG,MAAM,GAAG,EAAE;gBACrC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;YACpB;QACF;IACF;IAEA;;GAEC,GACD,AAAQ,YAAY,KAAU,EAAwB;QACpD,IAAI;QAEJ,IAAI,MAAM,OAAO,EAAE,SAAS,YAAY,MAAM,OAAO,EAAE,SAAS,qBAAqB;YACnF,gBAAgB;gBACd,MAAM,+HAAA,CAAA,oBAAiB,CAAC,cAAc;gBACtC,SAAS;gBACT,eAAe;YACjB;QACF,OAAO,IAAI,MAAM,OAAO,EAAE,SAAS,sBAAsB;YACvD,gBAAgB;gBACd,MAAM,+HAAA,CAAA,oBAAiB,CAAC,iBAAiB;gBACzC,SAAS;gBACT,eAAe;YACjB;QACF,OAAO,IAAI,MAAM,OAAO,EAAE,SAAS,oBAAoB;YACrD,gBAAgB;gBACd,MAAM,+HAAA,CAAA,oBAAiB,CAAC,eAAe;gBACvC,SAAS;gBACT,eAAe;YACjB;QACF,OAAO,IAAI,MAAM,IAAI,KAAK,eAAe,MAAM,OAAO,EAAE,SAAS,UAAU;YACzE,gBAAgB;gBACd,MAAM,+HAAA,CAAA,oBAAiB,CAAC,aAAa;gBACrC,SAAS;gBACT,eAAe;YACjB;QACF,OAAO;YACL,gBAAgB;gBACd,MAAM,+HAAA,CAAA,oBAAiB,CAAC,aAAa;gBACrC,SAAS,MAAM,OAAO,IAAI;gBAC1B,eAAe;YACjB;QACF;QAEA,OAAO;YAAE,SAAS;YAAO,OAAO;QAAc;IAChD;IAEA;;GAEC,GACD,aAAmB;QACjB,IAAI,CAAC,KAAK,CAAC,KAAK;IAClB;AACF", "debugId": null}}, {"offset": {"line": 1287, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/src/personal/bvr-safaris/src/lib/services/location/distance.ts"], "sourcesContent": ["/**\n * Distance calculation utilities for BVR Safaris geolocation\n * Provides accurate distance calculations and geographic utilities\n */\n\nimport { DistanceResult } from '@/lib/types/location'\n\nexport class DistanceService {\n  /**\n   * Calculate distance between two points using Haversine formula\n   * Returns distance in kilometers\n   */\n  static calculateDistance(\n    point1: { lat: number; lng: number },\n    point2: { lat: number; lng: number }\n  ): number {\n    const R = 6371 // Earth's radius in kilometers\n    const dLat = this.toRadians(point2.lat - point1.lat)\n    const dLng = this.toRadians(point2.lng - point1.lng)\n    \n    const a = \n      Math.sin(dLat / 2) * Math.sin(dLat / 2) +\n      Math.cos(this.toRadians(point1.lat)) * Math.cos(this.toRadians(point2.lat)) *\n      Math.sin(dLng / 2) * Math.sin(dLng / 2)\n    \n    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))\n    const distance = R * c\n    \n    return Math.round(distance * 100) / 100 // Round to 2 decimal places\n  }\n\n  /**\n   * Check if a point is within a specified radius of a center point\n   */\n  static isWithinRadius(\n    center: { lat: number; lng: number },\n    point: { lat: number; lng: number },\n    radiusKm: number\n  ): boolean {\n    const distance = this.calculateDistance(center, point)\n    return distance <= radiusKm\n  }\n\n  /**\n   * Calculate bounding box for efficient geographic queries\n   * Returns northeast and southwest corners of a box around a center point\n   */\n  static getBoundingBox(\n    center: { lat: number; lng: number },\n    radiusKm: number\n  ): {\n    northeast: { lat: number; lng: number }\n    southwest: { lat: number; lng: number }\n  } {\n    const R = 6371 // Earth's radius in kilometers\n    const lat = this.toRadians(center.lat)\n    const lng = this.toRadians(center.lng)\n    \n    // Angular distance in radians on a great circle\n    const angular = radiusKm / R\n\n    let minLat = lat - angular\n    let maxLat = lat + angular\n\n    let minLng: number\n    let maxLng: number\n\n    if (minLat > this.toRadians(-90) && maxLat < this.toRadians(90)) {\n      const deltaLng = Math.asin(Math.sin(angular) / Math.cos(lat))\n      minLng = lng - deltaLng\n      maxLng = lng + deltaLng\n\n      if (minLng < this.toRadians(-180)) minLng += 2 * Math.PI\n      if (maxLng > this.toRadians(180)) maxLng -= 2 * Math.PI\n    } else {\n      // A pole is within the distance\n      minLat = Math.max(minLat, this.toRadians(-90))\n      maxLat = Math.min(maxLat, this.toRadians(90))\n      minLng = this.toRadians(-180)\n      maxLng = this.toRadians(180)\n    }\n    \n    return {\n      southwest: {\n        lat: this.toDegrees(minLat),\n        lng: this.toDegrees(minLng)\n      },\n      northeast: {\n        lat: this.toDegrees(maxLat),\n        lng: this.toDegrees(maxLng)\n      }\n    }\n  }\n\n  /**\n   * Sort an array of points by distance from a center point\n   */\n  static sortByDistance<T extends { lat: number; lng: number }>(\n    points: T[],\n    center: { lat: number; lng: number }\n  ): (T & { distance: number })[] {\n    return points\n      .map(point => ({\n        ...point,\n        distance: this.calculateDistance(center, point)\n      }))\n      .sort((a, b) => a.distance - b.distance)\n  }\n\n  /**\n   * Filter points within a specified radius\n   */\n  static filterByRadius<T extends { lat: number; lng: number }>(\n    points: T[],\n    center: { lat: number; lng: number },\n    radiusKm: number\n  ): (T & { distance: number })[] {\n    return points\n      .map(point => ({\n        ...point,\n        distance: this.calculateDistance(center, point)\n      }))\n      .filter(point => point.distance <= radiusKm)\n      .sort((a, b) => a.distance - b.distance)\n  }\n\n  /**\n   * Get the center point (centroid) of multiple coordinates\n   */\n  static getCenterPoint(\n    points: { lat: number; lng: number }[]\n  ): { lat: number; lng: number } {\n    if (points.length === 0) {\n      throw new Error('Cannot calculate center of empty points array')\n    }\n\n    if (points.length === 1) {\n      return points[0]\n    }\n\n    let x = 0\n    let y = 0\n    let z = 0\n\n    for (const point of points) {\n      const lat = this.toRadians(point.lat)\n      const lng = this.toRadians(point.lng)\n\n      x += Math.cos(lat) * Math.cos(lng)\n      y += Math.cos(lat) * Math.sin(lng)\n      z += Math.sin(lat)\n    }\n\n    const total = points.length\n    x = x / total\n    y = y / total\n    z = z / total\n\n    const centralLng = Math.atan2(y, x)\n    const centralSquareRoot = Math.sqrt(x * x + y * y)\n    const centralLat = Math.atan2(z, centralSquareRoot)\n\n    return {\n      lat: this.toDegrees(centralLat),\n      lng: this.toDegrees(centralLng)\n    }\n  }\n\n  /**\n   * Calculate the bearing (direction) from one point to another\n   * Returns bearing in degrees (0-360)\n   */\n  static calculateBearing(\n    from: { lat: number; lng: number },\n    to: { lat: number; lng: number }\n  ): number {\n    const dLng = this.toRadians(to.lng - from.lng)\n    const lat1 = this.toRadians(from.lat)\n    const lat2 = this.toRadians(to.lat)\n\n    const y = Math.sin(dLng) * Math.cos(lat2)\n    const x = Math.cos(lat1) * Math.sin(lat2) - Math.sin(lat1) * Math.cos(lat2) * Math.cos(dLng)\n\n    let bearing = this.toDegrees(Math.atan2(y, x))\n    return (bearing + 360) % 360 // Normalize to 0-360\n  }\n\n  /**\n   * Format distance for display\n   */\n  static formatDistance(distanceKm: number): string {\n    if (distanceKm < 1) {\n      return `${Math.round(distanceKm * 1000)}m`\n    } else if (distanceKm < 10) {\n      return `${distanceKm.toFixed(1)}km`\n    } else {\n      return `${Math.round(distanceKm)}km`\n    }\n  }\n\n  /**\n   * Check if a point is within South Africa's approximate bounds\n   */\n  static isWithinSouthAfrica(point: { lat: number; lng: number }): boolean {\n    // Approximate bounds of South Africa\n    const bounds = {\n      north: -22.0,\n      south: -35.0,\n      east: 33.0,\n      west: 16.0\n    }\n\n    return (\n      point.lat >= bounds.south &&\n      point.lat <= bounds.north &&\n      point.lng >= bounds.west &&\n      point.lng <= bounds.east\n    )\n  }\n\n  /**\n   * Get approximate travel time based on distance (rough estimation)\n   * Assumes average speed of 80 km/h for highway travel\n   */\n  static estimateTravelTime(distanceKm: number): DistanceResult {\n    const averageSpeedKmh = 80\n    const durationMinutes = Math.round((distanceKm / averageSpeedKmh) * 60)\n    \n    return {\n      distance: distanceKm,\n      duration: durationMinutes\n    }\n  }\n\n  /**\n   * Format travel time for display\n   */\n  static formatTravelTime(minutes: number): string {\n    if (minutes < 60) {\n      return `${minutes} min`\n    } else {\n      const hours = Math.floor(minutes / 60)\n      const remainingMinutes = minutes % 60\n      if (remainingMinutes === 0) {\n        return `${hours}h`\n      } else {\n        return `${hours}h ${remainingMinutes}m`\n      }\n    }\n  }\n\n  /**\n   * Validate coordinates\n   */\n  static isValidCoordinate(point: { lat: number; lng: number }): boolean {\n    return (\n      typeof point.lat === 'number' &&\n      typeof point.lng === 'number' &&\n      point.lat >= -90 &&\n      point.lat <= 90 &&\n      point.lng >= -180 &&\n      point.lng <= 180 &&\n      !isNaN(point.lat) &&\n      !isNaN(point.lng)\n    )\n  }\n\n  /**\n   * Convert degrees to radians\n   */\n  private static toRadians(degrees: number): number {\n    return degrees * (Math.PI / 180)\n  }\n\n  /**\n   * Convert radians to degrees\n   */\n  private static toDegrees(radians: number): number {\n    return radians * (180 / Math.PI)\n  }\n}\n\n/**\n * Utility functions for working with geographic data\n */\nexport class GeoUtils {\n  /**\n   * Generate a simple geohash for efficient geographic indexing\n   * This is a simplified version - for production, consider using a proper geohash library\n   */\n  static generateSimpleGeoHash(\n    lat: number,\n    lng: number,\n    precision: number = 6\n  ): string {\n    const latRange = [-90, 90]\n    const lngRange = [-180, 180]\n    let hash = ''\n    let isEven = true\n    let bit = 0\n    let ch = 0\n\n    while (hash.length < precision) {\n      if (isEven) {\n        const mid = (lngRange[0] + lngRange[1]) / 2\n        if (lng >= mid) {\n          ch |= (1 << (4 - bit))\n          lngRange[0] = mid\n        } else {\n          lngRange[1] = mid\n        }\n      } else {\n        const mid = (latRange[0] + latRange[1]) / 2\n        if (lat >= mid) {\n          ch |= (1 << (4 - bit))\n          latRange[0] = mid\n        } else {\n          latRange[1] = mid\n        }\n      }\n\n      isEven = !isEven\n      bit++\n\n      if (bit === 5) {\n        hash += this.base32[ch]\n        bit = 0\n        ch = 0\n      }\n    }\n\n    return hash\n  }\n\n  private static base32 = '0123456789bcdefghjkmnpqrstuvwxyz'\n\n  /**\n   * Create a searchable location string from address components\n   */\n  static createSearchableLocation(addressComponents: any): string {\n    const components = []\n    \n    if (addressComponents.route) components.push(addressComponents.route)\n    if (addressComponents.locality) components.push(addressComponents.locality)\n    if (addressComponents.sublocality) components.push(addressComponents.sublocality)\n    if (addressComponents.administrativeAreaLevel2) components.push(addressComponents.administrativeAreaLevel2)\n    if (addressComponents.administrativeAreaLevel1) components.push(addressComponents.administrativeAreaLevel1)\n    if (addressComponents.postalCode) components.push(addressComponents.postalCode)\n    \n    return components.join(' ').toLowerCase()\n  }\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAIM,MAAM;IACX;;;GAGC,GACD,OAAO,kBACL,MAAoC,EACpC,MAAoC,EAC5B;QACR,MAAM,IAAI,KAAK,+BAA+B;;QAC9C,MAAM,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,GAAG,GAAG,OAAO,GAAG;QACnD,MAAM,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,GAAG,GAAG,OAAO,GAAG;QAEnD,MAAM,IACJ,KAAK,GAAG,CAAC,OAAO,KAAK,KAAK,GAAG,CAAC,OAAO,KACrC,KAAK,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,GAAG,KAAK,KAAK,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,GAAG,KACzE,KAAK,GAAG,CAAC,OAAO,KAAK,KAAK,GAAG,CAAC,OAAO;QAEvC,MAAM,IAAI,IAAI,KAAK,KAAK,CAAC,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI;QACrD,MAAM,WAAW,IAAI;QAErB,OAAO,KAAK,KAAK,CAAC,WAAW,OAAO,IAAI,4BAA4B;;IACtE;IAEA;;GAEC,GACD,OAAO,eACL,MAAoC,EACpC,KAAmC,EACnC,QAAgB,EACP;QACT,MAAM,WAAW,IAAI,CAAC,iBAAiB,CAAC,QAAQ;QAChD,OAAO,YAAY;IACrB;IAEA;;;GAGC,GACD,OAAO,eACL,MAAoC,EACpC,QAAgB,EAIhB;QACA,MAAM,IAAI,KAAK,+BAA+B;;QAC9C,MAAM,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,GAAG;QACrC,MAAM,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,GAAG;QAErC,gDAAgD;QAChD,MAAM,UAAU,WAAW;QAE3B,IAAI,SAAS,MAAM;QACnB,IAAI,SAAS,MAAM;QAEnB,IAAI;QACJ,IAAI;QAEJ,IAAI,SAAS,IAAI,CAAC,SAAS,CAAC,CAAC,OAAO,SAAS,IAAI,CAAC,SAAS,CAAC,KAAK;YAC/D,MAAM,WAAW,KAAK,IAAI,CAAC,KAAK,GAAG,CAAC,WAAW,KAAK,GAAG,CAAC;YACxD,SAAS,MAAM;YACf,SAAS,MAAM;YAEf,IAAI,SAAS,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,UAAU,IAAI,KAAK,EAAE;YACxD,IAAI,SAAS,IAAI,CAAC,SAAS,CAAC,MAAM,UAAU,IAAI,KAAK,EAAE;QACzD,OAAO;YACL,gCAAgC;YAChC,SAAS,KAAK,GAAG,CAAC,QAAQ,IAAI,CAAC,SAAS,CAAC,CAAC;YAC1C,SAAS,KAAK,GAAG,CAAC,QAAQ,IAAI,CAAC,SAAS,CAAC;YACzC,SAAS,IAAI,CAAC,SAAS,CAAC,CAAC;YACzB,SAAS,IAAI,CAAC,SAAS,CAAC;QAC1B;QAEA,OAAO;YACL,WAAW;gBACT,KAAK,IAAI,CAAC,SAAS,CAAC;gBACpB,KAAK,IAAI,CAAC,SAAS,CAAC;YACtB;YACA,WAAW;gBACT,KAAK,IAAI,CAAC,SAAS,CAAC;gBACpB,KAAK,IAAI,CAAC,SAAS,CAAC;YACtB;QACF;IACF;IAEA;;GAEC,GACD,OAAO,eACL,MAAW,EACX,MAAoC,EACN;QAC9B,OAAO,OACJ,GAAG,CAAC,CAAA,QAAS,CAAC;gBACb,GAAG,KAAK;gBACR,UAAU,IAAI,CAAC,iBAAiB,CAAC,QAAQ;YAC3C,CAAC,GACA,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,QAAQ,GAAG,EAAE,QAAQ;IAC3C;IAEA;;GAEC,GACD,OAAO,eACL,MAAW,EACX,MAAoC,EACpC,QAAgB,EACc;QAC9B,OAAO,OACJ,GAAG,CAAC,CAAA,QAAS,CAAC;gBACb,GAAG,KAAK;gBACR,UAAU,IAAI,CAAC,iBAAiB,CAAC,QAAQ;YAC3C,CAAC,GACA,MAAM,CAAC,CAAA,QAAS,MAAM,QAAQ,IAAI,UAClC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,QAAQ,GAAG,EAAE,QAAQ;IAC3C;IAEA;;GAEC,GACD,OAAO,eACL,MAAsC,EACR;QAC9B,IAAI,OAAO,MAAM,KAAK,GAAG;YACvB,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,OAAO,MAAM,KAAK,GAAG;YACvB,OAAO,MAAM,CAAC,EAAE;QAClB;QAEA,IAAI,IAAI;QACR,IAAI,IAAI;QACR,IAAI,IAAI;QAER,KAAK,MAAM,SAAS,OAAQ;YAC1B,MAAM,MAAM,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG;YACpC,MAAM,MAAM,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG;YAEpC,KAAK,KAAK,GAAG,CAAC,OAAO,KAAK,GAAG,CAAC;YAC9B,KAAK,KAAK,GAAG,CAAC,OAAO,KAAK,GAAG,CAAC;YAC9B,KAAK,KAAK,GAAG,CAAC;QAChB;QAEA,MAAM,QAAQ,OAAO,MAAM;QAC3B,IAAI,IAAI;QACR,IAAI,IAAI;QACR,IAAI,IAAI;QAER,MAAM,aAAa,KAAK,KAAK,CAAC,GAAG;QACjC,MAAM,oBAAoB,KAAK,IAAI,CAAC,IAAI,IAAI,IAAI;QAChD,MAAM,aAAa,KAAK,KAAK,CAAC,GAAG;QAEjC,OAAO;YACL,KAAK,IAAI,CAAC,SAAS,CAAC;YACpB,KAAK,IAAI,CAAC,SAAS,CAAC;QACtB;IACF;IAEA;;;GAGC,GACD,OAAO,iBACL,IAAkC,EAClC,EAAgC,EACxB;QACR,MAAM,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,GAAG,GAAG,KAAK,GAAG;QAC7C,MAAM,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,GAAG;QACpC,MAAM,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,GAAG;QAElC,MAAM,IAAI,KAAK,GAAG,CAAC,QAAQ,KAAK,GAAG,CAAC;QACpC,MAAM,IAAI,KAAK,GAAG,CAAC,QAAQ,KAAK,GAAG,CAAC,QAAQ,KAAK,GAAG,CAAC,QAAQ,KAAK,GAAG,CAAC,QAAQ,KAAK,GAAG,CAAC;QAEvF,IAAI,UAAU,IAAI,CAAC,SAAS,CAAC,KAAK,KAAK,CAAC,GAAG;QAC3C,OAAO,CAAC,UAAU,GAAG,IAAI,IAAI,qBAAqB;;IACpD;IAEA;;GAEC,GACD,OAAO,eAAe,UAAkB,EAAU;QAChD,IAAI,aAAa,GAAG;YAClB,OAAO,GAAG,KAAK,KAAK,CAAC,aAAa,MAAM,CAAC,CAAC;QAC5C,OAAO,IAAI,aAAa,IAAI;YAC1B,OAAO,GAAG,WAAW,OAAO,CAAC,GAAG,EAAE,CAAC;QACrC,OAAO;YACL,OAAO,GAAG,KAAK,KAAK,CAAC,YAAY,EAAE,CAAC;QACtC;IACF;IAEA;;GAEC,GACD,OAAO,oBAAoB,KAAmC,EAAW;QACvE,qCAAqC;QACrC,MAAM,SAAS;YACb,OAAO,CAAC;YACR,OAAO,CAAC;YACR,MAAM;YACN,MAAM;QACR;QAEA,OACE,MAAM,GAAG,IAAI,OAAO,KAAK,IACzB,MAAM,GAAG,IAAI,OAAO,KAAK,IACzB,MAAM,GAAG,IAAI,OAAO,IAAI,IACxB,MAAM,GAAG,IAAI,OAAO,IAAI;IAE5B;IAEA;;;GAGC,GACD,OAAO,mBAAmB,UAAkB,EAAkB;QAC5D,MAAM,kBAAkB;QACxB,MAAM,kBAAkB,KAAK,KAAK,CAAC,AAAC,aAAa,kBAAmB;QAEpE,OAAO;YACL,UAAU;YACV,UAAU;QACZ;IACF;IAEA;;GAEC,GACD,OAAO,iBAAiB,OAAe,EAAU;QAC/C,IAAI,UAAU,IAAI;YAChB,OAAO,GAAG,QAAQ,IAAI,CAAC;QACzB,OAAO;YACL,MAAM,QAAQ,KAAK,KAAK,CAAC,UAAU;YACnC,MAAM,mBAAmB,UAAU;YACnC,IAAI,qBAAqB,GAAG;gBAC1B,OAAO,GAAG,MAAM,CAAC,CAAC;YACpB,OAAO;gBACL,OAAO,GAAG,MAAM,EAAE,EAAE,iBAAiB,CAAC,CAAC;YACzC;QACF;IACF;IAEA;;GAEC,GACD,OAAO,kBAAkB,KAAmC,EAAW;QACrE,OACE,OAAO,MAAM,GAAG,KAAK,YACrB,OAAO,MAAM,GAAG,KAAK,YACrB,MAAM,GAAG,IAAI,CAAC,MACd,MAAM,GAAG,IAAI,MACb,MAAM,GAAG,IAAI,CAAC,OACd,MAAM,GAAG,IAAI,OACb,CAAC,MAAM,MAAM,GAAG,KAChB,CAAC,MAAM,MAAM,GAAG;IAEpB;IAEA;;GAEC,GACD,OAAe,UAAU,OAAe,EAAU;QAChD,OAAO,UAAU,CAAC,KAAK,EAAE,GAAG,GAAG;IACjC;IAEA;;GAEC,GACD,OAAe,UAAU,OAAe,EAAU;QAChD,OAAO,UAAU,CAAC,MAAM,KAAK,EAAE;IACjC;AACF;AAKO,MAAM;IACX;;;GAGC,GACD,OAAO,sBACL,GAAW,EACX,GAAW,EACX,YAAoB,CAAC,EACb;QACR,MAAM,WAAW;YAAC,CAAC;YAAI;SAAG;QAC1B,MAAM,WAAW;YAAC,CAAC;YAAK;SAAI;QAC5B,IAAI,OAAO;QACX,IAAI,SAAS;QACb,IAAI,MAAM;QACV,IAAI,KAAK;QAET,MAAO,KAAK,MAAM,GAAG,UAAW;YAC9B,IAAI,QAAQ;gBACV,MAAM,MAAM,CAAC,QAAQ,CAAC,EAAE,GAAG,QAAQ,CAAC,EAAE,IAAI;gBAC1C,IAAI,OAAO,KAAK;oBACd,MAAO,KAAM,IAAI;oBACjB,QAAQ,CAAC,EAAE,GAAG;gBAChB,OAAO;oBACL,QAAQ,CAAC,EAAE,GAAG;gBAChB;YACF,OAAO;gBACL,MAAM,MAAM,CAAC,QAAQ,CAAC,EAAE,GAAG,QAAQ,CAAC,EAAE,IAAI;gBAC1C,IAAI,OAAO,KAAK;oBACd,MAAO,KAAM,IAAI;oBACjB,QAAQ,CAAC,EAAE,GAAG;gBAChB,OAAO;oBACL,QAAQ,CAAC,EAAE,GAAG;gBAChB;YACF;YAEA,SAAS,CAAC;YACV;YAEA,IAAI,QAAQ,GAAG;gBACb,QAAQ,IAAI,CAAC,MAAM,CAAC,GAAG;gBACvB,MAAM;gBACN,KAAK;YACP;QACF;QAEA,OAAO;IACT;IAEA,OAAe,SAAS,mCAAkC;IAE1D;;GAEC,GACD,OAAO,yBAAyB,iBAAsB,EAAU;QAC9D,MAAM,aAAa,EAAE;QAErB,IAAI,kBAAkB,KAAK,EAAE,WAAW,IAAI,CAAC,kBAAkB,KAAK;QACpE,IAAI,kBAAkB,QAAQ,EAAE,WAAW,IAAI,CAAC,kBAAkB,QAAQ;QAC1E,IAAI,kBAAkB,WAAW,EAAE,WAAW,IAAI,CAAC,kBAAkB,WAAW;QAChF,IAAI,kBAAkB,wBAAwB,EAAE,WAAW,IAAI,CAAC,kBAAkB,wBAAwB;QAC1G,IAAI,kBAAkB,wBAAwB,EAAE,WAAW,IAAI,CAAC,kBAAkB,wBAAwB;QAC1G,IAAI,kBAAkB,UAAU,EAAE,WAAW,IAAI,CAAC,kBAAkB,UAAU;QAE9E,OAAO,WAAW,IAAI,CAAC,KAAK,WAAW;IACzC;AACF", "debugId": null}}, {"offset": {"line": 1543, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/src/personal/bvr-safaris/src/lib/services/location/index.ts"], "sourcesContent": ["/**\n * Location services entry point for BVR Safaris\n * Provides a unified interface for all location-related functionality\n */\n\nimport { GooglePlacesService } from './googlePlaces'\nimport { DistanceService, GeoUtils } from './distance'\nimport {\n  LocationSearchResult,\n  PlaceDetails,\n  GeocodeResult,\n  LocationData,\n  LocationError,\n  LocationErrorType,\n  ServiceResponse,\n  BrowserLocationResult,\n  BrowserLocationOptions,\n  DEFAULT_LOCATION_CONFIG,\n  PROVINCE_MAPPING\n} from '@/lib/types/location'\nimport { GeoPoint } from 'firebase/firestore'\nimport { SouthAfricanProvince } from '@/lib/constants'\n\n/**\n * Main location service class that orchestrates all location functionality\n */\nexport class LocationService {\n  private googlePlaces: GooglePlacesService\n  private static instance: LocationService\n\n  constructor(apiKey: string) {\n    this.googlePlaces = new GooglePlacesService(apiKey)\n  }\n\n  /**\n   * Get singleton instance\n   */\n  static getInstance(): LocationService {\n    if (!LocationService.instance) {\n      const apiKey = process.env.NEXT_PUBLIC_GOOGLE_PLACES_API_KEY\n      if (!apiKey) {\n        throw new Error('Google Places API key not configured')\n      }\n      LocationService.instance = new LocationService(apiKey)\n    }\n    return LocationService.instance\n  }\n\n  /**\n   * Get place predictions for autocomplete\n   */\n  async getPlacePredictions(\n    input: string,\n    options?: {\n      types?: string[]\n      sessionToken?: string\n    }\n  ): Promise<ServiceResponse<LocationSearchResult[]>> {\n    return this.googlePlaces.getPlacePredictions(input, {\n      ...options,\n      componentRestrictions: { country: DEFAULT_LOCATION_CONFIG.COUNTRY_RESTRICTION }\n    })\n  }\n\n  /**\n   * Get detailed place information and convert to LocationData\n   */\n  async getLocationData(placeId: string): Promise<ServiceResponse<LocationData>> {\n    const response = await this.googlePlaces.getPlaceDetails(placeId)\n    \n    if (!response.success || !response.data) {\n      return response as ServiceResponse<LocationData>\n    }\n\n    const placeDetails = response.data\n    const locationData = this.convertToLocationData(placeDetails)\n    \n    return { success: true, data: locationData, cached: response.cached }\n  }\n\n  /**\n   * Geocode an address and return LocationData\n   */\n  async geocodeToLocationData(address: string): Promise<ServiceResponse<LocationData>> {\n    const response = await this.googlePlaces.geocodeAddress(address)\n    \n    if (!response.success || !response.data) {\n      return response as ServiceResponse<LocationData>\n    }\n\n    const geocodeResult = response.data\n    \n    // Get place details if we have a place ID\n    if (geocodeResult.placeId) {\n      return this.getLocationData(geocodeResult.placeId)\n    }\n\n    // Create LocationData from geocode result\n    const locationData: LocationData = {\n      placeId: geocodeResult.placeId || '',\n      formattedAddress: geocodeResult.formattedAddress,\n      coordinates: new GeoPoint(geocodeResult.coordinates.lat, geocodeResult.coordinates.lng),\n      addressComponents: this.parseAddressComponents(geocodeResult.addressComponents),\n      placeTypes: geocodeResult.types\n    }\n\n    return { success: true, data: locationData, cached: response.cached }\n  }\n\n  /**\n   * Get user's current location using browser geolocation\n   */\n  async getCurrentLocation(\n    options?: BrowserLocationOptions\n  ): Promise<ServiceResponse<BrowserLocationResult>> {\n    return new Promise((resolve) => {\n      if (!navigator.geolocation) {\n        resolve({\n          success: false,\n          error: {\n            type: LocationErrorType.PERMISSION_DENIED,\n            message: 'Geolocation is not supported by this browser'\n          }\n        })\n        return\n      }\n\n      const defaultOptions: BrowserLocationOptions = {\n        enableHighAccuracy: true,\n        timeout: DEFAULT_LOCATION_CONFIG.GEOLOCATION_TIMEOUT,\n        maximumAge: DEFAULT_LOCATION_CONFIG.GEOLOCATION_MAX_AGE,\n        ...options\n      }\n\n      navigator.geolocation.getCurrentPosition(\n        (position) => {\n          const result: BrowserLocationResult = {\n            coordinates: {\n              lat: position.coords.latitude,\n              lng: position.coords.longitude\n            },\n            accuracy: position.coords.accuracy,\n            timestamp: position.timestamp\n          }\n          resolve({ success: true, data: result })\n        },\n        (error) => {\n          let errorType: LocationErrorType\n          let message: string\n\n          switch (error.code) {\n            case error.PERMISSION_DENIED:\n              errorType = LocationErrorType.PERMISSION_DENIED\n              message = 'Location access denied by user'\n              break\n            case error.POSITION_UNAVAILABLE:\n              errorType = LocationErrorType.API_ERROR\n              message = 'Location information unavailable'\n              break\n            case error.TIMEOUT:\n              errorType = LocationErrorType.NETWORK_ERROR\n              message = 'Location request timed out'\n              break\n            default:\n              errorType = LocationErrorType.UNKNOWN_ERROR\n              message = 'Unknown location error'\n          }\n\n          resolve({\n            success: false,\n            error: { type: errorType, message, originalError: error }\n          })\n        },\n        defaultOptions\n      )\n    })\n  }\n\n  /**\n   * Convert PlaceDetails to LocationData format\n   */\n  private convertToLocationData(placeDetails: PlaceDetails): LocationData {\n    return {\n      placeId: placeDetails.placeId,\n      formattedAddress: placeDetails.formattedAddress,\n      coordinates: new GeoPoint(placeDetails.coordinates.lat, placeDetails.coordinates.lng),\n      addressComponents: this.parseAddressComponents(placeDetails.addressComponents),\n      placeTypes: placeDetails.types,\n      viewport: placeDetails.viewport ? {\n        northeast: new GeoPoint(placeDetails.viewport.northeast.lat, placeDetails.viewport.northeast.lng),\n        southwest: new GeoPoint(placeDetails.viewport.southwest.lat, placeDetails.viewport.southwest.lng)\n      } : undefined,\n      name: placeDetails.name\n    }\n  }\n\n  /**\n   * Parse address components into structured format\n   */\n  private parseAddressComponents(components: any[]): LocationData['addressComponents'] {\n    const parsed: LocationData['addressComponents'] = {\n      country: 'South Africa',\n      administrativeAreaLevel1: ''\n    }\n\n    for (const component of components) {\n      const types = component.types\n\n      if (types.includes('street_number')) {\n        parsed.streetNumber = component.longName\n      } else if (types.includes('route')) {\n        parsed.route = component.longName\n      } else if (types.includes('locality')) {\n        parsed.locality = component.longName\n      } else if (types.includes('sublocality') || types.includes('sublocality_level_1')) {\n        parsed.sublocality = component.longName\n      } else if (types.includes('administrative_area_level_1')) {\n        parsed.administrativeAreaLevel1 = component.longName\n      } else if (types.includes('administrative_area_level_2')) {\n        parsed.administrativeAreaLevel2 = component.longName\n      } else if (types.includes('postal_code')) {\n        parsed.postalCode = component.longName\n      } else if (types.includes('country')) {\n        parsed.country = component.longName\n      }\n    }\n\n    return parsed\n  }\n\n  /**\n   * Map location data to South African province\n   */\n  static mapToSouthAfricanProvince(locationData: LocationData): SouthAfricanProvince | null {\n    const provinceName = locationData.addressComponents.administrativeAreaLevel1\n    \n    // Direct mapping\n    if (provinceName && PROVINCE_MAPPING[provinceName]) {\n      return PROVINCE_MAPPING[provinceName] as SouthAfricanProvince\n    }\n\n    // Fuzzy matching for common variations\n    const normalizedProvince = provinceName?.toLowerCase().trim()\n    \n    const provinceMap: Record<string, SouthAfricanProvince> = {\n      'eastern cape': 'Eastern Cape',\n      'free state': 'Free State',\n      'gauteng': 'Gauteng',\n      'kwazulu-natal': 'KwaZulu-Natal',\n      'kzn': 'KwaZulu-Natal',\n      'limpopo': 'Limpopo',\n      'mpumalanga': 'Mpumalanga',\n      'northern cape': 'Northern Cape',\n      'north west': 'North West',\n      'northwest': 'North West',\n      'western cape': 'Western Cape'\n    }\n\n    return normalizedProvince ? provinceMap[normalizedProvince] || null : null\n  }\n\n  /**\n   * Create searchable location string\n   */\n  static createSearchableLocation(locationData: LocationData): string {\n    return GeoUtils.createSearchableLocation(locationData.addressComponents)\n  }\n\n  /**\n   * Generate geohash for location\n   */\n  static generateGeoHash(coordinates: { lat: number; lng: number }): string {\n    return GeoUtils.generateSimpleGeoHash(coordinates.lat, coordinates.lng)\n  }\n\n  /**\n   * Clear all caches\n   */\n  clearCache(): void {\n    this.googlePlaces.clearCache()\n  }\n}\n\n// Export distance utilities\nexport { DistanceService, GeoUtils }\n\n// Export types\nexport type {\n  LocationSearchResult,\n  PlaceDetails,\n  LocationData,\n  GeocodeResult,\n  LocationError,\n  ServiceResponse,\n  BrowserLocationResult\n}\n\n// Export error types\nexport { LocationErrorType }\n\n// Create and export default instance\nlet defaultLocationService: LocationService | null = null\n\nexport function getLocationService(): LocationService {\n  if (!defaultLocationService) {\n    defaultLocationService = LocationService.getInstance()\n  }\n  return defaultLocationService\n}\n\n// Utility functions for common operations\nexport const locationUtils = {\n  /**\n   * Format coordinates for display\n   */\n  formatCoordinates(lat: number, lng: number): string {\n    return `${lat.toFixed(6)}, ${lng.toFixed(6)}`\n  },\n\n  /**\n   * Validate South African coordinates\n   */\n  isValidSouthAfricanLocation(lat: number, lng: number): boolean {\n    return DistanceService.isValidCoordinate({ lat, lng }) && \n           DistanceService.isWithinSouthAfrica({ lat, lng })\n  },\n\n  /**\n   * Get display name for location\n   */\n  getLocationDisplayName(locationData: LocationData): string {\n    const { addressComponents } = locationData\n    \n    if (addressComponents.locality && addressComponents.administrativeAreaLevel1) {\n      return `${addressComponents.locality}, ${addressComponents.administrativeAreaLevel1}`\n    } else if (addressComponents.administrativeAreaLevel2 && addressComponents.administrativeAreaLevel1) {\n      return `${addressComponents.administrativeAreaLevel2}, ${addressComponents.administrativeAreaLevel1}`\n    } else if (addressComponents.administrativeAreaLevel1) {\n      return addressComponents.administrativeAreaLevel1\n    } else {\n      return locationData.formattedAddress\n    }\n  },\n\n  /**\n   * Extract city/town from location data\n   */\n  getCityFromLocation(locationData: LocationData): string | null {\n    return locationData.addressComponents.locality || \n           locationData.addressComponents.sublocality || \n           locationData.addressComponents.administrativeAreaLevel2 || \n           null\n  }\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;AAED;AACA;AACA;AAaA;AAAA;;;;;AAMO,MAAM;IACH,aAAiC;IACzC,OAAe,SAAyB;IAExC,YAAY,MAAc,CAAE;QAC1B,IAAI,CAAC,YAAY,GAAG,IAAI,kJAAA,CAAA,sBAAmB,CAAC;IAC9C;IAEA;;GAEC,GACD,OAAO,cAA+B;QACpC,IAAI,CAAC,gBAAgB,QAAQ,EAAE;YAC7B,MAAM;YACN,uCAAa;;YAEb;YACA,gBAAgB,QAAQ,GAAG,IAAI,gBAAgB;QACjD;QACA,OAAO,gBAAgB,QAAQ;IACjC;IAEA;;GAEC,GACD,MAAM,oBACJ,KAAa,EACb,OAGC,EACiD;QAClD,OAAO,IAAI,CAAC,YAAY,CAAC,mBAAmB,CAAC,OAAO;YAClD,GAAG,OAAO;YACV,uBAAuB;gBAAE,SAAS,+HAAA,CAAA,0BAAuB,CAAC,mBAAmB;YAAC;QAChF;IACF;IAEA;;GAEC,GACD,MAAM,gBAAgB,OAAe,EAA0C;QAC7E,MAAM,WAAW,MAAM,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC;QAEzD,IAAI,CAAC,SAAS,OAAO,IAAI,CAAC,SAAS,IAAI,EAAE;YACvC,OAAO;QACT;QAEA,MAAM,eAAe,SAAS,IAAI;QAClC,MAAM,eAAe,IAAI,CAAC,qBAAqB,CAAC;QAEhD,OAAO;YAAE,SAAS;YAAM,MAAM;YAAc,QAAQ,SAAS,MAAM;QAAC;IACtE;IAEA;;GAEC,GACD,MAAM,sBAAsB,OAAe,EAA0C;QACnF,MAAM,WAAW,MAAM,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC;QAExD,IAAI,CAAC,SAAS,OAAO,IAAI,CAAC,SAAS,IAAI,EAAE;YACvC,OAAO;QACT;QAEA,MAAM,gBAAgB,SAAS,IAAI;QAEnC,0CAA0C;QAC1C,IAAI,cAAc,OAAO,EAAE;YACzB,OAAO,IAAI,CAAC,eAAe,CAAC,cAAc,OAAO;QACnD;QAEA,0CAA0C;QAC1C,MAAM,eAA6B;YACjC,SAAS,cAAc,OAAO,IAAI;YAClC,kBAAkB,cAAc,gBAAgB;YAChD,aAAa,IAAI,iKAAA,CAAA,WAAQ,CAAC,cAAc,WAAW,CAAC,GAAG,EAAE,cAAc,WAAW,CAAC,GAAG;YACtF,mBAAmB,IAAI,CAAC,sBAAsB,CAAC,cAAc,iBAAiB;YAC9E,YAAY,cAAc,KAAK;QACjC;QAEA,OAAO;YAAE,SAAS;YAAM,MAAM;YAAc,QAAQ,SAAS,MAAM;QAAC;IACtE;IAEA;;GAEC,GACD,MAAM,mBACJ,OAAgC,EACiB;QACjD,OAAO,IAAI,QAAQ,CAAC;YAClB,IAAI,CAAC,UAAU,WAAW,EAAE;gBAC1B,QAAQ;oBACN,SAAS;oBACT,OAAO;wBACL,MAAM,+HAAA,CAAA,oBAAiB,CAAC,iBAAiB;wBACzC,SAAS;oBACX;gBACF;gBACA;YACF;YAEA,MAAM,iBAAyC;gBAC7C,oBAAoB;gBACpB,SAAS,+HAAA,CAAA,0BAAuB,CAAC,mBAAmB;gBACpD,YAAY,+HAAA,CAAA,0BAAuB,CAAC,mBAAmB;gBACvD,GAAG,OAAO;YACZ;YAEA,UAAU,WAAW,CAAC,kBAAkB,CACtC,CAAC;gBACC,MAAM,SAAgC;oBACpC,aAAa;wBACX,KAAK,SAAS,MAAM,CAAC,QAAQ;wBAC7B,KAAK,SAAS,MAAM,CAAC,SAAS;oBAChC;oBACA,UAAU,SAAS,MAAM,CAAC,QAAQ;oBAClC,WAAW,SAAS,SAAS;gBAC/B;gBACA,QAAQ;oBAAE,SAAS;oBAAM,MAAM;gBAAO;YACxC,GACA,CAAC;gBACC,IAAI;gBACJ,IAAI;gBAEJ,OAAQ,MAAM,IAAI;oBAChB,KAAK,MAAM,iBAAiB;wBAC1B,YAAY,+HAAA,CAAA,oBAAiB,CAAC,iBAAiB;wBAC/C,UAAU;wBACV;oBACF,KAAK,MAAM,oBAAoB;wBAC7B,YAAY,+HAAA,CAAA,oBAAiB,CAAC,SAAS;wBACvC,UAAU;wBACV;oBACF,KAAK,MAAM,OAAO;wBAChB,YAAY,+HAAA,CAAA,oBAAiB,CAAC,aAAa;wBAC3C,UAAU;wBACV;oBACF;wBACE,YAAY,+HAAA,CAAA,oBAAiB,CAAC,aAAa;wBAC3C,UAAU;gBACd;gBAEA,QAAQ;oBACN,SAAS;oBACT,OAAO;wBAAE,MAAM;wBAAW;wBAAS,eAAe;oBAAM;gBAC1D;YACF,GACA;QAEJ;IACF;IAEA;;GAEC,GACD,AAAQ,sBAAsB,YAA0B,EAAgB;QACtE,OAAO;YACL,SAAS,aAAa,OAAO;YAC7B,kBAAkB,aAAa,gBAAgB;YAC/C,aAAa,IAAI,iKAAA,CAAA,WAAQ,CAAC,aAAa,WAAW,CAAC,GAAG,EAAE,aAAa,WAAW,CAAC,GAAG;YACpF,mBAAmB,IAAI,CAAC,sBAAsB,CAAC,aAAa,iBAAiB;YAC7E,YAAY,aAAa,KAAK;YAC9B,UAAU,aAAa,QAAQ,GAAG;gBAChC,WAAW,IAAI,iKAAA,CAAA,WAAQ,CAAC,aAAa,QAAQ,CAAC,SAAS,CAAC,GAAG,EAAE,aAAa,QAAQ,CAAC,SAAS,CAAC,GAAG;gBAChG,WAAW,IAAI,iKAAA,CAAA,WAAQ,CAAC,aAAa,QAAQ,CAAC,SAAS,CAAC,GAAG,EAAE,aAAa,QAAQ,CAAC,SAAS,CAAC,GAAG;YAClG,IAAI;YACJ,MAAM,aAAa,IAAI;QACzB;IACF;IAEA;;GAEC,GACD,AAAQ,uBAAuB,UAAiB,EAAqC;QACnF,MAAM,SAA4C;YAChD,SAAS;YACT,0BAA0B;QAC5B;QAEA,KAAK,MAAM,aAAa,WAAY;YAClC,MAAM,QAAQ,UAAU,KAAK;YAE7B,IAAI,MAAM,QAAQ,CAAC,kBAAkB;gBACnC,OAAO,YAAY,GAAG,UAAU,QAAQ;YAC1C,OAAO,IAAI,MAAM,QAAQ,CAAC,UAAU;gBAClC,OAAO,KAAK,GAAG,UAAU,QAAQ;YACnC,OAAO,IAAI,MAAM,QAAQ,CAAC,aAAa;gBACrC,OAAO,QAAQ,GAAG,UAAU,QAAQ;YACtC,OAAO,IAAI,MAAM,QAAQ,CAAC,kBAAkB,MAAM,QAAQ,CAAC,wBAAwB;gBACjF,OAAO,WAAW,GAAG,UAAU,QAAQ;YACzC,OAAO,IAAI,MAAM,QAAQ,CAAC,gCAAgC;gBACxD,OAAO,wBAAwB,GAAG,UAAU,QAAQ;YACtD,OAAO,IAAI,MAAM,QAAQ,CAAC,gCAAgC;gBACxD,OAAO,wBAAwB,GAAG,UAAU,QAAQ;YACtD,OAAO,IAAI,MAAM,QAAQ,CAAC,gBAAgB;gBACxC,OAAO,UAAU,GAAG,UAAU,QAAQ;YACxC,OAAO,IAAI,MAAM,QAAQ,CAAC,YAAY;gBACpC,OAAO,OAAO,GAAG,UAAU,QAAQ;YACrC;QACF;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,OAAO,0BAA0B,YAA0B,EAA+B;QACxF,MAAM,eAAe,aAAa,iBAAiB,CAAC,wBAAwB;QAE5E,iBAAiB;QACjB,IAAI,gBAAgB,+HAAA,CAAA,mBAAgB,CAAC,aAAa,EAAE;YAClD,OAAO,+HAAA,CAAA,mBAAgB,CAAC,aAAa;QACvC;QAEA,uCAAuC;QACvC,MAAM,qBAAqB,cAAc,cAAc;QAEvD,MAAM,cAAoD;YACxD,gBAAgB;YAChB,cAAc;YACd,WAAW;YACX,iBAAiB;YACjB,OAAO;YACP,WAAW;YACX,cAAc;YACd,iBAAiB;YACjB,cAAc;YACd,aAAa;YACb,gBAAgB;QAClB;QAEA,OAAO,qBAAqB,WAAW,CAAC,mBAAmB,IAAI,OAAO;IACxE;IAEA;;GAEC,GACD,OAAO,yBAAyB,YAA0B,EAAU;QAClE,OAAO,8IAAA,CAAA,WAAQ,CAAC,wBAAwB,CAAC,aAAa,iBAAiB;IACzE;IAEA;;GAEC,GACD,OAAO,gBAAgB,WAAyC,EAAU;QACxE,OAAO,8IAAA,CAAA,WAAQ,CAAC,qBAAqB,CAAC,YAAY,GAAG,EAAE,YAAY,GAAG;IACxE;IAEA;;GAEC,GACD,aAAmB;QACjB,IAAI,CAAC,YAAY,CAAC,UAAU;IAC9B;AACF;;;AAmBA,qCAAqC;AACrC,IAAI,yBAAiD;AAE9C,SAAS;IACd,IAAI,CAAC,wBAAwB;QAC3B,yBAAyB,gBAAgB,WAAW;IACtD;IACA,OAAO;AACT;AAGO,MAAM,gBAAgB;IAC3B;;GAEC,GACD,mBAAkB,GAAW,EAAE,GAAW;QACxC,OAAO,GAAG,IAAI,OAAO,CAAC,GAAG,EAAE,EAAE,IAAI,OAAO,CAAC,IAAI;IAC/C;IAEA;;GAEC,GACD,6BAA4B,GAAW,EAAE,GAAW;QAClD,OAAO,8IAAA,CAAA,kBAAe,CAAC,iBAAiB,CAAC;YAAE;YAAK;QAAI,MAC7C,8IAAA,CAAA,kBAAe,CAAC,mBAAmB,CAAC;YAAE;YAAK;QAAI;IACxD;IAEA;;GAEC,GACD,wBAAuB,YAA0B;QAC/C,MAAM,EAAE,iBAAiB,EAAE,GAAG;QAE9B,IAAI,kBAAkB,QAAQ,IAAI,kBAAkB,wBAAwB,EAAE;YAC5E,OAAO,GAAG,kBAAkB,QAAQ,CAAC,EAAE,EAAE,kBAAkB,wBAAwB,EAAE;QACvF,OAAO,IAAI,kBAAkB,wBAAwB,IAAI,kBAAkB,wBAAwB,EAAE;YACnG,OAAO,GAAG,kBAAkB,wBAAwB,CAAC,EAAE,EAAE,kBAAkB,wBAAwB,EAAE;QACvG,OAAO,IAAI,kBAAkB,wBAAwB,EAAE;YACrD,OAAO,kBAAkB,wBAAwB;QACnD,OAAO;YACL,OAAO,aAAa,gBAAgB;QACtC;IACF;IAEA;;GAEC,GACD,qBAAoB,YAA0B;QAC5C,OAAO,aAAa,iBAAiB,CAAC,QAAQ,IACvC,aAAa,iBAAiB,CAAC,WAAW,IAC1C,aAAa,iBAAiB,CAAC,wBAAwB,IACvD;IACT;AACF", "debugId": null}}, {"offset": {"line": 1844, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/src/personal/bvr-safaris/src/components/ui/LoadingSpinner.tsx"], "sourcesContent": ["import { cn } from '@/lib/utils'\n\ninterface LoadingSpinnerProps {\n  size?: 'sm' | 'md' | 'lg'\n  className?: string\n}\n\nexport function LoadingSpinner({ size = 'md', className }: LoadingSpinnerProps) {\n  const sizeClasses = {\n    sm: 'w-4 h-4',\n    md: 'w-6 h-6',\n    lg: 'w-12 h-12'\n  }\n\n  return (\n    <div className={cn('flex items-center justify-center', className)}>\n      <div\n        className={cn(\n          'animate-spin rounded-full border-2 border-white border-t-transparent',\n          sizeClasses[size]\n        )}\n      />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAOO,SAAS,eAAe,EAAE,OAAO,IAAI,EAAE,SAAS,EAAuB;IAC5E,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,oCAAoC;kBACrD,cAAA,8OAAC;YACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wEACA,WAAW,CAAC,KAAK;;;;;;;;;;;AAK3B", "debugId": null}}, {"offset": {"line": 1878, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/src/personal/bvr-safaris/src/components/ui/LocationAutocomplete.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState, useEffect, useRef, useCallback } from 'react'\nimport { cn } from '@/lib/utils'\nimport { \n  LocationSearchResult, \n  PlaceDetails, \n  LocationAutocompleteProps,\n  DEFAULT_LOCATION_CONFIG \n} from '@/lib/types/location'\nimport { getLocationService } from '@/lib/services/location'\nimport { LoadingSpinner } from './LoadingSpinner'\n\n/**\n * LocationAutocomplete component for Google Places integration\n * Provides address autocomplete with South African location focus\n */\nexport const LocationAutocomplete: React.FC<LocationAutocompleteProps> = ({\n  value = '',\n  onLocationSelect,\n  onInputChange,\n  placeholder = 'Enter location...',\n  countryRestriction = DEFAULT_LOCATION_CONFIG.COUNTRY_RESTRICTION,\n  types = [],\n  className,\n  disabled = false,\n  required = false,\n  error\n}) => {\n  const [inputValue, setInputValue] = useState(value)\n  const [suggestions, setSuggestions] = useState<LocationSearchResult[]>([])\n  const [isLoading, setIsLoading] = useState(false)\n  const [isOpen, setIsOpen] = useState(false)\n  const [selectedIndex, setSelectedIndex] = useState(-1)\n  const [sessionToken] = useState(() => crypto.randomUUID())\n\n  const inputRef = useRef<HTMLInputElement>(null)\n  const dropdownRef = useRef<HTMLDivElement>(null)\n  const debounceRef = useRef<NodeJS.Timeout>()\n\n  const locationService = getLocationService()\n\n  // Debounced search function\n  const debouncedSearch = useCallback(async (query: string) => {\n    if (query.length < 2) {\n      setSuggestions([])\n      setIsOpen(false)\n      return\n    }\n\n    setIsLoading(true)\n    \n    try {\n      const response = await locationService.getPlacePredictions(query, {\n        types: types.length > 0 ? types : undefined,\n        sessionToken\n      })\n\n      if (response.success && response.data) {\n        setSuggestions(response.data.slice(0, DEFAULT_LOCATION_CONFIG.MAX_AUTOCOMPLETE_RESULTS))\n        setIsOpen(true)\n      } else {\n        setSuggestions([])\n        setIsOpen(false)\n      }\n    } catch (error) {\n      console.error('Location search error:', error)\n      setSuggestions([])\n      setIsOpen(false)\n    } finally {\n      setIsLoading(false)\n    }\n  }, [locationService, types, sessionToken])\n\n  // Handle input change\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const newValue = e.target.value\n    setInputValue(newValue)\n    setSelectedIndex(-1)\n    \n    onInputChange?.(newValue)\n\n    // Clear existing debounce\n    if (debounceRef.current) {\n      clearTimeout(debounceRef.current)\n    }\n\n    // Set new debounce\n    debounceRef.current = setTimeout(() => {\n      debouncedSearch(newValue)\n    }, DEFAULT_LOCATION_CONFIG.DEBOUNCE_DELAY)\n  }\n\n  // Handle suggestion selection\n  const handleSuggestionSelect = async (suggestion: LocationSearchResult) => {\n    setInputValue(suggestion.description)\n    setIsOpen(false)\n    setSuggestions([])\n    setIsLoading(true)\n\n    try {\n      const response = await locationService.getLocationData(suggestion.placeId)\n      \n      if (response.success && response.data) {\n        onLocationSelect(response.data)\n      }\n    } catch (error) {\n      console.error('Error getting place details:', error)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  // Handle keyboard navigation\n  const handleKeyDown = (e: React.KeyboardEvent) => {\n    if (!isOpen || suggestions.length === 0) return\n\n    switch (e.key) {\n      case 'ArrowDown':\n        e.preventDefault()\n        setSelectedIndex(prev => \n          prev < suggestions.length - 1 ? prev + 1 : prev\n        )\n        break\n      case 'ArrowUp':\n        e.preventDefault()\n        setSelectedIndex(prev => prev > 0 ? prev - 1 : prev)\n        break\n      case 'Enter':\n        e.preventDefault()\n        if (selectedIndex >= 0 && selectedIndex < suggestions.length) {\n          handleSuggestionSelect(suggestions[selectedIndex])\n        }\n        break\n      case 'Escape':\n        setIsOpen(false)\n        setSelectedIndex(-1)\n        inputRef.current?.blur()\n        break\n    }\n  }\n\n  // Handle click outside\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (\n        dropdownRef.current &&\n        !dropdownRef.current.contains(event.target as Node) &&\n        !inputRef.current?.contains(event.target as Node)\n      ) {\n        setIsOpen(false)\n        setSelectedIndex(-1)\n      }\n    }\n\n    document.addEventListener('mousedown', handleClickOutside)\n    return () => document.removeEventListener('mousedown', handleClickOutside)\n  }, [])\n\n  // Update input value when prop changes\n  useEffect(() => {\n    setInputValue(value)\n  }, [value])\n\n  // Cleanup debounce on unmount\n  useEffect(() => {\n    return () => {\n      if (debounceRef.current) {\n        clearTimeout(debounceRef.current)\n      }\n    }\n  }, [])\n\n  return (\n    <div className={cn('relative w-full', className)}>\n      <div className=\"relative\">\n        <input\n          ref={inputRef}\n          type=\"text\"\n          value={inputValue}\n          onChange={handleInputChange}\n          onKeyDown={handleKeyDown}\n          onFocus={() => {\n            if (suggestions.length > 0) {\n              setIsOpen(true)\n            }\n          }}\n          placeholder={placeholder}\n          disabled={disabled}\n          required={required}\n          className={cn(\n            `\n            w-full px-4 py-2 pr-10 border-2 border-[var(--medium-gray)] \n            rounded-[var(--radius-md)] font-[var(--font-ui)] \n            transition-colors duration-300\n            focus:outline-none focus:border-[var(--primary-brown)] \n            focus:ring-0 focus:shadow-[0_0_0_3px_rgba(139,69,19,0.1)]\n            placeholder:text-[var(--medium-gray)]\n            disabled:bg-gray-100 disabled:cursor-not-allowed\n            `,\n            error && 'border-red-500 focus:border-red-500'\n          )}\n        />\n        \n        {/* Loading spinner */}\n        {isLoading && (\n          <div className=\"absolute right-3 top-1/2 transform -translate-y-1/2\">\n            <LoadingSpinner size=\"sm\" />\n          </div>\n        )}\n\n        {/* Location icon */}\n        {!isLoading && (\n          <div className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-[var(--medium-gray)]\">\n            <svg\n              width=\"16\"\n              height=\"16\"\n              viewBox=\"0 0 24 24\"\n              fill=\"none\"\n              stroke=\"currentColor\"\n              strokeWidth=\"2\"\n              strokeLinecap=\"round\"\n              strokeLinejoin=\"round\"\n            >\n              <path d=\"M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z\" />\n              <circle cx=\"12\" cy=\"10\" r=\"3\" />\n            </svg>\n          </div>\n        )}\n      </div>\n\n      {/* Dropdown */}\n      {isOpen && suggestions.length > 0 && (\n        <div\n          ref={dropdownRef}\n          className=\"absolute z-50 w-full mt-1 bg-white border border-[var(--medium-gray)] rounded-[var(--radius-md)] shadow-lg max-h-60 overflow-y-auto\"\n        >\n          {suggestions.map((suggestion, index) => (\n            <div\n              key={suggestion.placeId}\n              onClick={() => handleSuggestionSelect(suggestion)}\n              className={cn(\n                'px-4 py-3 cursor-pointer border-b border-gray-100 last:border-b-0 transition-colors',\n                'hover:bg-[var(--light-brown)] hover:bg-opacity-10',\n                selectedIndex === index && 'bg-[var(--light-brown)] bg-opacity-20'\n              )}\n            >\n              <div className=\"flex items-start space-x-3\">\n                <div className=\"flex-shrink-0 mt-1 text-[var(--medium-gray)]\">\n                  <svg\n                    width=\"14\"\n                    height=\"14\"\n                    viewBox=\"0 0 24 24\"\n                    fill=\"none\"\n                    stroke=\"currentColor\"\n                    strokeWidth=\"2\"\n                    strokeLinecap=\"round\"\n                    strokeLinejoin=\"round\"\n                  >\n                    <path d=\"M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z\" />\n                    <circle cx=\"12\" cy=\"10\" r=\"3\" />\n                  </svg>\n                </div>\n                <div className=\"flex-1 min-w-0\">\n                  <div className=\"font-medium text-[var(--primary-brown)] truncate\">\n                    {suggestion.mainText}\n                  </div>\n                  {suggestion.secondaryText && (\n                    <div className=\"text-sm text-[var(--medium-gray)] truncate\">\n                      {suggestion.secondaryText}\n                    </div>\n                  )}\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n      )}\n\n      {/* Error message */}\n      {error && (\n        <p className=\"mt-1 text-sm text-red-600\">{error}</p>\n      )}\n    </div>\n  )\n}\n\n/**\n * Simple location display component\n */\nexport const LocationDisplay: React.FC<{\n  locationData: any\n  showFullAddress?: boolean\n  className?: string\n}> = ({ locationData, showFullAddress = false, className }) => {\n  if (!locationData) return null\n\n  const displayText = showFullAddress\n    ? locationData.formattedAddress\n    : `${locationData.addressComponents?.locality || ''}, ${locationData.addressComponents?.administrativeAreaLevel1 || ''}`.replace(/^,\\s*/, '')\n\n  return (\n    <div className={cn('flex items-center space-x-2 text-[var(--medium-gray)]', className)}>\n      <svg\n        width=\"14\"\n        height=\"14\"\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke=\"currentColor\"\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n      >\n        <path d=\"M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z\" />\n        <circle cx=\"12\" cy=\"10\" r=\"3\" />\n      </svg>\n      <span className=\"text-sm\">{displayText}</span>\n    </div>\n  )\n}\n\n/**\n * Distance filter component for location-based searches\n */\nexport const DistanceFilter: React.FC<{\n  value?: number\n  onChange: (radius: number) => void\n  options?: number[]\n  className?: string\n  label?: string\n}> = ({\n  value,\n  onChange,\n  options = DEFAULT_LOCATION_CONFIG.DEFAULT_RADIUS_OPTIONS,\n  className,\n  label = 'Distance'\n}) => {\n  return (\n    <div className={cn('w-full', className)}>\n      {label && (\n        <label className=\"block mb-2 font-semibold text-[var(--primary-brown)] font-[var(--font-ui)]\">\n          {label}\n        </label>\n      )}\n      <select\n        value={value || ''}\n        onChange={(e) => onChange(Number(e.target.value))}\n        className={cn(\n          `\n          w-full px-4 py-2 border-2 border-[var(--medium-gray)]\n          rounded-[var(--radius-md)] font-[var(--font-ui)]\n          transition-colors duration-300\n          focus:outline-none focus:border-[var(--primary-brown)]\n          focus:ring-0 focus:shadow-[0_0_0_3px_rgba(139,69,19,0.1)]\n          bg-white\n          `\n        )}\n      >\n        <option value=\"\">Any distance</option>\n        {options.map(distance => (\n          <option key={distance} value={distance}>\n            Within {distance}km\n          </option>\n        ))}\n      </select>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AACA;AAMA;AAAA;AACA;AAXA;;;;;;;AAiBO,MAAM,uBAA4D,CAAC,EACxE,QAAQ,EAAE,EACV,gBAAgB,EAChB,aAAa,EACb,cAAc,mBAAmB,EACjC,qBAAqB,+HAAA,CAAA,0BAAuB,CAAC,mBAAmB,EAChE,QAAQ,EAAE,EACV,SAAS,EACT,WAAW,KAAK,EAChB,WAAW,KAAK,EAChB,KAAK,EACN;IACC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B,EAAE;IACzE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IACpD,MAAM,CAAC,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,IAAM,OAAO,UAAU;IAEvD,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAC1C,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC3C,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD;IAEzB,MAAM,kBAAkB,CAAA,GAAA,2JAAA,CAAA,qBAAkB,AAAD;IAEzC,4BAA4B;IAC5B,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACzC,IAAI,MAAM,MAAM,GAAG,GAAG;YACpB,eAAe,EAAE;YACjB,UAAU;YACV;QACF;QAEA,aAAa;QAEb,IAAI;YACF,MAAM,WAAW,MAAM,gBAAgB,mBAAmB,CAAC,OAAO;gBAChE,OAAO,MAAM,MAAM,GAAG,IAAI,QAAQ;gBAClC;YACF;YAEA,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;gBACrC,eAAe,SAAS,IAAI,CAAC,KAAK,CAAC,GAAG,+HAAA,CAAA,0BAAuB,CAAC,wBAAwB;gBACtF,UAAU;YACZ,OAAO;gBACL,eAAe,EAAE;gBACjB,UAAU;YACZ;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,eAAe,EAAE;YACjB,UAAU;QACZ,SAAU;YACR,aAAa;QACf;IACF,GAAG;QAAC;QAAiB;QAAO;KAAa;IAEzC,sBAAsB;IACtB,MAAM,oBAAoB,CAAC;QACzB,MAAM,WAAW,EAAE,MAAM,CAAC,KAAK;QAC/B,cAAc;QACd,iBAAiB,CAAC;QAElB,gBAAgB;QAEhB,0BAA0B;QAC1B,IAAI,YAAY,OAAO,EAAE;YACvB,aAAa,YAAY,OAAO;QAClC;QAEA,mBAAmB;QACnB,YAAY,OAAO,GAAG,WAAW;YAC/B,gBAAgB;QAClB,GAAG,+HAAA,CAAA,0BAAuB,CAAC,cAAc;IAC3C;IAEA,8BAA8B;IAC9B,MAAM,yBAAyB,OAAO;QACpC,cAAc,WAAW,WAAW;QACpC,UAAU;QACV,eAAe,EAAE;QACjB,aAAa;QAEb,IAAI;YACF,MAAM,WAAW,MAAM,gBAAgB,eAAe,CAAC,WAAW,OAAO;YAEzE,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;gBACrC,iBAAiB,SAAS,IAAI;YAChC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD,SAAU;YACR,aAAa;QACf;IACF;IAEA,6BAA6B;IAC7B,MAAM,gBAAgB,CAAC;QACrB,IAAI,CAAC,UAAU,YAAY,MAAM,KAAK,GAAG;QAEzC,OAAQ,EAAE,GAAG;YACX,KAAK;gBACH,EAAE,cAAc;gBAChB,iBAAiB,CAAA,OACf,OAAO,YAAY,MAAM,GAAG,IAAI,OAAO,IAAI;gBAE7C;YACF,KAAK;gBACH,EAAE,cAAc;gBAChB,iBAAiB,CAAA,OAAQ,OAAO,IAAI,OAAO,IAAI;gBAC/C;YACF,KAAK;gBACH,EAAE,cAAc;gBAChB,IAAI,iBAAiB,KAAK,gBAAgB,YAAY,MAAM,EAAE;oBAC5D,uBAAuB,WAAW,CAAC,cAAc;gBACnD;gBACA;YACF,KAAK;gBACH,UAAU;gBACV,iBAAiB,CAAC;gBAClB,SAAS,OAAO,EAAE;gBAClB;QACJ;IACF;IAEA,uBAAuB;IACvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB,CAAC;YAC1B,IACE,YAAY,OAAO,IACnB,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,KAC1C,CAAC,SAAS,OAAO,EAAE,SAAS,MAAM,MAAM,GACxC;gBACA,UAAU;gBACV,iBAAiB,CAAC;YACpB;QACF;QAEA,SAAS,gBAAgB,CAAC,aAAa;QACvC,OAAO,IAAM,SAAS,mBAAmB,CAAC,aAAa;IACzD,GAAG,EAAE;IAEL,uCAAuC;IACvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,cAAc;IAChB,GAAG;QAAC;KAAM;IAEV,8BAA8B;IAC9B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,OAAO;YACL,IAAI,YAAY,OAAO,EAAE;gBACvB,aAAa,YAAY,OAAO;YAClC;QACF;IACF,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;;0BACpC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,KAAK;wBACL,MAAK;wBACL,OAAO;wBACP,UAAU;wBACV,WAAW;wBACX,SAAS;4BACP,IAAI,YAAY,MAAM,GAAG,GAAG;gCAC1B,UAAU;4BACZ;wBACF;wBACA,aAAa;wBACb,UAAU;wBACV,UAAU;wBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,CAAC;;;;;;;;YAQD,CAAC,EACD,SAAS;;;;;;oBAKZ,2BACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,0IAAA,CAAA,iBAAc;4BAAC,MAAK;;;;;;;;;;;oBAKxB,CAAC,2BACA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,OAAM;4BACN,QAAO;4BACP,SAAQ;4BACR,MAAK;4BACL,QAAO;4BACP,aAAY;4BACZ,eAAc;4BACd,gBAAe;;8CAEf,8OAAC;oCAAK,GAAE;;;;;;8CACR,8OAAC;oCAAO,IAAG;oCAAK,IAAG;oCAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;YAOjC,UAAU,YAAY,MAAM,GAAG,mBAC9B,8OAAC;gBACC,KAAK;gBACL,WAAU;0BAET,YAAY,GAAG,CAAC,CAAC,YAAY,sBAC5B,8OAAC;wBAEC,SAAS,IAAM,uBAAuB;wBACtC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uFACA,qDACA,kBAAkB,SAAS;kCAG7B,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,OAAM;wCACN,QAAO;wCACP,SAAQ;wCACR,MAAK;wCACL,QAAO;wCACP,aAAY;wCACZ,eAAc;wCACd,gBAAe;;0DAEf,8OAAC;gDAAK,GAAE;;;;;;0DACR,8OAAC;gDAAO,IAAG;gDAAK,IAAG;gDAAK,GAAE;;;;;;;;;;;;;;;;;8CAG9B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACZ,WAAW,QAAQ;;;;;;wCAErB,WAAW,aAAa,kBACvB,8OAAC;4CAAI,WAAU;sDACZ,WAAW,aAAa;;;;;;;;;;;;;;;;;;uBA9B5B,WAAW,OAAO;;;;;;;;;;YAyC9B,uBACC,8OAAC;gBAAE,WAAU;0BAA6B;;;;;;;;;;;;AAIlD;AAKO,MAAM,kBAIR,CAAC,EAAE,YAAY,EAAE,kBAAkB,KAAK,EAAE,SAAS,EAAE;IACxD,IAAI,CAAC,cAAc,OAAO;IAE1B,MAAM,cAAc,kBAChB,aAAa,gBAAgB,GAC7B,GAAG,aAAa,iBAAiB,EAAE,YAAY,GAAG,EAAE,EAAE,aAAa,iBAAiB,EAAE,4BAA4B,IAAI,CAAC,OAAO,CAAC,SAAS;IAE5I,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,yDAAyD;;0BAC1E,8OAAC;gBACC,OAAM;gBACN,QAAO;gBACP,SAAQ;gBACR,MAAK;gBACL,QAAO;gBACP,aAAY;gBACZ,eAAc;gBACd,gBAAe;;kCAEf,8OAAC;wBAAK,GAAE;;;;;;kCACR,8OAAC;wBAAO,IAAG;wBAAK,IAAG;wBAAK,GAAE;;;;;;;;;;;;0BAE5B,8OAAC;gBAAK,WAAU;0BAAW;;;;;;;;;;;;AAGjC;AAKO,MAAM,iBAMR,CAAC,EACJ,KAAK,EACL,QAAQ,EACR,UAAU,+HAAA,CAAA,0BAAuB,CAAC,sBAAsB,EACxD,SAAS,EACT,QAAQ,UAAU,EACnB;IACC,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,UAAU;;YAC1B,uBACC,8OAAC;gBAAM,WAAU;0BACd;;;;;;0BAGL,8OAAC;gBACC,OAAO,SAAS;gBAChB,UAAU,CAAC,IAAM,SAAS,OAAO,EAAE,MAAM,CAAC,KAAK;gBAC/C,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,CAAC;;;;;;;UAOD,CAAC;;kCAGH,8OAAC;wBAAO,OAAM;kCAAG;;;;;;oBAChB,QAAQ,GAAG,CAAC,CAAA,yBACX,8OAAC;4BAAsB,OAAO;;gCAAU;gCAC9B;gCAAS;;2BADN;;;;;;;;;;;;;;;;;AAOvB", "debugId": null}}, {"offset": {"line": 2335, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/src/personal/bvr-safaris/src/lib/firebase/firestore.ts"], "sourcesContent": ["import {\n  collection,\n  doc,\n  getDoc,\n  getDocs,\n  addDoc,\n  setDoc,\n  updateDoc,\n  deleteDoc,\n  query,\n  where,\n  orderBy,\n  limit,\n  Timestamp,\n  DocumentSnapshot,\n  QueryDocumentSnapshot,\n  DocumentData,\n  Query\n} from 'firebase/firestore'\nimport { db } from './client'\nimport {\n  UserProfile,\n  GameFarm,\n  Booking,\n  Review,\n  FarmImage,\n  GameSpecies,\n  FarmAmenity,\n  FirestoreDocument\n} from '@/lib/types/firestore'\n\n// Helper function to convert Firestore document to typed object\nexport function docToData<T extends FirestoreDocument>(\n  doc: QueryDocumentSnapshot<DocumentData> | DocumentSnapshot<DocumentData>\n): T | null {\n  if (!doc.exists()) return null\n  \n  const data = doc.data()\n  return {\n    id: doc.id,\n    ...data,\n    // Convert Firestore Timestamps to Date objects for easier handling\n    createdAt: data?.createdAt?.toDate?.() || data?.createdAt,\n    updatedAt: data?.updatedAt?.toDate?.() || data?.updatedAt,\n  } as T\n}\n\n\n\n// User Profile operations\nexport const userProfileService = {\n  async get(userId: string): Promise<UserProfile | null> {\n    const docRef = doc(db, 'users', userId)\n    const docSnap = await getDoc(docRef)\n    return docToData<UserProfile>(docSnap)\n  },\n\n  async create(userId: string, data: Omit<UserProfile, 'id' | 'createdAt' | 'updatedAt'>): Promise<void> {\n    const docRef = doc(db, 'users', userId)\n    const now = new Date()\n    await setDoc(docRef, {\n      ...data,\n      createdAt: Timestamp.fromDate(now),\n      updatedAt: Timestamp.fromDate(now)\n    })\n  },\n\n  async update(userId: string, data: Partial<UserProfile>): Promise<void> {\n    const docRef = doc(db, 'users', userId)\n    await updateDoc(docRef, {\n      ...data,\n      updatedAt: Timestamp.now()\n    })\n  }\n}\n\n// Game Farm operations\nexport const farmService = {\n  async getAll(filters?: {\n    isActive?: boolean\n    featured?: boolean\n    province?: string\n    activityType?: string\n    limit?: number\n  }): Promise<GameFarm[]> {\n    let q: Query<DocumentData> = collection(db, 'farms')\n\n    if (filters?.isActive !== undefined) {\n      q = query(q, where('isActive', '==', filters.isActive))\n    }\n    if (filters?.featured !== undefined) {\n      q = query(q, where('featured', '==', filters.featured))\n    }\n    if (filters?.province) {\n      q = query(q, where('province', '==', filters.province))\n    }\n    if (filters?.activityType) {\n      q = query(q, where('activityTypes', '==', filters.activityType))\n    }\n\n    q = query(q, orderBy('createdAt', 'desc'))\n\n    if (filters?.limit) {\n      q = query(q, limit(filters.limit))\n    }\n\n    const querySnapshot = await getDocs(q)\n    return querySnapshot.docs.map(doc => docToData<GameFarm>(doc)!).filter(Boolean)\n  },\n\n  async get(farmId: string): Promise<GameFarm | null> {\n    const docRef = doc(db, 'farms', farmId)\n    const docSnap = await getDoc(docRef)\n    return docToData<GameFarm>(docSnap)\n  },\n\n  async getByOwner(ownerId: string): Promise<GameFarm[]> {\n    const q = query(\n      collection(db, 'farms'),\n      where('ownerId', '==', ownerId),\n      orderBy('createdAt', 'desc')\n    )\n    const querySnapshot = await getDocs(q)\n    return querySnapshot.docs.map(doc => docToData<GameFarm>(doc)!).filter(Boolean)\n  },\n\n  async getActive(limitCount?: number): Promise<GameFarm[]> {\n    let q = query(\n      collection(db, 'farms'),\n      where('isActive', '==', true),\n      orderBy('createdAt', 'desc')\n    )\n\n    if (limitCount) {\n      q = query(q, limit(limitCount))\n    }\n\n    const querySnapshot = await getDocs(q)\n    return querySnapshot.docs.map(doc => docToData<GameFarm>(doc)!).filter(Boolean)\n  },\n\n  async create(data: Omit<GameFarm, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {\n    const now = new Date()\n    const docRef = await addDoc(collection(db, 'farms'), {\n      ...data,\n      createdAt: Timestamp.fromDate(now),\n      updatedAt: Timestamp.fromDate(now)\n    })\n    return docRef.id\n  },\n\n  async update(farmId: string, data: Partial<GameFarm>): Promise<void> {\n    const docRef = doc(db, 'farms', farmId)\n    await updateDoc(docRef, {\n      ...data,\n      updatedAt: Timestamp.now()\n    })\n  },\n\n  async delete(farmId: string): Promise<void> {\n    const docRef = doc(db, 'farms', farmId)\n    await deleteDoc(docRef)\n  },\n\n  // Add farm images to subcollection\n  async addImage(farmId: string, imageData: Omit<FarmImage, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {\n    const now = new Date()\n    const docRef = await addDoc(collection(db, 'farms', farmId, 'images'), {\n      ...imageData,\n      createdAt: Timestamp.fromDate(now),\n      updatedAt: Timestamp.fromDate(now)\n    })\n    return docRef.id\n  },\n\n  // Get farm images\n  async getImages(farmId: string): Promise<FarmImage[]> {\n    const q = query(\n      collection(db, 'farms', farmId, 'images'),\n      orderBy('displayOrder', 'asc'),\n      orderBy('createdAt', 'asc')\n    )\n    const querySnapshot = await getDocs(q)\n    return querySnapshot.docs.map(doc => docToData<FarmImage>(doc)!).filter(Boolean)\n  },\n\n  // Delete farm image\n  async deleteImage(farmId: string, imageId: string): Promise<void> {\n    const docRef = doc(db, 'farms', farmId, 'images', imageId)\n    await deleteDoc(docRef)\n  }\n}\n\n// Booking operations\nexport const bookingService = {\n  async getAll(filters?: {\n    hunterId?: string\n    farmId?: string\n    status?: string\n    limit?: number\n  }): Promise<Booking[]> {\n    let q: Query<DocumentData> = collection(db, 'bookings')\n\n    if (filters?.hunterId) {\n      q = query(q, where('hunterId', '==', filters.hunterId))\n    }\n    if (filters?.farmId) {\n      q = query(q, where('farmId', '==', filters.farmId))\n    }\n    if (filters?.status) {\n      q = query(q, where('status', '==', filters.status))\n    }\n\n    q = query(q, orderBy('createdAt', 'desc'))\n\n    if (filters?.limit) {\n      q = query(q, limit(filters.limit))\n    }\n\n    const querySnapshot = await getDocs(q)\n    return querySnapshot.docs.map(doc => docToData<Booking>(doc)!).filter(Boolean)\n  },\n\n  async get(bookingId: string): Promise<Booking | null> {\n    const docRef = doc(db, 'bookings', bookingId)\n    const docSnap = await getDoc(docRef)\n    return docToData<Booking>(docSnap)\n  },\n\n  async create(data: Omit<Booking, 'id' | 'createdAt' | 'updatedAt' | 'bookingReference'>): Promise<string> {\n    const now = new Date()\n    const bookingReference = `BVR-${now.getFullYear()}${(now.getMonth() + 1).toString().padStart(2, '0')}${now.getDate().toString().padStart(2, '0')}-${Math.random().toString(36).substring(2, 8).toUpperCase()}`\n    \n    const docRef = await addDoc(collection(db, 'bookings'), {\n      ...data,\n      bookingReference,\n      createdAt: Timestamp.fromDate(now),\n      updatedAt: Timestamp.fromDate(now)\n    })\n    return docRef.id\n  },\n\n  async update(bookingId: string, data: Partial<Booking>): Promise<void> {\n    const docRef = doc(db, 'bookings', bookingId)\n    await updateDoc(docRef, {\n      ...data,\n      updatedAt: Timestamp.now()\n    })\n  }\n}\n\n// Review operations (subcollection under farms)\nexport const reviewService = {\n  async getByFarm(farmId: string): Promise<Review[]> {\n    const q = query(\n      collection(db, 'farms', farmId, 'reviews'),\n      where('isPublic', '==', true),\n      orderBy('createdAt', 'desc')\n    )\n    const querySnapshot = await getDocs(q)\n    return querySnapshot.docs.map(doc => docToData<Review>(doc)!).filter(Boolean)\n  },\n\n  async create(farmId: string, data: Omit<Review, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {\n    const now = new Date()\n    const docRef = await addDoc(collection(db, 'farms', farmId, 'reviews'), {\n      ...data,\n      createdAt: Timestamp.fromDate(now),\n      updatedAt: Timestamp.fromDate(now)\n    })\n    return docRef.id\n  },\n\n  async update(farmId: string, reviewId: string, data: Partial<Review>): Promise<void> {\n    const docRef = doc(db, 'farms', farmId, 'reviews', reviewId)\n    await updateDoc(docRef, {\n      ...data,\n      updatedAt: Timestamp.now()\n    })\n  }\n}\n\n// Species operations\nexport const speciesService = {\n  async getAll(): Promise<GameSpecies[]> {\n    const q = query(collection(db, 'species'), orderBy('name'))\n    const querySnapshot = await getDocs(q)\n    return querySnapshot.docs.map(doc => docToData<GameSpecies>(doc)!).filter(Boolean)\n  },\n\n  async get(speciesId: string): Promise<GameSpecies | null> {\n    const docRef = doc(db, 'species', speciesId)\n    const docSnap = await getDoc(docRef)\n    return docToData<GameSpecies>(docSnap)\n  }\n}\n\n// Amenities operations\nexport const amenityService = {\n  async getAll(): Promise<FarmAmenity[]> {\n    const q = query(collection(db, 'amenities'), orderBy('name'))\n    const querySnapshot = await getDocs(q)\n    return querySnapshot.docs.map(doc => docToData<FarmAmenity>(doc)!).filter(Boolean)\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAAA;AAmBA;AAAA;;;AAaO,SAAS,UACd,GAAyE;IAEzE,IAAI,CAAC,IAAI,MAAM,IAAI,OAAO;IAE1B,MAAM,OAAO,IAAI,IAAI;IACrB,OAAO;QACL,IAAI,IAAI,EAAE;QACV,GAAG,IAAI;QACP,mEAAmE;QACnE,WAAW,MAAM,WAAW,cAAc,MAAM;QAChD,WAAW,MAAM,WAAW,cAAc,MAAM;IAClD;AACF;AAKO,MAAM,qBAAqB;IAChC,MAAM,KAAI,MAAc;QACtB,MAAM,SAAS,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,SAAS;QAChC,MAAM,UAAU,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE;QAC7B,OAAO,UAAuB;IAChC;IAEA,MAAM,QAAO,MAAc,EAAE,IAAyD;QACpF,MAAM,SAAS,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,SAAS;QAChC,MAAM,MAAM,IAAI;QAChB,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE,QAAQ;YACnB,GAAG,IAAI;YACP,WAAW,iKAAA,CAAA,YAAS,CAAC,QAAQ,CAAC;YAC9B,WAAW,iKAAA,CAAA,YAAS,CAAC,QAAQ,CAAC;QAChC;IACF;IAEA,MAAM,QAAO,MAAc,EAAE,IAA0B;QACrD,MAAM,SAAS,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,SAAS;QAChC,MAAM,CAAA,GAAA,iKAAA,CAAA,YAAS,AAAD,EAAE,QAAQ;YACtB,GAAG,IAAI;YACP,WAAW,iKAAA,CAAA,YAAS,CAAC,GAAG;QAC1B;IACF;AACF;AAGO,MAAM,cAAc;IACzB,MAAM,QAAO,OAMZ;QACC,IAAI,IAAyB,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE;QAE5C,IAAI,SAAS,aAAa,WAAW;YACnC,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,YAAY,MAAM,QAAQ,QAAQ;QACvD;QACA,IAAI,SAAS,aAAa,WAAW;YACnC,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,YAAY,MAAM,QAAQ,QAAQ;QACvD;QACA,IAAI,SAAS,UAAU;YACrB,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,YAAY,MAAM,QAAQ,QAAQ;QACvD;QACA,IAAI,SAAS,cAAc;YACzB,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,iBAAiB,MAAM,QAAQ,YAAY;QAChE;QAEA,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;QAElC,IAAI,SAAS,OAAO;YAClB,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,QAAQ,KAAK;QAClC;QAEA,MAAM,gBAAgB,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;QACpC,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,UAAoB,MAAO,MAAM,CAAC;IACzE;IAEA,MAAM,KAAI,MAAc;QACtB,MAAM,SAAS,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,SAAS;QAChC,MAAM,UAAU,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE;QAC7B,OAAO,UAAoB;IAC7B;IAEA,MAAM,YAAW,OAAe;QAC9B,MAAM,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EACZ,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,UACf,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,WAAW,MAAM,UACvB,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;QAEvB,MAAM,gBAAgB,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;QACpC,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,UAAoB,MAAO,MAAM,CAAC;IACzE;IAEA,MAAM,WAAU,UAAmB;QACjC,IAAI,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EACV,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,UACf,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,YAAY,MAAM,OACxB,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;QAGvB,IAAI,YAAY;YACd,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE;QACrB;QAEA,MAAM,gBAAgB,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;QACpC,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,UAAoB,MAAO,MAAM,CAAC;IACzE;IAEA,MAAM,QAAO,IAAsD;QACjE,MAAM,MAAM,IAAI;QAChB,MAAM,SAAS,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,UAAU;YACnD,GAAG,IAAI;YACP,WAAW,iKAAA,CAAA,YAAS,CAAC,QAAQ,CAAC;YAC9B,WAAW,iKAAA,CAAA,YAAS,CAAC,QAAQ,CAAC;QAChC;QACA,OAAO,OAAO,EAAE;IAClB;IAEA,MAAM,QAAO,MAAc,EAAE,IAAuB;QAClD,MAAM,SAAS,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,SAAS;QAChC,MAAM,CAAA,GAAA,iKAAA,CAAA,YAAS,AAAD,EAAE,QAAQ;YACtB,GAAG,IAAI;YACP,WAAW,iKAAA,CAAA,YAAS,CAAC,GAAG;QAC1B;IACF;IAEA,MAAM,QAAO,MAAc;QACzB,MAAM,SAAS,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,SAAS;QAChC,MAAM,CAAA,GAAA,iKAAA,CAAA,YAAS,AAAD,EAAE;IAClB;IAEA,mCAAmC;IACnC,MAAM,UAAS,MAAc,EAAE,SAA4D;QACzF,MAAM,MAAM,IAAI;QAChB,MAAM,SAAS,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,SAAS,QAAQ,WAAW;YACrE,GAAG,SAAS;YACZ,WAAW,iKAAA,CAAA,YAAS,CAAC,QAAQ,CAAC;YAC9B,WAAW,iKAAA,CAAA,YAAS,CAAC,QAAQ,CAAC;QAChC;QACA,OAAO,OAAO,EAAE;IAClB;IAEA,kBAAkB;IAClB,MAAM,WAAU,MAAc;QAC5B,MAAM,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EACZ,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,SAAS,QAAQ,WAChC,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,gBAAgB,QACxB,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;QAEvB,MAAM,gBAAgB,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;QACpC,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,UAAqB,MAAO,MAAM,CAAC;IAC1E;IAEA,oBAAoB;IACpB,MAAM,aAAY,MAAc,EAAE,OAAe;QAC/C,MAAM,SAAS,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,SAAS,QAAQ,UAAU;QAClD,MAAM,CAAA,GAAA,iKAAA,CAAA,YAAS,AAAD,EAAE;IAClB;AACF;AAGO,MAAM,iBAAiB;IAC5B,MAAM,QAAO,OAKZ;QACC,IAAI,IAAyB,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE;QAE5C,IAAI,SAAS,UAAU;YACrB,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,YAAY,MAAM,QAAQ,QAAQ;QACvD;QACA,IAAI,SAAS,QAAQ;YACnB,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,UAAU,MAAM,QAAQ,MAAM;QACnD;QACA,IAAI,SAAS,QAAQ;YACnB,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,UAAU,MAAM,QAAQ,MAAM;QACnD;QAEA,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;QAElC,IAAI,SAAS,OAAO;YAClB,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,QAAQ,KAAK;QAClC;QAEA,MAAM,gBAAgB,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;QACpC,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,UAAmB,MAAO,MAAM,CAAC;IACxE;IAEA,MAAM,KAAI,SAAiB;QACzB,MAAM,SAAS,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,YAAY;QACnC,MAAM,UAAU,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE;QAC7B,OAAO,UAAmB;IAC5B;IAEA,MAAM,QAAO,IAA0E;QACrF,MAAM,MAAM,IAAI;QAChB,MAAM,mBAAmB,CAAC,IAAI,EAAE,IAAI,WAAW,KAAK,CAAC,IAAI,QAAQ,KAAK,CAAC,EAAE,QAAQ,GAAG,QAAQ,CAAC,GAAG,OAAO,IAAI,OAAO,GAAG,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG,GAAG,WAAW,IAAI;QAE9M,MAAM,SAAS,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,aAAa;YACtD,GAAG,IAAI;YACP;YACA,WAAW,iKAAA,CAAA,YAAS,CAAC,QAAQ,CAAC;YAC9B,WAAW,iKAAA,CAAA,YAAS,CAAC,QAAQ,CAAC;QAChC;QACA,OAAO,OAAO,EAAE;IAClB;IAEA,MAAM,QAAO,SAAiB,EAAE,IAAsB;QACpD,MAAM,SAAS,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,YAAY;QACnC,MAAM,CAAA,GAAA,iKAAA,CAAA,YAAS,AAAD,EAAE,QAAQ;YACtB,GAAG,IAAI;YACP,WAAW,iKAAA,CAAA,YAAS,CAAC,GAAG;QAC1B;IACF;AACF;AAGO,MAAM,gBAAgB;IAC3B,MAAM,WAAU,MAAc;QAC5B,MAAM,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EACZ,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,SAAS,QAAQ,YAChC,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,YAAY,MAAM,OACxB,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;QAEvB,MAAM,gBAAgB,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;QACpC,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,UAAkB,MAAO,MAAM,CAAC;IACvE;IAEA,MAAM,QAAO,MAAc,EAAE,IAAoD;QAC/E,MAAM,MAAM,IAAI;QAChB,MAAM,SAAS,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,SAAS,QAAQ,YAAY;YACtE,GAAG,IAAI;YACP,WAAW,iKAAA,CAAA,YAAS,CAAC,QAAQ,CAAC;YAC9B,WAAW,iKAAA,CAAA,YAAS,CAAC,QAAQ,CAAC;QAChC;QACA,OAAO,OAAO,EAAE;IAClB;IAEA,MAAM,QAAO,MAAc,EAAE,QAAgB,EAAE,IAAqB;QAClE,MAAM,SAAS,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,SAAS,QAAQ,WAAW;QACnD,MAAM,CAAA,GAAA,iKAAA,CAAA,YAAS,AAAD,EAAE,QAAQ;YACtB,GAAG,IAAI;YACP,WAAW,iKAAA,CAAA,YAAS,CAAC,GAAG;QAC1B;IACF;AACF;AAGO,MAAM,iBAAiB;IAC5B,MAAM;QACJ,MAAM,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,YAAY,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;QACnD,MAAM,gBAAgB,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;QACpC,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,UAAuB,MAAO,MAAM,CAAC;IAC5E;IAEA,MAAM,KAAI,SAAiB;QACzB,MAAM,SAAS,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,WAAW;QAClC,MAAM,UAAU,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE;QAC7B,OAAO,UAAuB;IAChC;AACF;AAGO,MAAM,iBAAiB;IAC5B,MAAM;QACJ,MAAM,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,cAAc,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;QACrD,MAAM,gBAAgB,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;QACpC,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,UAAuB,MAAO,MAAM,CAAC;IAC5E;AACF", "debugId": null}}, {"offset": {"line": 2557, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/src/personal/bvr-safaris/src/lib/services/farmService.ts"], "sourcesContent": ["/**\n * Enhanced farm service with location-based functionality\n * Extends the basic Firestore operations with geolocation features\n */\n\nimport {\n  collection,\n  query,\n  where,\n  orderBy,\n  limit,\n  getDocs,\n  doc,\n  getDoc,\n  addDoc,\n  updateDoc,\n  deleteDoc,\n  Timestamp,\n  GeoPoint,\n  Query,\n  DocumentData,\n  startAfter,\n  QueryDocumentSnapshot\n} from 'firebase/firestore'\nimport { db } from '@/lib/firebase/client'\nimport {\n  GameFarm,\n  FarmSearchFilters,\n  FarmWithDistance,\n  FarmSearchResult,\n  CreateFarmData\n} from '@/lib/types/firestore'\nimport { LocationData } from '@/lib/types/location'\nimport { DistanceService } from '@/lib/services/location/distance'\nimport { LocationService, locationUtils } from '@/lib/services/location'\nimport { docToData } from '@/lib/firebase/firestore'\n\nexport class EnhancedFarmService {\n  private locationService: LocationService\n\n  constructor() {\n    this.locationService = LocationService.getInstance()\n  }\n\n  /**\n   * Search farms with location-based filtering and sorting\n   */\n  async searchFarms(filters: FarmSearchFilters = {}): Promise<FarmSearchResult> {\n    let baseQuery: Query<DocumentData> = collection(db, 'farms')\n    \n    // Apply basic filters\n    baseQuery = this.applyBasicFilters(baseQuery, filters)\n    \n    // Get initial results\n    let querySnapshot = await getDocs(baseQuery)\n    let farms = querySnapshot.docs.map(doc => docToData<GameFarm>(doc)!).filter(Boolean)\n\n    // Apply location-based filtering and sorting\n    if (filters.location) {\n      farms = await this.applyLocationFilters(farms, filters.location, filters.sortBy)\n    }\n\n    // Apply other client-side filters\n    farms = this.applyClientSideFilters(farms, filters)\n\n    // Apply pagination\n    const { farms: paginatedFarms, hasMore } = this.applyPagination(farms, filters)\n\n    return {\n      farms: paginatedFarms,\n      total: farms.length,\n      hasMore,\n      searchCenter: filters.location?.center,\n      searchRadius: filters.location?.radius\n    }\n  }\n\n  /**\n   * Get farms near a specific location\n   */\n  async getFarmsNearLocation(\n    center: { lat: number; lng: number },\n    radiusKm: number = 50,\n    limitCount: number = 20\n  ): Promise<FarmWithDistance[]> {\n    // Get all active farms first\n    const q = query(\n      collection(db, 'farms'),\n      where('isActive', '==', true),\n      limit(limitCount * 2) // Get more than needed to account for distance filtering\n    )\n\n    const querySnapshot = await getDocs(q)\n    const farms = querySnapshot.docs.map(doc => docToData<GameFarm>(doc)!).filter(Boolean)\n\n    // Filter by distance and add distance information\n    const farmsWithDistance: FarmWithDistance[] = []\n\n    for (const farm of farms) {\n      if (farm.locationData?.coordinates) {\n        const farmCoords = {\n          lat: farm.locationData.coordinates.latitude,\n          lng: farm.locationData.coordinates.longitude\n        }\n\n        const distance = DistanceService.calculateDistance(center, farmCoords)\n        \n        if (distance <= radiusKm) {\n          const travelTime = DistanceService.estimateTravelTime(distance)\n          farmsWithDistance.push({\n            ...farm,\n            distance,\n            travelTime: travelTime.duration\n          })\n        }\n      }\n    }\n\n    // Sort by distance and limit results\n    return farmsWithDistance\n      .sort((a, b) => (a.distance || 0) - (b.distance || 0))\n      .slice(0, limitCount)\n  }\n\n  /**\n   * Create a new farm with enhanced location data\n   */\n  async createFarm(data: CreateFarmData, ownerId?: string): Promise<string> {\n    const now = new Date()\n\n    // Generate additional location-based fields\n    const enhancedData: Partial<GameFarm> = {\n      ...data,\n      ownerId: data.ownerId || ownerId, // Use provided ownerId or fallback\n      createdAt: Timestamp.fromDate(now),\n      updatedAt: Timestamp.fromDate(now),\n      isActive: true,\n      featured: false\n    }\n\n    // Generate searchable location and geohash if location data exists\n    if (data.locationData) {\n      enhancedData.searchableLocation = LocationService.createSearchableLocation(data.locationData)\n      enhancedData.geoHash = LocationService.generateGeoHash({\n        lat: data.locationData.coordinates.latitude,\n        lng: data.locationData.coordinates.longitude\n      })\n      \n      // Ensure province is set from location data\n      const mappedProvince = LocationService.mapToSouthAfricanProvince(data.locationData)\n      if (mappedProvince) {\n        enhancedData.province = mappedProvince\n      }\n    }\n\n    const docRef = await addDoc(collection(db, 'farms'), enhancedData)\n    return docRef.id\n  }\n\n  /**\n   * Update farm with location data processing\n   */\n  async updateFarm(farmId: string, data: Partial<GameFarm>): Promise<void> {\n    const updateData: Partial<GameFarm> = {\n      ...data,\n      updatedAt: Timestamp.now()\n    }\n\n    // Update location-based fields if location data changed\n    if (data.locationData) {\n      updateData.searchableLocation = LocationService.createSearchableLocation(data.locationData)\n      updateData.geoHash = LocationService.generateGeoHash({\n        lat: data.locationData.coordinates.latitude,\n        lng: data.locationData.coordinates.longitude\n      })\n      \n      // Update province from location data\n      const mappedProvince = LocationService.mapToSouthAfricanProvince(data.locationData)\n      if (mappedProvince) {\n        updateData.province = mappedProvince\n      }\n    }\n\n    const docRef = doc(db, 'farms', farmId)\n    await updateDoc(docRef, updateData)\n  }\n\n  /**\n   * Get farm with distance from a reference point\n   */\n  async getFarmWithDistance(\n    farmId: string,\n    referencePoint?: { lat: number; lng: number }\n  ): Promise<FarmWithDistance | null> {\n    const docRef = doc(db, 'farms', farmId)\n    const docSnap = await getDoc(docRef)\n    const farm = docToData<GameFarm>(docSnap)\n\n    if (!farm) return null\n\n    const farmWithDistance: FarmWithDistance = { ...farm }\n\n    if (referencePoint && farm.locationData?.coordinates) {\n      const farmCoords = {\n        lat: farm.locationData.coordinates.latitude,\n        lng: farm.locationData.coordinates.longitude\n      }\n\n      farmWithDistance.distance = DistanceService.calculateDistance(referencePoint, farmCoords)\n      const travelTime = DistanceService.estimateTravelTime(farmWithDistance.distance)\n      farmWithDistance.travelTime = travelTime.duration\n    }\n\n    return farmWithDistance\n  }\n\n  /**\n   * Apply basic Firestore filters\n   */\n  private applyBasicFilters(baseQuery: Query<DocumentData>, filters: FarmSearchFilters): Query<DocumentData> {\n    let q = baseQuery\n\n    // Always filter for active farms unless specifically requested\n    q = query(q, where('isActive', '==', true))\n\n    if (filters.provinces && filters.provinces.length > 0) {\n      q = query(q, where('province', 'in', filters.provinces))\n    }\n\n    if (filters.activities && filters.activities.length > 0) {\n      q = query(q, where('activityTypes', 'in', filters.activities))\n    }\n\n    // Text search using searchableLocation field\n    if (filters.query) {\n      // Note: Firestore doesn't support full-text search natively\n      // This is a simplified approach - consider using Algolia or similar for production\n      const searchTerms = filters.query.toLowerCase().split(' ')\n      // We'll handle text search client-side for now\n    }\n\n    // Apply sorting (distance sorting will be handled client-side)\n    if (filters.sortBy && filters.sortBy !== 'distance') {\n      const sortField = this.getSortField(filters.sortBy)\n      const sortOrder = filters.sortOrder || 'desc'\n      q = query(q, orderBy(sortField, sortOrder))\n    } else {\n      // Default sorting\n      q = query(q, orderBy('featured', 'desc'), orderBy('createdAt', 'desc'))\n    }\n\n    if (filters.limit) {\n      q = query(q, limit(filters.limit))\n    }\n\n    return q\n  }\n\n  /**\n   * Apply location-based filtering and sorting\n   */\n  private async applyLocationFilters(\n    farms: GameFarm[],\n    locationFilter: NonNullable<FarmSearchFilters['location']>,\n    sortBy?: string\n  ): Promise<FarmWithDistance[]> {\n    const farmsWithDistance: FarmWithDistance[] = []\n\n    for (const farm of farms) {\n      if (!farm.locationData?.coordinates) continue\n\n      const farmCoords = {\n        lat: farm.locationData.coordinates.latitude,\n        lng: farm.locationData.coordinates.longitude\n      }\n\n      let includeThisFarm = true\n      let distance: number | undefined\n\n      // Apply radius filter\n      if (locationFilter.center && locationFilter.radius) {\n        distance = DistanceService.calculateDistance(locationFilter.center, farmCoords)\n        includeThisFarm = distance <= locationFilter.radius\n      }\n\n      // Apply bounds filter\n      if (includeThisFarm && locationFilter.bounds) {\n        const { northeast, southwest } = locationFilter.bounds\n        includeThisFarm = (\n          farmCoords.lat >= southwest.lat &&\n          farmCoords.lat <= northeast.lat &&\n          farmCoords.lng >= southwest.lng &&\n          farmCoords.lng <= northeast.lng\n        )\n      }\n\n      if (includeThisFarm) {\n        const farmWithDistance: FarmWithDistance = { ...farm }\n        \n        if (distance !== undefined) {\n          farmWithDistance.distance = distance\n          const travelTime = DistanceService.estimateTravelTime(distance)\n          farmWithDistance.travelTime = travelTime.duration\n        }\n\n        farmsWithDistance.push(farmWithDistance)\n      }\n    }\n\n    // Sort by distance if requested\n    if (sortBy === 'distance') {\n      farmsWithDistance.sort((a, b) => (a.distance || 0) - (b.distance || 0))\n    }\n\n    return farmsWithDistance\n  }\n\n  /**\n   * Apply client-side filters that can't be done in Firestore\n   */\n  private applyClientSideFilters(farms: FarmWithDistance[], filters: FarmSearchFilters): FarmWithDistance[] {\n    let filteredFarms = [...farms]\n\n    // Text search\n    if (filters.query) {\n      const searchTerms = filters.query.toLowerCase().split(' ')\n      filteredFarms = filteredFarms.filter(farm => {\n        const searchText = [\n          farm.name,\n          farm.description,\n          farm.searchableLocation,\n          locationUtils.getLocationDisplayName(farm.locationData!)\n        ].join(' ').toLowerCase()\n\n        return searchTerms.every(term => searchText.includes(term))\n      })\n    }\n\n    // Price range filter\n    if (filters.priceRange && filters.priceRange.length === 2) {\n      const [minPrice, maxPrice] = filters.priceRange\n      filteredFarms = filteredFarms.filter(farm => {\n        if (!farm.pricePerDay) return true // Include farms without price info\n        return farm.pricePerDay >= minPrice && farm.pricePerDay <= maxPrice\n      })\n    }\n\n    // Size range filter\n    if (filters.sizeRange && filters.sizeRange.length === 2) {\n      const [minSize, maxSize] = filters.sizeRange\n      filteredFarms = filteredFarms.filter(farm => {\n        if (!farm.sizeHectares) return true // Include farms without size info\n        return farm.sizeHectares >= minSize && farm.sizeHectares <= maxSize\n      })\n    }\n\n    return filteredFarms\n  }\n\n  /**\n   * Apply pagination\n   */\n  private applyPagination(\n    farms: FarmWithDistance[],\n    filters: FarmSearchFilters\n  ): { farms: FarmWithDistance[]; hasMore: boolean } {\n    const limit = filters.limit || 20\n    const offset = filters.offset || 0\n\n    const paginatedFarms = farms.slice(offset, offset + limit)\n    const hasMore = farms.length > offset + limit\n\n    return { farms: paginatedFarms, hasMore }\n  }\n\n  /**\n   * Get Firestore field name for sorting\n   */\n  private getSortField(sortBy: string): string {\n    switch (sortBy) {\n      case 'name':\n        return 'name'\n      case 'created':\n        return 'createdAt'\n      case 'price':\n        return 'pricePerDay'\n      case 'size':\n        return 'sizeHectares'\n      case 'rating':\n        return 'averageRating' // This would need to be calculated and stored\n      default:\n        return 'createdAt'\n    }\n  }\n}\n\n// Create and export singleton instance\nexport const enhancedFarmService = new EnhancedFarmService()\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAED;AAAA;AAmBA;AAAA;AASA;AACA;AAAA;AACA;;;;;;AAEO,MAAM;IACH,gBAAgC;IAExC,aAAc;QACZ,IAAI,CAAC,eAAe,GAAG,2JAAA,CAAA,kBAAe,CAAC,WAAW;IACpD;IAEA;;GAEC,GACD,MAAM,YAAY,UAA6B,CAAC,CAAC,EAA6B;QAC5E,IAAI,YAAiC,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE;QAEpD,sBAAsB;QACtB,YAAY,IAAI,CAAC,iBAAiB,CAAC,WAAW;QAE9C,sBAAsB;QACtB,IAAI,gBAAgB,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;QAClC,IAAI,QAAQ,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAA,GAAA,mIAAA,CAAA,YAAS,AAAD,EAAY,MAAO,MAAM,CAAC;QAE5E,6CAA6C;QAC7C,IAAI,QAAQ,QAAQ,EAAE;YACpB,QAAQ,MAAM,IAAI,CAAC,oBAAoB,CAAC,OAAO,QAAQ,QAAQ,EAAE,QAAQ,MAAM;QACjF;QAEA,kCAAkC;QAClC,QAAQ,IAAI,CAAC,sBAAsB,CAAC,OAAO;QAE3C,mBAAmB;QACnB,MAAM,EAAE,OAAO,cAAc,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO;QAEvE,OAAO;YACL,OAAO;YACP,OAAO,MAAM,MAAM;YACnB;YACA,cAAc,QAAQ,QAAQ,EAAE;YAChC,cAAc,QAAQ,QAAQ,EAAE;QAClC;IACF;IAEA;;GAEC,GACD,MAAM,qBACJ,MAAoC,EACpC,WAAmB,EAAE,EACrB,aAAqB,EAAE,EACM;QAC7B,6BAA6B;QAC7B,MAAM,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EACZ,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,UACf,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,YAAY,MAAM,OACxB,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,aAAa,GAAG,yDAAyD;;QAGjF,MAAM,gBAAgB,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;QACpC,MAAM,QAAQ,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAA,GAAA,mIAAA,CAAA,YAAS,AAAD,EAAY,MAAO,MAAM,CAAC;QAE9E,kDAAkD;QAClD,MAAM,oBAAwC,EAAE;QAEhD,KAAK,MAAM,QAAQ,MAAO;YACxB,IAAI,KAAK,YAAY,EAAE,aAAa;gBAClC,MAAM,aAAa;oBACjB,KAAK,KAAK,YAAY,CAAC,WAAW,CAAC,QAAQ;oBAC3C,KAAK,KAAK,YAAY,CAAC,WAAW,CAAC,SAAS;gBAC9C;gBAEA,MAAM,WAAW,8IAAA,CAAA,kBAAe,CAAC,iBAAiB,CAAC,QAAQ;gBAE3D,IAAI,YAAY,UAAU;oBACxB,MAAM,aAAa,8IAAA,CAAA,kBAAe,CAAC,kBAAkB,CAAC;oBACtD,kBAAkB,IAAI,CAAC;wBACrB,GAAG,IAAI;wBACP;wBACA,YAAY,WAAW,QAAQ;oBACjC;gBACF;YACF;QACF;QAEA,qCAAqC;QACrC,OAAO,kBACJ,IAAI,CAAC,CAAC,GAAG,IAAM,CAAC,EAAE,QAAQ,IAAI,CAAC,IAAI,CAAC,EAAE,QAAQ,IAAI,CAAC,GACnD,KAAK,CAAC,GAAG;IACd;IAEA;;GAEC,GACD,MAAM,WAAW,IAAoB,EAAE,OAAgB,EAAmB;QACxE,MAAM,MAAM,IAAI;QAEhB,4CAA4C;QAC5C,MAAM,eAAkC;YACtC,GAAG,IAAI;YACP,SAAS,KAAK,OAAO,IAAI;YACzB,WAAW,iKAAA,CAAA,YAAS,CAAC,QAAQ,CAAC;YAC9B,WAAW,iKAAA,CAAA,YAAS,CAAC,QAAQ,CAAC;YAC9B,UAAU;YACV,UAAU;QACZ;QAEA,mEAAmE;QACnE,IAAI,KAAK,YAAY,EAAE;YACrB,aAAa,kBAAkB,GAAG,2JAAA,CAAA,kBAAe,CAAC,wBAAwB,CAAC,KAAK,YAAY;YAC5F,aAAa,OAAO,GAAG,2JAAA,CAAA,kBAAe,CAAC,eAAe,CAAC;gBACrD,KAAK,KAAK,YAAY,CAAC,WAAW,CAAC,QAAQ;gBAC3C,KAAK,KAAK,YAAY,CAAC,WAAW,CAAC,SAAS;YAC9C;YAEA,4CAA4C;YAC5C,MAAM,iBAAiB,2JAAA,CAAA,kBAAe,CAAC,yBAAyB,CAAC,KAAK,YAAY;YAClF,IAAI,gBAAgB;gBAClB,aAAa,QAAQ,GAAG;YAC1B;QACF;QAEA,MAAM,SAAS,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,UAAU;QACrD,OAAO,OAAO,EAAE;IAClB;IAEA;;GAEC,GACD,MAAM,WAAW,MAAc,EAAE,IAAuB,EAAiB;QACvE,MAAM,aAAgC;YACpC,GAAG,IAAI;YACP,WAAW,iKAAA,CAAA,YAAS,CAAC,GAAG;QAC1B;QAEA,wDAAwD;QACxD,IAAI,KAAK,YAAY,EAAE;YACrB,WAAW,kBAAkB,GAAG,2JAAA,CAAA,kBAAe,CAAC,wBAAwB,CAAC,KAAK,YAAY;YAC1F,WAAW,OAAO,GAAG,2JAAA,CAAA,kBAAe,CAAC,eAAe,CAAC;gBACnD,KAAK,KAAK,YAAY,CAAC,WAAW,CAAC,QAAQ;gBAC3C,KAAK,KAAK,YAAY,CAAC,WAAW,CAAC,SAAS;YAC9C;YAEA,qCAAqC;YACrC,MAAM,iBAAiB,2JAAA,CAAA,kBAAe,CAAC,yBAAyB,CAAC,KAAK,YAAY;YAClF,IAAI,gBAAgB;gBAClB,WAAW,QAAQ,GAAG;YACxB;QACF;QAEA,MAAM,SAAS,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,SAAS;QAChC,MAAM,CAAA,GAAA,iKAAA,CAAA,YAAS,AAAD,EAAE,QAAQ;IAC1B;IAEA;;GAEC,GACD,MAAM,oBACJ,MAAc,EACd,cAA6C,EACX;QAClC,MAAM,SAAS,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,SAAS;QAChC,MAAM,UAAU,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE;QAC7B,MAAM,OAAO,CAAA,GAAA,mIAAA,CAAA,YAAS,AAAD,EAAY;QAEjC,IAAI,CAAC,MAAM,OAAO;QAElB,MAAM,mBAAqC;YAAE,GAAG,IAAI;QAAC;QAErD,IAAI,kBAAkB,KAAK,YAAY,EAAE,aAAa;YACpD,MAAM,aAAa;gBACjB,KAAK,KAAK,YAAY,CAAC,WAAW,CAAC,QAAQ;gBAC3C,KAAK,KAAK,YAAY,CAAC,WAAW,CAAC,SAAS;YAC9C;YAEA,iBAAiB,QAAQ,GAAG,8IAAA,CAAA,kBAAe,CAAC,iBAAiB,CAAC,gBAAgB;YAC9E,MAAM,aAAa,8IAAA,CAAA,kBAAe,CAAC,kBAAkB,CAAC,iBAAiB,QAAQ;YAC/E,iBAAiB,UAAU,GAAG,WAAW,QAAQ;QACnD;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,kBAAkB,SAA8B,EAAE,OAA0B,EAAuB;QACzG,IAAI,IAAI;QAER,+DAA+D;QAC/D,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,YAAY,MAAM;QAErC,IAAI,QAAQ,SAAS,IAAI,QAAQ,SAAS,CAAC,MAAM,GAAG,GAAG;YACrD,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,YAAY,MAAM,QAAQ,SAAS;QACxD;QAEA,IAAI,QAAQ,UAAU,IAAI,QAAQ,UAAU,CAAC,MAAM,GAAG,GAAG;YACvD,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,iBAAiB,MAAM,QAAQ,UAAU;QAC9D;QAEA,6CAA6C;QAC7C,IAAI,QAAQ,KAAK,EAAE;YACjB,4DAA4D;YAC5D,mFAAmF;YACnF,MAAM,cAAc,QAAQ,KAAK,CAAC,WAAW,GAAG,KAAK,CAAC;QACtD,+CAA+C;QACjD;QAEA,+DAA+D;QAC/D,IAAI,QAAQ,MAAM,IAAI,QAAQ,MAAM,KAAK,YAAY;YACnD,MAAM,YAAY,IAAI,CAAC,YAAY,CAAC,QAAQ,MAAM;YAClD,MAAM,YAAY,QAAQ,SAAS,IAAI;YACvC,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,WAAW;QAClC,OAAO;YACL,kBAAkB;YAClB,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,YAAY,SAAS,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;QACjE;QAEA,IAAI,QAAQ,KAAK,EAAE;YACjB,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,QAAQ,KAAK;QAClC;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,MAAc,qBACZ,KAAiB,EACjB,cAA0D,EAC1D,MAAe,EACc;QAC7B,MAAM,oBAAwC,EAAE;QAEhD,KAAK,MAAM,QAAQ,MAAO;YACxB,IAAI,CAAC,KAAK,YAAY,EAAE,aAAa;YAErC,MAAM,aAAa;gBACjB,KAAK,KAAK,YAAY,CAAC,WAAW,CAAC,QAAQ;gBAC3C,KAAK,KAAK,YAAY,CAAC,WAAW,CAAC,SAAS;YAC9C;YAEA,IAAI,kBAAkB;YACtB,IAAI;YAEJ,sBAAsB;YACtB,IAAI,eAAe,MAAM,IAAI,eAAe,MAAM,EAAE;gBAClD,WAAW,8IAAA,CAAA,kBAAe,CAAC,iBAAiB,CAAC,eAAe,MAAM,EAAE;gBACpE,kBAAkB,YAAY,eAAe,MAAM;YACrD;YAEA,sBAAsB;YACtB,IAAI,mBAAmB,eAAe,MAAM,EAAE;gBAC5C,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,GAAG,eAAe,MAAM;gBACtD,kBACE,WAAW,GAAG,IAAI,UAAU,GAAG,IAC/B,WAAW,GAAG,IAAI,UAAU,GAAG,IAC/B,WAAW,GAAG,IAAI,UAAU,GAAG,IAC/B,WAAW,GAAG,IAAI,UAAU,GAAG;YAEnC;YAEA,IAAI,iBAAiB;gBACnB,MAAM,mBAAqC;oBAAE,GAAG,IAAI;gBAAC;gBAErD,IAAI,aAAa,WAAW;oBAC1B,iBAAiB,QAAQ,GAAG;oBAC5B,MAAM,aAAa,8IAAA,CAAA,kBAAe,CAAC,kBAAkB,CAAC;oBACtD,iBAAiB,UAAU,GAAG,WAAW,QAAQ;gBACnD;gBAEA,kBAAkB,IAAI,CAAC;YACzB;QACF;QAEA,gCAAgC;QAChC,IAAI,WAAW,YAAY;YACzB,kBAAkB,IAAI,CAAC,CAAC,GAAG,IAAM,CAAC,EAAE,QAAQ,IAAI,CAAC,IAAI,CAAC,EAAE,QAAQ,IAAI,CAAC;QACvE;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,uBAAuB,KAAyB,EAAE,OAA0B,EAAsB;QACxG,IAAI,gBAAgB;eAAI;SAAM;QAE9B,cAAc;QACd,IAAI,QAAQ,KAAK,EAAE;YACjB,MAAM,cAAc,QAAQ,KAAK,CAAC,WAAW,GAAG,KAAK,CAAC;YACtD,gBAAgB,cAAc,MAAM,CAAC,CAAA;gBACnC,MAAM,aAAa;oBACjB,KAAK,IAAI;oBACT,KAAK,WAAW;oBAChB,KAAK,kBAAkB;oBACvB,2JAAA,CAAA,gBAAa,CAAC,sBAAsB,CAAC,KAAK,YAAY;iBACvD,CAAC,IAAI,CAAC,KAAK,WAAW;gBAEvB,OAAO,YAAY,KAAK,CAAC,CAAA,OAAQ,WAAW,QAAQ,CAAC;YACvD;QACF;QAEA,qBAAqB;QACrB,IAAI,QAAQ,UAAU,IAAI,QAAQ,UAAU,CAAC,MAAM,KAAK,GAAG;YACzD,MAAM,CAAC,UAAU,SAAS,GAAG,QAAQ,UAAU;YAC/C,gBAAgB,cAAc,MAAM,CAAC,CAAA;gBACnC,IAAI,CAAC,KAAK,WAAW,EAAE,OAAO,KAAK,mCAAmC;;gBACtE,OAAO,KAAK,WAAW,IAAI,YAAY,KAAK,WAAW,IAAI;YAC7D;QACF;QAEA,oBAAoB;QACpB,IAAI,QAAQ,SAAS,IAAI,QAAQ,SAAS,CAAC,MAAM,KAAK,GAAG;YACvD,MAAM,CAAC,SAAS,QAAQ,GAAG,QAAQ,SAAS;YAC5C,gBAAgB,cAAc,MAAM,CAAC,CAAA;gBACnC,IAAI,CAAC,KAAK,YAAY,EAAE,OAAO,KAAK,kCAAkC;;gBACtE,OAAO,KAAK,YAAY,IAAI,WAAW,KAAK,YAAY,IAAI;YAC9D;QACF;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,gBACN,KAAyB,EACzB,OAA0B,EACuB;QACjD,MAAM,QAAQ,QAAQ,KAAK,IAAI;QAC/B,MAAM,SAAS,QAAQ,MAAM,IAAI;QAEjC,MAAM,iBAAiB,MAAM,KAAK,CAAC,QAAQ,SAAS;QACpD,MAAM,UAAU,MAAM,MAAM,GAAG,SAAS;QAExC,OAAO;YAAE,OAAO;YAAgB;QAAQ;IAC1C;IAEA;;GAEC,GACD,AAAQ,aAAa,MAAc,EAAU;QAC3C,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO,gBAAgB,8CAA8C;;YACvE;gBACE,OAAO;QACX;IACF;AACF;AAGO,MAAM,sBAAsB,IAAI", "debugId": null}}, {"offset": {"line": 2861, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/src/personal/bvr-safaris/src/lib/constants.ts"], "sourcesContent": ["/**\n * Centralized constants for the BvR Safaris application\n * This file contains all shared constants to ensure consistency across the application\n */\n\n/**\n * South African provinces in alphabetical order\n * Used throughout the application for province selection, filtering, and display\n */\nexport const SOUTH_AFRICAN_PROVINCES = [\n  'Eastern Cape',\n  'Free State',\n  'Gauteng',\n  'KwaZulu-Natal',\n  'Limpopo',\n  'Mpumalanga',\n  'Northern Cape',\n  'North West',\n  'Western Cape'\n] as const\n\n/**\n * Type for South African provinces to ensure type safety\n */\nexport type SouthAfricanProvince = typeof SOUTH_AFRICAN_PROVINCES[number]\n\n/**\n * Activity types available on the platform\n */\nexport const ACTIVITY_TYPES = {\n  HUNTING: 'hunting',\n  PHOTO_SAFARI: 'photo_safari',\n  BOTH: 'both'\n} as const\n\n/**\n * Booking status options\n */\nexport const BOOKING_STATUS = {\n  PENDING: 'pending',\n  CONFIRMED: 'confirmed',\n  CANCELLED: 'cancelled',\n  COMPLETED: 'completed'\n} as const\n\n/**\n * User roles available in the system\n * Updated to use the simplified role system with admin support\n */\nexport const USER_ROLES = {\n  FARM_OWNER: 'farm_owner',\n  GUEST: 'guest',\n  ADMIN: 'admin'\n} as const\n\n/**\n * Default price range for filtering (in ZAR)\n */\nexport const DEFAULT_PRICE_RANGE = [0, 20000] as const\n\n/**\n * Maximum file size for uploads (in bytes)\n */\nexport const MAX_FILE_SIZE = 50 * 1024 * 1024 // 50MB\n\n/**\n * Supported image formats for uploads\n */\nexport const SUPPORTED_IMAGE_FORMATS = [\n  'image/jpeg',\n  'image/jpg',\n  'image/png',\n  'image/webp'\n] as const\n\n/**\n * Default pagination limits\n */\nexport const PAGINATION = {\n  DEFAULT_LIMIT: 10,\n  MAX_LIMIT: 100,\n  FEATURED_FARMS_LIMIT: 3\n} as const\n\n/**\n * Farm amenities available for filtering\n * Used in filter panels and farm feature listings\n */\nexport const FARM_AMENITIES = [\n  'Luxury Lodge',\n  'Basic Accommodation',\n  'Restaurant',\n  'Bar',\n  'Swimming Pool',\n  'Spa',\n  'WiFi',\n  'Airport Transfer',\n  'Professional Guide',\n  'Trophy Preparation',\n  'Taxidermy'\n] as const\n\n/**\n * Type for farm amenities to ensure type safety\n */\nexport type FarmAmenity = typeof FARM_AMENITIES[number]\n\n/**\n * Game species available for hunting and viewing\n * Used in filter panels and farm species listings\n */\nexport const GAME_SPECIES = [\n  'Lion',\n  'Leopard',\n  'Elephant',\n  'Buffalo',\n  'Rhino',\n  'Kudu',\n  'Impala',\n  'Springbok',\n  'Eland',\n  'Sable',\n  'Gemsbok',\n  'Waterbuck',\n  'Bushbuck',\n  'Warthog'\n] as const\n\n/**\n * Type for game species to ensure type safety\n */\nexport type GameSpeciesName = typeof GAME_SPECIES[number]\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED;;;CAGC;;;;;;;;;;;;AACM,MAAM,0BAA0B;IACrC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAUM,MAAM,iBAAiB;IAC5B,SAAS;IACT,cAAc;IACd,MAAM;AACR;AAKO,MAAM,iBAAiB;IAC5B,SAAS;IACT,WAAW;IACX,WAAW;IACX,WAAW;AACb;AAMO,MAAM,aAAa;IACxB,YAAY;IACZ,OAAO;IACP,OAAO;AACT;AAKO,MAAM,sBAAsB;IAAC;IAAG;CAAM;AAKtC,MAAM,gBAAgB,KAAK,OAAO,KAAK,OAAO;;AAK9C,MAAM,0BAA0B;IACrC;IACA;IACA;IACA;CACD;AAKM,MAAM,aAAa;IACxB,eAAe;IACf,WAAW;IACX,sBAAsB;AACxB;AAMO,MAAM,iBAAiB;IAC5B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAWM,MAAM,eAAe;IAC1B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD", "debugId": null}}, {"offset": {"line": 2958, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/src/personal/bvr-safaris/src/app/farms/create/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { useRouter } from 'next/navigation'\nimport Image from 'next/image'\nimport { useAuth } from '@/hooks/useAuth'\nimport { Button } from '@/components/ui/Button'\nimport { Input } from '@/components/ui/Input'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'\nimport { ImageUpload } from '@/components/ui/FileUpload'\nimport { LocationAutocomplete } from '@/components/ui/LocationAutocomplete'\nimport { enhancedFarmService } from '@/lib/services/farmService'\nimport { ActivityType, CreateFarmData } from '@/lib/types/firestore'\nimport { LocationData } from '@/lib/types/location'\nimport { SOUTH_AFRICAN_PROVINCES, SouthAfricanProvince } from '@/lib/constants'\n\ninterface FarmFormData {\n  name: string\n  description: string\n  descriptionAfrikaans: string\n  locationData: LocationData | null\n  province: SouthAfricanProvince | ''\n  sizeHectares: string\n  activityTypes: ActivityType\n  contactEmail: string\n  contactPhone: string\n  websiteUrl: string\n  rules: string\n  rulesAfrikaans: string\n  pricingInfo: string\n}\n\n\n\nexport default function CreateFarmPage() {\n  const { user, loading: authLoading } = useAuth()\n  const router = useRouter()\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState<string | null>(null)\n  const [uploadedImages, setUploadedImages] = useState<string[]>([])\n\n  const [formData, setFormData] = useState<FarmFormData>({\n    name: '',\n    description: '',\n    descriptionAfrikaans: '',\n    locationData: null,\n    province: '',\n    sizeHectares: '',\n    activityTypes: 'both',\n    contactEmail: '',\n    contactPhone: '',\n    websiteUrl: '',\n    rules: '',\n    rulesAfrikaans: '',\n    pricingInfo: ''\n  })\n\n  useEffect(() => {\n    if (!authLoading && !user) {\n      router.push('/auth/login')\n      return\n    }\n\n    // TODO: Check if user is farm owner using custom claims or user profile\n    // For now, allow all authenticated users to create farms\n\n    // Pre-fill contact email with user's email\n    if (user?.email) {\n      setFormData(prev => ({ ...prev, contactEmail: user.email! }))\n    }\n  }, [user, authLoading, router])\n\n  const handleInputChange = (field: keyof FarmFormData, value: string) => {\n    setFormData(prev => ({ ...prev, [field]: value }))\n    setError(null)\n  }\n\n  const handleLocationSelect = (locationData: LocationData) => {\n    setFormData(prev => ({ ...prev, locationData }))\n\n    // Auto-populate province from location data if available\n    const mappedProvince = locationData.addressComponents.administrativeAreaLevel1\n    if (mappedProvince && SOUTH_AFRICAN_PROVINCES.includes(mappedProvince as SouthAfricanProvince)) {\n      setFormData(prev => ({ ...prev, province: mappedProvince as SouthAfricanProvince }))\n    }\n\n    setError(null)\n  }\n\n  const handleImageUpload = (imageUrl: string) => {\n    setUploadedImages(prev => [...prev, imageUrl])\n  }\n\n  const handleImageRemove = (imageUrl: string) => {\n    setUploadedImages(prev => prev.filter(url => url !== imageUrl))\n  }\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setLoading(true)\n    setError(null)\n\n    try {\n      // Validate required fields\n      if (!formData.name || !formData.locationData || !formData.province || !formData.contactEmail) {\n        setError('Please fill in all required fields including location')\n        setLoading(false)\n        return\n      }\n\n      // Validate website URL if provided\n      if (formData.websiteUrl && formData.websiteUrl.trim()) {\n        const urlPattern = /^(https?:\\/\\/)?([\\da-z\\.-]+)\\.([a-z\\.]{2,6})([\\/\\w \\.-]*)*\\/?$/\n        if (!urlPattern.test(formData.websiteUrl.trim())) {\n          setError('Please enter a valid website URL')\n          setLoading(false)\n          return\n        }\n      }\n\n      const farmData: CreateFarmData = {\n        name: formData.name,\n        description: formData.description || undefined,\n        descriptionAfrikaans: formData.descriptionAfrikaans || undefined,\n        locationData: formData.locationData,\n        province: formData.province as SouthAfricanProvince,\n        sizeHectares: formData.sizeHectares ? parseInt(formData.sizeHectares) : undefined,\n        activityTypes: formData.activityTypes,\n        contactEmail: formData.contactEmail,\n        contactPhone: formData.contactPhone || undefined,\n        websiteUrl: formData.websiteUrl || undefined,\n        rules: formData.rules || undefined,\n        rulesAfrikaans: formData.rulesAfrikaans || undefined,\n        pricingInfo: formData.pricingInfo || undefined\n      }\n\n      const farmId = await enhancedFarmService.createFarm(farmData, user!.uid)\n\n      // Handle farm images - they're already uploaded to Firebase Storage\n      // The uploadedImages array contains the Firebase Storage URLs\n      if (uploadedImages.length > 0) {\n        console.log(`Farm created with ${uploadedImages.length} images uploaded to Firebase Storage`)\n        // Images are already stored in Firebase Storage and URLs are in uploadedImages\n        // We could optionally store these URLs in a farm_images subcollection\n        // For now, the images are uploaded and accessible via their URLs\n      }\n\n      router.push(`/farms/${farmId}`)\n    } catch (err) {\n      setError('An unexpected error occurred')\n      console.error('Farm creation error:', err)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  if (authLoading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center bg-earth-100\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-accent-600\"></div>\n      </div>\n    )\n  }\n\n  // TODO: Implement proper role checking with Firebase custom claims\n  // For now, allow all authenticated users to create farms\n  if (!user) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center bg-earth-100\">\n        <div className=\"text-center\">\n          <p className=\"text-earth-600 mb-4\">Please log in to create a farm listing.</p>\n          <Button onClick={() => router.push('/auth/login')}>\n            Go to Login\n          </Button>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"min-h-screen bg-earth-100 py-8\">\n      <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"mb-8\">\n          <h1 className=\"text-3xl font-bold text-earth-900\">Create Farm Listing</h1>\n          <p className=\"text-earth-600 mt-2\">\n            Add your game farm to BvR Safaris and start receiving bookings\n          </p>\n        </div>\n\n        <form onSubmit={handleSubmit} className=\"space-y-8\">\n          {error && (\n            <div className=\"bg-red-50 border border-red-200 rounded-md p-3\">\n              <p className=\"text-red-600 text-sm\">{error}</p>\n            </div>\n          )}\n\n          {/* Basic Information */}\n          <Card>\n            <CardHeader>\n              <CardTitle>Basic Information</CardTitle>\n            </CardHeader>\n            <CardContent className=\"space-y-6\">\n              <div>\n                <label htmlFor=\"name\" className=\"block text-sm font-medium text-earth-700 mb-2\">\n                  Farm Name *\n                </label>\n                <Input\n                  id=\"name\"\n                  type=\"text\"\n                  value={formData.name}\n                  onChange={(e) => handleInputChange('name', e.target.value)}\n                  placeholder=\"Enter your farm name\"\n                  required\n                />\n              </div>\n\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                <div>\n                  <label className=\"block text-sm font-medium text-earth-700 mb-2\">\n                    Farm Location *\n                  </label>\n                  <LocationAutocomplete\n                    value={formData.locationData?.formattedAddress || ''}\n                    onLocationSelect={handleLocationSelect}\n                    placeholder=\"Search for your farm location...\"\n                    required\n                    className=\"w-full\"\n                  />\n                  <p className=\"text-xs text-earth-500 mt-1\">\n                    Start typing to search for your farm's address or nearby landmark\n                  </p>\n                </div>\n\n                <div>\n                  <label htmlFor=\"province\" className=\"block text-sm font-medium text-earth-700 mb-2\">\n                    Province *\n                  </label>\n                  <select\n                    id=\"province\"\n                    value={formData.province}\n                    onChange={(e) => handleInputChange('province', e.target.value)}\n                    className=\"w-full px-3 py-2 border border-earth-300 rounded-md focus:outline-none focus:ring-2 focus:ring-accent-600 focus:border-transparent\"\n                    required\n                  >\n                    <option value=\"\">Select Province</option>\n                    {SOUTH_AFRICAN_PROVINCES.map(province => (\n                      <option key={province} value={province}>{province}</option>\n                    ))}\n                  </select>\n                  <p className=\"text-xs text-earth-500 mt-1\">\n                    Province will be auto-filled from location if available\n                  </p>\n                </div>\n              </div>\n\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                <div>\n                  <label htmlFor=\"sizeHectares\" className=\"block text-sm font-medium text-earth-700 mb-2\">\n                    Size (Hectares)\n                  </label>\n                  <Input\n                    id=\"sizeHectares\"\n                    type=\"number\"\n                    value={formData.sizeHectares}\n                    onChange={(e) => handleInputChange('sizeHectares', e.target.value)}\n                    placeholder=\"e.g., 5000\"\n                  />\n                </div>\n\n                <div>\n                  <label htmlFor=\"activityTypes\" className=\"block text-sm font-medium text-earth-700 mb-2\">\n                    Activity Types *\n                  </label>\n                  <select\n                    id=\"activityTypes\"\n                    value={formData.activityTypes}\n                    onChange={(e) => handleInputChange('activityTypes', e.target.value as ActivityType)}\n                    className=\"w-full px-3 py-2 border border-earth-300 rounded-md focus:outline-none focus:ring-2 focus:ring-accent-600 focus:border-transparent\"\n                    required\n                  >\n                    <option value=\"hunting\">Hunting Only</option>\n                    <option value=\"photo_safari\">Photo Safari Only</option>\n                    <option value=\"both\">Both Hunting & Photo Safari</option>\n                  </select>\n                </div>\n              </div>\n\n              <div>\n                <label htmlFor=\"description\" className=\"block text-sm font-medium text-earth-700 mb-2\">\n                  Description (English)\n                </label>\n                <textarea\n                  id=\"description\"\n                  value={formData.description}\n                  onChange={(e) => handleInputChange('description', e.target.value)}\n                  placeholder=\"Describe your farm, facilities, and what makes it special...\"\n                  rows={4}\n                  className=\"w-full px-3 py-2 border border-earth-300 rounded-md focus:outline-none focus:ring-2 focus:ring-accent-600 focus:border-transparent\"\n                />\n              </div>\n\n              <div>\n                <label htmlFor=\"descriptionAfrikaans\" className=\"block text-sm font-medium text-earth-700 mb-2\">\n                  Description (Afrikaans)\n                </label>\n                <textarea\n                  id=\"descriptionAfrikaans\"\n                  value={formData.descriptionAfrikaans}\n                  onChange={(e) => handleInputChange('descriptionAfrikaans', e.target.value)}\n                  placeholder=\"Beskryf jou plaas, fasiliteite, en wat dit spesiaal maak...\"\n                  rows={4}\n                  className=\"w-full px-3 py-2 border border-earth-300 rounded-md focus:outline-none focus:ring-2 focus:ring-accent-600 focus:border-transparent\"\n                />\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* Contact Information */}\n          <Card>\n            <CardHeader>\n              <CardTitle>Contact Information</CardTitle>\n            </CardHeader>\n            <CardContent className=\"space-y-6\">\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                <div>\n                  <label htmlFor=\"contactEmail\" className=\"block text-sm font-medium text-earth-700 mb-2\">\n                    Contact Email *\n                  </label>\n                  <Input\n                    id=\"contactEmail\"\n                    type=\"email\"\n                    value={formData.contactEmail}\n                    onChange={(e) => handleInputChange('contactEmail', e.target.value)}\n                    placeholder=\"<EMAIL>\"\n                    required\n                  />\n                </div>\n\n                <div>\n                  <label htmlFor=\"contactPhone\" className=\"block text-sm font-medium text-earth-700 mb-2\">\n                    Contact Phone\n                  </label>\n                  <Input\n                    id=\"contactPhone\"\n                    type=\"tel\"\n                    value={formData.contactPhone}\n                    onChange={(e) => handleInputChange('contactPhone', e.target.value)}\n                    placeholder=\"+27 12 345 6789\"\n                  />\n                </div>\n              </div>\n\n              <div>\n                <label htmlFor=\"websiteUrl\" className=\"block text-sm font-medium text-earth-700 mb-2\">\n                  Website URL\n                </label>\n                <Input\n                  id=\"websiteUrl\"\n                  type=\"text\"\n                  value={formData.websiteUrl}\n                  onChange={(e) => handleInputChange('websiteUrl', e.target.value)}\n                  placeholder=\"www.yourfarm.com\"\n                />\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* Additional Information */}\n          <Card>\n            <CardHeader>\n              <CardTitle>Additional Information</CardTitle>\n            </CardHeader>\n            <CardContent className=\"space-y-6\">\n              <div>\n                <label htmlFor=\"pricingInfo\" className=\"block text-sm font-medium text-earth-700 mb-2\">\n                  Pricing Information\n                </label>\n                <textarea\n                  id=\"pricingInfo\"\n                  value={formData.pricingInfo}\n                  onChange={(e) => handleInputChange('pricingInfo', e.target.value)}\n                  placeholder=\"Provide general pricing information or mention 'Contact for pricing'...\"\n                  rows={3}\n                  className=\"w-full px-3 py-2 border border-earth-300 rounded-md focus:outline-none focus:ring-2 focus:ring-accent-600 focus:border-transparent\"\n                />\n              </div>\n\n              <div>\n                <label htmlFor=\"rules\" className=\"block text-sm font-medium text-earth-700 mb-2\">\n                  Rules & Regulations (English)\n                </label>\n                <textarea\n                  id=\"rules\"\n                  value={formData.rules}\n                  onChange={(e) => handleInputChange('rules', e.target.value)}\n                  placeholder=\"List important rules, safety requirements, what to bring, etc...\"\n                  rows={4}\n                  className=\"w-full px-3 py-2 border border-earth-300 rounded-md focus:outline-none focus:ring-2 focus:ring-accent-600 focus:border-transparent\"\n                />\n              </div>\n\n              <div>\n                <label htmlFor=\"rulesAfrikaans\" className=\"block text-sm font-medium text-earth-700 mb-2\">\n                  Rules & Regulations (Afrikaans)\n                </label>\n                <textarea\n                  id=\"rulesAfrikaans\"\n                  value={formData.rulesAfrikaans}\n                  onChange={(e) => handleInputChange('rulesAfrikaans', e.target.value)}\n                  placeholder=\"Lys belangrike reëls, veiligheidsvereistes, wat om te bring, ens...\"\n                  rows={4}\n                  className=\"w-full px-3 py-2 border border-earth-300 rounded-md focus:outline-none focus:ring-2 focus:ring-accent-600 focus:border-transparent\"\n                />\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* Farm Images */}\n          <Card>\n            <CardHeader>\n              <CardTitle>Farm Images</CardTitle>\n            </CardHeader>\n            <CardContent className=\"space-y-6\">\n              <div>\n                <label className=\"block text-sm font-medium text-earth-700 mb-4\">\n                  Upload Farm Photos\n                </label>\n                <p className=\"text-sm text-earth-600 mb-4\">\n                  Add photos of your farm, facilities, wildlife, and accommodations. The first image will be used as the main photo.\n                </p>\n\n                <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n                  {/* Uploaded Images */}\n                  {uploadedImages.map((imageUrl, index) => (\n                    <div key={imageUrl} className=\"relative group\">\n                      <div className=\"relative w-full h-48 rounded-lg border overflow-hidden\">\n                        <Image\n                          src={imageUrl}\n                          alt={`Farm image ${index + 1}`}\n                          fill\n                          className=\"object-cover\"\n                          sizes=\"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw\"\n                        />\n                      </div>\n                      <div className=\"absolute top-2 left-2\">\n                        {index === 0 && (\n                          <span className=\"bg-accent-600 text-white text-xs px-2 py-1 rounded\">\n                            Main Photo\n                          </span>\n                        )}\n                      </div>\n                      <button\n                        type=\"button\"\n                        onClick={() => handleImageRemove(imageUrl)}\n                        className=\"absolute top-2 right-2 bg-red-600 text-white rounded-full w-6 h-6 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity\"\n                      >\n                        ×\n                      </button>\n                    </div>\n                  ))}\n\n                  {/* Upload New Image */}\n                  {uploadedImages.length < 10 && user && (\n                    <ImageUpload\n                      bucket=\"farm-images\"\n                      farmId={user.uid}\n                      onUpload={handleImageUpload}\n                      maxSize={10}\n                      className=\"h-48\"\n                    />\n                  )}\n                </div>\n\n                {uploadedImages.length >= 10 && (\n                  <p className=\"text-sm text-earth-500 mt-2\">\n                    Maximum of 10 images allowed. Remove an image to add more.\n                  </p>\n                )}\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* Submit Buttons */}\n          <div className=\"flex gap-4\">\n            <Button\n              type=\"submit\"\n              variant=\"primary\"\n              isLoading={loading}\n              className=\"flex-1\"\n            >\n              {loading ? 'Creating Farm...' : 'Create Farm Listing'}\n            </Button>\n\n            <Button\n              type=\"button\"\n              variant=\"outline\"\n              onClick={() => router.push('/dashboard')}\n              className=\"flex-1\"\n            >\n              Cancel\n            </Button>\n          </div>\n        </form>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AAdA;;;;;;;;;;;;;AAkCe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,SAAS,WAAW,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,UAAO,AAAD;IAC7C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAEjE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB;QACrD,MAAM;QACN,aAAa;QACb,sBAAsB;QACtB,cAAc;QACd,UAAU;QACV,cAAc;QACd,eAAe;QACf,cAAc;QACd,cAAc;QACd,YAAY;QACZ,OAAO;QACP,gBAAgB;QAChB,aAAa;IACf;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,eAAe,CAAC,MAAM;YACzB,OAAO,IAAI,CAAC;YACZ;QACF;QAEA,wEAAwE;QACxE,yDAAyD;QAEzD,2CAA2C;QAC3C,IAAI,MAAM,OAAO;YACf,YAAY,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,cAAc,KAAK,KAAK;gBAAE,CAAC;QAC7D;IACF,GAAG;QAAC;QAAM;QAAa;KAAO;IAE9B,MAAM,oBAAoB,CAAC,OAA2B;QACpD,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAM,CAAC;QAChD,SAAS;IACX;IAEA,MAAM,uBAAuB,CAAC;QAC5B,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE;YAAa,CAAC;QAE9C,yDAAyD;QACzD,MAAM,iBAAiB,aAAa,iBAAiB,CAAC,wBAAwB;QAC9E,IAAI,kBAAkB,uHAAA,CAAA,0BAAuB,CAAC,QAAQ,CAAC,iBAAyC;YAC9F,YAAY,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,UAAU;gBAAuC,CAAC;QACpF;QAEA,SAAS;IACX;IAEA,MAAM,oBAAoB,CAAC;QACzB,kBAAkB,CAAA,OAAQ;mBAAI;gBAAM;aAAS;IAC/C;IAEA,MAAM,oBAAoB,CAAC;QACzB,kBAAkB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,MAAO,QAAQ;IACvD;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,WAAW;QACX,SAAS;QAET,IAAI;YACF,2BAA2B;YAC3B,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,YAAY,IAAI,CAAC,SAAS,QAAQ,IAAI,CAAC,SAAS,YAAY,EAAE;gBAC5F,SAAS;gBACT,WAAW;gBACX;YACF;YAEA,mCAAmC;YACnC,IAAI,SAAS,UAAU,IAAI,SAAS,UAAU,CAAC,IAAI,IAAI;gBACrD,MAAM,aAAa;gBACnB,IAAI,CAAC,WAAW,IAAI,CAAC,SAAS,UAAU,CAAC,IAAI,KAAK;oBAChD,SAAS;oBACT,WAAW;oBACX;gBACF;YACF;YAEA,MAAM,WAA2B;gBAC/B,MAAM,SAAS,IAAI;gBACnB,aAAa,SAAS,WAAW,IAAI;gBACrC,sBAAsB,SAAS,oBAAoB,IAAI;gBACvD,cAAc,SAAS,YAAY;gBACnC,UAAU,SAAS,QAAQ;gBAC3B,cAAc,SAAS,YAAY,GAAG,SAAS,SAAS,YAAY,IAAI;gBACxE,eAAe,SAAS,aAAa;gBACrC,cAAc,SAAS,YAAY;gBACnC,cAAc,SAAS,YAAY,IAAI;gBACvC,YAAY,SAAS,UAAU,IAAI;gBACnC,OAAO,SAAS,KAAK,IAAI;gBACzB,gBAAgB,SAAS,cAAc,IAAI;gBAC3C,aAAa,SAAS,WAAW,IAAI;YACvC;YAEA,MAAM,SAAS,MAAM,qIAAA,CAAA,sBAAmB,CAAC,UAAU,CAAC,UAAU,KAAM,GAAG;YAEvE,oEAAoE;YACpE,8DAA8D;YAC9D,IAAI,eAAe,MAAM,GAAG,GAAG;gBAC7B,QAAQ,GAAG,CAAC,CAAC,kBAAkB,EAAE,eAAe,MAAM,CAAC,oCAAoC,CAAC;YAC5F,+EAA+E;YAC/E,sEAAsE;YACtE,iEAAiE;YACnE;YAEA,OAAO,IAAI,CAAC,CAAC,OAAO,EAAE,QAAQ;QAChC,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,QAAQ,KAAK,CAAC,wBAAwB;QACxC,SAAU;YACR,WAAW;QACb;IACF;IAEA,IAAI,aAAa;QACf,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,mEAAmE;IACnE,yDAAyD;IACzD,IAAI,CAAC,MAAM;QACT,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAE,WAAU;kCAAsB;;;;;;kCACnC,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAS,IAAM,OAAO,IAAI,CAAC;kCAAgB;;;;;;;;;;;;;;;;;IAM3D;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAoC;;;;;;sCAClD,8OAAC;4BAAE,WAAU;sCAAsB;;;;;;;;;;;;8BAKrC,8OAAC;oBAAK,UAAU;oBAAc,WAAU;;wBACrC,uBACC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;sCAKzC,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;8CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;kDAAC;;;;;;;;;;;8CAEb,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,8OAAC;;8DACC,8OAAC;oDAAM,SAAQ;oDAAO,WAAU;8DAAgD;;;;;;8DAGhF,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,MAAK;oDACL,OAAO,SAAS,IAAI;oDACpB,UAAU,CAAC,IAAM,kBAAkB,QAAQ,EAAE,MAAM,CAAC,KAAK;oDACzD,aAAY;oDACZ,QAAQ;;;;;;;;;;;;sDAIZ,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAM,WAAU;sEAAgD;;;;;;sEAGjE,8OAAC,gJAAA,CAAA,uBAAoB;4DACnB,OAAO,SAAS,YAAY,EAAE,oBAAoB;4DAClD,kBAAkB;4DAClB,aAAY;4DACZ,QAAQ;4DACR,WAAU;;;;;;sEAEZ,8OAAC;4DAAE,WAAU;sEAA8B;;;;;;;;;;;;8DAK7C,8OAAC;;sEACC,8OAAC;4DAAM,SAAQ;4DAAW,WAAU;sEAAgD;;;;;;sEAGpF,8OAAC;4DACC,IAAG;4DACH,OAAO,SAAS,QAAQ;4DACxB,UAAU,CAAC,IAAM,kBAAkB,YAAY,EAAE,MAAM,CAAC,KAAK;4DAC7D,WAAU;4DACV,QAAQ;;8EAER,8OAAC;oEAAO,OAAM;8EAAG;;;;;;gEAChB,uHAAA,CAAA,0BAAuB,CAAC,GAAG,CAAC,CAAA,yBAC3B,8OAAC;wEAAsB,OAAO;kFAAW;uEAA5B;;;;;;;;;;;sEAGjB,8OAAC;4DAAE,WAAU;sEAA8B;;;;;;;;;;;;;;;;;;sDAM/C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAM,SAAQ;4DAAe,WAAU;sEAAgD;;;;;;sEAGxF,8OAAC,iIAAA,CAAA,QAAK;4DACJ,IAAG;4DACH,MAAK;4DACL,OAAO,SAAS,YAAY;4DAC5B,UAAU,CAAC,IAAM,kBAAkB,gBAAgB,EAAE,MAAM,CAAC,KAAK;4DACjE,aAAY;;;;;;;;;;;;8DAIhB,8OAAC;;sEACC,8OAAC;4DAAM,SAAQ;4DAAgB,WAAU;sEAAgD;;;;;;sEAGzF,8OAAC;4DACC,IAAG;4DACH,OAAO,SAAS,aAAa;4DAC7B,UAAU,CAAC,IAAM,kBAAkB,iBAAiB,EAAE,MAAM,CAAC,KAAK;4DAClE,WAAU;4DACV,QAAQ;;8EAER,8OAAC;oEAAO,OAAM;8EAAU;;;;;;8EACxB,8OAAC;oEAAO,OAAM;8EAAe;;;;;;8EAC7B,8OAAC;oEAAO,OAAM;8EAAO;;;;;;;;;;;;;;;;;;;;;;;;sDAK3B,8OAAC;;8DACC,8OAAC;oDAAM,SAAQ;oDAAc,WAAU;8DAAgD;;;;;;8DAGvF,8OAAC;oDACC,IAAG;oDACH,OAAO,SAAS,WAAW;oDAC3B,UAAU,CAAC,IAAM,kBAAkB,eAAe,EAAE,MAAM,CAAC,KAAK;oDAChE,aAAY;oDACZ,MAAM;oDACN,WAAU;;;;;;;;;;;;sDAId,8OAAC;;8DACC,8OAAC;oDAAM,SAAQ;oDAAuB,WAAU;8DAAgD;;;;;;8DAGhG,8OAAC;oDACC,IAAG;oDACH,OAAO,SAAS,oBAAoB;oDACpC,UAAU,CAAC,IAAM,kBAAkB,wBAAwB,EAAE,MAAM,CAAC,KAAK;oDACzE,aAAY;oDACZ,MAAM;oDACN,WAAU;;;;;;;;;;;;;;;;;;;;;;;;sCAOlB,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;8CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;kDAAC;;;;;;;;;;;8CAEb,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAM,SAAQ;4DAAe,WAAU;sEAAgD;;;;;;sEAGxF,8OAAC,iIAAA,CAAA,QAAK;4DACJ,IAAG;4DACH,MAAK;4DACL,OAAO,SAAS,YAAY;4DAC5B,UAAU,CAAC,IAAM,kBAAkB,gBAAgB,EAAE,MAAM,CAAC,KAAK;4DACjE,aAAY;4DACZ,QAAQ;;;;;;;;;;;;8DAIZ,8OAAC;;sEACC,8OAAC;4DAAM,SAAQ;4DAAe,WAAU;sEAAgD;;;;;;sEAGxF,8OAAC,iIAAA,CAAA,QAAK;4DACJ,IAAG;4DACH,MAAK;4DACL,OAAO,SAAS,YAAY;4DAC5B,UAAU,CAAC,IAAM,kBAAkB,gBAAgB,EAAE,MAAM,CAAC,KAAK;4DACjE,aAAY;;;;;;;;;;;;;;;;;;sDAKlB,8OAAC;;8DACC,8OAAC;oDAAM,SAAQ;oDAAa,WAAU;8DAAgD;;;;;;8DAGtF,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,MAAK;oDACL,OAAO,SAAS,UAAU;oDAC1B,UAAU,CAAC,IAAM,kBAAkB,cAAc,EAAE,MAAM,CAAC,KAAK;oDAC/D,aAAY;;;;;;;;;;;;;;;;;;;;;;;;sCAOpB,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;8CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;kDAAC;;;;;;;;;;;8CAEb,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,8OAAC;;8DACC,8OAAC;oDAAM,SAAQ;oDAAc,WAAU;8DAAgD;;;;;;8DAGvF,8OAAC;oDACC,IAAG;oDACH,OAAO,SAAS,WAAW;oDAC3B,UAAU,CAAC,IAAM,kBAAkB,eAAe,EAAE,MAAM,CAAC,KAAK;oDAChE,aAAY;oDACZ,MAAM;oDACN,WAAU;;;;;;;;;;;;sDAId,8OAAC;;8DACC,8OAAC;oDAAM,SAAQ;oDAAQ,WAAU;8DAAgD;;;;;;8DAGjF,8OAAC;oDACC,IAAG;oDACH,OAAO,SAAS,KAAK;oDACrB,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;oDAC1D,aAAY;oDACZ,MAAM;oDACN,WAAU;;;;;;;;;;;;sDAId,8OAAC;;8DACC,8OAAC;oDAAM,SAAQ;oDAAiB,WAAU;8DAAgD;;;;;;8DAG1F,8OAAC;oDACC,IAAG;oDACH,OAAO,SAAS,cAAc;oDAC9B,UAAU,CAAC,IAAM,kBAAkB,kBAAkB,EAAE,MAAM,CAAC,KAAK;oDACnE,aAAY;oDACZ,MAAM;oDACN,WAAU;;;;;;;;;;;;;;;;;;;;;;;;sCAOlB,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;8CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;kDAAC;;;;;;;;;;;8CAEb,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAAgD;;;;;;0DAGjE,8OAAC;gDAAE,WAAU;0DAA8B;;;;;;0DAI3C,8OAAC;gDAAI,WAAU;;oDAEZ,eAAe,GAAG,CAAC,CAAC,UAAU,sBAC7B,8OAAC;4DAAmB,WAAU;;8EAC5B,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;wEACJ,KAAK;wEACL,KAAK,CAAC,WAAW,EAAE,QAAQ,GAAG;wEAC9B,IAAI;wEACJ,WAAU;wEACV,OAAM;;;;;;;;;;;8EAGV,8OAAC;oEAAI,WAAU;8EACZ,UAAU,mBACT,8OAAC;wEAAK,WAAU;kFAAqD;;;;;;;;;;;8EAKzE,8OAAC;oEACC,MAAK;oEACL,SAAS,IAAM,kBAAkB;oEACjC,WAAU;8EACX;;;;;;;2DArBO;;;;;oDA4BX,eAAe,MAAM,GAAG,MAAM,sBAC7B,8OAAC,sIAAA,CAAA,cAAW;wDACV,QAAO;wDACP,QAAQ,KAAK,GAAG;wDAChB,UAAU;wDACV,SAAS;wDACT,WAAU;;;;;;;;;;;;4CAKf,eAAe,MAAM,IAAI,oBACxB,8OAAC;gDAAE,WAAU;0DAA8B;;;;;;;;;;;;;;;;;;;;;;;sCASnD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAQ;oCACR,WAAW;oCACX,WAAU;8CAET,UAAU,qBAAqB;;;;;;8CAGlC,8OAAC,kIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAQ;oCACR,SAAS,IAAM,OAAO,IAAI,CAAC;oCAC3B,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb", "debugId": null}}]}