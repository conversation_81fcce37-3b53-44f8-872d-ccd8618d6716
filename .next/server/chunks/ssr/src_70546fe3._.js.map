{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/src/personal/bvr-safaris/src/components/ui/Card.tsx"], "sourcesContent": ["import { HTMLAttributes, forwardRef } from 'react'\nimport Image from 'next/image'\nimport { cn } from '@/lib/utils'\n\nexport interface CardProps extends HTMLAttributes<HTMLDivElement> {\n  hover?: boolean\n}\n\nconst Card = forwardRef<HTMLDivElement, CardProps>(\n  ({ className, hover = true, children, ...props }, ref) => {\n    return (\n      <div\n        ref={ref}\n        className={cn(\n          `\n          bg-white rounded-[var(--radius-lg)] shadow-[var(--shadow-md)] \n          overflow-hidden transition-all duration-300\n          `,\n          hover && 'hover:transform hover:-translate-y-1 hover:shadow-[var(--shadow-xl)]',\n          className\n        )}\n        {...props}\n      >\n        {children}\n      </div>\n    )\n  }\n)\n\nCard.displayName = 'Card'\n\nconst CardContent = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn('p-[var(--space-lg)]', className)}\n      {...props}\n    />\n  )\n)\n\nCardContent.displayName = 'CardContent'\n\nconst CardHeader = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn('px-[var(--space-lg)] pt-[var(--space-lg)]', className)}\n      {...props}\n    />\n  )\n)\n\nCardHeader.displayName = 'CardHeader'\n\nconst CardTitle = forwardRef<HTMLHeadingElement, HTMLAttributes<HTMLHeadingElement>>(\n  ({ className, ...props }, ref) => (\n    <h3\n      ref={ref}\n      className={cn(\n        'text-xl font-bold mb-2 text-[var(--primary-brown)] font-[var(--font-display)]',\n        className\n      )}\n      {...props}\n    />\n  )\n)\n\nCardTitle.displayName = 'CardTitle'\n\nconst CardImage = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement> & {\n  src?: string\n  alt?: string\n  children?: React.ReactNode\n}>(\n  ({ className, src, alt, children, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(\n        'relative w-full h-48 bg-gradient-to-br from-[var(--primary-green)] to-[var(--secondary-sunset)] flex items-center justify-center text-white text-5xl',\n        className\n      )}\n      {...props}\n    >\n      {src ? (\n        <Image src={src} alt={alt || ''} fill className=\"object-cover\" />\n      ) : (\n        children\n      )}\n    </div>\n  )\n)\n\nCardImage.displayName = 'CardImage'\n\nexport { Card, CardContent, CardHeader, CardTitle, CardImage }"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;AACA;;;;;AAMA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACpB,CAAC,EAAE,SAAS,EAAE,QAAQ,IAAI,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IAChD,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,CAAC;;;UAGD,CAAC,EACD,SAAS,wEACT;QAED,GAAG,KAAK;kBAER;;;;;;AAGP;AAGF,KAAK,WAAW,GAAG;AAEnB,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAKf,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAC1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAKf,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACzB,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iFACA;QAED,GAAG,KAAK;;;;;;AAKf,UAAU,WAAW,GAAG;AAExB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAKzB,CAAC,EAAE,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAC5C,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wJACA;QAED,GAAG,KAAK;kBAER,oBACC,8OAAC,6HAAA,CAAA,UAAK;YAAC,KAAK;YAAK,KAAK,OAAO;YAAI,IAAI;YAAC,WAAU;;;;;mBAEhD;;;;;;AAMR,UAAU,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 95, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/src/personal/bvr-safaris/src/components/ui/LoadingSpinner.tsx"], "sourcesContent": ["import { cn } from '@/lib/utils'\n\ninterface LoadingSpinnerProps {\n  size?: 'sm' | 'md' | 'lg'\n  className?: string\n}\n\nexport function LoadingSpinner({ size = 'md', className }: LoadingSpinnerProps) {\n  const sizeClasses = {\n    sm: 'w-4 h-4',\n    md: 'w-6 h-6',\n    lg: 'w-12 h-12'\n  }\n\n  return (\n    <div className={cn('flex items-center justify-center', className)}>\n      <div\n        className={cn(\n          'animate-spin rounded-full border-2 border-white border-t-transparent',\n          sizeClasses[size]\n        )}\n      />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAOO,SAAS,eAAe,EAAE,OAAO,IAAI,EAAE,SAAS,EAAuB;IAC5E,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,oCAAoC;kBACrD,cAAA,8OAAC;YACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wEACA,WAAW,CAAC,KAAK;;;;;;;;;;;AAK3B", "debugId": null}}, {"offset": {"line": 129, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/src/personal/bvr-safaris/src/lib/firebase/firestore.ts"], "sourcesContent": ["import {\n  collection,\n  doc,\n  getDoc,\n  getDocs,\n  addDoc,\n  setDoc,\n  updateDoc,\n  deleteDoc,\n  query,\n  where,\n  orderBy,\n  limit,\n  Timestamp,\n  DocumentSnapshot,\n  QueryDocumentSnapshot,\n  DocumentData,\n  Query\n} from 'firebase/firestore'\nimport { db } from './client'\nimport {\n  UserProfile,\n  GameFarm,\n  Booking,\n  Review,\n  FarmImage,\n  GameSpecies,\n  FarmAmenity,\n  FirestoreDocument\n} from '@/lib/types/firestore'\n\n// Helper function to convert Firestore document to typed object\nexport function docToData<T extends FirestoreDocument>(\n  doc: QueryDocumentSnapshot<DocumentData> | DocumentSnapshot<DocumentData>\n): T | null {\n  if (!doc.exists()) return null\n  \n  const data = doc.data()\n  return {\n    id: doc.id,\n    ...data,\n    // Convert Firestore Timestamps to Date objects for easier handling\n    createdAt: data?.createdAt?.toDate?.() || data?.createdAt,\n    updatedAt: data?.updatedAt?.toDate?.() || data?.updatedAt,\n  } as T\n}\n\n\n\n// User Profile operations\nexport const userProfileService = {\n  async get(userId: string): Promise<UserProfile | null> {\n    const docRef = doc(db, 'users', userId)\n    const docSnap = await getDoc(docRef)\n    return docToData<UserProfile>(docSnap)\n  },\n\n  async create(userId: string, data: Omit<UserProfile, 'id' | 'createdAt' | 'updatedAt'>): Promise<void> {\n    const docRef = doc(db, 'users', userId)\n    const now = new Date()\n    await setDoc(docRef, {\n      ...data,\n      createdAt: Timestamp.fromDate(now),\n      updatedAt: Timestamp.fromDate(now)\n    })\n  },\n\n  async update(userId: string, data: Partial<UserProfile>): Promise<void> {\n    const docRef = doc(db, 'users', userId)\n    await updateDoc(docRef, {\n      ...data,\n      updatedAt: Timestamp.now()\n    })\n  }\n}\n\n// Game Farm operations\nexport const farmService = {\n  async getAll(filters?: {\n    isActive?: boolean\n    featured?: boolean\n    province?: string\n    activityType?: string\n    limit?: number\n  }): Promise<GameFarm[]> {\n    let q: Query<DocumentData> = collection(db, 'farms')\n\n    if (filters?.isActive !== undefined) {\n      q = query(q, where('isActive', '==', filters.isActive))\n    }\n    if (filters?.featured !== undefined) {\n      q = query(q, where('featured', '==', filters.featured))\n    }\n    if (filters?.province) {\n      q = query(q, where('province', '==', filters.province))\n    }\n    if (filters?.activityType) {\n      q = query(q, where('activityTypes', '==', filters.activityType))\n    }\n\n    q = query(q, orderBy('createdAt', 'desc'))\n\n    if (filters?.limit) {\n      q = query(q, limit(filters.limit))\n    }\n\n    const querySnapshot = await getDocs(q)\n    return querySnapshot.docs.map(doc => docToData<GameFarm>(doc)!).filter(Boolean)\n  },\n\n  async get(farmId: string): Promise<GameFarm | null> {\n    const docRef = doc(db, 'farms', farmId)\n    const docSnap = await getDoc(docRef)\n    return docToData<GameFarm>(docSnap)\n  },\n\n  async getByOwner(ownerId: string): Promise<GameFarm[]> {\n    const q = query(\n      collection(db, 'farms'),\n      where('ownerId', '==', ownerId),\n      orderBy('createdAt', 'desc')\n    )\n    const querySnapshot = await getDocs(q)\n    return querySnapshot.docs.map(doc => docToData<GameFarm>(doc)!).filter(Boolean)\n  },\n\n  async getActive(limitCount?: number): Promise<GameFarm[]> {\n    let q = query(\n      collection(db, 'farms'),\n      where('isActive', '==', true),\n      orderBy('createdAt', 'desc')\n    )\n\n    if (limitCount) {\n      q = query(q, limit(limitCount))\n    }\n\n    const querySnapshot = await getDocs(q)\n    return querySnapshot.docs.map(doc => docToData<GameFarm>(doc)!).filter(Boolean)\n  },\n\n  async create(data: Omit<GameFarm, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {\n    const now = new Date()\n    const docRef = await addDoc(collection(db, 'farms'), {\n      ...data,\n      createdAt: Timestamp.fromDate(now),\n      updatedAt: Timestamp.fromDate(now)\n    })\n    return docRef.id\n  },\n\n  async update(farmId: string, data: Partial<GameFarm>): Promise<void> {\n    const docRef = doc(db, 'farms', farmId)\n    await updateDoc(docRef, {\n      ...data,\n      updatedAt: Timestamp.now()\n    })\n  },\n\n  async delete(farmId: string): Promise<void> {\n    const docRef = doc(db, 'farms', farmId)\n    await deleteDoc(docRef)\n  },\n\n  // Add farm images to subcollection\n  async addImage(farmId: string, imageData: Omit<FarmImage, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {\n    const now = new Date()\n    const docRef = await addDoc(collection(db, 'farms', farmId, 'images'), {\n      ...imageData,\n      createdAt: Timestamp.fromDate(now),\n      updatedAt: Timestamp.fromDate(now)\n    })\n    return docRef.id\n  },\n\n  // Get farm images\n  async getImages(farmId: string): Promise<FarmImage[]> {\n    const q = query(\n      collection(db, 'farms', farmId, 'images'),\n      orderBy('displayOrder', 'asc'),\n      orderBy('createdAt', 'asc')\n    )\n    const querySnapshot = await getDocs(q)\n    return querySnapshot.docs.map(doc => docToData<FarmImage>(doc)!).filter(Boolean)\n  },\n\n  // Delete farm image\n  async deleteImage(farmId: string, imageId: string): Promise<void> {\n    const docRef = doc(db, 'farms', farmId, 'images', imageId)\n    await deleteDoc(docRef)\n  }\n}\n\n// Booking operations\nexport const bookingService = {\n  async getAll(filters?: {\n    hunterId?: string\n    farmId?: string\n    status?: string\n    limit?: number\n  }): Promise<Booking[]> {\n    let q: Query<DocumentData> = collection(db, 'bookings')\n\n    if (filters?.hunterId) {\n      q = query(q, where('hunterId', '==', filters.hunterId))\n    }\n    if (filters?.farmId) {\n      q = query(q, where('farmId', '==', filters.farmId))\n    }\n    if (filters?.status) {\n      q = query(q, where('status', '==', filters.status))\n    }\n\n    q = query(q, orderBy('createdAt', 'desc'))\n\n    if (filters?.limit) {\n      q = query(q, limit(filters.limit))\n    }\n\n    const querySnapshot = await getDocs(q)\n    return querySnapshot.docs.map(doc => docToData<Booking>(doc)!).filter(Boolean)\n  },\n\n  async get(bookingId: string): Promise<Booking | null> {\n    const docRef = doc(db, 'bookings', bookingId)\n    const docSnap = await getDoc(docRef)\n    return docToData<Booking>(docSnap)\n  },\n\n  async create(data: Omit<Booking, 'id' | 'createdAt' | 'updatedAt' | 'bookingReference'>): Promise<string> {\n    const now = new Date()\n    const bookingReference = `BVR-${now.getFullYear()}${(now.getMonth() + 1).toString().padStart(2, '0')}${now.getDate().toString().padStart(2, '0')}-${Math.random().toString(36).substring(2, 8).toUpperCase()}`\n    \n    const docRef = await addDoc(collection(db, 'bookings'), {\n      ...data,\n      bookingReference,\n      createdAt: Timestamp.fromDate(now),\n      updatedAt: Timestamp.fromDate(now)\n    })\n    return docRef.id\n  },\n\n  async update(bookingId: string, data: Partial<Booking>): Promise<void> {\n    const docRef = doc(db, 'bookings', bookingId)\n    await updateDoc(docRef, {\n      ...data,\n      updatedAt: Timestamp.now()\n    })\n  }\n}\n\n// Review operations (subcollection under farms)\nexport const reviewService = {\n  async getByFarm(farmId: string): Promise<Review[]> {\n    const q = query(\n      collection(db, 'farms', farmId, 'reviews'),\n      where('isPublic', '==', true),\n      orderBy('createdAt', 'desc')\n    )\n    const querySnapshot = await getDocs(q)\n    return querySnapshot.docs.map(doc => docToData<Review>(doc)!).filter(Boolean)\n  },\n\n  async create(farmId: string, data: Omit<Review, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {\n    const now = new Date()\n    const docRef = await addDoc(collection(db, 'farms', farmId, 'reviews'), {\n      ...data,\n      createdAt: Timestamp.fromDate(now),\n      updatedAt: Timestamp.fromDate(now)\n    })\n    return docRef.id\n  },\n\n  async update(farmId: string, reviewId: string, data: Partial<Review>): Promise<void> {\n    const docRef = doc(db, 'farms', farmId, 'reviews', reviewId)\n    await updateDoc(docRef, {\n      ...data,\n      updatedAt: Timestamp.now()\n    })\n  }\n}\n\n// Species operations\nexport const speciesService = {\n  async getAll(): Promise<GameSpecies[]> {\n    const q = query(collection(db, 'species'), orderBy('name'))\n    const querySnapshot = await getDocs(q)\n    return querySnapshot.docs.map(doc => docToData<GameSpecies>(doc)!).filter(Boolean)\n  },\n\n  async get(speciesId: string): Promise<GameSpecies | null> {\n    const docRef = doc(db, 'species', speciesId)\n    const docSnap = await getDoc(docRef)\n    return docToData<GameSpecies>(docSnap)\n  }\n}\n\n// Amenities operations\nexport const amenityService = {\n  async getAll(): Promise<FarmAmenity[]> {\n    const q = query(collection(db, 'amenities'), orderBy('name'))\n    const querySnapshot = await getDocs(q)\n    return querySnapshot.docs.map(doc => docToData<FarmAmenity>(doc)!).filter(Boolean)\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAAA;AAmBA;AAAA;;;AAaO,SAAS,UACd,GAAyE;IAEzE,IAAI,CAAC,IAAI,MAAM,IAAI,OAAO;IAE1B,MAAM,OAAO,IAAI,IAAI;IACrB,OAAO;QACL,IAAI,IAAI,EAAE;QACV,GAAG,IAAI;QACP,mEAAmE;QACnE,WAAW,MAAM,WAAW,cAAc,MAAM;QAChD,WAAW,MAAM,WAAW,cAAc,MAAM;IAClD;AACF;AAKO,MAAM,qBAAqB;IAChC,MAAM,KAAI,MAAc;QACtB,MAAM,SAAS,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,SAAS;QAChC,MAAM,UAAU,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE;QAC7B,OAAO,UAAuB;IAChC;IAEA,MAAM,QAAO,MAAc,EAAE,IAAyD;QACpF,MAAM,SAAS,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,SAAS;QAChC,MAAM,MAAM,IAAI;QAChB,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE,QAAQ;YACnB,GAAG,IAAI;YACP,WAAW,iKAAA,CAAA,YAAS,CAAC,QAAQ,CAAC;YAC9B,WAAW,iKAAA,CAAA,YAAS,CAAC,QAAQ,CAAC;QAChC;IACF;IAEA,MAAM,QAAO,MAAc,EAAE,IAA0B;QACrD,MAAM,SAAS,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,SAAS;QAChC,MAAM,CAAA,GAAA,iKAAA,CAAA,YAAS,AAAD,EAAE,QAAQ;YACtB,GAAG,IAAI;YACP,WAAW,iKAAA,CAAA,YAAS,CAAC,GAAG;QAC1B;IACF;AACF;AAGO,MAAM,cAAc;IACzB,MAAM,QAAO,OAMZ;QACC,IAAI,IAAyB,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE;QAE5C,IAAI,SAAS,aAAa,WAAW;YACnC,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,YAAY,MAAM,QAAQ,QAAQ;QACvD;QACA,IAAI,SAAS,aAAa,WAAW;YACnC,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,YAAY,MAAM,QAAQ,QAAQ;QACvD;QACA,IAAI,SAAS,UAAU;YACrB,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,YAAY,MAAM,QAAQ,QAAQ;QACvD;QACA,IAAI,SAAS,cAAc;YACzB,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,iBAAiB,MAAM,QAAQ,YAAY;QAChE;QAEA,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;QAElC,IAAI,SAAS,OAAO;YAClB,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,QAAQ,KAAK;QAClC;QAEA,MAAM,gBAAgB,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;QACpC,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,UAAoB,MAAO,MAAM,CAAC;IACzE;IAEA,MAAM,KAAI,MAAc;QACtB,MAAM,SAAS,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,SAAS;QAChC,MAAM,UAAU,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE;QAC7B,OAAO,UAAoB;IAC7B;IAEA,MAAM,YAAW,OAAe;QAC9B,MAAM,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EACZ,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,UACf,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,WAAW,MAAM,UACvB,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;QAEvB,MAAM,gBAAgB,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;QACpC,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,UAAoB,MAAO,MAAM,CAAC;IACzE;IAEA,MAAM,WAAU,UAAmB;QACjC,IAAI,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EACV,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,UACf,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,YAAY,MAAM,OACxB,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;QAGvB,IAAI,YAAY;YACd,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE;QACrB;QAEA,MAAM,gBAAgB,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;QACpC,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,UAAoB,MAAO,MAAM,CAAC;IACzE;IAEA,MAAM,QAAO,IAAsD;QACjE,MAAM,MAAM,IAAI;QAChB,MAAM,SAAS,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,UAAU;YACnD,GAAG,IAAI;YACP,WAAW,iKAAA,CAAA,YAAS,CAAC,QAAQ,CAAC;YAC9B,WAAW,iKAAA,CAAA,YAAS,CAAC,QAAQ,CAAC;QAChC;QACA,OAAO,OAAO,EAAE;IAClB;IAEA,MAAM,QAAO,MAAc,EAAE,IAAuB;QAClD,MAAM,SAAS,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,SAAS;QAChC,MAAM,CAAA,GAAA,iKAAA,CAAA,YAAS,AAAD,EAAE,QAAQ;YACtB,GAAG,IAAI;YACP,WAAW,iKAAA,CAAA,YAAS,CAAC,GAAG;QAC1B;IACF;IAEA,MAAM,QAAO,MAAc;QACzB,MAAM,SAAS,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,SAAS;QAChC,MAAM,CAAA,GAAA,iKAAA,CAAA,YAAS,AAAD,EAAE;IAClB;IAEA,mCAAmC;IACnC,MAAM,UAAS,MAAc,EAAE,SAA4D;QACzF,MAAM,MAAM,IAAI;QAChB,MAAM,SAAS,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,SAAS,QAAQ,WAAW;YACrE,GAAG,SAAS;YACZ,WAAW,iKAAA,CAAA,YAAS,CAAC,QAAQ,CAAC;YAC9B,WAAW,iKAAA,CAAA,YAAS,CAAC,QAAQ,CAAC;QAChC;QACA,OAAO,OAAO,EAAE;IAClB;IAEA,kBAAkB;IAClB,MAAM,WAAU,MAAc;QAC5B,MAAM,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EACZ,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,SAAS,QAAQ,WAChC,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,gBAAgB,QACxB,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;QAEvB,MAAM,gBAAgB,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;QACpC,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,UAAqB,MAAO,MAAM,CAAC;IAC1E;IAEA,oBAAoB;IACpB,MAAM,aAAY,MAAc,EAAE,OAAe;QAC/C,MAAM,SAAS,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,SAAS,QAAQ,UAAU;QAClD,MAAM,CAAA,GAAA,iKAAA,CAAA,YAAS,AAAD,EAAE;IAClB;AACF;AAGO,MAAM,iBAAiB;IAC5B,MAAM,QAAO,OAKZ;QACC,IAAI,IAAyB,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE;QAE5C,IAAI,SAAS,UAAU;YACrB,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,YAAY,MAAM,QAAQ,QAAQ;QACvD;QACA,IAAI,SAAS,QAAQ;YACnB,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,UAAU,MAAM,QAAQ,MAAM;QACnD;QACA,IAAI,SAAS,QAAQ;YACnB,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,UAAU,MAAM,QAAQ,MAAM;QACnD;QAEA,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;QAElC,IAAI,SAAS,OAAO;YAClB,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,QAAQ,KAAK;QAClC;QAEA,MAAM,gBAAgB,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;QACpC,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,UAAmB,MAAO,MAAM,CAAC;IACxE;IAEA,MAAM,KAAI,SAAiB;QACzB,MAAM,SAAS,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,YAAY;QACnC,MAAM,UAAU,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE;QAC7B,OAAO,UAAmB;IAC5B;IAEA,MAAM,QAAO,IAA0E;QACrF,MAAM,MAAM,IAAI;QAChB,MAAM,mBAAmB,CAAC,IAAI,EAAE,IAAI,WAAW,KAAK,CAAC,IAAI,QAAQ,KAAK,CAAC,EAAE,QAAQ,GAAG,QAAQ,CAAC,GAAG,OAAO,IAAI,OAAO,GAAG,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG,GAAG,WAAW,IAAI;QAE9M,MAAM,SAAS,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,aAAa;YACtD,GAAG,IAAI;YACP;YACA,WAAW,iKAAA,CAAA,YAAS,CAAC,QAAQ,CAAC;YAC9B,WAAW,iKAAA,CAAA,YAAS,CAAC,QAAQ,CAAC;QAChC;QACA,OAAO,OAAO,EAAE;IAClB;IAEA,MAAM,QAAO,SAAiB,EAAE,IAAsB;QACpD,MAAM,SAAS,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,YAAY;QACnC,MAAM,CAAA,GAAA,iKAAA,CAAA,YAAS,AAAD,EAAE,QAAQ;YACtB,GAAG,IAAI;YACP,WAAW,iKAAA,CAAA,YAAS,CAAC,GAAG;QAC1B;IACF;AACF;AAGO,MAAM,gBAAgB;IAC3B,MAAM,WAAU,MAAc;QAC5B,MAAM,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EACZ,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,SAAS,QAAQ,YAChC,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,YAAY,MAAM,OACxB,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;QAEvB,MAAM,gBAAgB,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;QACpC,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,UAAkB,MAAO,MAAM,CAAC;IACvE;IAEA,MAAM,QAAO,MAAc,EAAE,IAAoD;QAC/E,MAAM,MAAM,IAAI;QAChB,MAAM,SAAS,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,SAAS,QAAQ,YAAY;YACtE,GAAG,IAAI;YACP,WAAW,iKAAA,CAAA,YAAS,CAAC,QAAQ,CAAC;YAC9B,WAAW,iKAAA,CAAA,YAAS,CAAC,QAAQ,CAAC;QAChC;QACA,OAAO,OAAO,EAAE;IAClB;IAEA,MAAM,QAAO,MAAc,EAAE,QAAgB,EAAE,IAAqB;QAClE,MAAM,SAAS,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,SAAS,QAAQ,WAAW;QACnD,MAAM,CAAA,GAAA,iKAAA,CAAA,YAAS,AAAD,EAAE,QAAQ;YACtB,GAAG,IAAI;YACP,WAAW,iKAAA,CAAA,YAAS,CAAC,GAAG;QAC1B;IACF;AACF;AAGO,MAAM,iBAAiB;IAC5B,MAAM;QACJ,MAAM,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,YAAY,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;QACnD,MAAM,gBAAgB,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;QACpC,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,UAAuB,MAAO,MAAM,CAAC;IAC5E;IAEA,MAAM,KAAI,SAAiB;QACzB,MAAM,SAAS,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,WAAW;QAClC,MAAM,UAAU,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE;QAC7B,OAAO,UAAuB;IAChC;AACF;AAGO,MAAM,iBAAiB;IAC5B,MAAM;QACJ,MAAM,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,cAAc,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;QACrD,MAAM,gBAAgB,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;QACpC,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,UAAuB,MAAO,MAAM,CAAC;IAC5E;AACF", "debugId": null}}, {"offset": {"line": 351, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/src/personal/bvr-safaris/src/components/features/FarmOwnerDashboard.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { useAuth } from '@/hooks/useAuth'\nimport { Button } from '@/components/ui/Button'\nimport { Card } from '@/components/ui/Card'\nimport { LoadingSpinner } from '@/components/ui/LoadingSpinner'\nimport { farmService, bookingService } from '@/lib/firebase/firestore'\nimport { GameFarm, Booking } from '@/lib/types/firestore'\nimport { Plus, MapPin, Calendar, DollarSign, Users, Eye, Edit, BarChart3, Star, Trash2 } from 'lucide-react'\nimport Link from 'next/link'\n\nexport function FarmOwnerDashboard() {\n  const { userProfile } = useAuth()\n  const [farms, setFarms] = useState<GameFarm[]>([])\n  const [bookings, setBookings] = useState<Booking[]>([])\n  const [loading, setLoading] = useState(true)\n  const [error, setError] = useState<string | null>(null)\n  const [deletingFarmId, setDeletingFarmId] = useState<string | null>(null)\n\n  useEffect(() => {\n    const fetchData = async () => {\n      if (!userProfile?.id) return\n\n      try {\n        setLoading(true)\n        setError(null)\n\n        // Fetch user's farms\n        const userFarms = await farmService.getByOwner(userProfile.id)\n        setFarms(userFarms)\n\n        // Fetch bookings for user's farms\n        const farmIds = userFarms.map(farm => farm.id)\n        if (farmIds.length > 0) {\n          // Fetch bookings for each farm separately to respect security rules\n          const allFarmBookings: Booking[] = []\n          for (const farmId of farmIds) {\n            try {\n              const farmBookings = await bookingService.getAll({ farmId, limit: 50 })\n              allFarmBookings.push(...farmBookings)\n            } catch (error) {\n              console.error(`Error fetching bookings for farm ${farmId}:`, error)\n            }\n          }\n          setBookings(allFarmBookings)\n        }\n      } catch (err) {\n        console.error('Error fetching dashboard data:', err)\n        setError('Failed to load dashboard data')\n      } finally {\n        setLoading(false)\n      }\n    }\n\n    fetchData()\n  }, [userProfile?.id])\n\n  const handleDeleteFarm = async (farmId: string, farmName: string) => {\n    if (!confirm(`Are you sure you want to delete \"${farmName}\"? This action cannot be undone.`)) {\n      return\n    }\n\n    try {\n      setDeletingFarmId(farmId)\n      await farmService.delete(farmId)\n\n      // Remove the farm from the local state\n      setFarms(prevFarms => prevFarms.filter(farm => farm.id !== farmId))\n\n      // Also remove any bookings for this farm\n      setBookings(prevBookings => prevBookings.filter(booking => booking.farmId !== farmId))\n    } catch (err) {\n      console.error('Error deleting farm:', err)\n      setError('Failed to delete farm. Please try again.')\n    } finally {\n      setDeletingFarmId(null)\n    }\n  }\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <LoadingSpinner size=\"lg\" />\n      </div>\n    )\n  }\n\n  if (error) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"text-center\">\n          <p className=\"text-red-600 mb-4\">{error}</p>\n          <Button onClick={() => window.location.reload()}>\n            Try Again\n          </Button>\n        </div>\n      </div>\n    )\n  }\n\n  // Calculate statistics\n  const totalBookings = bookings.length\n  const pendingBookings = bookings.filter(b => b.status === 'pending').length\n  const totalRevenue = bookings\n    .filter(b => b.status === 'confirmed')\n    .reduce((sum, b) => sum + (b.totalPrice || 0), 0)\n\n  return (\n    <div className=\"container mx-auto px-4 py-8\">\n      {/* Header */}\n      <div className=\"mb-8\">\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h1 className=\"text-3xl font-bold text-earth-900\">\n              Welcome back, {userProfile?.firstName || 'Farm Owner'}!\n            </h1>\n            <p className=\"text-earth-600 mt-2\">\n              Manage your farms and bookings from your dashboard\n            </p>\n          </div>\n          <Link href=\"/farms/create\">\n            <Button variant=\"primary\" className=\"flex items-center gap-2\">\n              <Plus className=\"w-4 h-4\" />\n              Add New Farm\n            </Button>\n          </Link>\n        </div>\n      </div>\n\n      {/* Statistics Cards */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n        <Card className=\"p-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-earth-600 text-sm font-medium\">Total Farms</p>\n              <p className=\"text-2xl font-bold text-earth-900\">{farms.length}</p>\n            </div>\n            <MapPin className=\"w-8 h-8 text-accent-600\" />\n          </div>\n        </Card>\n\n        <Card className=\"p-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-earth-600 text-sm font-medium\">Total Bookings</p>\n              <p className=\"text-2xl font-bold text-earth-900\">{totalBookings}</p>\n            </div>\n            <Calendar className=\"w-8 h-8 text-accent-600\" />\n          </div>\n        </Card>\n\n        <Card className=\"p-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-earth-600 text-sm font-medium\">Pending Requests</p>\n              <p className=\"text-2xl font-bold text-earth-900\">{pendingBookings}</p>\n            </div>\n            <Users className=\"w-8 h-8 text-accent-600\" />\n          </div>\n        </Card>\n\n        <Card className=\"p-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-earth-600 text-sm font-medium\">Total Revenue</p>\n              <p className=\"text-2xl font-bold text-earth-900\">\n                R{totalRevenue.toLocaleString()}\n              </p>\n            </div>\n            <DollarSign className=\"w-8 h-8 text-accent-600\" />\n          </div>\n        </Card>\n      </div>\n\n      {/* My Farms Section */}\n      <div className=\"mb-8\">\n        <div className=\"flex items-center justify-between mb-6\">\n          <h2 className=\"text-2xl font-semibold text-earth-900\">My Farms</h2>\n          <Link href=\"/farms/create\">\n            <Button variant=\"primary\" className=\"flex items-center gap-2\">\n              <Plus className=\"w-4 h-4\" />\n              Add New Farm\n            </Button>\n          </Link>\n        </div>\n\n        {farms.length === 0 ? (\n          <Card className=\"p-8\">\n            <div className=\"text-center\">\n              <MapPin className=\"w-16 h-16 text-earth-400 mx-auto mb-4\" />\n              <h3 className=\"text-xl font-semibold text-earth-900 mb-2\">\n                No farms yet\n              </h3>\n              <p className=\"text-earth-600 mb-6\">\n                Create your first farm listing to start receiving bookings from safari enthusiasts.\n              </p>\n              <Link href=\"/farms/create\">\n                <Button variant=\"primary\" className=\"flex items-center gap-2\">\n                  <Plus className=\"w-4 h-4\" />\n                  Create Your First Farm\n                </Button>\n              </Link>\n            </div>\n          </Card>\n        ) : (\n          <>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n              {farms.slice(0, 9).map((farm) => (\n                <Card key={farm.id} className=\"overflow-hidden\">\n                  <div className=\"p-0\">\n                    {/* Farm Image Placeholder */}\n                    <div className=\"h-48 bg-gradient-to-br from-earth-200 to-earth-300 flex items-center justify-center\">\n                      <MapPin className=\"w-12 h-12 text-earth-600\" />\n                    </div>\n\n                    <div className=\"p-6\">\n                      {/* Farm Header */}\n                      <div className=\"flex items-start justify-between mb-3\">\n                        <div>\n                          <h3 className=\"text-lg font-semibold text-earth-900 mb-1\">\n                            {farm.name}\n                          </h3>\n                          <p className=\"text-sm text-earth-600 flex items-center gap-1\">\n                            <MapPin className=\"w-3 h-3\" />\n                            {farm.location}, {farm.province}\n                          </p>\n                        </div>\n                        <div className=\"flex items-center gap-1\">\n                          <div className={`w-2 h-2 rounded-full ${farm.isActive ? 'bg-green-500' : 'bg-red-500'}`} />\n                          <span className=\"text-xs text-earth-600\">\n                            {farm.isActive ? 'Active' : 'Inactive'}\n                          </span>\n                        </div>\n                      </div>\n\n                      {/* Farm Details */}\n                      <div className=\"space-y-2 mb-4\">\n                        <div className=\"flex items-center justify-between text-sm\">\n                          <span className=\"text-earth-600\">Activity Type:</span>\n                          <span className=\"font-medium capitalize text-earth-900\">\n                            {farm.activityTypes.replace('_', ' ')}\n                          </span>\n                        </div>\n                        {farm.sizeHectares && (\n                          <div className=\"flex items-center justify-between text-sm\">\n                            <span className=\"text-earth-600\">Size:</span>\n                            <span className=\"font-medium text-earth-900\">\n                              {farm.sizeHectares.toLocaleString()} hectares\n                            </span>\n                          </div>\n                        )}\n                        {farm.pricePerDay && (\n                          <div className=\"flex items-center justify-between text-sm\">\n                            <span className=\"text-earth-600\">Price:</span>\n                            <span className=\"font-medium text-earth-900\">\n                              R{farm.pricePerDay.toLocaleString()}/day\n                            </span>\n                          </div>\n                        )}\n                      </div>\n\n                      {/* Action Buttons */}\n                      <div className=\"grid grid-cols-2 gap-2 mb-2\">\n                        <Link href={`/farms/${farm.id}`}>\n                          <Button variant=\"outline\" size=\"sm\" className=\"w-full flex items-center gap-1\">\n                            <Eye className=\"w-3 h-3\" />\n                            View\n                          </Button>\n                        </Link>\n                        <Link href={`/farms/${farm.id}/edit`}>\n                          <Button variant=\"outline\" size=\"sm\" className=\"w-full flex items-center gap-1\">\n                            <Edit className=\"w-3 h-3\" />\n                            Edit\n                          </Button>\n                        </Link>\n                      </div>\n\n                      <div className=\"grid grid-cols-2 gap-2\">\n                        <Link href={`/farms/${farm.id}/analytics`}>\n                          <Button variant=\"primary\" size=\"sm\" className=\"w-full flex items-center gap-1\">\n                            <BarChart3 className=\"w-3 h-3\" />\n                            Analytics\n                          </Button>\n                        </Link>\n                        <Button\n                          variant=\"outline\"\n                          size=\"sm\"\n                          className=\"w-full flex items-center gap-1 text-red-600 hover:text-red-700 hover:bg-red-50\"\n                          onClick={() => handleDeleteFarm(farm.id, farm.name)}\n                          disabled={deletingFarmId === farm.id}\n                        >\n                          <Trash2 className=\"w-3 h-3\" />\n                          {deletingFarmId === farm.id ? 'Deleting...' : 'Delete'}\n                        </Button>\n                      </div>\n\n                      {/* Quick Stats */}\n                      <div className=\"mt-4 pt-4 border-t border-earth-200\">\n                        <div className=\"grid grid-cols-2 gap-4 text-center\">\n                          <div>\n                            <div className=\"flex items-center justify-center gap-1 text-earth-600 mb-1\">\n                              <Users className=\"w-3 h-3\" />\n                              <span className=\"text-xs\">Bookings</span>\n                            </div>\n                            <p className=\"text-sm font-semibold text-earth-900\">\n                              {bookings.filter(b => b.farmId === farm.id).length}\n                            </p>\n                          </div>\n                          <div>\n                            <div className=\"flex items-center justify-center gap-1 text-earth-600 mb-1\">\n                              <Star className=\"w-3 h-3\" />\n                              <span className=\"text-xs\">Rating</span>\n                            </div>\n                            <p className=\"text-sm font-semibold text-earth-900\">N/A</p>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </Card>\n              ))}\n            </div>\n\n            {farms.length > 9 && (\n              <div className=\"text-center mt-6\">\n                <Link href=\"/dashboard/farms\">\n                  <Button variant=\"outline\">\n                    View All {farms.length} Farms\n                  </Button>\n                </Link>\n              </div>\n            )}\n          </>\n        )}\n      </div>\n\n      {/* Recent Booking Requests */}\n      <div>\n        <Card className=\"p-6\">\n          <div className=\"flex items-center justify-between mb-6\">\n            <h2 className=\"text-xl font-semibold text-earth-900\">Recent Booking Requests</h2>\n            <Link href=\"/dashboard/bookings\">\n              <Button variant=\"outline\" size=\"sm\">\n                View All\n              </Button>\n            </Link>\n          </div>\n\n          {bookings.length === 0 ? (\n            <div className=\"text-center py-8\">\n              <Calendar className=\"w-12 h-12 text-earth-400 mx-auto mb-4\" />\n              <p className=\"text-earth-600\">No booking requests yet</p>\n            </div>\n          ) : (\n            <div className=\"space-y-4\">\n              {bookings.slice(0, 3).map((booking) => {\n                const farm = farms.find(f => f.id === booking.farmId)\n                return (\n                  <div key={booking.id} className=\"flex items-center justify-between p-4 bg-earth-50 rounded-lg\">\n                    <div>\n                      <h3 className=\"font-medium text-earth-900\">{farm?.name || 'Unknown Farm'}</h3>\n                      <p className=\"text-sm text-earth-600\">\n                        {new Date(booking.startDate).toLocaleDateString()} - {new Date(booking.endDate).toLocaleDateString()}\n                      </p>\n                    </div>\n                    <div className=\"text-right\">\n                      <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${\n                        booking.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :\n                        booking.status === 'confirmed' ? 'bg-green-100 text-green-800' :\n                        'bg-red-100 text-red-800'\n                      }`}>\n                        {booking.status}\n                      </span>\n                      <p className=\"text-sm font-medium text-earth-900 mt-1\">\n                        R{booking.totalPrice?.toLocaleString()}\n                      </p>\n                    </div>\n                  </div>\n                )\n              })}\n            </div>\n          )}\n        </Card>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAVA;;;;;;;;;;AAYO,SAAS;IACd,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,UAAO,AAAD;IAC9B,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACjD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAEpE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,YAAY;YAChB,IAAI,CAAC,aAAa,IAAI;YAEtB,IAAI;gBACF,WAAW;gBACX,SAAS;gBAET,qBAAqB;gBACrB,MAAM,YAAY,MAAM,mIAAA,CAAA,cAAW,CAAC,UAAU,CAAC,YAAY,EAAE;gBAC7D,SAAS;gBAET,kCAAkC;gBAClC,MAAM,UAAU,UAAU,GAAG,CAAC,CAAA,OAAQ,KAAK,EAAE;gBAC7C,IAAI,QAAQ,MAAM,GAAG,GAAG;oBACtB,oEAAoE;oBACpE,MAAM,kBAA6B,EAAE;oBACrC,KAAK,MAAM,UAAU,QAAS;wBAC5B,IAAI;4BACF,MAAM,eAAe,MAAM,mIAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;gCAAE;gCAAQ,OAAO;4BAAG;4BACrE,gBAAgB,IAAI,IAAI;wBAC1B,EAAE,OAAO,OAAO;4BACd,QAAQ,KAAK,CAAC,CAAC,iCAAiC,EAAE,OAAO,CAAC,CAAC,EAAE;wBAC/D;oBACF;oBACA,YAAY;gBACd;YACF,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,kCAAkC;gBAChD,SAAS;YACX,SAAU;gBACR,WAAW;YACb;QACF;QAEA;IACF,GAAG;QAAC,aAAa;KAAG;IAEpB,MAAM,mBAAmB,OAAO,QAAgB;QAC9C,IAAI,CAAC,QAAQ,CAAC,iCAAiC,EAAE,SAAS,gCAAgC,CAAC,GAAG;YAC5F;QACF;QAEA,IAAI;YACF,kBAAkB;YAClB,MAAM,mIAAA,CAAA,cAAW,CAAC,MAAM,CAAC;YAEzB,uCAAuC;YACvC,SAAS,CAAA,YAAa,UAAU,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;YAE3D,yCAAyC;YACzC,YAAY,CAAA,eAAgB,aAAa,MAAM,CAAC,CAAA,UAAW,QAAQ,MAAM,KAAK;QAChF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,wBAAwB;YACtC,SAAS;QACX,SAAU;YACR,kBAAkB;QACpB;IACF;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,0IAAA,CAAA,iBAAc;gBAAC,MAAK;;;;;;;;;;;IAG3B;IAEA,IAAI,OAAO;QACT,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAClC,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;kCAAI;;;;;;;;;;;;;;;;;IAMzD;IAEA,uBAAuB;IACvB,MAAM,gBAAgB,SAAS,MAAM;IACrC,MAAM,kBAAkB,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,WAAW,MAAM;IAC3E,MAAM,eAAe,SAClB,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aACzB,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,CAAC,EAAE,UAAU,IAAI,CAAC,GAAG;IAEjD,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;;wCAAoC;wCACjC,aAAa,aAAa;wCAAa;;;;;;;8CAExD,8OAAC;oCAAE,WAAU;8CAAsB;;;;;;;;;;;;sCAIrC,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;sCACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,WAAU;;kDAClC,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAY;;;;;;;;;;;;;;;;;;;;;;;0BAQpC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAE,WAAU;sDAAqC;;;;;;sDAClD,8OAAC;4CAAE,WAAU;sDAAqC,MAAM,MAAM;;;;;;;;;;;;8CAEhE,8OAAC,0MAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAItB,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAE,WAAU;sDAAqC;;;;;;sDAClD,8OAAC;4CAAE,WAAU;sDAAqC;;;;;;;;;;;;8CAEpD,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAIxB,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAE,WAAU;sDAAqC;;;;;;sDAClD,8OAAC;4CAAE,WAAU;sDAAqC;;;;;;;;;;;;8CAEpD,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAIrB,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAE,WAAU;sDAAqC;;;;;;sDAClD,8OAAC;4CAAE,WAAU;;gDAAoC;gDAC7C,aAAa,cAAc;;;;;;;;;;;;;8CAGjC,8OAAC,kNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0BAM5B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAwC;;;;;;0CACtD,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,WAAU;;sDAClC,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAY;;;;;;;;;;;;;;;;;;oBAMjC,MAAM,MAAM,KAAK,kBAChB,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,0MAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,8OAAC;oCAAG,WAAU;8CAA4C;;;;;;8CAG1D,8OAAC;oCAAE,WAAU;8CAAsB;;;;;;8CAGnC,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;8CACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,WAAU;;0DAClC,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;;;;;;;;;;;6CAOpC;;0CACE,8OAAC;gCAAI,WAAU;0CACZ,MAAM,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,qBACtB,8OAAC,gIAAA,CAAA,OAAI;wCAAe,WAAU;kDAC5B,cAAA,8OAAC;4CAAI,WAAU;;8DAEb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,0MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;;;;;;8DAGpB,8OAAC;oDAAI,WAAU;;sEAEb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;;sFACC,8OAAC;4EAAG,WAAU;sFACX,KAAK,IAAI;;;;;;sFAEZ,8OAAC;4EAAE,WAAU;;8FACX,8OAAC,0MAAA,CAAA,SAAM;oFAAC,WAAU;;;;;;gFACjB,KAAK,QAAQ;gFAAC;gFAAG,KAAK,QAAQ;;;;;;;;;;;;;8EAGnC,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAW,CAAC,qBAAqB,EAAE,KAAK,QAAQ,GAAG,iBAAiB,cAAc;;;;;;sFACvF,8OAAC;4EAAK,WAAU;sFACb,KAAK,QAAQ,GAAG,WAAW;;;;;;;;;;;;;;;;;;sEAMlC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAU;sFAAiB;;;;;;sFACjC,8OAAC;4EAAK,WAAU;sFACb,KAAK,aAAa,CAAC,OAAO,CAAC,KAAK;;;;;;;;;;;;gEAGpC,KAAK,YAAY,kBAChB,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAU;sFAAiB;;;;;;sFACjC,8OAAC;4EAAK,WAAU;;gFACb,KAAK,YAAY,CAAC,cAAc;gFAAG;;;;;;;;;;;;;gEAIzC,KAAK,WAAW,kBACf,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAU;sFAAiB;;;;;;sFACjC,8OAAC;4EAAK,WAAU;;gFAA6B;gFACzC,KAAK,WAAW,CAAC,cAAc;gFAAG;;;;;;;;;;;;;;;;;;;sEAO5C,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,4JAAA,CAAA,UAAI;oEAAC,MAAM,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE;8EAC7B,cAAA,8OAAC,kIAAA,CAAA,SAAM;wEAAC,SAAQ;wEAAU,MAAK;wEAAK,WAAU;;0FAC5C,8OAAC,gMAAA,CAAA,MAAG;gFAAC,WAAU;;;;;;4EAAY;;;;;;;;;;;;8EAI/B,8OAAC,4JAAA,CAAA,UAAI;oEAAC,MAAM,CAAC,OAAO,EAAE,KAAK,EAAE,CAAC,KAAK,CAAC;8EAClC,cAAA,8OAAC,kIAAA,CAAA,SAAM;wEAAC,SAAQ;wEAAU,MAAK;wEAAK,WAAU;;0FAC5C,8OAAC,2MAAA,CAAA,OAAI;gFAAC,WAAU;;;;;;4EAAY;;;;;;;;;;;;;;;;;;sEAMlC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,4JAAA,CAAA,UAAI;oEAAC,MAAM,CAAC,OAAO,EAAE,KAAK,EAAE,CAAC,UAAU,CAAC;8EACvC,cAAA,8OAAC,kIAAA,CAAA,SAAM;wEAAC,SAAQ;wEAAU,MAAK;wEAAK,WAAU;;0FAC5C,8OAAC,kNAAA,CAAA,YAAS;gFAAC,WAAU;;;;;;4EAAY;;;;;;;;;;;;8EAIrC,8OAAC,kIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,MAAK;oEACL,WAAU;oEACV,SAAS,IAAM,iBAAiB,KAAK,EAAE,EAAE,KAAK,IAAI;oEAClD,UAAU,mBAAmB,KAAK,EAAE;;sFAEpC,8OAAC,0MAAA,CAAA,SAAM;4EAAC,WAAU;;;;;;wEACjB,mBAAmB,KAAK,EAAE,GAAG,gBAAgB;;;;;;;;;;;;;sEAKlD,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;;0FACC,8OAAC;gFAAI,WAAU;;kGACb,8OAAC,oMAAA,CAAA,QAAK;wFAAC,WAAU;;;;;;kGACjB,8OAAC;wFAAK,WAAU;kGAAU;;;;;;;;;;;;0FAE5B,8OAAC;gFAAE,WAAU;0FACV,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,KAAK,EAAE,EAAE,MAAM;;;;;;;;;;;;kFAGtD,8OAAC;;0FACC,8OAAC;gFAAI,WAAU;;kGACb,8OAAC,kMAAA,CAAA,OAAI;wFAAC,WAAU;;;;;;kGAChB,8OAAC;wFAAK,WAAU;kGAAU;;;;;;;;;;;;0FAE5B,8OAAC;gFAAE,WAAU;0FAAuC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uCAzGrD,KAAK,EAAE;;;;;;;;;;4BAmHrB,MAAM,MAAM,GAAG,mBACd,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;8CACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;;4CAAU;4CACd,MAAM,MAAM;4CAAC;;;;;;;;;;;;;;;;;;;;;;;;;0BAUrC,8OAAC;0BACC,cAAA,8OAAC,gIAAA,CAAA,OAAI;oBAAC,WAAU;;sCACd,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAuC;;;;;;8CACrD,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;8CACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;kDAAK;;;;;;;;;;;;;;;;;wBAMvC,SAAS,MAAM,KAAK,kBACnB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,8OAAC;oCAAE,WAAU;8CAAiB;;;;;;;;;;;iDAGhC,8OAAC;4BAAI,WAAU;sCACZ,SAAS,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;gCACzB,MAAM,OAAO,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,QAAQ,MAAM;gCACpD,qBACE,8OAAC;oCAAqB,WAAU;;sDAC9B,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAA8B,MAAM,QAAQ;;;;;;8DAC1D,8OAAC;oDAAE,WAAU;;wDACV,IAAI,KAAK,QAAQ,SAAS,EAAE,kBAAkB;wDAAG;wDAAI,IAAI,KAAK,QAAQ,OAAO,EAAE,kBAAkB;;;;;;;;;;;;;sDAGtG,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAW,CAAC,uDAAuD,EACvE,QAAQ,MAAM,KAAK,YAAY,kCAC/B,QAAQ,MAAM,KAAK,cAAc,gCACjC,2BACA;8DACC,QAAQ,MAAM;;;;;;8DAEjB,8OAAC;oDAAE,WAAU;;wDAA0C;wDACnD,QAAQ,UAAU,EAAE;;;;;;;;;;;;;;mCAhBlB,QAAQ,EAAE;;;;;4BAqBxB;;;;;;;;;;;;;;;;;;;;;;;AAOd", "debugId": null}}, {"offset": {"line": 1492, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/src/personal/bvr-safaris/src/components/features/GuestDashboard.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { useAuth } from '@/hooks/useAuth'\nimport { Button } from '@/components/ui/Button'\nimport { Card } from '@/components/ui/Card'\nimport { LoadingSpinner } from '@/components/ui/LoadingSpinner'\nimport { bookingService, farmService } from '@/lib/firebase/firestore'\nimport { Booking, GameFarm } from '@/lib/types/firestore'\nimport { Calendar, MapPin, Heart, Search, Clock, CheckCircle } from 'lucide-react'\nimport Link from 'next/link'\n\nexport function GuestDashboard() {\n  const { userProfile } = useAuth()\n  const [bookings, setBookings] = useState<Booking[]>([])\n  const [savedFarms, setSavedFarms] = useState<GameFarm[]>([])\n  const [loading, setLoading] = useState(true)\n  const [error, setError] = useState<string | null>(null)\n\n  useEffect(() => {\n    const fetchData = async () => {\n      if (!userProfile?.id) return\n\n      try {\n        setLoading(true)\n        setError(null)\n\n        // Fetch user's bookings using filters to respect security rules\n        const userBookings = await bookingService.getAll({\n          hunterId: userProfile.id,\n          limit: 10\n        })\n        setBookings(userBookings)\n\n        // Fetch active farms that guests can view (public farms)\n        const activeFarms = await farmService.getActive(3) // Get first 3 active farms\n        setSavedFarms(activeFarms)\n\n      } catch (err) {\n        console.error('Error fetching dashboard data:', err)\n        setError('Failed to load dashboard data')\n      } finally {\n        setLoading(false)\n      }\n    }\n\n    fetchData()\n  }, [userProfile?.id])\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <LoadingSpinner size=\"lg\" />\n      </div>\n    )\n  }\n\n  if (error) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"text-center\">\n          <p className=\"text-red-600 mb-4\">{error}</p>\n          <Button onClick={() => window.location.reload()}>\n            Try Again\n          </Button>\n        </div>\n      </div>\n    )\n  }\n\n  // Calculate statistics\n  const totalBookings = bookings.length\n  const upcomingBookings = bookings.filter(b => \n    new Date(b.startDate) > new Date() && b.status === 'confirmed'\n  ).length\n  const completedBookings = bookings.filter(b => \n    new Date(b.endDate) < new Date() && b.status === 'confirmed'\n  ).length\n\n  return (\n    <div className=\"container mx-auto px-4 py-8\">\n      {/* Header */}\n      <div className=\"mb-8\">\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h1 className=\"text-3xl font-bold text-earth-900\">\n              Welcome back, {userProfile?.firstName || 'Guest'}!\n            </h1>\n            <p className=\"text-earth-600 mt-2\">\n              Ready for your next safari adventure?\n            </p>\n          </div>\n          <Link href=\"/farms\">\n            <Button variant=\"primary\" className=\"flex items-center gap-2\">\n              <Search className=\"w-4 h-4\" />\n              Browse Farms\n            </Button>\n          </Link>\n        </div>\n      </div>\n\n      {/* Statistics Cards */}\n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\">\n        <Card className=\"p-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-earth-600 text-sm font-medium\">Total Bookings</p>\n              <p className=\"text-2xl font-bold text-earth-900\">{totalBookings}</p>\n            </div>\n            <Calendar className=\"w-8 h-8 text-accent-600\" />\n          </div>\n        </Card>\n\n        <Card className=\"p-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-earth-600 text-sm font-medium\">Upcoming Trips</p>\n              <p className=\"text-2xl font-bold text-earth-900\">{upcomingBookings}</p>\n            </div>\n            <Clock className=\"w-8 h-8 text-accent-600\" />\n          </div>\n        </Card>\n\n        <Card className=\"p-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-earth-600 text-sm font-medium\">Completed Trips</p>\n              <p className=\"text-2xl font-bold text-earth-900\">{completedBookings}</p>\n            </div>\n            <CheckCircle className=\"w-8 h-8 text-accent-600\" />\n          </div>\n        </Card>\n      </div>\n\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n        {/* My Bookings */}\n        <Card className=\"p-6\">\n          <div className=\"flex items-center justify-between mb-6\">\n            <h2 className=\"text-xl font-semibold text-earth-900\">My Bookings</h2>\n            <Link href=\"/dashboard/bookings\">\n              <Button variant=\"outline\" size=\"sm\">\n                View All\n              </Button>\n            </Link>\n          </div>\n\n          {bookings.length === 0 ? (\n            <div className=\"text-center py-8\">\n              <Calendar className=\"w-12 h-12 text-earth-400 mx-auto mb-4\" />\n              <p className=\"text-earth-600 mb-4\">You haven&apos;t made any bookings yet</p>\n              <Link href=\"/farms\">\n                <Button variant=\"primary\">Browse Farms</Button>\n              </Link>\n            </div>\n          ) : (\n            <div className=\"space-y-4\">\n              {bookings.slice(0, 3).map((booking) => (\n                <div key={booking.id} className=\"flex items-center justify-between p-4 bg-earth-50 rounded-lg\">\n                  <div>\n                    <h3 className=\"font-medium text-earth-900\">Booking #{booking.id.slice(-6)}</h3>\n                    <p className=\"text-sm text-earth-600\">\n                      {new Date(booking.startDate).toLocaleDateString()} - {new Date(booking.endDate).toLocaleDateString()}\n                    </p>\n                  </div>\n                  <div className=\"text-right\">\n                    <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${\n                      booking.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :\n                      booking.status === 'confirmed' ? 'bg-green-100 text-green-800' :\n                      'bg-red-100 text-red-800'\n                    }`}>\n                      {booking.status}\n                    </span>\n                    <p className=\"text-sm font-medium text-earth-900 mt-1\">\n                      R{booking.totalPrice?.toLocaleString()}\n                    </p>\n                  </div>\n                </div>\n              ))}\n              {bookings.length > 3 && (\n                <div className=\"text-center pt-4\">\n                  <Link href=\"/dashboard/bookings\">\n                    <Button variant=\"outline\">View All Bookings</Button>\n                  </Link>\n                </div>\n              )}\n            </div>\n          )}\n        </Card>\n\n        {/* Recommended Farms */}\n        <Card className=\"p-6\">\n          <div className=\"flex items-center justify-between mb-6\">\n            <h2 className=\"text-xl font-semibold text-earth-900\">Recommended Farms</h2>\n            <Link href=\"/farms\">\n              <Button variant=\"outline\" size=\"sm\">\n                Browse All\n              </Button>\n            </Link>\n          </div>\n\n          {savedFarms.length === 0 ? (\n            <div className=\"text-center py-8\">\n              <Heart className=\"w-12 h-12 text-earth-400 mx-auto mb-4\" />\n              <p className=\"text-earth-600 mb-4\">Discover amazing safari destinations</p>\n              <Link href=\"/farms\">\n                <Button variant=\"primary\">Explore Farms</Button>\n              </Link>\n            </div>\n          ) : (\n            <div className=\"space-y-4\">\n              {savedFarms.map((farm) => (\n                <div key={farm.id} className=\"flex items-center justify-between p-4 bg-earth-50 rounded-lg\">\n                  <div>\n                    <h3 className=\"font-medium text-earth-900\">{farm.name}</h3>\n                    <p className=\"text-sm text-earth-600 flex items-center gap-1\">\n                      <MapPin className=\"w-3 h-3\" />\n                      {farm.location}\n                    </p>\n                  </div>\n                  <div className=\"text-right\">\n                    <p className=\"text-sm font-medium text-earth-900\">\n                      R{farm.pricePerDay?.toLocaleString()}/day\n                    </p>\n                    <Link href={`/farms/${farm.id}`}>\n                      <Button variant=\"outline\" size=\"sm\">\n                        View Details\n                      </Button>\n                    </Link>\n                  </div>\n                </div>\n              ))}\n            </div>\n          )}\n        </Card>\n      </div>\n\n      {/* Quick Actions */}\n      <Card className=\"p-6 mt-8\">\n        <h2 className=\"text-xl font-semibold text-earth-900 mb-6\">Quick Actions</h2>\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n          <Link href=\"/farms\">\n            <Button variant=\"outline\" className=\"w-full flex items-center gap-2\">\n              <Search className=\"w-4 h-4\" />\n              Browse Farms\n            </Button>\n          </Link>\n          <Link href=\"/profile\">\n            <Button variant=\"outline\" className=\"w-full flex items-center gap-2\">\n              <Calendar className=\"w-4 h-4\" />\n              Manage Profile\n            </Button>\n          </Link>\n          <Link href=\"/dashboard/bookings\">\n            <Button variant=\"outline\" className=\"w-full flex items-center gap-2\">\n              <Clock className=\"w-4 h-4\" />\n              View Bookings\n            </Button>\n          </Link>\n        </div>\n      </Card>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAVA;;;;;;;;;;AAYO,SAAS;IACd,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,UAAO,AAAD;IAC9B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IAC3D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,YAAY;YAChB,IAAI,CAAC,aAAa,IAAI;YAEtB,IAAI;gBACF,WAAW;gBACX,SAAS;gBAET,gEAAgE;gBAChE,MAAM,eAAe,MAAM,mIAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;oBAC/C,UAAU,YAAY,EAAE;oBACxB,OAAO;gBACT;gBACA,YAAY;gBAEZ,yDAAyD;gBACzD,MAAM,cAAc,MAAM,mIAAA,CAAA,cAAW,CAAC,SAAS,CAAC,GAAG,2BAA2B;;gBAC9E,cAAc;YAEhB,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,kCAAkC;gBAChD,SAAS;YACX,SAAU;gBACR,WAAW;YACb;QACF;QAEA;IACF,GAAG;QAAC,aAAa;KAAG;IAEpB,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,0IAAA,CAAA,iBAAc;gBAAC,MAAK;;;;;;;;;;;IAG3B;IAEA,IAAI,OAAO;QACT,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAClC,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;kCAAI;;;;;;;;;;;;;;;;;IAMzD;IAEA,uBAAuB;IACvB,MAAM,gBAAgB,SAAS,MAAM;IACrC,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAA,IACvC,IAAI,KAAK,EAAE,SAAS,IAAI,IAAI,UAAU,EAAE,MAAM,KAAK,aACnD,MAAM;IACR,MAAM,oBAAoB,SAAS,MAAM,CAAC,CAAA,IACxC,IAAI,KAAK,EAAE,OAAO,IAAI,IAAI,UAAU,EAAE,MAAM,KAAK,aACjD,MAAM;IAER,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;;wCAAoC;wCACjC,aAAa,aAAa;wCAAQ;;;;;;;8CAEnD,8OAAC;oCAAE,WAAU;8CAAsB;;;;;;;;;;;;sCAIrC,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;sCACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,WAAU;;kDAClC,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAY;;;;;;;;;;;;;;;;;;;;;;;0BAQtC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAE,WAAU;sDAAqC;;;;;;sDAClD,8OAAC;4CAAE,WAAU;sDAAqC;;;;;;;;;;;;8CAEpD,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAIxB,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAE,WAAU;sDAAqC;;;;;;sDAClD,8OAAC;4CAAE,WAAU;sDAAqC;;;;;;;;;;;;8CAEpD,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAIrB,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAE,WAAU;sDAAqC;;;;;;sDAClD,8OAAC;4CAAE,WAAU;sDAAqC;;;;;;;;;;;;8CAEpD,8OAAC,2NAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0BAK7B,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAuC;;;;;;kDACrD,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;sDAAK;;;;;;;;;;;;;;;;;4BAMvC,SAAS,MAAM,KAAK,kBACnB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;wCAAE,WAAU;kDAAsB;;;;;;kDACnC,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;sDAAU;;;;;;;;;;;;;;;;qDAI9B,8OAAC;gCAAI,WAAU;;oCACZ,SAAS,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,wBACzB,8OAAC;4CAAqB,WAAU;;8DAC9B,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;;gEAA6B;gEAAU,QAAQ,EAAE,CAAC,KAAK,CAAC,CAAC;;;;;;;sEACvE,8OAAC;4DAAE,WAAU;;gEACV,IAAI,KAAK,QAAQ,SAAS,EAAE,kBAAkB;gEAAG;gEAAI,IAAI,KAAK,QAAQ,OAAO,EAAE,kBAAkB;;;;;;;;;;;;;8DAGtG,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAW,CAAC,uDAAuD,EACvE,QAAQ,MAAM,KAAK,YAAY,kCAC/B,QAAQ,MAAM,KAAK,cAAc,gCACjC,2BACA;sEACC,QAAQ,MAAM;;;;;;sEAEjB,8OAAC;4DAAE,WAAU;;gEAA0C;gEACnD,QAAQ,UAAU,EAAE;;;;;;;;;;;;;;2CAhBlB,QAAQ,EAAE;;;;;oCAqBrB,SAAS,MAAM,GAAG,mBACjB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;sDACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDAAC,SAAQ;0DAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAStC,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAuC;;;;;;kDACrD,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;sDAAK;;;;;;;;;;;;;;;;;4BAMvC,WAAW,MAAM,KAAK,kBACrB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,8OAAC;wCAAE,WAAU;kDAAsB;;;;;;kDACnC,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;sDAAU;;;;;;;;;;;;;;;;qDAI9B,8OAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC;wCAAkB,WAAU;;0DAC3B,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAA8B,KAAK,IAAI;;;;;;kEACrD,8OAAC;wDAAE,WAAU;;0EACX,8OAAC,0MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;4DACjB,KAAK,QAAQ;;;;;;;;;;;;;0DAGlB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;;4DAAqC;4DAC9C,KAAK,WAAW,EAAE;4DAAiB;;;;;;;kEAEvC,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAM,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE;kEAC7B,cAAA,8OAAC,kIAAA,CAAA,SAAM;4DAAC,SAAQ;4DAAU,MAAK;sEAAK;;;;;;;;;;;;;;;;;;uCAbhC,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;0BA0B3B,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC;wBAAG,WAAU;kCAA4C;;;;;;kCAC1D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,WAAU;;sDAClC,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAY;;;;;;;;;;;;0CAIlC,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,WAAU;;sDAClC,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAY;;;;;;;;;;;;0CAIpC,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,WAAU;;sDAClC,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;wCAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ3C", "debugId": null}}, {"offset": {"line": 2296, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/src/personal/bvr-safaris/src/lib/utils/debugAuth.ts"], "sourcesContent": ["import { auth } from '@/lib/firebase/client'\n\n/**\n * Debug utility to check authentication state and custom claims\n */\nexport async function debugAuthState() {\n  const user = auth.currentUser\n  \n  if (!user) {\n    console.log('🔍 Auth Debug: No user logged in')\n    return null\n  }\n\n  try {\n    // Get the ID token result to see custom claims\n    const idTokenResult = await user.getIdTokenResult(true) // Force refresh\n    \n    console.log('🔍 Auth Debug Information:')\n    console.log('  User UID:', user.uid)\n    console.log('  Email:', user.email)\n    console.log('  Display Name:', user.displayName)\n    console.log('  Email Verified:', user.emailVerified)\n    console.log('  Custom Claims:', idTokenResult.claims)\n    console.log('  Role from Claims:', idTokenResult.claims.role || 'NO ROLE SET')\n    console.log('  Token Expiration:', new Date(idTokenResult.expirationTime))\n    console.log('  Auth Time:', new Date(idTokenResult.authTime))\n    console.log('  Issued At:', new Date(idTokenResult.issuedAtTime))\n    \n    // Check if role is set\n    if (!idTokenResult.claims.role) {\n      console.warn('⚠️  WARNING: No role set in custom claims!')\n      console.log('   This will cause Firestore permission errors.')\n      console.log('   The user may need to re-register or have their role set manually.')\n    }\n    \n    return {\n      uid: user.uid,\n      email: user.email,\n      role: (idTokenResult.claims.role as string) || 'NO ROLE',\n      claims: idTokenResult.claims as Record<string, unknown>,\n      hasRole: !!idTokenResult.claims.role\n    }\n    \n  } catch (error) {\n    console.error('🔍 Auth Debug Error:', error)\n    return null\n  }\n}\n\n/**\n * Check if the current user has the required role\n */\nexport async function checkUserRole(requiredRole?: string) {\n  const debugInfo = await debugAuthState()\n  \n  if (!debugInfo) {\n    return false\n  }\n  \n  if (requiredRole) {\n    const hasRole = debugInfo.role === requiredRole\n    console.log(`🔍 Role Check: Required \"${requiredRole}\", User has \"${debugInfo.role}\" - ${hasRole ? 'PASS' : 'FAIL'}`)\n    return hasRole\n  }\n  \n  return !!debugInfo.hasRole\n}\n\n/**\n * Force refresh the user's ID token to get latest custom claims\n */\nexport async function refreshUserToken() {\n  const user = auth.currentUser\n  \n  if (!user) {\n    console.log('🔄 Token Refresh: No user logged in')\n    return false\n  }\n  \n  try {\n    console.log('🔄 Refreshing user token...')\n    await user.getIdToken(true) // Force refresh\n    console.log('✅ Token refreshed successfully')\n    return true\n  } catch (error) {\n    console.error('❌ Token refresh failed:', error)\n    return false\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;AAAA;;AAKO,eAAe;IACpB,MAAM,OAAO,gIAAA,CAAA,OAAI,CAAC,WAAW;IAE7B,IAAI,CAAC,MAAM;QACT,QAAQ,GAAG,CAAC;QACZ,OAAO;IACT;IAEA,IAAI;QACF,+CAA+C;QAC/C,MAAM,gBAAgB,MAAM,KAAK,gBAAgB,CAAC,MAAM,gBAAgB;;QAExE,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,eAAe,KAAK,GAAG;QACnC,QAAQ,GAAG,CAAC,YAAY,KAAK,KAAK;QAClC,QAAQ,GAAG,CAAC,mBAAmB,KAAK,WAAW;QAC/C,QAAQ,GAAG,CAAC,qBAAqB,KAAK,aAAa;QACnD,QAAQ,GAAG,CAAC,oBAAoB,cAAc,MAAM;QACpD,QAAQ,GAAG,CAAC,uBAAuB,cAAc,MAAM,CAAC,IAAI,IAAI;QAChE,QAAQ,GAAG,CAAC,uBAAuB,IAAI,KAAK,cAAc,cAAc;QACxE,QAAQ,GAAG,CAAC,gBAAgB,IAAI,KAAK,cAAc,QAAQ;QAC3D,QAAQ,GAAG,CAAC,gBAAgB,IAAI,KAAK,cAAc,YAAY;QAE/D,uBAAuB;QACvB,IAAI,CAAC,cAAc,MAAM,CAAC,IAAI,EAAE;YAC9B,QAAQ,IAAI,CAAC;YACb,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CAAC;QACd;QAEA,OAAO;YACL,KAAK,KAAK,GAAG;YACb,OAAO,KAAK,KAAK;YACjB,MAAM,AAAC,cAAc,MAAM,CAAC,IAAI,IAAe;YAC/C,QAAQ,cAAc,MAAM;YAC5B,SAAS,CAAC,CAAC,cAAc,MAAM,CAAC,IAAI;QACtC;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,OAAO;IACT;AACF;AAKO,eAAe,cAAc,YAAqB;IACvD,MAAM,YAAY,MAAM;IAExB,IAAI,CAAC,WAAW;QACd,OAAO;IACT;IAEA,IAAI,cAAc;QAChB,MAAM,UAAU,UAAU,IAAI,KAAK;QACnC,QAAQ,GAAG,CAAC,CAAC,yBAAyB,EAAE,aAAa,aAAa,EAAE,UAAU,IAAI,CAAC,IAAI,EAAE,UAAU,SAAS,QAAQ;QACpH,OAAO;IACT;IAEA,OAAO,CAAC,CAAC,UAAU,OAAO;AAC5B;AAKO,eAAe;IACpB,MAAM,OAAO,gIAAA,CAAA,OAAI,CAAC,WAAW;IAE7B,IAAI,CAAC,MAAM;QACT,QAAQ,GAAG,CAAC;QACZ,OAAO;IACT;IAEA,IAAI;QACF,QAAQ,GAAG,CAAC;QACZ,MAAM,KAAK,UAAU,CAAC,MAAM,gBAAgB;;QAC5C,QAAQ,GAAG,CAAC;QACZ,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 2377, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/src/personal/bvr-safaris/src/components/debug/AuthDebugPanel.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Button } from '@/components/ui/Button'\nimport { Card } from '@/components/ui/Card'\nimport { debugAuthState, refreshUserToken } from '@/lib/utils/debugAuth'\nimport { useAuth } from '@/hooks/useAuth'\n\ninterface AuthDebugInfo {\n  uid: string\n  email: string | null\n  role: string\n  hasRole: boolean\n  claims: Record<string, unknown>\n}\n\nexport function AuthDebugPanel() {\n  const [debugInfo, setDebugInfo] = useState<AuthDebugInfo | null>(null)\n  const [isDebugging, setIsDebugging] = useState(false)\n  const [isRefreshing, setIsRefreshing] = useState(false)\n  const { user } = useAuth()\n\n  const handleDebugAuth = async () => {\n    setIsDebugging(true)\n    try {\n      const info = await debugAuthState()\n      setDebugInfo(info)\n    } catch (error) {\n      console.error('Debug failed:', error)\n    } finally {\n      setIsDebugging(false)\n    }\n  }\n\n  const handleRefreshToken = async () => {\n    setIsRefreshing(true)\n    try {\n      await refreshUserToken()\n      // Re-run debug after refresh\n      const info = await debugAuthState()\n      setDebugInfo(info)\n    } catch (error) {\n      console.error('Token refresh failed:', error)\n    } finally {\n      setIsRefreshing(false)\n    }\n  }\n\n  const handleSetRole = async () => {\n    if (!user) return\n\n    try {\n      const idToken = await user.getIdToken()\n      const response = await fetch('/api/auth/set-role', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${idToken}`,\n        },\n        body: JSON.stringify({\n          uid: user.uid,\n          role: 'guest' // Default to guest role\n        }),\n      })\n\n      if (response.ok) {\n        console.log('Role set successfully')\n        // Refresh token to get new claims\n        await handleRefreshToken()\n      } else {\n        const errorData = await response.json()\n        console.error('Failed to set role:', errorData.error)\n      }\n    } catch (error) {\n      console.error('Error setting role:', error)\n    }\n  }\n\n  if (!user) {\n    return null\n  }\n\n  return (\n    <Card className=\"p-4 mb-4 bg-yellow-50 border-yellow-200\">\n      <h3 className=\"text-lg font-semibold text-yellow-800 mb-3\">\n        🔍 Authentication Debug Panel\n      </h3>\n      \n      <div className=\"space-y-3\">\n        <div className=\"flex gap-2\">\n          <Button\n            onClick={handleDebugAuth}\n            variant=\"outline\"\n            size=\"sm\"\n            isLoading={isDebugging}\n          >\n            Check Auth State\n          </Button>\n          \n          <Button\n            onClick={handleRefreshToken}\n            variant=\"outline\"\n            size=\"sm\"\n            isLoading={isRefreshing}\n          >\n            Refresh Token\n          </Button>\n\n          <Button\n            onClick={handleSetRole}\n            variant=\"outline\"\n            size=\"sm\"\n          >\n            Set Role (Guest)\n          </Button>\n        </div>\n\n        {debugInfo && (\n          <div className=\"bg-white p-3 rounded border text-sm\">\n            <div className=\"grid grid-cols-2 gap-2\">\n              <div><strong>UID:</strong> {debugInfo.uid}</div>\n              <div><strong>Email:</strong> {debugInfo.email}</div>\n              <div><strong>Role:</strong> {debugInfo.role || 'NOT SET'}</div>\n              <div><strong>Has Role:</strong> {debugInfo.hasRole ? '✅' : '❌'}</div>\n            </div>\n            \n            {!debugInfo.hasRole && (\n              <div className=\"mt-2 p-2 bg-red-50 border border-red-200 rounded\">\n                <p className=\"text-red-700 text-xs\">\n                  ⚠️ No role set in custom claims! This will cause permission errors.\n                  Click &quot;Set Role (Guest)&quot; to fix this issue.\n                </p>\n              </div>\n            )}\n            \n            <details className=\"mt-2\">\n              <summary className=\"cursor-pointer text-xs text-gray-600\">\n                View All Claims\n              </summary>\n              <pre className=\"text-xs bg-gray-100 p-2 rounded mt-1 overflow-auto\">\n                {JSON.stringify(debugInfo.claims, null, 2)}\n              </pre>\n            </details>\n          </div>\n        )}\n      </div>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAgBO,SAAS;IACd,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB;IACjE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,UAAO,AAAD;IAEvB,MAAM,kBAAkB;QACtB,eAAe;QACf,IAAI;YACF,MAAM,OAAO,MAAM,CAAA,GAAA,gIAAA,CAAA,iBAAc,AAAD;YAChC,aAAa;QACf,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;QACjC,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,qBAAqB;QACzB,gBAAgB;QAChB,IAAI;YACF,MAAM,CAAA,GAAA,gIAAA,CAAA,mBAAgB,AAAD;YACrB,6BAA6B;YAC7B,MAAM,OAAO,MAAM,CAAA,GAAA,gIAAA,CAAA,iBAAc,AAAD;YAChC,aAAa;QACf,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACzC,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,gBAAgB;QACpB,IAAI,CAAC,MAAM;QAEX,IAAI;YACF,MAAM,UAAU,MAAM,KAAK,UAAU;YACrC,MAAM,WAAW,MAAM,MAAM,sBAAsB;gBACjD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;oBAChB,iBAAiB,CAAC,OAAO,EAAE,SAAS;gBACtC;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,KAAK,KAAK,GAAG;oBACb,MAAM,QAAQ,wBAAwB;gBACxC;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,QAAQ,GAAG,CAAC;gBACZ,kCAAkC;gBAClC,MAAM;YACR,OAAO;gBACL,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,QAAQ,KAAK,CAAC,uBAAuB,UAAU,KAAK;YACtD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;QACvC;IACF;IAEA,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,8OAAC;gBAAG,WAAU;0BAA6C;;;;;;0BAI3D,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAS;gCACT,SAAQ;gCACR,MAAK;gCACL,WAAW;0CACZ;;;;;;0CAID,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAS;gCACT,SAAQ;gCACR,MAAK;gCACL,WAAW;0CACZ;;;;;;0CAID,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAS;gCACT,SAAQ;gCACR,MAAK;0CACN;;;;;;;;;;;;oBAKF,2BACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DAAI,8OAAC;0DAAO;;;;;;4CAAa;4CAAE,UAAU,GAAG;;;;;;;kDACzC,8OAAC;;0DAAI,8OAAC;0DAAO;;;;;;4CAAe;4CAAE,UAAU,KAAK;;;;;;;kDAC7C,8OAAC;;0DAAI,8OAAC;0DAAO;;;;;;4CAAc;4CAAE,UAAU,IAAI,IAAI;;;;;;;kDAC/C,8OAAC;;0DAAI,8OAAC;0DAAO;;;;;;4CAAkB;4CAAE,UAAU,OAAO,GAAG,MAAM;;;;;;;;;;;;;4BAG5D,CAAC,UAAU,OAAO,kBACjB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,WAAU;8CAAuB;;;;;;;;;;;0CAOxC,8OAAC;gCAAQ,WAAU;;kDACjB,8OAAC;wCAAQ,WAAU;kDAAuC;;;;;;kDAG1D,8OAAC;wCAAI,WAAU;kDACZ,KAAK,SAAS,CAAC,UAAU,MAAM,EAAE,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQxD", "debugId": null}}, {"offset": {"line": 2652, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/src/personal/bvr-safaris/src/components/features/UnifiedDashboard.tsx"], "sourcesContent": ["'use client'\n\nimport { useAuth } from '@/hooks/useAuth'\nimport { FarmOwnerDashboard } from './FarmOwnerDashboard'\nimport { GuestDashboard } from './GuestDashboard'\nimport { LoadingSpinner } from '@/components/ui/LoadingSpinner'\nimport { debugAuthState } from '@/lib/utils/debugAuth'\nimport { AuthDebugPanel } from '@/components/debug/AuthDebugPanel'\nimport { useEffect } from 'react'\n\nexport function UnifiedDashboard() {\n  const { userProfile, loading } = useAuth()\n\n  // Debug authentication state when component mounts\n  useEffect(() => {\n    if (!loading && userProfile) {\n      debugAuthState()\n    }\n  }, [loading, userProfile])\n\n  if (loading || !userProfile) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <LoadingSpinner size=\"lg\" />\n      </div>\n    )\n  }\n\n  // Render dashboard based on user role\n  return (\n    <div>\n      {/* Temporary debug panel - remove in production */}\n      <AuthDebugPanel />\n\n      {userProfile.role === 'farm_owner' ? (\n        <FarmOwnerDashboard />\n      ) : (\n        <GuestDashboard />\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AARA;;;;;;;;;AAUO,SAAS;IACd,MAAM,EAAE,WAAW,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,UAAO,AAAD;IAEvC,mDAAmD;IACnD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,WAAW,aAAa;YAC3B,CAAA,GAAA,gIAAA,CAAA,iBAAc,AAAD;QACf;IACF,GAAG;QAAC;QAAS;KAAY;IAEzB,IAAI,WAAW,CAAC,aAAa;QAC3B,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,0IAAA,CAAA,iBAAc;gBAAC,MAAK;;;;;;;;;;;IAG3B;IAEA,sCAAsC;IACtC,qBACE,8OAAC;;0BAEC,8OAAC,6IAAA,CAAA,iBAAc;;;;;YAEd,YAAY,IAAI,KAAK,6BACpB,8OAAC,oJAAA,CAAA,qBAAkB;;;;qCAEnB,8OAAC,gJAAA,CAAA,iBAAc;;;;;;;;;;;AAIvB", "debugId": null}}, {"offset": {"line": 2729, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/src/personal/bvr-safaris/src/app/dashboard/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useRequireAuth } from '@/hooks/useAuth'\nimport { UnifiedDashboard } from '@/components/features/UnifiedDashboard'\nimport { LoadingSpinner } from '@/components/ui/LoadingSpinner'\nimport { useRouter } from 'next/navigation'\nimport { useEffect } from 'react'\n\nexport default function DashboardPage() {\n  const { loading, isAuthenticated } = useRequireAuth()\n  const router = useRouter()\n\n  useEffect(() => {\n    if (!loading && !isAuthenticated) {\n      router.push('/auth/login')\n    }\n  }, [loading, isAuthenticated, router])\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center bg-earth-100\">\n        <LoadingSpinner size=\"lg\" />\n      </div>\n    )\n  }\n\n  if (!isAuthenticated) {\n    return null // Will redirect to login\n  }\n\n  return (\n    <div className=\"min-h-screen bg-earth-100\">\n      <UnifiedDashboard />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAQe,SAAS;IACtB,MAAM,EAAE,OAAO,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD;IAClD,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,WAAW,CAAC,iBAAiB;YAChC,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAS;QAAiB;KAAO;IAErC,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,0IAAA,CAAA,iBAAc;gBAAC,MAAK;;;;;;;;;;;IAG3B;IAEA,IAAI,CAAC,iBAAiB;QACpB,OAAO,KAAK,yBAAyB;;IACvC;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,kJAAA,CAAA,mBAAgB;;;;;;;;;;AAGvB", "debugId": null}}]}