{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/src/personal/bvr-safaris/src/components/ui/LoadingSpinner.tsx"], "sourcesContent": ["import { cn } from '@/lib/utils'\n\ninterface LoadingSpinnerProps {\n  size?: 'sm' | 'md' | 'lg'\n  className?: string\n}\n\nexport function LoadingSpinner({ size = 'md', className }: LoadingSpinnerProps) {\n  const sizeClasses = {\n    sm: 'w-4 h-4',\n    md: 'w-6 h-6',\n    lg: 'w-12 h-12'\n  }\n\n  return (\n    <div className={cn('flex items-center justify-center', className)}>\n      <div\n        className={cn(\n          'animate-spin rounded-full border-2 border-white border-t-transparent',\n          sizeClasses[size]\n        )}\n      />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAOO,SAAS,eAAe,EAAE,OAAO,IAAI,EAAE,SAAS,EAAuB;IAC5E,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,oCAAoC;kBACrD,cAAA,8OAAC;YACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wEACA,WAAW,CAAC,KAAK;;;;;;;;;;;AAK3B", "debugId": null}}, {"offset": {"line": 41, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/src/personal/bvr-safaris/src/lib/firebase/heroImages.ts"], "sourcesContent": ["/**\n * Hero image service for fetching random background images from local banner_images directory\n */\n\n// Available banner images in the public/banner_images/ directory\nconst BANNER_IMAGES = [\n  'IMG_6015-min.JPG',\n  'IMG_6077-min.JPG',\n  'IMG_6207-min.JPG',\n  'IMG_6297-min.JPG',\n  'IMG_6333-min.JPG',\n  'IMG_6395-min.JPG',\n  'IMG_6498-min.JPG',\n  'IMG_6610-min.JPG',\n  'IMG_6632-min.JPG',\n  'IMG_6695-min.JPG',\n  'IMG_6738-min.JPG',\n  'IMG_6744-min.JPG',\n  'IMG_6784-min.JPG'\n]\n\n// Default banner image path (using first banner image as default)\nexport const DEFAULT_HERO_IMAGE = `/banner_images/${BANNER_IMAGES[0]}`\n\nexport interface HeroImageMetadata {\n  name: string\n  fullPath: string\n  downloadURL: string\n}\n\n/**\n * Fetches a random hero image URL from local banner_images directory\n * @returns Promise<string> - Path to a random hero image, or default banner if none available\n */\nexport async function getRandomHeroImageUrl(): Promise<string> {\n  try {\n    // Check if there are any images available\n    if (!BANNER_IMAGES || BANNER_IMAGES.length === 0) {\n      console.warn('No hero images found in banner_images directory, using default banner')\n      return DEFAULT_HERO_IMAGE\n    }\n\n    // Pick a random image from the available items\n    const randomIndex = Math.floor(Math.random() * BANNER_IMAGES.length)\n    const randomImageName = BANNER_IMAGES[randomIndex]\n\n    // Return the path to the selected image\n    const imagePath = `/banner_images/${randomImageName}`\n\n    return imagePath\n  } catch (error) {\n    // Fallback to default banner on error\n    console.error('Error selecting random hero image from local directory, using default banner:', error)\n    return DEFAULT_HERO_IMAGE\n  }\n}\n\n/**\n * Gets the default hero image URL (immediate, no async needed)\n * @returns string - Path to the default banner image\n */\nexport function getDefaultHeroImageUrl(): string {\n  return DEFAULT_HERO_IMAGE\n}\n\n/**\n * Fetches all available hero images with metadata\n * @returns Promise<HeroImageMetadata[]> - Array of hero image metadata\n */\nexport async function getAllHeroImages(): Promise<HeroImageMetadata[]> {\n  try {\n    if (!BANNER_IMAGES || BANNER_IMAGES.length === 0) {\n      return []\n    }\n\n    // Create metadata for all available images\n    const heroImages: HeroImageMetadata[] = BANNER_IMAGES.map((imageName) => {\n      const imagePath = `/banner_images/${imageName}`\n      return {\n        name: imageName,\n        fullPath: imagePath,\n        downloadURL: imagePath\n      }\n    })\n\n    return heroImages\n  } catch (error) {\n    console.error('Error fetching all hero images from local directory:', error)\n    return []\n  }\n}\n\n/**\n * Preloads a random hero image for better performance\n * @returns Promise<string | null> - Path of the preloaded image\n */\nexport async function preloadRandomHeroImage(): Promise<string | null> {\n  const imageUrl = await getRandomHeroImageUrl()\n\n  if (imageUrl) {\n    // Create an image element to preload the image\n    const img = new Image()\n    img.src = imageUrl\n\n    // Return the URL after initiating preload\n    return imageUrl\n  }\n\n  return null\n}\n"], "names": [], "mappings": "AAAA;;CAEC,GAED,iEAAiE;;;;;;;;AACjE,MAAM,gBAAgB;IACpB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAGM,MAAM,qBAAqB,CAAC,eAAe,EAAE,aAAa,CAAC,EAAE,EAAE;AAY/D,eAAe;IACpB,IAAI;QACF,0CAA0C;QAC1C,IAAI,CAAC,iBAAiB,cAAc,MAAM,KAAK,GAAG;YAChD,QAAQ,IAAI,CAAC;YACb,OAAO;QACT;QAEA,+CAA+C;QAC/C,MAAM,cAAc,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,cAAc,MAAM;QACnE,MAAM,kBAAkB,aAAa,CAAC,YAAY;QAElD,wCAAwC;QACxC,MAAM,YAAY,CAAC,eAAe,EAAE,iBAAiB;QAErD,OAAO;IACT,EAAE,OAAO,OAAO;QACd,sCAAsC;QACtC,QAAQ,KAAK,CAAC,iFAAiF;QAC/F,OAAO;IACT;AACF;AAMO,SAAS;IACd,OAAO;AACT;AAMO,eAAe;IACpB,IAAI;QACF,IAAI,CAAC,iBAAiB,cAAc,MAAM,KAAK,GAAG;YAChD,OAAO,EAAE;QACX;QAEA,2CAA2C;QAC3C,MAAM,aAAkC,cAAc,GAAG,CAAC,CAAC;YACzD,MAAM,YAAY,CAAC,eAAe,EAAE,WAAW;YAC/C,OAAO;gBACL,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;QACF;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wDAAwD;QACtE,OAAO,EAAE;IACX;AACF;AAMO,eAAe;IACpB,MAAM,WAAW,MAAM;IAEvB,IAAI,UAAU;QACZ,+CAA+C;QAC/C,MAAM,MAAM,IAAI;QAChB,IAAI,GAAG,GAAG;QAEV,0CAA0C;QAC1C,OAAO;IACT;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 126, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/src/personal/bvr-safaris/src/components/ui/HeroImageLoader.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport Image from 'next/image'\nimport { LoadingSpinner } from './LoadingSpinner'\nimport { getRandomHeroImageUrl, getDefaultHeroImageUrl } from '@/lib/firebase/heroImages'\nimport { cn } from '@/lib/utils'\n\ninterface HeroImageLoaderProps {\n  className?: string\n  children?: React.ReactNode\n  priority?: boolean\n  onImageLoad?: (imageUrl: string) => void\n}\n\nexport function HeroImageLoader({ \n  className, \n  children, \n  priority = false,\n  onImageLoad \n}: HeroImageLoaderProps) {\n  const [imageUrl, setImageUrl] = useState<string>(getDefaultHeroImageUrl())\n  const [isLoading, setIsLoading] = useState(true)\n\n  useEffect(() => {\n    const loadRandomImage = async () => {\n      try {\n        setIsLoading(true)\n        const randomImageUrl = await getRandomHeroImageUrl()\n        setImageUrl(randomImageUrl)\n        onImageLoad?.(randomImageUrl)\n      } catch (error) {\n        console.error('Error loading hero image:', error)\n        setImageUrl(getDefaultHeroImageUrl())\n      } finally {\n        setIsLoading(false)\n      }\n    }\n\n    loadRandomImage()\n  }, [onImageLoad])\n\n  const handleImageLoad = () => {\n    setIsLoading(false)\n  }\n\n  const handleImageError = () => {\n    console.error('Failed to load hero image, falling back to default')\n    setImageUrl(getDefaultHeroImageUrl())\n    setIsLoading(false)\n  }\n\n  return (\n    <div className={cn('relative w-full h-full overflow-hidden', className)}>\n      {/* Loading overlay */}\n      {isLoading && (\n        <div className=\"absolute inset-0 z-10 bg-gradient-to-br from-[var(--primary-green)] to-[var(--secondary-sunset)] flex items-center justify-center\">\n          <div className=\"text-center text-white\">\n            <LoadingSpinner size=\"lg\" className=\"mx-auto mb-4\" />\n            <p className=\"text-sm font-medium\">Loading beautiful safari imagery...</p>\n          </div>\n        </div>\n      )}\n\n      {/* Hero Image */}\n      <Image\n        src={imageUrl}\n        alt=\"Safari landscape\"\n        fill\n        className=\"object-cover\"\n        priority={priority}\n        onLoad={handleImageLoad}\n        onError={handleImageError}\n        sizes=\"100vw\"\n      />\n\n      {/* Dark overlay for text readability */}\n      <div className=\"absolute inset-0 bg-black bg-opacity-40\" />\n\n      {/* Content overlay */}\n      {children && (\n        <div className=\"absolute inset-0 z-20\">\n          {children}\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAeO,SAAS,gBAAgB,EAC9B,SAAS,EACT,QAAQ,EACR,WAAW,KAAK,EAChB,WAAW,EACU;IACrB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,CAAA,GAAA,oIAAA,CAAA,yBAAsB,AAAD;IACtE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,kBAAkB;YACtB,IAAI;gBACF,aAAa;gBACb,MAAM,iBAAiB,MAAM,CAAA,GAAA,oIAAA,CAAA,wBAAqB,AAAD;gBACjD,YAAY;gBACZ,cAAc;YAChB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,6BAA6B;gBAC3C,YAAY,CAAA,GAAA,oIAAA,CAAA,yBAAsB,AAAD;YACnC,SAAU;gBACR,aAAa;YACf;QACF;QAEA;IACF,GAAG;QAAC;KAAY;IAEhB,MAAM,kBAAkB;QACtB,aAAa;IACf;IAEA,MAAM,mBAAmB;QACvB,QAAQ,KAAK,CAAC;QACd,YAAY,CAAA,GAAA,oIAAA,CAAA,yBAAsB,AAAD;QACjC,aAAa;IACf;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;;YAE1D,2BACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,0IAAA,CAAA,iBAAc;4BAAC,MAAK;4BAAK,WAAU;;;;;;sCACpC,8OAAC;4BAAE,WAAU;sCAAsB;;;;;;;;;;;;;;;;;0BAMzC,8OAAC,6HAAA,CAAA,UAAK;gBACJ,KAAK;gBACL,KAAI;gBACJ,IAAI;gBACJ,WAAU;gBACV,UAAU;gBACV,QAAQ;gBACR,SAAS;gBACT,OAAM;;;;;;0BAIR,8OAAC;gBAAI,WAAU;;;;;;YAGd,0BACC,8OAAC;gBAAI,WAAU;0BACZ;;;;;;;;;;;;AAKX", "debugId": null}}, {"offset": {"line": 248, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/src/personal/bvr-safaris/src/components/ui/Input.tsx"], "sourcesContent": ["import { InputHTMLAttributes, forwardRef } from 'react'\nimport { cn } from '@/lib/utils'\n\nexport interface InputProps extends InputHTMLAttributes<HTMLInputElement> {\n  label?: string\n  error?: string\n}\n\nconst Input = forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, label, error, ...props }, ref) => {\n    return (\n      <div className=\"w-full\">\n        {label && (\n          <label className=\"block mb-2 font-semibold text-[var(--primary-brown)] font-[var(--font-ui)]\">\n            {label}\n          </label>\n        )}\n        <input\n          type={type}\n          className={cn(\n            `\n            w-full px-4 py-2 border-2 border-[var(--medium-gray)] \n            rounded-[var(--radius-md)] font-[var(--font-ui)] \n            transition-colors duration-300\n            focus:outline-none focus:border-[var(--primary-brown)] \n            focus:ring-0 focus:shadow-[0_0_0_3px_rgba(139,69,19,0.1)]\n            placeholder:text-[var(--medium-gray)]\n            `,\n            error && 'border-red-500 focus:border-red-500',\n            className\n          )}\n          ref={ref}\n          {...props}\n        />\n        {error && (\n          <p className=\"mt-1 text-sm text-red-600\">{error}</p>\n        )}\n      </div>\n    )\n  }\n)\n\nInput.displayName = 'Input'\n\nexport { Input }"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAOA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACrB,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE;IAC5C,qBACE,8OAAC;QAAI,WAAU;;YACZ,uBACC,8OAAC;gBAAM,WAAU;0BACd;;;;;;0BAGL,8OAAC;gBACC,MAAM;gBACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,CAAC;;;;;;;YAOD,CAAC,EACD,SAAS,uCACT;gBAEF,KAAK;gBACJ,GAAG,KAAK;;;;;;YAEV,uBACC,8OAAC;gBAAE,WAAU;0BAA6B;;;;;;;;;;;;AAIlD;AAGF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 309, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/src/personal/bvr-safaris/src/components/features/SearchBar.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Input } from '@/components/ui/Input'\nimport { Button } from '@/components/ui/Button'\n\nexport interface SearchBarProps {\n  onSearch?: (query: string, location: string) => void\n  placeholder?: string\n  className?: string\n}\n\nexport function SearchBar({ \n  onSearch, \n  placeholder = \"Search farms, activities, or species...\",\n  className \n}: SearchBarProps) {\n  const [query, setQuery] = useState('')\n  const [location, setLocation] = useState('')\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault()\n    onSearch?.(query, location)\n  }\n\n  return (\n    <form onSubmit={handleSubmit} className={className}>\n      <div className=\"flex flex-col md:flex-row gap-4 p-6 bg-white rounded-lg shadow-lg\">\n        <div className=\"flex-1\">\n          <Input\n            type=\"text\"\n            placeholder={placeholder}\n            value={query}\n            onChange={(e) => setQuery(e.target.value)}\n            className=\"border-earth-300 focus:border-accent-600\"\n          />\n        </div>\n        \n        <div className=\"flex-1 md:max-w-xs\">\n          <Input\n            type=\"text\"\n            placeholder=\"Location (province, city)\"\n            value={location}\n            onChange={(e) => setLocation(e.target.value)}\n            className=\"border-earth-300 focus:border-accent-600\"\n          />\n        </div>\n        \n        <Button \n          type=\"submit\" \n          variant=\"primary\" \n          size=\"lg\"\n          className=\"md:px-8\"\n        >\n          🔍 Search\n        </Button>\n      </div>\n    </form>\n  )\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAYO,SAAS,UAAU,EACxB,QAAQ,EACR,cAAc,yCAAyC,EACvD,SAAS,EACM;IACf,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,WAAW,OAAO;IACpB;IAEA,qBACE,8OAAC;QAAK,UAAU;QAAc,WAAW;kBACvC,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;wBACJ,MAAK;wBACL,aAAa;wBACb,OAAO;wBACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;wBACxC,WAAU;;;;;;;;;;;8BAId,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;wBACJ,MAAK;wBACL,aAAY;wBACZ,OAAO;wBACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;wBAC3C,WAAU;;;;;;;;;;;8BAId,8OAAC,kIAAA,CAAA,SAAM;oBACL,MAAK;oBACL,SAAQ;oBACR,MAAK;oBACL,WAAU;8BACX;;;;;;;;;;;;;;;;;AAMT", "debugId": null}}, {"offset": {"line": 399, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/src/personal/bvr-safaris/src/components/ui/Card.tsx"], "sourcesContent": ["import { HTMLAttributes, forwardRef } from 'react'\nimport Image from 'next/image'\nimport { cn } from '@/lib/utils'\n\nexport interface CardProps extends HTMLAttributes<HTMLDivElement> {\n  hover?: boolean\n}\n\nconst Card = forwardRef<HTMLDivElement, CardProps>(\n  ({ className, hover = true, children, ...props }, ref) => {\n    return (\n      <div\n        ref={ref}\n        className={cn(\n          `\n          bg-white rounded-[var(--radius-lg)] shadow-[var(--shadow-md)] \n          overflow-hidden transition-all duration-300\n          `,\n          hover && 'hover:transform hover:-translate-y-1 hover:shadow-[var(--shadow-xl)]',\n          className\n        )}\n        {...props}\n      >\n        {children}\n      </div>\n    )\n  }\n)\n\nCard.displayName = 'Card'\n\nconst CardContent = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn('p-[var(--space-lg)]', className)}\n      {...props}\n    />\n  )\n)\n\nCardContent.displayName = 'CardContent'\n\nconst CardHeader = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn('px-[var(--space-lg)] pt-[var(--space-lg)]', className)}\n      {...props}\n    />\n  )\n)\n\nCardHeader.displayName = 'CardHeader'\n\nconst CardTitle = forwardRef<HTMLHeadingElement, HTMLAttributes<HTMLHeadingElement>>(\n  ({ className, ...props }, ref) => (\n    <h3\n      ref={ref}\n      className={cn(\n        'text-xl font-bold mb-2 text-[var(--primary-brown)] font-[var(--font-display)]',\n        className\n      )}\n      {...props}\n    />\n  )\n)\n\nCardTitle.displayName = 'CardTitle'\n\nconst CardImage = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement> & {\n  src?: string\n  alt?: string\n  children?: React.ReactNode\n}>(\n  ({ className, src, alt, children, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(\n        'relative w-full h-48 bg-gradient-to-br from-[var(--primary-green)] to-[var(--secondary-sunset)] flex items-center justify-center text-white text-5xl',\n        className\n      )}\n      {...props}\n    >\n      {src ? (\n        <Image src={src} alt={alt || ''} fill className=\"object-cover\" />\n      ) : (\n        children\n      )}\n    </div>\n  )\n)\n\nCardImage.displayName = 'CardImage'\n\nexport { Card, CardContent, CardHeader, CardTitle, CardImage }"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;AACA;;;;;AAMA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACpB,CAAC,EAAE,SAAS,EAAE,QAAQ,IAAI,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IAChD,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,CAAC;;;UAGD,CAAC,EACD,SAAS,wEACT;QAED,GAAG,KAAK;kBAER;;;;;;AAGP;AAGF,KAAK,WAAW,GAAG;AAEnB,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAKf,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAC1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAKf,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACzB,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iFACA;QAED,GAAG,KAAK;;;;;;AAKf,UAAU,WAAW,GAAG;AAExB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAKzB,CAAC,EAAE,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAC5C,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wJACA;QAED,GAAG,KAAK;kBAER,oBACC,8OAAC,6HAAA,CAAA,UAAK;YAAC,KAAK;YAAK,KAAK,OAAO;YAAI,IAAI;YAAC,WAAU;;;;;mBAEhD;;;;;;AAMR,UAAU,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 487, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/src/personal/bvr-safaris/src/components/ui/Badge.tsx"], "sourcesContent": ["import { HTMLAttributes, forwardRef } from 'react'\nimport { cn } from '@/lib/utils'\n\nexport interface BadgeProps extends HTMLAttributes<HTMLSpanElement> {\n  variant?: 'hunting' | 'photo' | 'location' | 'default'\n}\n\nconst Badge = forwardRef<HTMLSpanElement, BadgeProps>(\n  ({ className, variant = 'default', ...props }, ref) => {\n    const variants = {\n      hunting: 'bg-[var(--hunting-accent)] text-white',\n      photo: 'bg-[var(--photo-accent)] text-white',\n      location: 'bg-[var(--secondary-stone)] text-white',\n      default: 'bg-[var(--medium-gray)] text-white'\n    }\n\n    return (\n      <span\n        ref={ref}\n        className={cn(\n          'inline-flex items-center px-2 py-1 rounded-[var(--radius-sm)] text-sm font-semibold',\n          variants[variant],\n          className\n        )}\n        {...props}\n      />\n    )\n  }\n)\n\nBadge.displayName = 'Badge'\n\nexport { Badge }"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAMA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACrB,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,GAAG,OAAO,EAAE;IAC7C,MAAM,WAAW;QACf,SAAS;QACT,OAAO;QACP,UAAU;QACV,SAAS;IACX;IAEA,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uFACA,QAAQ,CAAC,QAAQ,EACjB;QAED,GAAG,KAAK;;;;;;AAGf;AAGF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 521, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/src/personal/bvr-safaris/src/components/features/FarmCard.tsx"], "sourcesContent": ["import Link from 'next/link'\nimport { Card, CardContent, CardImage, CardTitle } from '@/components/ui/Card'\nimport { Badge } from '@/components/ui/Badge'\n\nexport interface FarmCardProps {\n  id: string\n  name: string\n  location: string\n  province: string\n  description: string\n  imageUrl?: string\n  activities: ('hunting' | 'photo_safari')[]\n  priceRange?: string\n  rating?: number\n  reviewCount?: number\n}\n\nexport function FarmCard({\n  id,\n  name,\n  location,\n  province,\n  description,\n  imageUrl,\n  activities,\n  priceRange,\n  rating,\n  reviewCount\n}: FarmCardProps) {\n  return (\n    <Link href={`/farms/${id}`}>\n      <Card className=\"h-full cursor-pointer\">\n        <CardImage src={imageUrl} alt={`${name} farm`} />\n        \n        <CardContent className=\"flex flex-col h-full\">\n          <div className=\"flex-1\">\n            <div className=\"flex items-start justify-between mb-3\">\n              <CardTitle className=\"text-lg leading-tight\">{name}</CardTitle>\n              {rating && (\n                <div className=\"flex items-center text-sm text-earth-600\">\n                  <span className=\"text-accent-600 mr-1\">★</span>\n                  <span>{rating}</span>\n                  {reviewCount && <span className=\"ml-1\">({reviewCount})</span>}\n                </div>\n              )}\n            </div>\n\n            <div className=\"text-earth-600 text-sm mb-3\">\n              {location}, {province}\n            </div>\n\n            <p className=\"text-earth-700 text-sm mb-4 line-clamp-3\">\n              {description}\n            </p>\n\n            <div className=\"flex flex-wrap gap-2 mb-4\">\n              {activities.map((activity) => (\n                <Badge \n                  key={activity} \n                  variant={activity === 'hunting' ? 'hunting' : 'photo'}\n                >\n                  {activity === 'hunting' ? 'Hunting' : 'Photo Safari'}\n                </Badge>\n              ))}\n            </div>\n          </div>\n\n          {priceRange && (\n            <div className=\"text-earth-900 font-semibold text-lg\">\n              {priceRange}\n            </div>\n          )}\n        </CardContent>\n      </Card>\n    </Link>\n  )\n}"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAeO,SAAS,SAAS,EACvB,EAAE,EACF,IAAI,EACJ,QAAQ,EACR,QAAQ,EACR,WAAW,EACX,QAAQ,EACR,UAAU,EACV,UAAU,EACV,MAAM,EACN,WAAW,EACG;IACd,qBACE,8OAAC,4JAAA,CAAA,UAAI;QAAC,MAAM,CAAC,OAAO,EAAE,IAAI;kBACxB,cAAA,8OAAC,gIAAA,CAAA,OAAI;YAAC,WAAU;;8BACd,8OAAC,gIAAA,CAAA,YAAS;oBAAC,KAAK;oBAAU,KAAK,GAAG,KAAK,KAAK,CAAC;;;;;;8BAE7C,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAyB;;;;;;wCAC7C,wBACC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAuB;;;;;;8DACvC,8OAAC;8DAAM;;;;;;gDACN,6BAAe,8OAAC;oDAAK,WAAU;;wDAAO;wDAAE;wDAAY;;;;;;;;;;;;;;;;;;;8CAK3D,8OAAC;oCAAI,WAAU;;wCACZ;wCAAS;wCAAG;;;;;;;8CAGf,8OAAC;oCAAE,WAAU;8CACV;;;;;;8CAGH,8OAAC;oCAAI,WAAU;8CACZ,WAAW,GAAG,CAAC,CAAC,yBACf,8OAAC,iIAAA,CAAA,QAAK;4CAEJ,SAAS,aAAa,YAAY,YAAY;sDAE7C,aAAa,YAAY,YAAY;2CAHjC;;;;;;;;;;;;;;;;wBASZ,4BACC,8OAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;;;;;;;;;;;;AAOf", "debugId": null}}, {"offset": {"line": 678, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/src/personal/bvr-safaris/src/components/features/FeaturedFarmsCarousel.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { FarmCard } from './FarmCard'\nimport { ChevronLeft, ChevronRight } from 'lucide-react'\nimport { GameFarm } from '@/lib/types/firestore'\n\ninterface FarmWithStats extends GameFarm {\n  rating?: number\n  reviewCount?: number\n  activities: ('hunting' | 'photo_safari')[]\n  imageUrl?: string\n}\n\ninterface FeaturedFarmsCarouselProps {\n  farms: FarmWithStats[]\n  loading?: boolean\n  error?: string | null\n  onRetry?: () => void\n}\n\nexport function FeaturedFarmsCarousel({ \n  farms, \n  loading = false, \n  error = null, \n  onRetry \n}: FeaturedFarmsCarouselProps) {\n  const [currentIndex, setCurrentIndex] = useState(0)\n  const [isAutoPlaying, setIsAutoPlaying] = useState(true)\n\n  // Auto-play functionality\n  useEffect(() => {\n    if (!isAutoPlaying || farms.length <= 1) return\n\n    const interval = setInterval(() => {\n      setCurrentIndex((prevIndex) => \n        prevIndex === farms.length - 1 ? 0 : prevIndex + 1\n      )\n    }, 5000) // Change slide every 5 seconds\n\n    return () => clearInterval(interval)\n  }, [isAutoPlaying, farms.length])\n\n  const goToPrevious = () => {\n    setIsAutoPlaying(false)\n    setCurrentIndex(currentIndex === 0 ? farms.length - 1 : currentIndex - 1)\n    // Resume auto-play after 10 seconds\n    setTimeout(() => setIsAutoPlaying(true), 10000)\n  }\n\n  const goToNext = () => {\n    setIsAutoPlaying(false)\n    setCurrentIndex(currentIndex === farms.length - 1 ? 0 : currentIndex + 1)\n    // Resume auto-play after 10 seconds\n    setTimeout(() => setIsAutoPlaying(true), 10000)\n  }\n\n  const goToSlide = (index: number) => {\n    setIsAutoPlaying(false)\n    setCurrentIndex(index)\n    // Resume auto-play after 10 seconds\n    setTimeout(() => setIsAutoPlaying(true), 10000)\n  }\n\n  if (loading) {\n    return (\n      <div className=\"text-center py-16\">\n        <p className=\"text-cream\" style={{\n          fontFamily: 'var(--font-body)',\n          fontSize: 'var(--text-secondary-body)'\n        }}>Loading featured farms...</p>\n      </div>\n    )\n  }\n\n  if (error) {\n    return (\n      <div className=\"text-center py-16\">\n        <p className=\"text-red-400 mb-4\" style={{\n          fontFamily: 'var(--font-body)',\n          fontSize: 'var(--text-secondary-body)'\n        }}>{error}</p>\n        {onRetry && (\n          <button\n            onClick={onRetry}\n            className=\"mt-4 px-4 py-2 rounded-lg hover:opacity-80 transition-opacity\"\n            style={{\n              backgroundColor: 'var(--deep-brown)',\n              color: 'var(--cream)',\n              fontFamily: 'var(--font-body)',\n              fontSize: 'var(--text-secondary-body)',\n              borderRadius: '12px'\n            }}\n          >\n            Try Again\n          </button>\n        )}\n      </div>\n    )\n  }\n\n  if (farms.length === 0) {\n    return null\n  }\n\n  return (\n    <div className=\"relative\">\n      {/* Carousel Container */}\n      <div className=\"relative overflow-hidden rounded-lg\">\n        <div \n          className=\"flex transition-transform duration-500 ease-in-out\"\n          style={{ transform: `translateX(-${currentIndex * 100}%)` }}\n        >\n          {farms.map((farm) => (\n            <div key={farm.id} className=\"w-full flex-shrink-0 px-4\">\n              <div className=\"bg-white/10 rounded-lg p-4 backdrop-blur-sm\">\n                <FarmCard\n                  id={farm.id}\n                  name={farm.name}\n                  location={farm.location}\n                  province={farm.province}\n                  description={farm.description || ''}\n                  imageUrl={farm.imageUrl}\n                  activities={farm.activities}\n                  priceRange={farm.pricingInfo || 'Contact for pricing'}\n                  rating={farm.rating}\n                  reviewCount={farm.reviewCount}\n                />\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n\n      {/* Navigation Arrows */}\n      {farms.length > 1 && (\n        <>\n          <button\n            onClick={goToPrevious}\n            className=\"absolute left-4 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white p-2 rounded-full transition-all duration-200 z-10\"\n            aria-label=\"Previous farm\"\n          >\n            <ChevronLeft className=\"w-6 h-6\" />\n          </button>\n          \n          <button\n            onClick={goToNext}\n            className=\"absolute right-4 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white p-2 rounded-full transition-all duration-200 z-10\"\n            aria-label=\"Next farm\"\n          >\n            <ChevronRight className=\"w-6 h-6\" />\n          </button>\n        </>\n      )}\n\n      {/* Dots Indicator */}\n      {farms.length > 1 && (\n        <div className=\"flex justify-center mt-6 space-x-2\">\n          {farms.map((_, index) => (\n            <button\n              key={index}\n              onClick={() => goToSlide(index)}\n              className={`w-3 h-3 rounded-full transition-all duration-200 ${\n                index === currentIndex \n                  ? 'bg-cream' \n                  : 'bg-cream/30 hover:bg-cream/50'\n              }`}\n              aria-label={`Go to farm ${index + 1}`}\n            />\n          ))}\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAJA;;;;;AAqBO,SAAS,sBAAsB,EACpC,KAAK,EACL,UAAU,KAAK,EACf,QAAQ,IAAI,EACZ,OAAO,EACoB;IAC3B,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,0BAA0B;IAC1B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,iBAAiB,MAAM,MAAM,IAAI,GAAG;QAEzC,MAAM,WAAW,YAAY;YAC3B,gBAAgB,CAAC,YACf,cAAc,MAAM,MAAM,GAAG,IAAI,IAAI,YAAY;QAErD,GAAG,MAAM,+BAA+B;;QAExC,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC;QAAe,MAAM,MAAM;KAAC;IAEhC,MAAM,eAAe;QACnB,iBAAiB;QACjB,gBAAgB,iBAAiB,IAAI,MAAM,MAAM,GAAG,IAAI,eAAe;QACvE,oCAAoC;QACpC,WAAW,IAAM,iBAAiB,OAAO;IAC3C;IAEA,MAAM,WAAW;QACf,iBAAiB;QACjB,gBAAgB,iBAAiB,MAAM,MAAM,GAAG,IAAI,IAAI,eAAe;QACvE,oCAAoC;QACpC,WAAW,IAAM,iBAAiB,OAAO;IAC3C;IAEA,MAAM,YAAY,CAAC;QACjB,iBAAiB;QACjB,gBAAgB;QAChB,oCAAoC;QACpC,WAAW,IAAM,iBAAiB,OAAO;IAC3C;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAE,WAAU;gBAAa,OAAO;oBAC/B,YAAY;oBACZ,UAAU;gBACZ;0BAAG;;;;;;;;;;;IAGT;IAEA,IAAI,OAAO;QACT,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAE,WAAU;oBAAoB,OAAO;wBACtC,YAAY;wBACZ,UAAU;oBACZ;8BAAI;;;;;;gBACH,yBACC,8OAAC;oBACC,SAAS;oBACT,WAAU;oBACV,OAAO;wBACL,iBAAiB;wBACjB,OAAO;wBACP,YAAY;wBACZ,UAAU;wBACV,cAAc;oBAChB;8BACD;;;;;;;;;;;;IAMT;IAEA,IAAI,MAAM,MAAM,KAAK,GAAG;QACtB,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,WAAU;oBACV,OAAO;wBAAE,WAAW,CAAC,YAAY,EAAE,eAAe,IAAI,EAAE,CAAC;oBAAC;8BAEzD,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC;4BAAkB,WAAU;sCAC3B,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,0IAAA,CAAA,WAAQ;oCACP,IAAI,KAAK,EAAE;oCACX,MAAM,KAAK,IAAI;oCACf,UAAU,KAAK,QAAQ;oCACvB,UAAU,KAAK,QAAQ;oCACvB,aAAa,KAAK,WAAW,IAAI;oCACjC,UAAU,KAAK,QAAQ;oCACvB,YAAY,KAAK,UAAU;oCAC3B,YAAY,KAAK,WAAW,IAAI;oCAChC,QAAQ,KAAK,MAAM;oCACnB,aAAa,KAAK,WAAW;;;;;;;;;;;2BAZzB,KAAK,EAAE;;;;;;;;;;;;;;;YAqBtB,MAAM,MAAM,GAAG,mBACd;;kCACE,8OAAC;wBACC,SAAS;wBACT,WAAU;wBACV,cAAW;kCAEX,cAAA,8OAAC,oNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;kCAGzB,8OAAC;wBACC,SAAS;wBACT,WAAU;wBACV,cAAW;kCAEX,cAAA,8OAAC,sNAAA,CAAA,eAAY;4BAAC,WAAU;;;;;;;;;;;;;YAM7B,MAAM,MAAM,GAAG,mBACd,8OAAC;gBAAI,WAAU;0BACZ,MAAM,GAAG,CAAC,CAAC,GAAG,sBACb,8OAAC;wBAEC,SAAS,IAAM,UAAU;wBACzB,WAAW,CAAC,iDAAiD,EAC3D,UAAU,eACN,aACA,iCACJ;wBACF,cAAY,CAAC,WAAW,EAAE,QAAQ,GAAG;uBAPhC;;;;;;;;;;;;;;;;AAcnB", "debugId": null}}, {"offset": {"line": 902, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/src/personal/bvr-safaris/src/lib/firebase/firestore.ts"], "sourcesContent": ["import {\n  collection,\n  doc,\n  getDoc,\n  getDocs,\n  addDoc,\n  setDoc,\n  updateDoc,\n  deleteDoc,\n  query,\n  where,\n  orderBy,\n  limit,\n  Timestamp,\n  DocumentSnapshot,\n  QueryDocumentSnapshot,\n  DocumentData,\n  Query\n} from 'firebase/firestore'\nimport { db } from './client'\nimport {\n  UserProfile,\n  GameFarm,\n  Booking,\n  Review,\n  FarmImage,\n  GameSpecies,\n  FarmAmenity,\n  FirestoreDocument\n} from '@/lib/types/firestore'\n\n// Helper function to convert Firestore document to typed object\nexport function docToData<T extends FirestoreDocument>(\n  doc: QueryDocumentSnapshot<DocumentData> | DocumentSnapshot<DocumentData>\n): T | null {\n  if (!doc.exists()) return null\n  \n  const data = doc.data()\n  return {\n    id: doc.id,\n    ...data,\n    // Convert Firestore Timestamps to Date objects for easier handling\n    createdAt: data?.createdAt?.toDate?.() || data?.createdAt,\n    updatedAt: data?.updatedAt?.toDate?.() || data?.updatedAt,\n  } as T\n}\n\n\n\n// User Profile operations\nexport const userProfileService = {\n  async get(userId: string): Promise<UserProfile | null> {\n    const docRef = doc(db, 'users', userId)\n    const docSnap = await getDoc(docRef)\n    return docToData<UserProfile>(docSnap)\n  },\n\n  async create(userId: string, data: Omit<UserProfile, 'id' | 'createdAt' | 'updatedAt'>): Promise<void> {\n    const docRef = doc(db, 'users', userId)\n    const now = new Date()\n    await setDoc(docRef, {\n      ...data,\n      createdAt: Timestamp.fromDate(now),\n      updatedAt: Timestamp.fromDate(now)\n    })\n  },\n\n  async update(userId: string, data: Partial<UserProfile>): Promise<void> {\n    const docRef = doc(db, 'users', userId)\n    await updateDoc(docRef, {\n      ...data,\n      updatedAt: Timestamp.now()\n    })\n  }\n}\n\n// Game Farm operations\nexport const farmService = {\n  async getAll(filters?: {\n    isActive?: boolean\n    featured?: boolean\n    province?: string\n    activityType?: string\n    limit?: number\n  }): Promise<GameFarm[]> {\n    let q: Query<DocumentData> = collection(db, 'farms')\n\n    if (filters?.isActive !== undefined) {\n      q = query(q, where('isActive', '==', filters.isActive))\n    }\n    if (filters?.featured !== undefined) {\n      q = query(q, where('featured', '==', filters.featured))\n    }\n    if (filters?.province) {\n      q = query(q, where('province', '==', filters.province))\n    }\n    if (filters?.activityType) {\n      q = query(q, where('activityTypes', '==', filters.activityType))\n    }\n\n    q = query(q, orderBy('createdAt', 'desc'))\n\n    if (filters?.limit) {\n      q = query(q, limit(filters.limit))\n    }\n\n    const querySnapshot = await getDocs(q)\n    return querySnapshot.docs.map(doc => docToData<GameFarm>(doc)!).filter(Boolean)\n  },\n\n  async get(farmId: string): Promise<GameFarm | null> {\n    const docRef = doc(db, 'farms', farmId)\n    const docSnap = await getDoc(docRef)\n    return docToData<GameFarm>(docSnap)\n  },\n\n  async getByOwner(ownerId: string): Promise<GameFarm[]> {\n    const q = query(\n      collection(db, 'farms'),\n      where('ownerId', '==', ownerId),\n      orderBy('createdAt', 'desc')\n    )\n    const querySnapshot = await getDocs(q)\n    return querySnapshot.docs.map(doc => docToData<GameFarm>(doc)!).filter(Boolean)\n  },\n\n  async getActive(limitCount?: number): Promise<GameFarm[]> {\n    let q = query(\n      collection(db, 'farms'),\n      where('isActive', '==', true),\n      orderBy('createdAt', 'desc')\n    )\n\n    if (limitCount) {\n      q = query(q, limit(limitCount))\n    }\n\n    const querySnapshot = await getDocs(q)\n    return querySnapshot.docs.map(doc => docToData<GameFarm>(doc)!).filter(Boolean)\n  },\n\n  async create(data: Omit<GameFarm, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {\n    const now = new Date()\n    const docRef = await addDoc(collection(db, 'farms'), {\n      ...data,\n      createdAt: Timestamp.fromDate(now),\n      updatedAt: Timestamp.fromDate(now)\n    })\n    return docRef.id\n  },\n\n  async update(farmId: string, data: Partial<GameFarm>): Promise<void> {\n    const docRef = doc(db, 'farms', farmId)\n    await updateDoc(docRef, {\n      ...data,\n      updatedAt: Timestamp.now()\n    })\n  },\n\n  async delete(farmId: string): Promise<void> {\n    const docRef = doc(db, 'farms', farmId)\n    await deleteDoc(docRef)\n  },\n\n  // Add farm images to subcollection\n  async addImage(farmId: string, imageData: Omit<FarmImage, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {\n    const now = new Date()\n    const docRef = await addDoc(collection(db, 'farms', farmId, 'images'), {\n      ...imageData,\n      createdAt: Timestamp.fromDate(now),\n      updatedAt: Timestamp.fromDate(now)\n    })\n    return docRef.id\n  },\n\n  // Get farm images\n  async getImages(farmId: string): Promise<FarmImage[]> {\n    const q = query(\n      collection(db, 'farms', farmId, 'images'),\n      orderBy('displayOrder', 'asc'),\n      orderBy('createdAt', 'asc')\n    )\n    const querySnapshot = await getDocs(q)\n    return querySnapshot.docs.map(doc => docToData<FarmImage>(doc)!).filter(Boolean)\n  },\n\n  // Delete farm image\n  async deleteImage(farmId: string, imageId: string): Promise<void> {\n    const docRef = doc(db, 'farms', farmId, 'images', imageId)\n    await deleteDoc(docRef)\n  }\n}\n\n// Booking operations\nexport const bookingService = {\n  async getAll(filters?: {\n    hunterId?: string\n    farmId?: string\n    status?: string\n    limit?: number\n  }): Promise<Booking[]> {\n    let q: Query<DocumentData> = collection(db, 'bookings')\n\n    if (filters?.hunterId) {\n      q = query(q, where('hunterId', '==', filters.hunterId))\n    }\n    if (filters?.farmId) {\n      q = query(q, where('farmId', '==', filters.farmId))\n    }\n    if (filters?.status) {\n      q = query(q, where('status', '==', filters.status))\n    }\n\n    q = query(q, orderBy('createdAt', 'desc'))\n\n    if (filters?.limit) {\n      q = query(q, limit(filters.limit))\n    }\n\n    const querySnapshot = await getDocs(q)\n    return querySnapshot.docs.map(doc => docToData<Booking>(doc)!).filter(Boolean)\n  },\n\n  async get(bookingId: string): Promise<Booking | null> {\n    const docRef = doc(db, 'bookings', bookingId)\n    const docSnap = await getDoc(docRef)\n    return docToData<Booking>(docSnap)\n  },\n\n  async create(data: Omit<Booking, 'id' | 'createdAt' | 'updatedAt' | 'bookingReference'>): Promise<string> {\n    const now = new Date()\n    const bookingReference = `BVR-${now.getFullYear()}${(now.getMonth() + 1).toString().padStart(2, '0')}${now.getDate().toString().padStart(2, '0')}-${Math.random().toString(36).substring(2, 8).toUpperCase()}`\n    \n    const docRef = await addDoc(collection(db, 'bookings'), {\n      ...data,\n      bookingReference,\n      createdAt: Timestamp.fromDate(now),\n      updatedAt: Timestamp.fromDate(now)\n    })\n    return docRef.id\n  },\n\n  async update(bookingId: string, data: Partial<Booking>): Promise<void> {\n    const docRef = doc(db, 'bookings', bookingId)\n    await updateDoc(docRef, {\n      ...data,\n      updatedAt: Timestamp.now()\n    })\n  }\n}\n\n// Review operations (subcollection under farms)\nexport const reviewService = {\n  async getByFarm(farmId: string): Promise<Review[]> {\n    const q = query(\n      collection(db, 'farms', farmId, 'reviews'),\n      where('isPublic', '==', true),\n      orderBy('createdAt', 'desc')\n    )\n    const querySnapshot = await getDocs(q)\n    return querySnapshot.docs.map(doc => docToData<Review>(doc)!).filter(Boolean)\n  },\n\n  async create(farmId: string, data: Omit<Review, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {\n    const now = new Date()\n    const docRef = await addDoc(collection(db, 'farms', farmId, 'reviews'), {\n      ...data,\n      createdAt: Timestamp.fromDate(now),\n      updatedAt: Timestamp.fromDate(now)\n    })\n    return docRef.id\n  },\n\n  async update(farmId: string, reviewId: string, data: Partial<Review>): Promise<void> {\n    const docRef = doc(db, 'farms', farmId, 'reviews', reviewId)\n    await updateDoc(docRef, {\n      ...data,\n      updatedAt: Timestamp.now()\n    })\n  }\n}\n\n// Species operations\nexport const speciesService = {\n  async getAll(): Promise<GameSpecies[]> {\n    const q = query(collection(db, 'species'), orderBy('name'))\n    const querySnapshot = await getDocs(q)\n    return querySnapshot.docs.map(doc => docToData<GameSpecies>(doc)!).filter(Boolean)\n  },\n\n  async get(speciesId: string): Promise<GameSpecies | null> {\n    const docRef = doc(db, 'species', speciesId)\n    const docSnap = await getDoc(docRef)\n    return docToData<GameSpecies>(docSnap)\n  }\n}\n\n// Amenities operations\nexport const amenityService = {\n  async getAll(): Promise<FarmAmenity[]> {\n    const q = query(collection(db, 'amenities'), orderBy('name'))\n    const querySnapshot = await getDocs(q)\n    return querySnapshot.docs.map(doc => docToData<FarmAmenity>(doc)!).filter(Boolean)\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAAA;AAmBA;AAAA;;;AAaO,SAAS,UACd,GAAyE;IAEzE,IAAI,CAAC,IAAI,MAAM,IAAI,OAAO;IAE1B,MAAM,OAAO,IAAI,IAAI;IACrB,OAAO;QACL,IAAI,IAAI,EAAE;QACV,GAAG,IAAI;QACP,mEAAmE;QACnE,WAAW,MAAM,WAAW,cAAc,MAAM;QAChD,WAAW,MAAM,WAAW,cAAc,MAAM;IAClD;AACF;AAKO,MAAM,qBAAqB;IAChC,MAAM,KAAI,MAAc;QACtB,MAAM,SAAS,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,SAAS;QAChC,MAAM,UAAU,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE;QAC7B,OAAO,UAAuB;IAChC;IAEA,MAAM,QAAO,MAAc,EAAE,IAAyD;QACpF,MAAM,SAAS,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,SAAS;QAChC,MAAM,MAAM,IAAI;QAChB,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE,QAAQ;YACnB,GAAG,IAAI;YACP,WAAW,iKAAA,CAAA,YAAS,CAAC,QAAQ,CAAC;YAC9B,WAAW,iKAAA,CAAA,YAAS,CAAC,QAAQ,CAAC;QAChC;IACF;IAEA,MAAM,QAAO,MAAc,EAAE,IAA0B;QACrD,MAAM,SAAS,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,SAAS;QAChC,MAAM,CAAA,GAAA,iKAAA,CAAA,YAAS,AAAD,EAAE,QAAQ;YACtB,GAAG,IAAI;YACP,WAAW,iKAAA,CAAA,YAAS,CAAC,GAAG;QAC1B;IACF;AACF;AAGO,MAAM,cAAc;IACzB,MAAM,QAAO,OAMZ;QACC,IAAI,IAAyB,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE;QAE5C,IAAI,SAAS,aAAa,WAAW;YACnC,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,YAAY,MAAM,QAAQ,QAAQ;QACvD;QACA,IAAI,SAAS,aAAa,WAAW;YACnC,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,YAAY,MAAM,QAAQ,QAAQ;QACvD;QACA,IAAI,SAAS,UAAU;YACrB,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,YAAY,MAAM,QAAQ,QAAQ;QACvD;QACA,IAAI,SAAS,cAAc;YACzB,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,iBAAiB,MAAM,QAAQ,YAAY;QAChE;QAEA,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;QAElC,IAAI,SAAS,OAAO;YAClB,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,QAAQ,KAAK;QAClC;QAEA,MAAM,gBAAgB,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;QACpC,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,UAAoB,MAAO,MAAM,CAAC;IACzE;IAEA,MAAM,KAAI,MAAc;QACtB,MAAM,SAAS,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,SAAS;QAChC,MAAM,UAAU,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE;QAC7B,OAAO,UAAoB;IAC7B;IAEA,MAAM,YAAW,OAAe;QAC9B,MAAM,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EACZ,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,UACf,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,WAAW,MAAM,UACvB,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;QAEvB,MAAM,gBAAgB,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;QACpC,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,UAAoB,MAAO,MAAM,CAAC;IACzE;IAEA,MAAM,WAAU,UAAmB;QACjC,IAAI,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EACV,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,UACf,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,YAAY,MAAM,OACxB,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;QAGvB,IAAI,YAAY;YACd,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE;QACrB;QAEA,MAAM,gBAAgB,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;QACpC,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,UAAoB,MAAO,MAAM,CAAC;IACzE;IAEA,MAAM,QAAO,IAAsD;QACjE,MAAM,MAAM,IAAI;QAChB,MAAM,SAAS,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,UAAU;YACnD,GAAG,IAAI;YACP,WAAW,iKAAA,CAAA,YAAS,CAAC,QAAQ,CAAC;YAC9B,WAAW,iKAAA,CAAA,YAAS,CAAC,QAAQ,CAAC;QAChC;QACA,OAAO,OAAO,EAAE;IAClB;IAEA,MAAM,QAAO,MAAc,EAAE,IAAuB;QAClD,MAAM,SAAS,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,SAAS;QAChC,MAAM,CAAA,GAAA,iKAAA,CAAA,YAAS,AAAD,EAAE,QAAQ;YACtB,GAAG,IAAI;YACP,WAAW,iKAAA,CAAA,YAAS,CAAC,GAAG;QAC1B;IACF;IAEA,MAAM,QAAO,MAAc;QACzB,MAAM,SAAS,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,SAAS;QAChC,MAAM,CAAA,GAAA,iKAAA,CAAA,YAAS,AAAD,EAAE;IAClB;IAEA,mCAAmC;IACnC,MAAM,UAAS,MAAc,EAAE,SAA4D;QACzF,MAAM,MAAM,IAAI;QAChB,MAAM,SAAS,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,SAAS,QAAQ,WAAW;YACrE,GAAG,SAAS;YACZ,WAAW,iKAAA,CAAA,YAAS,CAAC,QAAQ,CAAC;YAC9B,WAAW,iKAAA,CAAA,YAAS,CAAC,QAAQ,CAAC;QAChC;QACA,OAAO,OAAO,EAAE;IAClB;IAEA,kBAAkB;IAClB,MAAM,WAAU,MAAc;QAC5B,MAAM,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EACZ,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,SAAS,QAAQ,WAChC,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,gBAAgB,QACxB,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;QAEvB,MAAM,gBAAgB,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;QACpC,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,UAAqB,MAAO,MAAM,CAAC;IAC1E;IAEA,oBAAoB;IACpB,MAAM,aAAY,MAAc,EAAE,OAAe;QAC/C,MAAM,SAAS,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,SAAS,QAAQ,UAAU;QAClD,MAAM,CAAA,GAAA,iKAAA,CAAA,YAAS,AAAD,EAAE;IAClB;AACF;AAGO,MAAM,iBAAiB;IAC5B,MAAM,QAAO,OAKZ;QACC,IAAI,IAAyB,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE;QAE5C,IAAI,SAAS,UAAU;YACrB,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,YAAY,MAAM,QAAQ,QAAQ;QACvD;QACA,IAAI,SAAS,QAAQ;YACnB,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,UAAU,MAAM,QAAQ,MAAM;QACnD;QACA,IAAI,SAAS,QAAQ;YACnB,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,UAAU,MAAM,QAAQ,MAAM;QACnD;QAEA,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;QAElC,IAAI,SAAS,OAAO;YAClB,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,QAAQ,KAAK;QAClC;QAEA,MAAM,gBAAgB,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;QACpC,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,UAAmB,MAAO,MAAM,CAAC;IACxE;IAEA,MAAM,KAAI,SAAiB;QACzB,MAAM,SAAS,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,YAAY;QACnC,MAAM,UAAU,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE;QAC7B,OAAO,UAAmB;IAC5B;IAEA,MAAM,QAAO,IAA0E;QACrF,MAAM,MAAM,IAAI;QAChB,MAAM,mBAAmB,CAAC,IAAI,EAAE,IAAI,WAAW,KAAK,CAAC,IAAI,QAAQ,KAAK,CAAC,EAAE,QAAQ,GAAG,QAAQ,CAAC,GAAG,OAAO,IAAI,OAAO,GAAG,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG,GAAG,WAAW,IAAI;QAE9M,MAAM,SAAS,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,aAAa;YACtD,GAAG,IAAI;YACP;YACA,WAAW,iKAAA,CAAA,YAAS,CAAC,QAAQ,CAAC;YAC9B,WAAW,iKAAA,CAAA,YAAS,CAAC,QAAQ,CAAC;QAChC;QACA,OAAO,OAAO,EAAE;IAClB;IAEA,MAAM,QAAO,SAAiB,EAAE,IAAsB;QACpD,MAAM,SAAS,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,YAAY;QACnC,MAAM,CAAA,GAAA,iKAAA,CAAA,YAAS,AAAD,EAAE,QAAQ;YACtB,GAAG,IAAI;YACP,WAAW,iKAAA,CAAA,YAAS,CAAC,GAAG;QAC1B;IACF;AACF;AAGO,MAAM,gBAAgB;IAC3B,MAAM,WAAU,MAAc;QAC5B,MAAM,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EACZ,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,SAAS,QAAQ,YAChC,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,YAAY,MAAM,OACxB,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;QAEvB,MAAM,gBAAgB,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;QACpC,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,UAAkB,MAAO,MAAM,CAAC;IACvE;IAEA,MAAM,QAAO,MAAc,EAAE,IAAoD;QAC/E,MAAM,MAAM,IAAI;QAChB,MAAM,SAAS,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,SAAS,QAAQ,YAAY;YACtE,GAAG,IAAI;YACP,WAAW,iKAAA,CAAA,YAAS,CAAC,QAAQ,CAAC;YAC9B,WAAW,iKAAA,CAAA,YAAS,CAAC,QAAQ,CAAC;QAChC;QACA,OAAO,OAAO,EAAE;IAClB;IAEA,MAAM,QAAO,MAAc,EAAE,QAAgB,EAAE,IAAqB;QAClE,MAAM,SAAS,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,SAAS,QAAQ,WAAW;QACnD,MAAM,CAAA,GAAA,iKAAA,CAAA,YAAS,AAAD,EAAE,QAAQ;YACtB,GAAG,IAAI;YACP,WAAW,iKAAA,CAAA,YAAS,CAAC,GAAG;QAC1B;IACF;AACF;AAGO,MAAM,iBAAiB;IAC5B,MAAM;QACJ,MAAM,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,YAAY,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;QACnD,MAAM,gBAAgB,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;QACpC,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,UAAuB,MAAO,MAAM,CAAC;IAC5E;IAEA,MAAM,KAAI,SAAiB;QACzB,MAAM,SAAS,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,WAAW;QAClC,MAAM,UAAU,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE;QAC7B,OAAO,UAAuB;IAChC;AACF;AAGO,MAAM,iBAAiB;IAC5B,MAAM;QACJ,MAAM,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,cAAc,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;QACrD,MAAM,gBAAgB,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;QACpC,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,UAAuB,MAAO,MAAM,CAAC;IAC5E;AACF", "debugId": null}}, {"offset": {"line": 1124, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/src/personal/bvr-safaris/src/app/page.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useEffect, useState, useCallback } from 'react'\nimport { HeroImageLoader } from '@/components/ui/HeroImageLoader'\nimport { SearchBar } from '@/components/features/SearchBar'\nimport { FeaturedFarmsCarousel } from '@/components/features/FeaturedFarmsCarousel'\nimport { farmService } from '@/lib/firebase/firestore'\nimport { GameFarm } from '@/lib/types/firestore'\nimport { useRouter } from 'next/navigation'\n\ninterface FarmWithStats extends GameFarm {\n  rating?: number\n  reviewCount?: number\n  activities: ('hunting' | 'photo_safari')[]\n  imageUrl?: string\n}\n\nexport default function Home() {\n  const [featuredFarms, setFeaturedFarms] = useState<FarmWithStats[]>([])\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState<string | null>(null)\n\n  const fetchFeaturedFarms = useCallback(async () => {\n    try {\n      setLoading(true)\n      setError(null)\n\n      const farmsData = await farmService.getAll({\n        featured: true,\n        isActive: true,\n        limit: 3\n      })\n\n      const processedFarms: FarmWithStats[] = farmsData.map((farm) => {\n        // Convert activityTypes to activities array\n        const activities: ('hunting' | 'photo_safari')[] = []\n        if (farm.activityTypes === 'hunting' || farm.activityTypes === 'both') {\n          activities.push('hunting')\n        }\n        if (farm.activityTypes === 'photo_safari' || farm.activityTypes === 'both') {\n          activities.push('photo_safari')\n        }\n\n        return {\n          ...farm,\n          activities,\n          rating: undefined, // TODO: Calculate from reviews subcollection\n          reviewCount: 0, // TODO: Count from reviews subcollection\n          imageUrl: '/globe.svg' // TODO: Get from images subcollection\n        }\n      })\n\n      setFeaturedFarms(processedFarms)\n    } catch (err) {\n      console.error('Error fetching featured farms:', err)\n      setError('Failed to load featured farms')\n    } finally {\n      setLoading(false)\n    }\n  }, [])\n\n  useEffect(() => {\n    fetchFeaturedFarms()\n  }, [fetchFeaturedFarms])\n\n  const router = useRouter()\n\n  const handleSearch = (query: string, location: string) => {\n    const params = new URLSearchParams()\n    if (query) params.set('search', query)\n    if (location) params.set('location', location)\n    router.push(`/farms?${params.toString()}`)\n  }\n\n  return (\n    <div className=\"min-h-screen\">\n      {/* Hero Section - Design Guide Layout with Left-Aligned Text */}\n      <section className=\"relative text-white\" style={{\n        backgroundColor: 'var(--dark-charcoal)',\n        height: '600px',\n        minHeight: '600px'\n      }}>\n        <HeroImageLoader priority className=\"absolute inset-0\">\n          <div className=\"relative z-20 h-full flex items-center\" style={{\n            maxWidth: '995px',\n            margin: '0 auto',\n            padding: '0 60px'\n          }}>\n            {/* Left-aligned text container matching design mockup - moved further left */}\n            <div className=\"text-left max-w-2xl\" style={{ marginLeft: '-150px' }}>\n              {/* Hero Title - Design Guide Typography with Design Mockup Text */}\n              <h1 className=\"hero-title mb-6\" style={{\n                fontFamily: 'var(--font-display)',\n                fontSize: 'var(--text-large-heading)',\n                fontWeight: '400',\n                color: 'var(--cream)',\n                lineHeight: '1.4',\n                letterSpacing: '0em'\n              }}>\n                The wild is calling,<br/>\n                will you answer?\n              </h1>\n\n            </div>\n          </div>\n        </HeroImageLoader>\n      </section>\n\n      {/* Search Bar Section - Standalone Section Below Hero */}\n      <section className=\"py-12\" style={{ backgroundColor: 'var(--off-white)' }}>\n        <div className=\"mx-auto\" style={{ maxWidth: '995px', padding: '0 89px' }}>\n          <div className=\"text-center mb-8\">\n            <h2 className=\"section-header mb-4\" style={{\n              fontFamily: 'var(--font-body)',\n              fontSize: 'var(--text-primary-body)',\n              fontWeight: '400',\n              color: 'var(--deep-brown)',\n              lineHeight: '1.4',\n              letterSpacing: '0em'\n            }}>\n              Search\n            </h2>\n            <p className=\"body-secondary max-w-4xl mx-auto\" style={{\n              fontFamily: 'var(--font-body)',\n              fontSize: 'var(--text-secondary-body)',\n              color: 'var(--forest-green)',\n              lineHeight: '1.5',\n              letterSpacing: '0em'\n            }}>\n              Use our powerful search bar to quickly find the ideal hunting destination tailored to your needs.\n              Simply enter a location, province, or game farm name to explore options in your preferred area. You\n              can also filter your search by the type of game available—whether you&rsquo;re after plains game, dangerous\n              game, or specific species like kudu, impala, or warthog. Refine your results further by selecting\n              hunting methods (rifle, bow, or walk-and-stalk), accommodation type, group size, available dates, or\n              budget range. Our intuitive filters make it easy to compare listings and find exactly what you&rsquo;re looking\n              for, whether it&rsquo;s a weekend getaway or a full-scale hunting safari.\n            </p>\n          </div>\n\n          <div className=\"max-w-4xl mx-auto\">\n            <SearchBar onSearch={handleSearch} />\n          </div>\n        </div>\n      </section>\n\n      {/* Featured Farms Carousel - Design Guide Styling */}\n      {featuredFarms.length > 0 && (\n        <section className=\"py-16\" style={{ backgroundColor: 'var(--dark-charcoal)', minHeight: '593px' }}>\n          <div className=\"mx-auto\" style={{ maxWidth: '995px', padding: '0 89px' }}>\n            <div className=\"text-center mb-12\">\n              <h2 className=\"section-header mb-6\" style={{\n                fontFamily: 'var(--font-display)',\n                fontSize: 'var(--text-section-header)',\n                fontWeight: '700',\n                color: 'var(--cream)',\n                lineHeight: '1.4',\n                letterSpacing: '0em'\n              }}>\n                Featured Listings\n              </h2>\n              <p className=\"body-primary max-w-2xl mx-auto\" style={{\n                fontFamily: 'var(--font-body)',\n                fontSize: 'var(--text-primary-body)',\n                color: 'var(--cream)',\n                lineHeight: '1.5',\n                letterSpacing: '0em'\n              }}>\n                Use area as a carousel of recommended listings or specials.\n              </p>\n            </div>\n\n            <FeaturedFarmsCarousel\n              farms={featuredFarms}\n              loading={loading}\n              error={error}\n              onRetry={fetchFeaturedFarms}\n            />\n          </div>\n        </section>\n      )}\n\n      {/* Why Choose Us Section - Design Guide Styling */}\n      <section className=\"py-16\" style={{ backgroundColor: 'var(--off-white)', minHeight: '792px' }}>\n        <div className=\"mx-auto\" style={{ maxWidth: '995px', padding: '0 89px' }}>\n          <div className=\"text-center mb-12\">\n            <h2 className=\"section-header mb-6\" style={{\n              fontFamily: 'var(--font-display)',\n              fontSize: 'var(--text-section-header)',\n              fontWeight: '700',\n              color: 'var(--forest-green)',\n              lineHeight: '1.4',\n              letterSpacing: '0em'\n            }}>\n              Why choose us?\n            </h2>\n          </div>\n\n          <div className=\"max-w-4xl mx-auto\">\n            <p className=\"body-primary text-center\" style={{\n              fontFamily: 'var(--font-body)',\n              fontSize: 'var(--text-primary-body)',\n              color: 'var(--dark-text)',\n              lineHeight: '1.5',\n              letterSpacing: '0em'\n            }}>\n              At BvR Safaris, we connect passionate hunters with premium hunting\n              destinations across South Africa, offering a curated selection of lodges, farms,\n              and outfitters that meet the highest standards of quality, safety, and ethical\n              hunting practices. Whether you&rsquo;re seeking a self-catering bushveld getaway or\n              a fully guided trophy hunt, our platform simplifies the booking process and\n              ensures transparent pricing, verified reviews, and expert support every step of\n              the way. With a focus on conservation, professionalism, and unforgettable\n              outdoor experiences, we are your trusted partner in planning the perfect\n              hunting escape.\n            </p>\n          </div>\n        </div>\n      </section>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAEA;AARA;;;;;;;;AAiBe,SAAS;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IACtE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACrC,IAAI;YACF,WAAW;YACX,SAAS;YAET,MAAM,YAAY,MAAM,mIAAA,CAAA,cAAW,CAAC,MAAM,CAAC;gBACzC,UAAU;gBACV,UAAU;gBACV,OAAO;YACT;YAEA,MAAM,iBAAkC,UAAU,GAAG,CAAC,CAAC;gBACrD,4CAA4C;gBAC5C,MAAM,aAA6C,EAAE;gBACrD,IAAI,KAAK,aAAa,KAAK,aAAa,KAAK,aAAa,KAAK,QAAQ;oBACrE,WAAW,IAAI,CAAC;gBAClB;gBACA,IAAI,KAAK,aAAa,KAAK,kBAAkB,KAAK,aAAa,KAAK,QAAQ;oBAC1E,WAAW,IAAI,CAAC;gBAClB;gBAEA,OAAO;oBACL,GAAG,IAAI;oBACP;oBACA,QAAQ;oBACR,aAAa;oBACb,UAAU,aAAa,sCAAsC;gBAC/D;YACF;YAEA,iBAAiB;QACnB,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,kCAAkC;YAChD,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAmB;IAEvB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe,CAAC,OAAe;QACnC,MAAM,SAAS,IAAI;QACnB,IAAI,OAAO,OAAO,GAAG,CAAC,UAAU;QAChC,IAAI,UAAU,OAAO,GAAG,CAAC,YAAY;QACrC,OAAO,IAAI,CAAC,CAAC,OAAO,EAAE,OAAO,QAAQ,IAAI;IAC3C;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAQ,WAAU;gBAAsB,OAAO;oBAC9C,iBAAiB;oBACjB,QAAQ;oBACR,WAAW;gBACb;0BACE,cAAA,8OAAC,2IAAA,CAAA,kBAAe;oBAAC,QAAQ;oBAAC,WAAU;8BAClC,cAAA,8OAAC;wBAAI,WAAU;wBAAyC,OAAO;4BAC7D,UAAU;4BACV,QAAQ;4BACR,SAAS;wBACX;kCAEE,cAAA,8OAAC;4BAAI,WAAU;4BAAsB,OAAO;gCAAE,YAAY;4BAAS;sCAEjE,cAAA,8OAAC;gCAAG,WAAU;gCAAkB,OAAO;oCACrC,YAAY;oCACZ,UAAU;oCACV,YAAY;oCACZ,OAAO;oCACP,YAAY;oCACZ,eAAe;gCACjB;;oCAAG;kDACmB,8OAAC;;;;;oCAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUnC,8OAAC;gBAAQ,WAAU;gBAAQ,OAAO;oBAAE,iBAAiB;gBAAmB;0BACtE,cAAA,8OAAC;oBAAI,WAAU;oBAAU,OAAO;wBAAE,UAAU;wBAAS,SAAS;oBAAS;;sCACrE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;oCAAsB,OAAO;wCACzC,YAAY;wCACZ,UAAU;wCACV,YAAY;wCACZ,OAAO;wCACP,YAAY;wCACZ,eAAe;oCACjB;8CAAG;;;;;;8CAGH,8OAAC;oCAAE,WAAU;oCAAmC,OAAO;wCACrD,YAAY;wCACZ,UAAU;wCACV,OAAO;wCACP,YAAY;wCACZ,eAAe;oCACjB;8CAAG;;;;;;;;;;;;sCAWL,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,2IAAA,CAAA,YAAS;gCAAC,UAAU;;;;;;;;;;;;;;;;;;;;;;YAM1B,cAAc,MAAM,GAAG,mBACtB,8OAAC;gBAAQ,WAAU;gBAAQ,OAAO;oBAAE,iBAAiB;oBAAwB,WAAW;gBAAQ;0BAC9F,cAAA,8OAAC;oBAAI,WAAU;oBAAU,OAAO;wBAAE,UAAU;wBAAS,SAAS;oBAAS;;sCACrE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;oCAAsB,OAAO;wCACzC,YAAY;wCACZ,UAAU;wCACV,YAAY;wCACZ,OAAO;wCACP,YAAY;wCACZ,eAAe;oCACjB;8CAAG;;;;;;8CAGH,8OAAC;oCAAE,WAAU;oCAAiC,OAAO;wCACnD,YAAY;wCACZ,UAAU;wCACV,OAAO;wCACP,YAAY;wCACZ,eAAe;oCACjB;8CAAG;;;;;;;;;;;;sCAKL,8OAAC,uJAAA,CAAA,wBAAqB;4BACpB,OAAO;4BACP,SAAS;4BACT,OAAO;4BACP,SAAS;;;;;;;;;;;;;;;;;0BAOjB,8OAAC;gBAAQ,WAAU;gBAAQ,OAAO;oBAAE,iBAAiB;oBAAoB,WAAW;gBAAQ;0BAC1F,cAAA,8OAAC;oBAAI,WAAU;oBAAU,OAAO;wBAAE,UAAU;wBAAS,SAAS;oBAAS;;sCACrE,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAG,WAAU;gCAAsB,OAAO;oCACzC,YAAY;oCACZ,UAAU;oCACV,YAAY;oCACZ,OAAO;oCACP,YAAY;oCACZ,eAAe;gCACjB;0CAAG;;;;;;;;;;;sCAKL,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;gCAA2B,OAAO;oCAC7C,YAAY;oCACZ,UAAU;oCACV,OAAO;oCACP,YAAY;oCACZ,eAAe;gCACjB;0CAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgBf", "debugId": null}}]}