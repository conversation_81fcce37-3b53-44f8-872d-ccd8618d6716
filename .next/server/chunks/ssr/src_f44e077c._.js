module.exports = {

"[project]/src/components/ui/Input.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Input": (()=>Input)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-ssr] (ecmascript)");
;
;
;
const Input = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRef"])(({ className, type, label, error, ...props }, ref)=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "w-full",
        children: [
            label && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                className: "block mb-2 font-semibold text-[var(--primary-brown)] font-[var(--font-ui)]",
                children: label
            }, void 0, false, {
                fileName: "[project]/src/components/ui/Input.tsx",
                lineNumber: 14,
                columnNumber: 11
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                type: type,
                className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])(`
            w-full px-4 py-2 border-2 border-[var(--medium-gray)] 
            rounded-[var(--radius-md)] font-[var(--font-ui)] 
            transition-colors duration-300
            focus:outline-none focus:border-[var(--primary-brown)] 
            focus:ring-0 focus:shadow-[0_0_0_3px_rgba(139,69,19,0.1)]
            placeholder:text-[var(--medium-gray)]
            `, error && 'border-red-500 focus:border-red-500', className),
                ref: ref,
                ...props
            }, void 0, false, {
                fileName: "[project]/src/components/ui/Input.tsx",
                lineNumber: 18,
                columnNumber: 9
            }, this),
            error && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                className: "mt-1 text-sm text-red-600",
                children: error
            }, void 0, false, {
                fileName: "[project]/src/components/ui/Input.tsx",
                lineNumber: 36,
                columnNumber: 11
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/ui/Input.tsx",
        lineNumber: 12,
        columnNumber: 7
    }, this);
});
Input.displayName = 'Input';
;
}}),
"[project]/src/components/ui/Card.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Card": (()=>Card),
    "CardContent": (()=>CardContent),
    "CardHeader": (()=>CardHeader),
    "CardImage": (()=>CardImage),
    "CardTitle": (()=>CardTitle)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/image.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-ssr] (ecmascript)");
;
;
;
;
const Card = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRef"])(({ className, hover = true, children, ...props }, ref)=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        ref: ref,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])(`
          bg-white rounded-[var(--radius-lg)] shadow-[var(--shadow-md)] 
          overflow-hidden transition-all duration-300
          `, hover && 'hover:transform hover:-translate-y-1 hover:shadow-[var(--shadow-xl)]', className),
        ...props,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/components/ui/Card.tsx",
        lineNumber: 12,
        columnNumber: 7
    }, this);
});
Card.displayName = 'Card';
const CardContent = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRef"])(({ className, ...props }, ref)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        ref: ref,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])('p-[var(--space-lg)]', className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/Card.tsx",
        lineNumber: 34,
        columnNumber: 5
    }, this));
CardContent.displayName = 'CardContent';
const CardHeader = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRef"])(({ className, ...props }, ref)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        ref: ref,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])('px-[var(--space-lg)] pt-[var(--space-lg)]', className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/Card.tsx",
        lineNumber: 46,
        columnNumber: 5
    }, this));
CardHeader.displayName = 'CardHeader';
const CardTitle = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRef"])(({ className, ...props }, ref)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
        ref: ref,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])('text-xl font-bold mb-2 text-[var(--primary-brown)] font-[var(--font-display)]', className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/Card.tsx",
        lineNumber: 58,
        columnNumber: 5
    }, this));
CardTitle.displayName = 'CardTitle';
const CardImage = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRef"])(({ className, src, alt, children, ...props }, ref)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        ref: ref,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])('relative w-full h-48 bg-gradient-to-br from-[var(--primary-green)] to-[var(--secondary-sunset)] flex items-center justify-center text-white text-5xl', className),
        ...props,
        children: src ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
            src: src,
            alt: alt || '',
            fill: true,
            className: "object-cover"
        }, void 0, false, {
            fileName: "[project]/src/components/ui/Card.tsx",
            lineNumber: 86,
            columnNumber: 9
        }, this) : children
    }, void 0, false, {
        fileName: "[project]/src/components/ui/Card.tsx",
        lineNumber: 77,
        columnNumber: 5
    }, this));
CardImage.displayName = 'CardImage';
;
}}),
"[project]/src/lib/firebase/storage.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "farmImageService": (()=>farmImageService),
    "profileImageService": (()=>profileImageService),
    "storageService": (()=>storageService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$firebase$2f$storage$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/firebase/storage/dist/index.mjs [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$storage$2f$dist$2f$node$2d$esm$2f$index$2e$node$2e$esm$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@firebase/storage/dist/node-esm/index.node.esm.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/firebase/client.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/firebase/config.ts [app-ssr] (ecmascript)");
;
;
;
// Get the Cloud Function URL based on environment
function getCloudFunctionURL() {
    const projectId = ("TURBOPACK compile-time value", "rvbsafaris");
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    if (("TURBOPACK compile-time value", "development") === 'development' && process.env.NEXT_PUBLIC_USE_FIREBASE_EMULATORS === 'true') {
        return `http://localhost:5001/${projectId}/us-central1/uploadImage`;
    }
    // Production URL
    return `https://us-central1-${projectId}.cloudfunctions.net/uploadImage`;
}
// Upload file using Cloud Function
async function uploadFileViaCloudFunction(file, bucket, farmId, userId, options) {
    try {
        // Get authentication token
        const user = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["auth"].currentUser;
        if (!user) {
            throw new Error('User must be authenticated to upload files');
        }
        const token = await user.getIdToken();
        // Create FormData
        const formData = new FormData();
        formData.append('file', file);
        formData.append('bucket', bucket);
        if (farmId) {
            formData.append('farmId', farmId);
        }
        if (userId) {
            formData.append('userId', userId);
        }
        // Simulate progress tracking since we can't get real progress from fetch
        const simulateProgress = ()=>{
            if (!options?.onProgress) return;
            let progress = 0;
            const interval = setInterval(()=>{
                progress += Math.random() * 20;
                if (progress >= 90) {
                    clearInterval(interval);
                    return;
                }
                options.onProgress?.({
                    bytesTransferred: Math.floor(progress / 100 * file.size),
                    totalBytes: file.size,
                    progress
                });
            }, 200);
            return interval;
        };
        const progressInterval = simulateProgress();
        try {
            // Upload to Cloud Function
            const response = await fetch(getCloudFunctionURL(), {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`
                },
                body: formData
            });
            // Clear progress simulation
            if (progressInterval) {
                clearInterval(progressInterval);
            }
            if (!response.ok) {
                const errorData = await response.json().catch(()=>({}));
                throw new Error(errorData.error || `Upload failed with status ${response.status}`);
            }
            const result = await response.json();
            if (!result.success || !result.downloadURL) {
                throw new Error(result.error || 'Upload failed');
            }
            // Complete progress
            options?.onProgress?.({
                bytesTransferred: file.size,
                totalBytes: file.size,
                progress: 100
            });
            options?.onComplete?.(result.downloadURL);
            return result.downloadURL;
        } catch (error) {
            // Clear progress simulation on error
            if (progressInterval) {
                clearInterval(progressInterval);
            }
            const uploadError = error instanceof Error ? error : new Error('Upload failed');
            options?.onError?.(uploadError);
            throw uploadError;
        }
    } catch (error) {
        console.error('Error uploading file via Cloud Function:', error);
        const uploadError = error instanceof Error ? error : new Error('Upload failed');
        options?.onError?.(uploadError);
        throw uploadError;
    }
}
const storageService = {
    // Delete a file from Firebase Storage
    async deleteFile (path) {
        try {
            const storageRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$storage$2f$dist$2f$node$2d$esm$2f$index$2e$node$2e$esm$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ref"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["storage"], path);
            await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$storage$2f$dist$2f$node$2d$esm$2f$index$2e$node$2e$esm$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["deleteObject"])(storageRef);
        } catch (error) {
            console.error('Error deleting file:', error);
            throw error;
        }
    },
    // Get download URL for a file
    async getDownloadURL (path) {
        try {
            const storageRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$storage$2f$dist$2f$node$2d$esm$2f$index$2e$node$2e$esm$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ref"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["storage"], path);
            return await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$storage$2f$dist$2f$node$2d$esm$2f$index$2e$node$2e$esm$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getDownloadURL"])(storageRef);
        } catch (error) {
            console.error('Error getting download URL:', error);
            throw error;
        }
    },
    // List all files in a directory
    async listFiles (path) {
        try {
            const storageRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$storage$2f$dist$2f$node$2d$esm$2f$index$2e$node$2e$esm$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ref"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["storage"], path);
            const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$storage$2f$dist$2f$node$2d$esm$2f$index$2e$node$2e$esm$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["listAll"])(storageRef);
            return result.items;
        } catch (error) {
            console.error('Error listing files:', error);
            throw error;
        }
    },
    // Get file metadata
    async getMetadata (path) {
        try {
            const storageRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$storage$2f$dist$2f$node$2d$esm$2f$index$2e$node$2e$esm$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ref"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["storage"], path);
            return await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$storage$2f$dist$2f$node$2d$esm$2f$index$2e$node$2e$esm$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getMetadata"])(storageRef);
        } catch (error) {
            console.error('Error getting metadata:', error);
            throw error;
        }
    },
    // Update file metadata
    async updateMetadata (path, metadata) {
        try {
            const storageRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$storage$2f$dist$2f$node$2d$esm$2f$index$2e$node$2e$esm$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ref"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["storage"], path);
            return await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$storage$2f$dist$2f$node$2d$esm$2f$index$2e$node$2e$esm$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["updateMetadata"])(storageRef, metadata);
        } catch (error) {
            console.error('Error updating metadata:', error);
            throw error;
        }
    }
};
const farmImageService = {
    // Upload farm image using Cloud Function
    async uploadFarmImage (farmId, file, options) {
        console.log('Using Cloud Function for farm image upload');
        return await uploadFileViaCloudFunction(file, 'farm-images', farmId, undefined, options);
    },
    // Delete farm image
    async deleteFarmImage (farmId, imageUrl) {
        try {
            // Extract path from Firebase Storage URL
            const path = extractPathFromURL(imageUrl);
            if (path && path.startsWith(`farm-images/${farmId}/`)) {
                await storageService.deleteFile(path);
            }
        } catch (error) {
            console.error('Error deleting farm image:', error);
            throw error;
        }
    },
    // List all images for a farm
    async listFarmImages (farmId) {
        try {
            const files = await storageService.listFiles(`farm-images/${farmId}`);
            const urls = await Promise.all(files.map((file)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$storage$2f$dist$2f$node$2d$esm$2f$index$2e$node$2e$esm$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getDownloadURL"])(file)));
            return urls;
        } catch (error) {
            console.error('Error listing farm images:', error);
            throw error;
        }
    }
};
const profileImageService = {
    // Upload profile image using Cloud Function
    async uploadProfileImage (userId, file, options) {
        console.log('Using Cloud Function for profile image upload');
        return await uploadFileViaCloudFunction(file, 'profile-images', undefined, userId, options);
    },
    // Delete profile image
    async deleteProfileImage (userId, imageUrl) {
        try {
            const path = extractPathFromURL(imageUrl);
            if (path && path.startsWith(`profile-images/${userId}/`)) {
                await storageService.deleteFile(path);
            }
        } catch (error) {
            console.error('Error deleting profile image:', error);
            throw error;
        }
    }
};
// Helper function to extract storage path from Firebase Storage URL
function extractPathFromURL(url) {
    try {
        const urlObj = new URL(url);
        if (urlObj.hostname === 'firebasestorage.googleapis.com') {
            const pathMatch = urlObj.pathname.match(/\/o\/(.+)\?/);
            if (pathMatch) {
                return decodeURIComponent(pathMatch[1]);
            }
        }
        return null;
    } catch (error) {
        console.error('Error extracting path from URL:', error);
        return null;
    }
}
}}),
"[project]/src/components/ui/FileUpload.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "FileUpload": (()=>FileUpload),
    "ImageUpload": (()=>ImageUpload)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/image.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/Button.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2f$storage$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/firebase/storage.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
;
;
function FileUpload({ onUpload, bucket, farmId, userId, accept = 'image/*', maxSize = 5, className = '', children }) {
    const [uploading, setUploading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [uploadProgress, setUploadProgress] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(0);
    const [error, setError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [uploadMethod, setUploadMethod] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const fileInputRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    const handleFileSelect = async (event)=>{
        const file = event.target.files?.[0];
        if (!file) return;
        setError(null);
        setUploadProgress(0);
        setUploadMethod(null);
        // Validate file size
        if (file.size > maxSize * 1024 * 1024) {
            setError(`File size must be less than ${maxSize}MB`);
            return;
        }
        // Validate file type
        if (accept && !file.type.match(accept.replace('*', '.*'))) {
            setError('Invalid file type');
            return;
        }
        // Validate required parameters
        if (bucket === 'farm-images' && !farmId) {
            setError('Farm ID is required for farm image uploads');
            return;
        }
        if (bucket === 'profile-images' && !userId) {
            setError('User ID is required for profile image uploads');
            return;
        }
        setUploading(true);
        try {
            let downloadURL;
            if (bucket === 'farm-images' && farmId) {
                console.log('Starting farm image upload for farmId:', farmId);
                // Track which upload method is being used
                const originalConsoleLog = console.log;
                console.log = (...args)=>{
                    if (args[0]?.includes('Using Cloud Function')) {
                        setUploadMethod('cloud-function');
                    } else if (args[0]?.includes('falling back to direct upload')) {
                        setUploadMethod('direct');
                    }
                    originalConsoleLog(...args);
                };
                downloadURL = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2f$storage$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["farmImageService"].uploadFarmImage(farmId, file, {
                    onProgress: (progress)=>{
                        console.log('Upload progress:', progress.progress);
                        setUploadProgress(progress.progress);
                    },
                    onError: (error)=>{
                        console.error('Upload error:', error);
                        setError(error.message);
                    }
                });
                // Restore console.log
                console.log = originalConsoleLog;
            } else if (bucket === 'profile-images' && userId) {
                console.log('Starting profile image upload for userId:', userId);
                // Track which upload method is being used
                const originalConsoleLog = console.log;
                console.log = (...args)=>{
                    if (args[0]?.includes('Using Cloud Function')) {
                        setUploadMethod('cloud-function');
                    } else if (args[0]?.includes('falling back to direct upload')) {
                        setUploadMethod('direct');
                    }
                    originalConsoleLog(...args);
                };
                downloadURL = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2f$storage$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["profileImageService"].uploadProfileImage(userId, file, {
                    onProgress: (progress)=>{
                        console.log('Upload progress:', progress.progress);
                        setUploadProgress(progress.progress);
                    },
                    onError: (error)=>{
                        console.error('Upload error:', error);
                        setError(error.message);
                    }
                });
                // Restore console.log
                console.log = originalConsoleLog;
            } else {
                throw new Error('Invalid bucket or missing required parameters');
            }
            onUpload(downloadURL);
            setUploadProgress(100);
        } catch (error) {
            console.error('Upload error:', error);
            setError(error instanceof Error ? error.message : 'Upload failed');
        } finally{
            setUploading(false);
            // Reset progress after a delay
            setTimeout(()=>setUploadProgress(0), 2000);
        }
    };
    const handleClick = ()=>{
        fileInputRef.current?.click();
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: className,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                ref: fileInputRef,
                type: "file",
                accept: accept,
                onChange: handleFileSelect,
                className: "hidden"
            }, void 0, false, {
                fileName: "[project]/src/components/ui/FileUpload.tsx",
                lineNumber: 147,
                columnNumber: 7
            }, this),
            children ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                onClick: handleClick,
                className: "cursor-pointer",
                children: children
            }, void 0, false, {
                fileName: "[project]/src/components/ui/FileUpload.tsx",
                lineNumber: 156,
                columnNumber: 9
            }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                type: "button",
                variant: "outline",
                onClick: handleClick,
                isLoading: uploading,
                disabled: uploading,
                children: uploading ? `Uploading... ${Math.round(uploadProgress)}%` : 'Upload File'
            }, void 0, false, {
                fileName: "[project]/src/components/ui/FileUpload.tsx",
                lineNumber: 160,
                columnNumber: 9
            }, this),
            error && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                className: "text-red-600 text-sm mt-2",
                children: error
            }, void 0, false, {
                fileName: "[project]/src/components/ui/FileUpload.tsx",
                lineNumber: 172,
                columnNumber: 9
            }, this),
            uploading && uploadMethod && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                className: "text-blue-600 text-xs mt-1",
                children: uploadMethod === 'cloud-function' ? '🚀 Using secure server-side upload' : '📁 Using direct upload'
            }, void 0, false, {
                fileName: "[project]/src/components/ui/FileUpload.tsx",
                lineNumber: 176,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/ui/FileUpload.tsx",
        lineNumber: 146,
        columnNumber: 5
    }, this);
}
function ImageUpload({ currentImage, alt = 'Upload preview', onUpload, bucket, farmId, userId, maxSize = 5, className = '' }) {
    const [uploading, setUploading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [uploadProgress, setUploadProgress] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(0);
    const [error, setError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [preview, setPreview] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(currentImage || null);
    const fileInputRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    const handleFileSelect = async (event)=>{
        const file = event.target.files?.[0];
        if (!file) return;
        setError(null);
        setUploadProgress(0);
        // Validate file size
        if (file.size > maxSize * 1024 * 1024) {
            setError(`File size must be less than ${maxSize}MB`);
            return;
        }
        // Validate file type
        if (!file.type.startsWith('image/')) {
            setError('Please select an image file');
            return;
        }
        // Validate required parameters
        if (bucket === 'farm-images' && !farmId) {
            setError('Farm ID is required for farm image uploads');
            return;
        }
        if (bucket === 'profile-images' && !userId) {
            setError('User ID is required for profile image uploads');
            return;
        }
        // Create preview
        const reader = new FileReader();
        reader.onload = (e)=>{
            setPreview(e.target?.result);
        };
        reader.readAsDataURL(file);
        setUploading(true);
        try {
            let downloadURL;
            if (bucket === 'farm-images' && farmId) {
                console.log('Starting farm image upload for farmId:', farmId);
                downloadURL = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2f$storage$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["farmImageService"].uploadFarmImage(farmId, file, {
                    onProgress: (progress)=>{
                        console.log('Upload progress:', progress.progress);
                        setUploadProgress(progress.progress);
                    },
                    onError: (error)=>{
                        console.error('Upload error:', error);
                        setError(error.message);
                    }
                });
            } else if (bucket === 'profile-images' && userId) {
                console.log('Starting profile image upload for userId:', userId);
                downloadURL = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2f$storage$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["profileImageService"].uploadProfileImage(userId, file, {
                    onProgress: (progress)=>{
                        console.log('Upload progress:', progress.progress);
                        setUploadProgress(progress.progress);
                    },
                    onError: (error)=>{
                        console.error('Upload error:', error);
                        setError(error.message);
                    }
                });
            } else {
                throw new Error('Invalid bucket or missing required parameters');
            }
            // Update preview to use the uploaded URL
            setPreview(downloadURL);
            onUpload(downloadURL);
            setUploadProgress(100);
        } catch (error) {
            console.error('Upload error:', error);
            setError(error instanceof Error ? error.message : 'Upload failed');
            // Reset preview on error
            setPreview(currentImage || null);
        } finally{
            setUploading(false);
            // Reset progress after a delay
            setTimeout(()=>setUploadProgress(0), 2000);
        }
    };
    const handleClick = ()=>{
        fileInputRef.current?.click();
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: className,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                ref: fileInputRef,
                type: "file",
                accept: "image/*",
                onChange: handleFileSelect,
                className: "hidden"
            }, void 0, false, {
                fileName: "[project]/src/components/ui/FileUpload.tsx",
                lineNumber: 299,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                onClick: handleClick,
                className: "relative border-2 border-dashed border-earth-300 rounded-lg p-6 hover:border-accent-600 transition-colors cursor-pointer group",
                children: preview ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "relative",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "relative w-full h-48 rounded-lg overflow-hidden",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                src: preview,
                                alt: alt,
                                fill: true,
                                className: "object-cover",
                                sizes: "(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                            }, void 0, false, {
                                fileName: "[project]/src/components/ui/FileUpload.tsx",
                                lineNumber: 314,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/FileUpload.tsx",
                            lineNumber: 313,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all rounded-lg flex items-center justify-center",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "opacity-0 group-hover:opacity-100 transition-opacity",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                    className: "w-8 h-8 text-white",
                                    fill: "none",
                                    stroke: "currentColor",
                                    viewBox: "0 0 24 24",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                            strokeLinecap: "round",
                                            strokeLinejoin: "round",
                                            strokeWidth: 2,
                                            d: "M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/ui/FileUpload.tsx",
                                            lineNumber: 325,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                            strokeLinecap: "round",
                                            strokeLinejoin: "round",
                                            strokeWidth: 2,
                                            d: "M15 13a3 3 0 11-6 0 3 3 0 016 0z"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/ui/FileUpload.tsx",
                                            lineNumber: 326,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/ui/FileUpload.tsx",
                                    lineNumber: 324,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/ui/FileUpload.tsx",
                                lineNumber: 323,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/FileUpload.tsx",
                            lineNumber: 322,
                            columnNumber: 13
                        }, this),
                        uploading && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "absolute inset-0 bg-black bg-opacity-50 rounded-lg flex items-center justify-center",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "text-center text-white",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-2"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ui/FileUpload.tsx",
                                        lineNumber: 333,
                                        columnNumber: 19
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-sm",
                                        children: [
                                            Math.round(uploadProgress),
                                            "%"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/ui/FileUpload.tsx",
                                        lineNumber: 334,
                                        columnNumber: 19
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/ui/FileUpload.tsx",
                                lineNumber: 332,
                                columnNumber: 17
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/FileUpload.tsx",
                            lineNumber: 331,
                            columnNumber: 15
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/ui/FileUpload.tsx",
                    lineNumber: 312,
                    columnNumber: 11
                }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "text-center",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                            className: "mx-auto h-12 w-12 text-earth-400",
                            stroke: "currentColor",
                            fill: "none",
                            viewBox: "0 0 48 48",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                d: "M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02",
                                strokeWidth: 2,
                                strokeLinecap: "round",
                                strokeLinejoin: "round"
                            }, void 0, false, {
                                fileName: "[project]/src/components/ui/FileUpload.tsx",
                                lineNumber: 342,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/FileUpload.tsx",
                            lineNumber: 341,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "mt-4",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "text-sm text-earth-600",
                                    children: uploading ? `Uploading... ${Math.round(uploadProgress)}%` : 'Click to upload an image'
                                }, void 0, false, {
                                    fileName: "[project]/src/components/ui/FileUpload.tsx",
                                    lineNumber: 345,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "text-xs text-earth-500 mt-1",
                                    children: [
                                        "PNG, JPG, GIF up to ",
                                        maxSize,
                                        "MB"
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/ui/FileUpload.tsx",
                                    lineNumber: 348,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/ui/FileUpload.tsx",
                            lineNumber: 344,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/ui/FileUpload.tsx",
                    lineNumber: 340,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/ui/FileUpload.tsx",
                lineNumber: 307,
                columnNumber: 7
            }, this),
            error && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                className: "text-red-600 text-sm mt-2",
                children: error
            }, void 0, false, {
                fileName: "[project]/src/components/ui/FileUpload.tsx",
                lineNumber: 357,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/ui/FileUpload.tsx",
        lineNumber: 298,
        columnNumber: 5
    }, this);
}
}}),
"[project]/src/lib/types/location.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Location-related types for BVR Safaris geolocation functionality
 */ __turbopack_context__.s({
    "DEFAULT_LOCATION_CONFIG": (()=>DEFAULT_LOCATION_CONFIG),
    "LocationErrorType": (()=>LocationErrorType),
    "PROVINCE_MAPPING": (()=>PROVINCE_MAPPING),
    "SOUTH_AFRICAN_PLACE_TYPES": (()=>SOUTH_AFRICAN_PLACE_TYPES)
});
var LocationErrorType = /*#__PURE__*/ function(LocationErrorType) {
    LocationErrorType["API_ERROR"] = "API_ERROR";
    LocationErrorType["NETWORK_ERROR"] = "NETWORK_ERROR";
    LocationErrorType["INVALID_REQUEST"] = "INVALID_REQUEST";
    LocationErrorType["QUOTA_EXCEEDED"] = "QUOTA_EXCEEDED";
    LocationErrorType["PERMISSION_DENIED"] = "PERMISSION_DENIED";
    LocationErrorType["UNKNOWN_ERROR"] = "UNKNOWN_ERROR";
    return LocationErrorType;
}({});
const PROVINCE_MAPPING = {
    'Eastern Cape': 'Eastern Cape',
    'Free State': 'Free State',
    'Gauteng': 'Gauteng',
    'KwaZulu-Natal': 'KwaZulu-Natal',
    'Limpopo': 'Limpopo',
    'Mpumalanga': 'Mpumalanga',
    'Northern Cape': 'Northern Cape',
    'North West': 'North West',
    'Western Cape': 'Western Cape'
};
const SOUTH_AFRICAN_PLACE_TYPES = {
    ESTABLISHMENT: 'establishment',
    GEOCODE: 'geocode',
    LOCALITY: 'locality',
    SUBLOCALITY: 'sublocality',
    ADMINISTRATIVE_AREA_LEVEL_1: 'administrative_area_level_1',
    ADMINISTRATIVE_AREA_LEVEL_2: 'administrative_area_level_2',
    COUNTRY: 'country',
    POSTAL_CODE: 'postal_code'
};
const DEFAULT_LOCATION_CONFIG = {
    COUNTRY_RESTRICTION: 'za',
    LANGUAGE: 'en',
    REGION: 'za',
    CACHE_TTL: 5 * 60 * 1000,
    DEBOUNCE_DELAY: 300,
    DEFAULT_RADIUS_OPTIONS: [
        5,
        10,
        25,
        50,
        100
    ],
    MAX_AUTOCOMPLETE_RESULTS: 5,
    GEOLOCATION_TIMEOUT: 10000,
    GEOLOCATION_MAX_AGE: 60000 // 1 minute
};
}}),
"[project]/src/lib/services/location/googlePlaces.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Google Places API service for BVR Safaris
 * Handles autocomplete, place details, and geocoding functionality
 */ __turbopack_context__.s({
    "GooglePlacesService": (()=>GooglePlacesService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$types$2f$location$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/types/location.ts [app-ssr] (ecmascript)");
;
class GooglePlacesService {
    apiKey;
    config;
    cache = new Map();
    requestQueue = new Map();
    constructor(apiKey, config){
        this.apiKey = apiKey;
        this.config = {
            apiKey,
            countryRestriction: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$types$2f$location$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DEFAULT_LOCATION_CONFIG"].COUNTRY_RESTRICTION,
            language: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$types$2f$location$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DEFAULT_LOCATION_CONFIG"].LANGUAGE,
            region: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$types$2f$location$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DEFAULT_LOCATION_CONFIG"].REGION,
            ...config
        };
    }
    /**
   * Get place predictions for autocomplete
   */ async getPlacePredictions(input, options) {
        if (!input || input.length < 2) {
            return {
                success: true,
                data: []
            };
        }
        const cacheKey = `predictions_${input}_${JSON.stringify(options)}`;
        // Check cache first
        const cached = this.getFromCache(cacheKey);
        if (cached) {
            return {
                success: true,
                data: cached,
                cached: true
            };
        }
        // Check if request is already in progress
        if (this.requestQueue.has(cacheKey)) {
            try {
                const result = await this.requestQueue.get(cacheKey);
                return {
                    success: true,
                    data: result
                };
            } catch (error) {
                return this.handleError(error);
            }
        }
        // Create new request
        const requestPromise = this.fetchPlacePredictions(input, options);
        this.requestQueue.set(cacheKey, requestPromise);
        try {
            const results = await requestPromise;
            this.setCache(cacheKey, results);
            this.requestQueue.delete(cacheKey);
            return {
                success: true,
                data: results
            };
        } catch (error) {
            this.requestQueue.delete(cacheKey);
            return this.handleError(error);
        }
    }
    /**
   * Get detailed place information
   */ async getPlaceDetails(placeId, fields) {
        const cacheKey = `details_${placeId}_${fields?.join(',') || 'default'}`;
        // Check cache first
        const cached = this.getFromCache(cacheKey);
        if (cached) {
            return {
                success: true,
                data: cached,
                cached: true
            };
        }
        try {
            const details = await this.fetchPlaceDetails(placeId, fields);
            this.setCache(cacheKey, details);
            return {
                success: true,
                data: details
            };
        } catch (error) {
            return this.handleError(error);
        }
    }
    /**
   * Geocode an address to coordinates
   */ async geocodeAddress(address) {
        const cacheKey = `geocode_${address}`;
        // Check cache first
        const cached = this.getFromCache(cacheKey);
        if (cached) {
            return {
                success: true,
                data: cached,
                cached: true
            };
        }
        try {
            const result = await this.fetchGeocode(address);
            this.setCache(cacheKey, result);
            return {
                success: true,
                data: result
            };
        } catch (error) {
            return this.handleError(error);
        }
    }
    /**
   * Reverse geocode coordinates to address
   */ async reverseGeocode(lat, lng) {
        const cacheKey = `reverse_${lat}_${lng}`;
        // Check cache first
        const cached = this.getFromCache(cacheKey);
        if (cached) {
            return {
                success: true,
                data: cached,
                cached: true
            };
        }
        try {
            const result = await this.fetchReverseGeocode(lat, lng);
            this.setCache(cacheKey, result);
            return {
                success: true,
                data: result
            };
        } catch (error) {
            return this.handleError(error);
        }
    }
    /**
   * Private method to fetch place predictions from Google API
   */ async fetchPlacePredictions(input, options) {
        const params = new URLSearchParams({
            input,
            key: this.apiKey,
            language: this.config.language,
            components: `country:${options?.componentRestrictions?.country || this.config.countryRestriction}`
        });
        if (options?.types) {
            params.append('types', options.types.join('|'));
        }
        if (options?.sessionToken) {
            params.append('sessiontoken', options.sessionToken);
        }
        const response = await fetch(`https://maps.googleapis.com/maps/api/place/autocomplete/json?${params}`);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        const data = await response.json();
        if (data.status !== 'OK' && data.status !== 'ZERO_RESULTS') {
            throw new Error(`Google Places API error: ${data.status} - ${data.error_message || 'Unknown error'}`);
        }
        return (data.predictions || []).map((prediction)=>({
                placeId: prediction.place_id,
                description: prediction.description,
                mainText: prediction.structured_formatting?.main_text || prediction.description,
                secondaryText: prediction.structured_formatting?.secondary_text || '',
                types: prediction.types || [],
                structuredFormatting: prediction.structured_formatting
            }));
    }
    /**
   * Private method to fetch place details from Google API
   */ async fetchPlaceDetails(placeId, fields) {
        const defaultFields = [
            'place_id',
            'formatted_address',
            'geometry',
            'address_components',
            'types',
            'name',
            'business_status'
        ];
        const params = new URLSearchParams({
            place_id: placeId,
            key: this.apiKey,
            language: this.config.language,
            fields: (fields || defaultFields).join(',')
        });
        const response = await fetch(`https://maps.googleapis.com/maps/api/place/details/json?${params}`);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        const data = await response.json();
        if (data.status !== 'OK') {
            throw new Error(`Google Places API error: ${data.status} - ${data.error_message || 'Unknown error'}`);
        }
        const place = data.result;
        return {
            placeId: place.place_id,
            formattedAddress: place.formatted_address,
            coordinates: {
                lat: place.geometry.location.lat,
                lng: place.geometry.location.lng
            },
            addressComponents: (place.address_components || []).map((component)=>({
                    longName: component.long_name,
                    shortName: component.short_name,
                    types: component.types
                })),
            types: place.types || [],
            viewport: place.geometry.viewport ? {
                northeast: {
                    lat: place.geometry.viewport.northeast.lat,
                    lng: place.geometry.viewport.northeast.lng
                },
                southwest: {
                    lat: place.geometry.viewport.southwest.lat,
                    lng: place.geometry.viewport.southwest.lng
                }
            } : undefined,
            name: place.name,
            businessStatus: place.business_status
        };
    }
    /**
   * Private method to geocode address
   */ async fetchGeocode(address) {
        const params = new URLSearchParams({
            address,
            key: this.apiKey,
            language: this.config.language,
            region: this.config.region
        });
        const response = await fetch(`https://maps.googleapis.com/maps/api/geocode/json?${params}`);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        const data = await response.json();
        if (data.status !== 'OK') {
            throw new Error(`Geocoding API error: ${data.status} - ${data.error_message || 'Unknown error'}`);
        }
        const result = data.results[0];
        return {
            coordinates: {
                lat: result.geometry.location.lat,
                lng: result.geometry.location.lng
            },
            formattedAddress: result.formatted_address,
            addressComponents: (result.address_components || []).map((component)=>({
                    longName: component.long_name,
                    shortName: component.short_name,
                    types: component.types
                })),
            placeId: result.place_id,
            types: result.types || []
        };
    }
    /**
   * Private method to reverse geocode coordinates
   */ async fetchReverseGeocode(lat, lng) {
        const params = new URLSearchParams({
            latlng: `${lat},${lng}`,
            key: this.apiKey,
            language: this.config.language
        });
        const response = await fetch(`https://maps.googleapis.com/maps/api/geocode/json?${params}`);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        const data = await response.json();
        if (data.status !== 'OK') {
            throw new Error(`Reverse geocoding API error: ${data.status} - ${data.error_message || 'Unknown error'}`);
        }
        const result = data.results[0];
        return {
            placeId: result.place_id,
            formattedAddress: result.formatted_address,
            coordinates: {
                lat,
                lng
            },
            addressComponents: (result.address_components || []).map((component)=>({
                    longName: component.long_name,
                    shortName: component.short_name,
                    types: component.types
                })),
            types: result.types || []
        };
    }
    /**
   * Cache management
   */ getFromCache(key) {
        const entry = this.cache.get(key);
        if (!entry) return null;
        const { data, timestamp, ttl } = entry;
        if (Date.now() - timestamp > ttl) {
            this.cache.delete(key);
            return null;
        }
        return data;
    }
    setCache(key, data, ttl = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$types$2f$location$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DEFAULT_LOCATION_CONFIG"].CACHE_TTL) {
        this.cache.set(key, {
            data,
            timestamp: Date.now(),
            ttl
        });
        // Clean up old entries periodically
        if (this.cache.size > 100) {
            this.cleanupCache();
        }
    }
    cleanupCache() {
        const now = Date.now();
        for (const [key, entry] of this.cache.entries()){
            if (now - entry.timestamp > entry.ttl) {
                this.cache.delete(key);
            }
        }
    }
    /**
   * Error handling
   */ handleError(error) {
        let locationError;
        if (error.message?.includes('quota') || error.message?.includes('OVER_QUERY_LIMIT')) {
            locationError = {
                type: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$types$2f$location$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["LocationErrorType"].QUOTA_EXCEEDED,
                message: 'API quota exceeded. Please try again later.',
                originalError: error
            };
        } else if (error.message?.includes('PERMISSION_DENIED')) {
            locationError = {
                type: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$types$2f$location$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["LocationErrorType"].PERMISSION_DENIED,
                message: 'Permission denied. Please check API key configuration.',
                originalError: error
            };
        } else if (error.message?.includes('INVALID_REQUEST')) {
            locationError = {
                type: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$types$2f$location$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["LocationErrorType"].INVALID_REQUEST,
                message: 'Invalid request parameters.',
                originalError: error
            };
        } else if (error.name === 'TypeError' || error.message?.includes('fetch')) {
            locationError = {
                type: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$types$2f$location$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["LocationErrorType"].NETWORK_ERROR,
                message: 'Network error. Please check your internet connection.',
                originalError: error
            };
        } else {
            locationError = {
                type: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$types$2f$location$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["LocationErrorType"].UNKNOWN_ERROR,
                message: error.message || 'An unknown error occurred.',
                originalError: error
            };
        }
        return {
            success: false,
            error: locationError
        };
    }
    /**
   * Clear cache
   */ clearCache() {
        this.cache.clear();
    }
}
}}),
"[project]/src/lib/services/location/distance.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Distance calculation utilities for BVR Safaris geolocation
 * Provides accurate distance calculations and geographic utilities
 */ __turbopack_context__.s({
    "DistanceService": (()=>DistanceService),
    "GeoUtils": (()=>GeoUtils)
});
class DistanceService {
    /**
   * Calculate distance between two points using Haversine formula
   * Returns distance in kilometers
   */ static calculateDistance(point1, point2) {
        const R = 6371 // Earth's radius in kilometers
        ;
        const dLat = this.toRadians(point2.lat - point1.lat);
        const dLng = this.toRadians(point2.lng - point1.lng);
        const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) + Math.cos(this.toRadians(point1.lat)) * Math.cos(this.toRadians(point2.lat)) * Math.sin(dLng / 2) * Math.sin(dLng / 2);
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        const distance = R * c;
        return Math.round(distance * 100) / 100 // Round to 2 decimal places
        ;
    }
    /**
   * Check if a point is within a specified radius of a center point
   */ static isWithinRadius(center, point, radiusKm) {
        const distance = this.calculateDistance(center, point);
        return distance <= radiusKm;
    }
    /**
   * Calculate bounding box for efficient geographic queries
   * Returns northeast and southwest corners of a box around a center point
   */ static getBoundingBox(center, radiusKm) {
        const R = 6371 // Earth's radius in kilometers
        ;
        const lat = this.toRadians(center.lat);
        const lng = this.toRadians(center.lng);
        // Angular distance in radians on a great circle
        const angular = radiusKm / R;
        let minLat = lat - angular;
        let maxLat = lat + angular;
        let minLng;
        let maxLng;
        if (minLat > this.toRadians(-90) && maxLat < this.toRadians(90)) {
            const deltaLng = Math.asin(Math.sin(angular) / Math.cos(lat));
            minLng = lng - deltaLng;
            maxLng = lng + deltaLng;
            if (minLng < this.toRadians(-180)) minLng += 2 * Math.PI;
            if (maxLng > this.toRadians(180)) maxLng -= 2 * Math.PI;
        } else {
            // A pole is within the distance
            minLat = Math.max(minLat, this.toRadians(-90));
            maxLat = Math.min(maxLat, this.toRadians(90));
            minLng = this.toRadians(-180);
            maxLng = this.toRadians(180);
        }
        return {
            southwest: {
                lat: this.toDegrees(minLat),
                lng: this.toDegrees(minLng)
            },
            northeast: {
                lat: this.toDegrees(maxLat),
                lng: this.toDegrees(maxLng)
            }
        };
    }
    /**
   * Sort an array of points by distance from a center point
   */ static sortByDistance(points, center) {
        return points.map((point)=>({
                ...point,
                distance: this.calculateDistance(center, point)
            })).sort((a, b)=>a.distance - b.distance);
    }
    /**
   * Filter points within a specified radius
   */ static filterByRadius(points, center, radiusKm) {
        return points.map((point)=>({
                ...point,
                distance: this.calculateDistance(center, point)
            })).filter((point)=>point.distance <= radiusKm).sort((a, b)=>a.distance - b.distance);
    }
    /**
   * Get the center point (centroid) of multiple coordinates
   */ static getCenterPoint(points) {
        if (points.length === 0) {
            throw new Error('Cannot calculate center of empty points array');
        }
        if (points.length === 1) {
            return points[0];
        }
        let x = 0;
        let y = 0;
        let z = 0;
        for (const point of points){
            const lat = this.toRadians(point.lat);
            const lng = this.toRadians(point.lng);
            x += Math.cos(lat) * Math.cos(lng);
            y += Math.cos(lat) * Math.sin(lng);
            z += Math.sin(lat);
        }
        const total = points.length;
        x = x / total;
        y = y / total;
        z = z / total;
        const centralLng = Math.atan2(y, x);
        const centralSquareRoot = Math.sqrt(x * x + y * y);
        const centralLat = Math.atan2(z, centralSquareRoot);
        return {
            lat: this.toDegrees(centralLat),
            lng: this.toDegrees(centralLng)
        };
    }
    /**
   * Calculate the bearing (direction) from one point to another
   * Returns bearing in degrees (0-360)
   */ static calculateBearing(from, to) {
        const dLng = this.toRadians(to.lng - from.lng);
        const lat1 = this.toRadians(from.lat);
        const lat2 = this.toRadians(to.lat);
        const y = Math.sin(dLng) * Math.cos(lat2);
        const x = Math.cos(lat1) * Math.sin(lat2) - Math.sin(lat1) * Math.cos(lat2) * Math.cos(dLng);
        let bearing = this.toDegrees(Math.atan2(y, x));
        return (bearing + 360) % 360 // Normalize to 0-360
        ;
    }
    /**
   * Format distance for display
   */ static formatDistance(distanceKm) {
        if (distanceKm < 1) {
            return `${Math.round(distanceKm * 1000)}m`;
        } else if (distanceKm < 10) {
            return `${distanceKm.toFixed(1)}km`;
        } else {
            return `${Math.round(distanceKm)}km`;
        }
    }
    /**
   * Check if a point is within South Africa's approximate bounds
   */ static isWithinSouthAfrica(point) {
        // Approximate bounds of South Africa
        const bounds = {
            north: -22.0,
            south: -35.0,
            east: 33.0,
            west: 16.0
        };
        return point.lat >= bounds.south && point.lat <= bounds.north && point.lng >= bounds.west && point.lng <= bounds.east;
    }
    /**
   * Get approximate travel time based on distance (rough estimation)
   * Assumes average speed of 80 km/h for highway travel
   */ static estimateTravelTime(distanceKm) {
        const averageSpeedKmh = 80;
        const durationMinutes = Math.round(distanceKm / averageSpeedKmh * 60);
        return {
            distance: distanceKm,
            duration: durationMinutes
        };
    }
    /**
   * Format travel time for display
   */ static formatTravelTime(minutes) {
        if (minutes < 60) {
            return `${minutes} min`;
        } else {
            const hours = Math.floor(minutes / 60);
            const remainingMinutes = minutes % 60;
            if (remainingMinutes === 0) {
                return `${hours}h`;
            } else {
                return `${hours}h ${remainingMinutes}m`;
            }
        }
    }
    /**
   * Validate coordinates
   */ static isValidCoordinate(point) {
        return typeof point.lat === 'number' && typeof point.lng === 'number' && point.lat >= -90 && point.lat <= 90 && point.lng >= -180 && point.lng <= 180 && !isNaN(point.lat) && !isNaN(point.lng);
    }
    /**
   * Convert degrees to radians
   */ static toRadians(degrees) {
        return degrees * (Math.PI / 180);
    }
    /**
   * Convert radians to degrees
   */ static toDegrees(radians) {
        return radians * (180 / Math.PI);
    }
}
class GeoUtils {
    /**
   * Generate a simple geohash for efficient geographic indexing
   * This is a simplified version - for production, consider using a proper geohash library
   */ static generateSimpleGeoHash(lat, lng, precision = 6) {
        const latRange = [
            -90,
            90
        ];
        const lngRange = [
            -180,
            180
        ];
        let hash = '';
        let isEven = true;
        let bit = 0;
        let ch = 0;
        while(hash.length < precision){
            if (isEven) {
                const mid = (lngRange[0] + lngRange[1]) / 2;
                if (lng >= mid) {
                    ch |= 1 << 4 - bit;
                    lngRange[0] = mid;
                } else {
                    lngRange[1] = mid;
                }
            } else {
                const mid = (latRange[0] + latRange[1]) / 2;
                if (lat >= mid) {
                    ch |= 1 << 4 - bit;
                    latRange[0] = mid;
                } else {
                    latRange[1] = mid;
                }
            }
            isEven = !isEven;
            bit++;
            if (bit === 5) {
                hash += this.base32[ch];
                bit = 0;
                ch = 0;
            }
        }
        return hash;
    }
    static base32 = '0123456789bcdefghjkmnpqrstuvwxyz';
    /**
   * Create a searchable location string from address components
   */ static createSearchableLocation(addressComponents) {
        const components = [];
        if (addressComponents.route) components.push(addressComponents.route);
        if (addressComponents.locality) components.push(addressComponents.locality);
        if (addressComponents.sublocality) components.push(addressComponents.sublocality);
        if (addressComponents.administrativeAreaLevel2) components.push(addressComponents.administrativeAreaLevel2);
        if (addressComponents.administrativeAreaLevel1) components.push(addressComponents.administrativeAreaLevel1);
        if (addressComponents.postalCode) components.push(addressComponents.postalCode);
        return components.join(' ').toLowerCase();
    }
}
}}),
"[project]/src/lib/services/location/index.ts [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Location services entry point for BVR Safaris
 * Provides a unified interface for all location-related functionality
 */ __turbopack_context__.s({
    "LocationService": (()=>LocationService),
    "getLocationService": (()=>getLocationService),
    "locationUtils": (()=>locationUtils)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$location$2f$googlePlaces$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/services/location/googlePlaces.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$location$2f$distance$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/services/location/distance.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$types$2f$location$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/types/location.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$firebase$2f$firestore$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/firebase/firestore/dist/index.mjs [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@firebase/firestore/dist/index.node.mjs [app-ssr] (ecmascript)");
;
;
;
;
class LocationService {
    googlePlaces;
    static instance;
    constructor(apiKey){
        this.googlePlaces = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$location$2f$googlePlaces$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["GooglePlacesService"](apiKey);
    }
    /**
   * Get singleton instance
   */ static getInstance() {
        if (!LocationService.instance) {
            const apiKey = ("TURBOPACK compile-time value", "AIzaSyDqSUMt9q4adVyPQ398NcnSv0nJslQNT6M");
            if ("TURBOPACK compile-time falsy", 0) {
                "TURBOPACK unreachable";
            }
            LocationService.instance = new LocationService(apiKey);
        }
        return LocationService.instance;
    }
    /**
   * Get place predictions for autocomplete
   */ async getPlacePredictions(input, options) {
        return this.googlePlaces.getPlacePredictions(input, {
            ...options,
            componentRestrictions: {
                country: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$types$2f$location$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DEFAULT_LOCATION_CONFIG"].COUNTRY_RESTRICTION
            }
        });
    }
    /**
   * Get detailed place information and convert to LocationData
   */ async getLocationData(placeId) {
        const response = await this.googlePlaces.getPlaceDetails(placeId);
        if (!response.success || !response.data) {
            return response;
        }
        const placeDetails = response.data;
        const locationData = this.convertToLocationData(placeDetails);
        return {
            success: true,
            data: locationData,
            cached: response.cached
        };
    }
    /**
   * Geocode an address and return LocationData
   */ async geocodeToLocationData(address) {
        const response = await this.googlePlaces.geocodeAddress(address);
        if (!response.success || !response.data) {
            return response;
        }
        const geocodeResult = response.data;
        // Get place details if we have a place ID
        if (geocodeResult.placeId) {
            return this.getLocationData(geocodeResult.placeId);
        }
        // Create LocationData from geocode result
        const locationData = {
            placeId: geocodeResult.placeId || '',
            formattedAddress: geocodeResult.formattedAddress,
            coordinates: new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["GeoPoint"](geocodeResult.coordinates.lat, geocodeResult.coordinates.lng),
            addressComponents: this.parseAddressComponents(geocodeResult.addressComponents),
            placeTypes: geocodeResult.types
        };
        return {
            success: true,
            data: locationData,
            cached: response.cached
        };
    }
    /**
   * Get user's current location using browser geolocation
   */ async getCurrentLocation(options) {
        return new Promise((resolve)=>{
            if (!navigator.geolocation) {
                resolve({
                    success: false,
                    error: {
                        type: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$types$2f$location$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["LocationErrorType"].PERMISSION_DENIED,
                        message: 'Geolocation is not supported by this browser'
                    }
                });
                return;
            }
            const defaultOptions = {
                enableHighAccuracy: true,
                timeout: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$types$2f$location$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DEFAULT_LOCATION_CONFIG"].GEOLOCATION_TIMEOUT,
                maximumAge: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$types$2f$location$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DEFAULT_LOCATION_CONFIG"].GEOLOCATION_MAX_AGE,
                ...options
            };
            navigator.geolocation.getCurrentPosition((position)=>{
                const result = {
                    coordinates: {
                        lat: position.coords.latitude,
                        lng: position.coords.longitude
                    },
                    accuracy: position.coords.accuracy,
                    timestamp: position.timestamp
                };
                resolve({
                    success: true,
                    data: result
                });
            }, (error)=>{
                let errorType;
                let message;
                switch(error.code){
                    case error.PERMISSION_DENIED:
                        errorType = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$types$2f$location$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["LocationErrorType"].PERMISSION_DENIED;
                        message = 'Location access denied by user';
                        break;
                    case error.POSITION_UNAVAILABLE:
                        errorType = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$types$2f$location$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["LocationErrorType"].API_ERROR;
                        message = 'Location information unavailable';
                        break;
                    case error.TIMEOUT:
                        errorType = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$types$2f$location$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["LocationErrorType"].NETWORK_ERROR;
                        message = 'Location request timed out';
                        break;
                    default:
                        errorType = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$types$2f$location$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["LocationErrorType"].UNKNOWN_ERROR;
                        message = 'Unknown location error';
                }
                resolve({
                    success: false,
                    error: {
                        type: errorType,
                        message,
                        originalError: error
                    }
                });
            }, defaultOptions);
        });
    }
    /**
   * Convert PlaceDetails to LocationData format
   */ convertToLocationData(placeDetails) {
        return {
            placeId: placeDetails.placeId,
            formattedAddress: placeDetails.formattedAddress,
            coordinates: new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["GeoPoint"](placeDetails.coordinates.lat, placeDetails.coordinates.lng),
            addressComponents: this.parseAddressComponents(placeDetails.addressComponents),
            placeTypes: placeDetails.types,
            viewport: placeDetails.viewport ? {
                northeast: new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["GeoPoint"](placeDetails.viewport.northeast.lat, placeDetails.viewport.northeast.lng),
                southwest: new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["GeoPoint"](placeDetails.viewport.southwest.lat, placeDetails.viewport.southwest.lng)
            } : undefined,
            name: placeDetails.name
        };
    }
    /**
   * Parse address components into structured format
   */ parseAddressComponents(components) {
        const parsed = {
            country: 'South Africa',
            administrativeAreaLevel1: ''
        };
        for (const component of components){
            const types = component.types;
            if (types.includes('street_number')) {
                parsed.streetNumber = component.longName;
            } else if (types.includes('route')) {
                parsed.route = component.longName;
            } else if (types.includes('locality')) {
                parsed.locality = component.longName;
            } else if (types.includes('sublocality') || types.includes('sublocality_level_1')) {
                parsed.sublocality = component.longName;
            } else if (types.includes('administrative_area_level_1')) {
                parsed.administrativeAreaLevel1 = component.longName;
            } else if (types.includes('administrative_area_level_2')) {
                parsed.administrativeAreaLevel2 = component.longName;
            } else if (types.includes('postal_code')) {
                parsed.postalCode = component.longName;
            } else if (types.includes('country')) {
                parsed.country = component.longName;
            }
        }
        return parsed;
    }
    /**
   * Map location data to South African province
   */ static mapToSouthAfricanProvince(locationData) {
        const provinceName = locationData.addressComponents.administrativeAreaLevel1;
        // Direct mapping
        if (provinceName && __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$types$2f$location$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PROVINCE_MAPPING"][provinceName]) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$types$2f$location$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PROVINCE_MAPPING"][provinceName];
        }
        // Fuzzy matching for common variations
        const normalizedProvince = provinceName?.toLowerCase().trim();
        const provinceMap = {
            'eastern cape': 'Eastern Cape',
            'free state': 'Free State',
            'gauteng': 'Gauteng',
            'kwazulu-natal': 'KwaZulu-Natal',
            'kzn': 'KwaZulu-Natal',
            'limpopo': 'Limpopo',
            'mpumalanga': 'Mpumalanga',
            'northern cape': 'Northern Cape',
            'north west': 'North West',
            'northwest': 'North West',
            'western cape': 'Western Cape'
        };
        return normalizedProvince ? provinceMap[normalizedProvince] || null : null;
    }
    /**
   * Create searchable location string
   */ static createSearchableLocation(locationData) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$location$2f$distance$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["GeoUtils"].createSearchableLocation(locationData.addressComponents);
    }
    /**
   * Generate geohash for location
   */ static generateGeoHash(coordinates) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$location$2f$distance$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["GeoUtils"].generateSimpleGeoHash(coordinates.lat, coordinates.lng);
    }
    /**
   * Clear all caches
   */ clearCache() {
        this.googlePlaces.clearCache();
    }
}
;
;
// Create and export default instance
let defaultLocationService = null;
function getLocationService() {
    if (!defaultLocationService) {
        defaultLocationService = LocationService.getInstance();
    }
    return defaultLocationService;
}
const locationUtils = {
    /**
   * Format coordinates for display
   */ formatCoordinates (lat, lng) {
        return `${lat.toFixed(6)}, ${lng.toFixed(6)}`;
    },
    /**
   * Validate South African coordinates
   */ isValidSouthAfricanLocation (lat, lng) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$location$2f$distance$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DistanceService"].isValidCoordinate({
            lat,
            lng
        }) && __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$location$2f$distance$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DistanceService"].isWithinSouthAfrica({
            lat,
            lng
        });
    },
    /**
   * Get display name for location
   */ getLocationDisplayName (locationData) {
        const { addressComponents } = locationData;
        if (addressComponents.locality && addressComponents.administrativeAreaLevel1) {
            return `${addressComponents.locality}, ${addressComponents.administrativeAreaLevel1}`;
        } else if (addressComponents.administrativeAreaLevel2 && addressComponents.administrativeAreaLevel1) {
            return `${addressComponents.administrativeAreaLevel2}, ${addressComponents.administrativeAreaLevel1}`;
        } else if (addressComponents.administrativeAreaLevel1) {
            return addressComponents.administrativeAreaLevel1;
        } else {
            return locationData.formattedAddress;
        }
    },
    /**
   * Extract city/town from location data
   */ getCityFromLocation (locationData) {
        return locationData.addressComponents.locality || locationData.addressComponents.sublocality || locationData.addressComponents.administrativeAreaLevel2 || null;
    }
};
}}),
"[project]/src/lib/services/location/index.ts [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$location$2f$googlePlaces$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/services/location/googlePlaces.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$location$2f$distance$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/services/location/distance.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$types$2f$location$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/types/location.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$firebase$2f$firestore$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/firebase/firestore/dist/index.mjs [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$location$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/services/location/index.ts [app-ssr] (ecmascript) <locals>");
}}),
"[project]/src/components/ui/LoadingSpinner.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "LoadingSpinner": (()=>LoadingSpinner)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-ssr] (ecmascript)");
;
;
function LoadingSpinner({ size = 'md', className }) {
    const sizeClasses = {
        sm: 'w-4 h-4',
        md: 'w-6 h-6',
        lg: 'w-12 h-12'
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])('flex items-center justify-center', className),
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])('animate-spin rounded-full border-2 border-white border-t-transparent', sizeClasses[size])
        }, void 0, false, {
            fileName: "[project]/src/components/ui/LoadingSpinner.tsx",
            lineNumber: 17,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/ui/LoadingSpinner.tsx",
        lineNumber: 16,
        columnNumber: 5
    }, this);
}
}}),
"[project]/src/components/ui/LocationAutocomplete.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "DistanceFilter": (()=>DistanceFilter),
    "LocationAutocomplete": (()=>LocationAutocomplete),
    "LocationDisplay": (()=>LocationDisplay)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$types$2f$location$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/types/location.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$location$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/services/location/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$location$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/services/location/index.ts [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$LoadingSpinner$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/LoadingSpinner.tsx [app-ssr] (ecmascript)");
'use client';
;
;
;
;
;
;
const LocationAutocomplete = ({ value = '', onLocationSelect, onInputChange, placeholder = 'Enter location...', countryRestriction = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$types$2f$location$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DEFAULT_LOCATION_CONFIG"].COUNTRY_RESTRICTION, types = [], className, disabled = false, required = false, error })=>{
    const [inputValue, setInputValue] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(value);
    const [suggestions, setSuggestions] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isOpen, setIsOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [selectedIndex, setSelectedIndex] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(-1);
    const [sessionToken] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(()=>crypto.randomUUID());
    const inputRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    const dropdownRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    const debounceRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])();
    const locationService = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$location$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["getLocationService"])();
    // Debounced search function
    const debouncedSearch = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (query)=>{
        if (query.length < 2) {
            setSuggestions([]);
            setIsOpen(false);
            return;
        }
        setIsLoading(true);
        try {
            const response = await locationService.getPlacePredictions(query, {
                types: types.length > 0 ? types : undefined,
                sessionToken
            });
            if (response.success && response.data) {
                setSuggestions(response.data.slice(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$types$2f$location$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DEFAULT_LOCATION_CONFIG"].MAX_AUTOCOMPLETE_RESULTS));
                setIsOpen(true);
            } else {
                setSuggestions([]);
                setIsOpen(false);
            }
        } catch (error) {
            console.error('Location search error:', error);
            setSuggestions([]);
            setIsOpen(false);
        } finally{
            setIsLoading(false);
        }
    }, [
        locationService,
        types,
        sessionToken
    ]);
    // Handle input change
    const handleInputChange = (e)=>{
        const newValue = e.target.value;
        setInputValue(newValue);
        setSelectedIndex(-1);
        onInputChange?.(newValue);
        // Clear existing debounce
        if (debounceRef.current) {
            clearTimeout(debounceRef.current);
        }
        // Set new debounce
        debounceRef.current = setTimeout(()=>{
            debouncedSearch(newValue);
        }, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$types$2f$location$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DEFAULT_LOCATION_CONFIG"].DEBOUNCE_DELAY);
    };
    // Handle suggestion selection
    const handleSuggestionSelect = async (suggestion)=>{
        setInputValue(suggestion.description);
        setIsOpen(false);
        setSuggestions([]);
        setIsLoading(true);
        try {
            const response = await locationService.getLocationData(suggestion.placeId);
            if (response.success && response.data) {
                onLocationSelect(response.data);
            }
        } catch (error) {
            console.error('Error getting place details:', error);
        } finally{
            setIsLoading(false);
        }
    };
    // Handle keyboard navigation
    const handleKeyDown = (e)=>{
        if (!isOpen || suggestions.length === 0) return;
        switch(e.key){
            case 'ArrowDown':
                e.preventDefault();
                setSelectedIndex((prev)=>prev < suggestions.length - 1 ? prev + 1 : prev);
                break;
            case 'ArrowUp':
                e.preventDefault();
                setSelectedIndex((prev)=>prev > 0 ? prev - 1 : prev);
                break;
            case 'Enter':
                e.preventDefault();
                if (selectedIndex >= 0 && selectedIndex < suggestions.length) {
                    handleSuggestionSelect(suggestions[selectedIndex]);
                }
                break;
            case 'Escape':
                setIsOpen(false);
                setSelectedIndex(-1);
                inputRef.current?.blur();
                break;
        }
    };
    // Handle click outside
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const handleClickOutside = (event)=>{
            if (dropdownRef.current && !dropdownRef.current.contains(event.target) && !inputRef.current?.contains(event.target)) {
                setIsOpen(false);
                setSelectedIndex(-1);
            }
        };
        document.addEventListener('mousedown', handleClickOutside);
        return ()=>document.removeEventListener('mousedown', handleClickOutside);
    }, []);
    // Update input value when prop changes
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        setInputValue(value);
    }, [
        value
    ]);
    // Cleanup debounce on unmount
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        return ()=>{
            if (debounceRef.current) {
                clearTimeout(debounceRef.current);
            }
        };
    }, []);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])('relative w-full', className),
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "relative",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                        ref: inputRef,
                        type: "text",
                        value: inputValue,
                        onChange: handleInputChange,
                        onKeyDown: handleKeyDown,
                        onFocus: ()=>{
                            if (suggestions.length > 0) {
                                setIsOpen(true);
                            }
                        },
                        placeholder: placeholder,
                        disabled: disabled,
                        required: required,
                        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])(`
            w-full px-4 py-2 pr-10 border-2 border-[var(--medium-gray)] 
            rounded-[var(--radius-md)] font-[var(--font-ui)] 
            transition-colors duration-300
            focus:outline-none focus:border-[var(--primary-brown)] 
            focus:ring-0 focus:shadow-[0_0_0_3px_rgba(139,69,19,0.1)]
            placeholder:text-[var(--medium-gray)]
            disabled:bg-gray-100 disabled:cursor-not-allowed
            `, error && 'border-red-500 focus:border-red-500')
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/LocationAutocomplete.tsx",
                        lineNumber: 177,
                        columnNumber: 9
                    }, this),
                    isLoading && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "absolute right-3 top-1/2 transform -translate-y-1/2",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$LoadingSpinner$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["LoadingSpinner"], {
                            size: "sm"
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/LocationAutocomplete.tsx",
                            lineNumber: 208,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/LocationAutocomplete.tsx",
                        lineNumber: 207,
                        columnNumber: 11
                    }, this),
                    !isLoading && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "absolute right-3 top-1/2 transform -translate-y-1/2 text-[var(--medium-gray)]",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                            width: "16",
                            height: "16",
                            viewBox: "0 0 24 24",
                            fill: "none",
                            stroke: "currentColor",
                            strokeWidth: "2",
                            strokeLinecap: "round",
                            strokeLinejoin: "round",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                    d: "M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/ui/LocationAutocomplete.tsx",
                                    lineNumber: 225,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("circle", {
                                    cx: "12",
                                    cy: "10",
                                    r: "3"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/ui/LocationAutocomplete.tsx",
                                    lineNumber: 226,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/ui/LocationAutocomplete.tsx",
                            lineNumber: 215,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/LocationAutocomplete.tsx",
                        lineNumber: 214,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/ui/LocationAutocomplete.tsx",
                lineNumber: 176,
                columnNumber: 7
            }, this),
            isOpen && suggestions.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                ref: dropdownRef,
                className: "absolute z-50 w-full mt-1 bg-white border border-[var(--medium-gray)] rounded-[var(--radius-md)] shadow-lg max-h-60 overflow-y-auto",
                children: suggestions.map((suggestion, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        onClick: ()=>handleSuggestionSelect(suggestion),
                        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])('px-4 py-3 cursor-pointer border-b border-gray-100 last:border-b-0 transition-colors', 'hover:bg-[var(--light-brown)] hover:bg-opacity-10', selectedIndex === index && 'bg-[var(--light-brown)] bg-opacity-20'),
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-start space-x-3",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex-shrink-0 mt-1 text-[var(--medium-gray)]",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                        width: "14",
                                        height: "14",
                                        viewBox: "0 0 24 24",
                                        fill: "none",
                                        stroke: "currentColor",
                                        strokeWidth: "2",
                                        strokeLinecap: "round",
                                        strokeLinejoin: "round",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                                d: "M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/ui/LocationAutocomplete.tsx",
                                                lineNumber: 260,
                                                columnNumber: 21
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("circle", {
                                                cx: "12",
                                                cy: "10",
                                                r: "3"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/ui/LocationAutocomplete.tsx",
                                                lineNumber: 261,
                                                columnNumber: 21
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/ui/LocationAutocomplete.tsx",
                                        lineNumber: 250,
                                        columnNumber: 19
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/ui/LocationAutocomplete.tsx",
                                    lineNumber: 249,
                                    columnNumber: 17
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex-1 min-w-0",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "font-medium text-[var(--primary-brown)] truncate",
                                            children: suggestion.mainText
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/ui/LocationAutocomplete.tsx",
                                            lineNumber: 265,
                                            columnNumber: 19
                                        }, this),
                                        suggestion.secondaryText && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "text-sm text-[var(--medium-gray)] truncate",
                                            children: suggestion.secondaryText
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/ui/LocationAutocomplete.tsx",
                                            lineNumber: 269,
                                            columnNumber: 21
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/ui/LocationAutocomplete.tsx",
                                    lineNumber: 264,
                                    columnNumber: 17
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/ui/LocationAutocomplete.tsx",
                            lineNumber: 248,
                            columnNumber: 15
                        }, this)
                    }, suggestion.placeId, false, {
                        fileName: "[project]/src/components/ui/LocationAutocomplete.tsx",
                        lineNumber: 239,
                        columnNumber: 13
                    }, this))
            }, void 0, false, {
                fileName: "[project]/src/components/ui/LocationAutocomplete.tsx",
                lineNumber: 234,
                columnNumber: 9
            }, this),
            error && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                className: "mt-1 text-sm text-red-600",
                children: error
            }, void 0, false, {
                fileName: "[project]/src/components/ui/LocationAutocomplete.tsx",
                lineNumber: 282,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/ui/LocationAutocomplete.tsx",
        lineNumber: 175,
        columnNumber: 5
    }, this);
};
const LocationDisplay = ({ locationData, showFullAddress = false, className })=>{
    if (!locationData) return null;
    const displayText = showFullAddress ? locationData.formattedAddress : `${locationData.addressComponents?.locality || ''}, ${locationData.addressComponents?.administrativeAreaLevel1 || ''}`.replace(/^,\s*/, '');
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])('flex items-center space-x-2 text-[var(--medium-gray)]', className),
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                width: "14",
                height: "14",
                viewBox: "0 0 24 24",
                fill: "none",
                stroke: "currentColor",
                strokeWidth: "2",
                strokeLinecap: "round",
                strokeLinejoin: "round",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/LocationAutocomplete.tsx",
                        lineNumber: 314,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("circle", {
                        cx: "12",
                        cy: "10",
                        r: "3"
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/LocationAutocomplete.tsx",
                        lineNumber: 315,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/ui/LocationAutocomplete.tsx",
                lineNumber: 304,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                className: "text-sm",
                children: displayText
            }, void 0, false, {
                fileName: "[project]/src/components/ui/LocationAutocomplete.tsx",
                lineNumber: 317,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/ui/LocationAutocomplete.tsx",
        lineNumber: 303,
        columnNumber: 5
    }, this);
};
const DistanceFilter = ({ value, onChange, options = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$types$2f$location$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DEFAULT_LOCATION_CONFIG"].DEFAULT_RADIUS_OPTIONS, className, label = 'Distance' })=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])('w-full', className),
        children: [
            label && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                className: "block mb-2 font-semibold text-[var(--primary-brown)] font-[var(--font-ui)]",
                children: label
            }, void 0, false, {
                fileName: "[project]/src/components/ui/LocationAutocomplete.tsx",
                lineNumber: 341,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("select", {
                value: value || '',
                onChange: (e)=>onChange(Number(e.target.value)),
                className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])(`
          w-full px-4 py-2 border-2 border-[var(--medium-gray)]
          rounded-[var(--radius-md)] font-[var(--font-ui)]
          transition-colors duration-300
          focus:outline-none focus:border-[var(--primary-brown)]
          focus:ring-0 focus:shadow-[0_0_0_3px_rgba(139,69,19,0.1)]
          bg-white
          `),
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                        value: "",
                        children: "Any distance"
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/LocationAutocomplete.tsx",
                        lineNumber: 359,
                        columnNumber: 9
                    }, this),
                    options.map((distance)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                            value: distance,
                            children: [
                                "Within ",
                                distance,
                                "km"
                            ]
                        }, distance, true, {
                            fileName: "[project]/src/components/ui/LocationAutocomplete.tsx",
                            lineNumber: 361,
                            columnNumber: 11
                        }, this))
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/ui/LocationAutocomplete.tsx",
                lineNumber: 345,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/ui/LocationAutocomplete.tsx",
        lineNumber: 339,
        columnNumber: 5
    }, this);
};
}}),
"[project]/src/lib/firebase/firestore.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "amenityService": (()=>amenityService),
    "bookingService": (()=>bookingService),
    "docToData": (()=>docToData),
    "farmService": (()=>farmService),
    "reviewService": (()=>reviewService),
    "speciesService": (()=>speciesService),
    "userProfileService": (()=>userProfileService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$firebase$2f$firestore$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/firebase/firestore/dist/index.mjs [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@firebase/firestore/dist/index.node.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/firebase/client.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/firebase/config.ts [app-ssr] (ecmascript)");
;
;
function docToData(doc) {
    if (!doc.exists()) return null;
    const data = doc.data();
    return {
        id: doc.id,
        ...data,
        // Convert Firestore Timestamps to Date objects for easier handling
        createdAt: data?.createdAt?.toDate?.() || data?.createdAt,
        updatedAt: data?.updatedAt?.toDate?.() || data?.updatedAt
    };
}
const userProfileService = {
    async get (userId) {
        const docRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["doc"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["db"], 'users', userId);
        const docSnap = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getDoc"])(docRef);
        return docToData(docSnap);
    },
    async create (userId, data) {
        const docRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["doc"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["db"], 'users', userId);
        const now = new Date();
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["setDoc"])(docRef, {
            ...data,
            createdAt: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Timestamp"].fromDate(now),
            updatedAt: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Timestamp"].fromDate(now)
        });
    },
    async update (userId, data) {
        const docRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["doc"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["db"], 'users', userId);
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["updateDoc"])(docRef, {
            ...data,
            updatedAt: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Timestamp"].now()
        });
    }
};
const farmService = {
    async getAll (filters) {
        let q = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["collection"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["db"], 'farms');
        if (filters?.isActive !== undefined) {
            q = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["query"])(q, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["where"])('isActive', '==', filters.isActive));
        }
        if (filters?.featured !== undefined) {
            q = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["query"])(q, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["where"])('featured', '==', filters.featured));
        }
        if (filters?.province) {
            q = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["query"])(q, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["where"])('province', '==', filters.province));
        }
        if (filters?.activityType) {
            q = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["query"])(q, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["where"])('activityTypes', '==', filters.activityType));
        }
        q = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["query"])(q, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["orderBy"])('createdAt', 'desc'));
        if (filters?.limit) {
            q = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["query"])(q, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["limit"])(filters.limit));
        }
        const querySnapshot = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getDocs"])(q);
        return querySnapshot.docs.map((doc)=>docToData(doc)).filter(Boolean);
    },
    async get (farmId) {
        const docRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["doc"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["db"], 'farms', farmId);
        const docSnap = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getDoc"])(docRef);
        return docToData(docSnap);
    },
    async getByOwner (ownerId) {
        const q = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["query"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["collection"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["db"], 'farms'), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["where"])('ownerId', '==', ownerId), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["orderBy"])('createdAt', 'desc'));
        const querySnapshot = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getDocs"])(q);
        return querySnapshot.docs.map((doc)=>docToData(doc)).filter(Boolean);
    },
    async getActive (limitCount) {
        let q = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["query"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["collection"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["db"], 'farms'), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["where"])('isActive', '==', true), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["orderBy"])('createdAt', 'desc'));
        if (limitCount) {
            q = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["query"])(q, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["limit"])(limitCount));
        }
        const querySnapshot = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getDocs"])(q);
        return querySnapshot.docs.map((doc)=>docToData(doc)).filter(Boolean);
    },
    async create (data) {
        const now = new Date();
        const docRef = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["addDoc"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["collection"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["db"], 'farms'), {
            ...data,
            createdAt: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Timestamp"].fromDate(now),
            updatedAt: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Timestamp"].fromDate(now)
        });
        return docRef.id;
    },
    async update (farmId, data) {
        const docRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["doc"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["db"], 'farms', farmId);
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["updateDoc"])(docRef, {
            ...data,
            updatedAt: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Timestamp"].now()
        });
    },
    async delete (farmId) {
        const docRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["doc"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["db"], 'farms', farmId);
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["deleteDoc"])(docRef);
    },
    // Add farm images to subcollection
    async addImage (farmId, imageData) {
        const now = new Date();
        const docRef = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["addDoc"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["collection"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["db"], 'farms', farmId, 'images'), {
            ...imageData,
            createdAt: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Timestamp"].fromDate(now),
            updatedAt: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Timestamp"].fromDate(now)
        });
        return docRef.id;
    },
    // Get farm images
    async getImages (farmId) {
        const q = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["query"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["collection"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["db"], 'farms', farmId, 'images'), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["orderBy"])('displayOrder', 'asc'), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["orderBy"])('createdAt', 'asc'));
        const querySnapshot = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getDocs"])(q);
        return querySnapshot.docs.map((doc)=>docToData(doc)).filter(Boolean);
    },
    // Delete farm image
    async deleteImage (farmId, imageId) {
        const docRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["doc"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["db"], 'farms', farmId, 'images', imageId);
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["deleteDoc"])(docRef);
    }
};
const bookingService = {
    async getAll (filters) {
        let q = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["collection"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["db"], 'bookings');
        if (filters?.hunterId) {
            q = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["query"])(q, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["where"])('hunterId', '==', filters.hunterId));
        }
        if (filters?.farmId) {
            q = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["query"])(q, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["where"])('farmId', '==', filters.farmId));
        }
        if (filters?.status) {
            q = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["query"])(q, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["where"])('status', '==', filters.status));
        }
        q = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["query"])(q, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["orderBy"])('createdAt', 'desc'));
        if (filters?.limit) {
            q = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["query"])(q, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["limit"])(filters.limit));
        }
        const querySnapshot = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getDocs"])(q);
        return querySnapshot.docs.map((doc)=>docToData(doc)).filter(Boolean);
    },
    async get (bookingId) {
        const docRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["doc"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["db"], 'bookings', bookingId);
        const docSnap = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getDoc"])(docRef);
        return docToData(docSnap);
    },
    async create (data) {
        const now = new Date();
        const bookingReference = `BVR-${now.getFullYear()}${(now.getMonth() + 1).toString().padStart(2, '0')}${now.getDate().toString().padStart(2, '0')}-${Math.random().toString(36).substring(2, 8).toUpperCase()}`;
        const docRef = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["addDoc"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["collection"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["db"], 'bookings'), {
            ...data,
            bookingReference,
            createdAt: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Timestamp"].fromDate(now),
            updatedAt: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Timestamp"].fromDate(now)
        });
        return docRef.id;
    },
    async update (bookingId, data) {
        const docRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["doc"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["db"], 'bookings', bookingId);
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["updateDoc"])(docRef, {
            ...data,
            updatedAt: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Timestamp"].now()
        });
    }
};
const reviewService = {
    async getByFarm (farmId) {
        const q = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["query"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["collection"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["db"], 'farms', farmId, 'reviews'), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["where"])('isPublic', '==', true), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["orderBy"])('createdAt', 'desc'));
        const querySnapshot = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getDocs"])(q);
        return querySnapshot.docs.map((doc)=>docToData(doc)).filter(Boolean);
    },
    async create (farmId, data) {
        const now = new Date();
        const docRef = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["addDoc"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["collection"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["db"], 'farms', farmId, 'reviews'), {
            ...data,
            createdAt: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Timestamp"].fromDate(now),
            updatedAt: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Timestamp"].fromDate(now)
        });
        return docRef.id;
    },
    async update (farmId, reviewId, data) {
        const docRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["doc"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["db"], 'farms', farmId, 'reviews', reviewId);
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["updateDoc"])(docRef, {
            ...data,
            updatedAt: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Timestamp"].now()
        });
    }
};
const speciesService = {
    async getAll () {
        const q = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["query"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["collection"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["db"], 'species'), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["orderBy"])('name'));
        const querySnapshot = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getDocs"])(q);
        return querySnapshot.docs.map((doc)=>docToData(doc)).filter(Boolean);
    },
    async get (speciesId) {
        const docRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["doc"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["db"], 'species', speciesId);
        const docSnap = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getDoc"])(docRef);
        return docToData(docSnap);
    }
};
const amenityService = {
    async getAll () {
        const q = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["query"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["collection"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["db"], 'amenities'), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["orderBy"])('name'));
        const querySnapshot = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getDocs"])(q);
        return querySnapshot.docs.map((doc)=>docToData(doc)).filter(Boolean);
    }
};
}}),
"[project]/src/lib/services/farmService.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Enhanced farm service with location-based functionality
 * Extends the basic Firestore operations with geolocation features
 */ __turbopack_context__.s({
    "EnhancedFarmService": (()=>EnhancedFarmService),
    "enhancedFarmService": (()=>enhancedFarmService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$firebase$2f$firestore$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/firebase/firestore/dist/index.mjs [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@firebase/firestore/dist/index.node.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/firebase/client.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/firebase/config.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$location$2f$distance$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/services/location/distance.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$location$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/services/location/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$location$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/services/location/index.ts [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2f$firestore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/firebase/firestore.ts [app-ssr] (ecmascript)");
;
;
;
;
;
class EnhancedFarmService {
    locationService;
    constructor(){
        this.locationService = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$location$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["LocationService"].getInstance();
    }
    /**
   * Search farms with location-based filtering and sorting
   */ async searchFarms(filters = {}) {
        let baseQuery = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["collection"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["db"], 'farms');
        // Apply basic filters
        baseQuery = this.applyBasicFilters(baseQuery, filters);
        // Get initial results
        let querySnapshot = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getDocs"])(baseQuery);
        let farms = querySnapshot.docs.map((doc)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2f$firestore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["docToData"])(doc)).filter(Boolean);
        // Apply location-based filtering and sorting
        if (filters.location) {
            farms = await this.applyLocationFilters(farms, filters.location, filters.sortBy);
        }
        // Apply other client-side filters
        farms = this.applyClientSideFilters(farms, filters);
        // Apply pagination
        const { farms: paginatedFarms, hasMore } = this.applyPagination(farms, filters);
        return {
            farms: paginatedFarms,
            total: farms.length,
            hasMore,
            searchCenter: filters.location?.center,
            searchRadius: filters.location?.radius
        };
    }
    /**
   * Get farms near a specific location
   */ async getFarmsNearLocation(center, radiusKm = 50, limitCount = 20) {
        // Get all active farms first
        const q = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["query"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["collection"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["db"], 'farms'), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["where"])('isActive', '==', true), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["limit"])(limitCount * 2) // Get more than needed to account for distance filtering
        );
        const querySnapshot = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getDocs"])(q);
        const farms = querySnapshot.docs.map((doc)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2f$firestore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["docToData"])(doc)).filter(Boolean);
        // Filter by distance and add distance information
        const farmsWithDistance = [];
        for (const farm of farms){
            if (farm.locationData?.coordinates) {
                const farmCoords = {
                    lat: farm.locationData.coordinates.latitude,
                    lng: farm.locationData.coordinates.longitude
                };
                const distance = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$location$2f$distance$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DistanceService"].calculateDistance(center, farmCoords);
                if (distance <= radiusKm) {
                    const travelTime = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$location$2f$distance$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DistanceService"].estimateTravelTime(distance);
                    farmsWithDistance.push({
                        ...farm,
                        distance,
                        travelTime: travelTime.duration
                    });
                }
            }
        }
        // Sort by distance and limit results
        return farmsWithDistance.sort((a, b)=>(a.distance || 0) - (b.distance || 0)).slice(0, limitCount);
    }
    /**
   * Create a new farm with enhanced location data
   */ async createFarm(data, ownerId) {
        const now = new Date();
        // Generate additional location-based fields
        const enhancedData = {
            ...data,
            ownerId: data.ownerId || ownerId,
            createdAt: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Timestamp"].fromDate(now),
            updatedAt: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Timestamp"].fromDate(now),
            isActive: true,
            featured: false
        };
        // Generate searchable location and geohash if location data exists
        if (data.locationData) {
            enhancedData.searchableLocation = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$location$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["LocationService"].createSearchableLocation(data.locationData);
            enhancedData.geoHash = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$location$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["LocationService"].generateGeoHash({
                lat: data.locationData.coordinates.latitude,
                lng: data.locationData.coordinates.longitude
            });
            // Ensure province is set from location data
            const mappedProvince = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$location$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["LocationService"].mapToSouthAfricanProvince(data.locationData);
            if (mappedProvince) {
                enhancedData.province = mappedProvince;
            }
        }
        const docRef = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["addDoc"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["collection"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["db"], 'farms'), enhancedData);
        return docRef.id;
    }
    /**
   * Update farm with location data processing
   */ async updateFarm(farmId, data) {
        const updateData = {
            ...data,
            updatedAt: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Timestamp"].now()
        };
        // Update location-based fields if location data changed
        if (data.locationData) {
            updateData.searchableLocation = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$location$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["LocationService"].createSearchableLocation(data.locationData);
            updateData.geoHash = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$location$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["LocationService"].generateGeoHash({
                lat: data.locationData.coordinates.latitude,
                lng: data.locationData.coordinates.longitude
            });
            // Update province from location data
            const mappedProvince = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$location$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["LocationService"].mapToSouthAfricanProvince(data.locationData);
            if (mappedProvince) {
                updateData.province = mappedProvince;
            }
        }
        const docRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["doc"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["db"], 'farms', farmId);
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["updateDoc"])(docRef, updateData);
    }
    /**
   * Get farm with distance from a reference point
   */ async getFarmWithDistance(farmId, referencePoint) {
        const docRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["doc"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2f$config$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["db"], 'farms', farmId);
        const docSnap = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getDoc"])(docRef);
        const farm = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2f$firestore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["docToData"])(docSnap);
        if (!farm) return null;
        const farmWithDistance = {
            ...farm
        };
        if (referencePoint && farm.locationData?.coordinates) {
            const farmCoords = {
                lat: farm.locationData.coordinates.latitude,
                lng: farm.locationData.coordinates.longitude
            };
            farmWithDistance.distance = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$location$2f$distance$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DistanceService"].calculateDistance(referencePoint, farmCoords);
            const travelTime = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$location$2f$distance$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DistanceService"].estimateTravelTime(farmWithDistance.distance);
            farmWithDistance.travelTime = travelTime.duration;
        }
        return farmWithDistance;
    }
    /**
   * Apply basic Firestore filters
   */ applyBasicFilters(baseQuery, filters) {
        let q = baseQuery;
        // Always filter for active farms unless specifically requested
        q = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["query"])(q, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["where"])('isActive', '==', true));
        if (filters.provinces && filters.provinces.length > 0) {
            q = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["query"])(q, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["where"])('province', 'in', filters.provinces));
        }
        if (filters.activities && filters.activities.length > 0) {
            q = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["query"])(q, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["where"])('activityTypes', 'in', filters.activities));
        }
        // Text search using searchableLocation field
        if (filters.query) {
            // Note: Firestore doesn't support full-text search natively
            // This is a simplified approach - consider using Algolia or similar for production
            const searchTerms = filters.query.toLowerCase().split(' ');
        // We'll handle text search client-side for now
        }
        // Apply sorting (distance sorting will be handled client-side)
        if (filters.sortBy && filters.sortBy !== 'distance') {
            const sortField = this.getSortField(filters.sortBy);
            const sortOrder = filters.sortOrder || 'desc';
            q = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["query"])(q, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["orderBy"])(sortField, sortOrder));
        } else {
            // Default sorting
            q = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["query"])(q, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["orderBy"])('featured', 'desc'), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["orderBy"])('createdAt', 'desc'));
        }
        if (filters.limit) {
            q = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["query"])(q, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["limit"])(filters.limit));
        }
        return q;
    }
    /**
   * Apply location-based filtering and sorting
   */ async applyLocationFilters(farms, locationFilter, sortBy) {
        const farmsWithDistance = [];
        for (const farm of farms){
            if (!farm.locationData?.coordinates) continue;
            const farmCoords = {
                lat: farm.locationData.coordinates.latitude,
                lng: farm.locationData.coordinates.longitude
            };
            let includeThisFarm = true;
            let distance;
            // Apply radius filter
            if (locationFilter.center && locationFilter.radius) {
                distance = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$location$2f$distance$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DistanceService"].calculateDistance(locationFilter.center, farmCoords);
                includeThisFarm = distance <= locationFilter.radius;
            }
            // Apply bounds filter
            if (includeThisFarm && locationFilter.bounds) {
                const { northeast, southwest } = locationFilter.bounds;
                includeThisFarm = farmCoords.lat >= southwest.lat && farmCoords.lat <= northeast.lat && farmCoords.lng >= southwest.lng && farmCoords.lng <= northeast.lng;
            }
            if (includeThisFarm) {
                const farmWithDistance = {
                    ...farm
                };
                if (distance !== undefined) {
                    farmWithDistance.distance = distance;
                    const travelTime = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$location$2f$distance$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DistanceService"].estimateTravelTime(distance);
                    farmWithDistance.travelTime = travelTime.duration;
                }
                farmsWithDistance.push(farmWithDistance);
            }
        }
        // Sort by distance if requested
        if (sortBy === 'distance') {
            farmsWithDistance.sort((a, b)=>(a.distance || 0) - (b.distance || 0));
        }
        return farmsWithDistance;
    }
    /**
   * Apply client-side filters that can't be done in Firestore
   */ applyClientSideFilters(farms, filters) {
        let filteredFarms = [
            ...farms
        ];
        // Text search
        if (filters.query) {
            const searchTerms = filters.query.toLowerCase().split(' ');
            filteredFarms = filteredFarms.filter((farm)=>{
                const searchText = [
                    farm.name,
                    farm.description,
                    farm.searchableLocation,
                    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$location$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["locationUtils"].getLocationDisplayName(farm.locationData)
                ].join(' ').toLowerCase();
                return searchTerms.every((term)=>searchText.includes(term));
            });
        }
        // Price range filter
        if (filters.priceRange && filters.priceRange.length === 2) {
            const [minPrice, maxPrice] = filters.priceRange;
            filteredFarms = filteredFarms.filter((farm)=>{
                if (!farm.pricePerDay) return true // Include farms without price info
                ;
                return farm.pricePerDay >= minPrice && farm.pricePerDay <= maxPrice;
            });
        }
        // Size range filter
        if (filters.sizeRange && filters.sizeRange.length === 2) {
            const [minSize, maxSize] = filters.sizeRange;
            filteredFarms = filteredFarms.filter((farm)=>{
                if (!farm.sizeHectares) return true // Include farms without size info
                ;
                return farm.sizeHectares >= minSize && farm.sizeHectares <= maxSize;
            });
        }
        return filteredFarms;
    }
    /**
   * Apply pagination
   */ applyPagination(farms, filters) {
        const limit = filters.limit || 20;
        const offset = filters.offset || 0;
        const paginatedFarms = farms.slice(offset, offset + limit);
        const hasMore = farms.length > offset + limit;
        return {
            farms: paginatedFarms,
            hasMore
        };
    }
    /**
   * Get Firestore field name for sorting
   */ getSortField(sortBy) {
        switch(sortBy){
            case 'name':
                return 'name';
            case 'created':
                return 'createdAt';
            case 'price':
                return 'pricePerDay';
            case 'size':
                return 'sizeHectares';
            case 'rating':
                return 'averageRating' // This would need to be calculated and stored
                ;
            default:
                return 'createdAt';
        }
    }
}
const enhancedFarmService = new EnhancedFarmService();
}}),
"[project]/src/lib/constants.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Centralized constants for the BvR Safaris application
 * This file contains all shared constants to ensure consistency across the application
 */ /**
 * South African provinces in alphabetical order
 * Used throughout the application for province selection, filtering, and display
 */ __turbopack_context__.s({
    "ACTIVITY_TYPES": (()=>ACTIVITY_TYPES),
    "BOOKING_STATUS": (()=>BOOKING_STATUS),
    "DEFAULT_PRICE_RANGE": (()=>DEFAULT_PRICE_RANGE),
    "FARM_AMENITIES": (()=>FARM_AMENITIES),
    "GAME_SPECIES": (()=>GAME_SPECIES),
    "MAX_FILE_SIZE": (()=>MAX_FILE_SIZE),
    "PAGINATION": (()=>PAGINATION),
    "SOUTH_AFRICAN_PROVINCES": (()=>SOUTH_AFRICAN_PROVINCES),
    "SUPPORTED_IMAGE_FORMATS": (()=>SUPPORTED_IMAGE_FORMATS),
    "USER_ROLES": (()=>USER_ROLES)
});
const SOUTH_AFRICAN_PROVINCES = [
    'Eastern Cape',
    'Free State',
    'Gauteng',
    'KwaZulu-Natal',
    'Limpopo',
    'Mpumalanga',
    'Northern Cape',
    'North West',
    'Western Cape'
];
const ACTIVITY_TYPES = {
    HUNTING: 'hunting',
    PHOTO_SAFARI: 'photo_safari',
    BOTH: 'both'
};
const BOOKING_STATUS = {
    PENDING: 'pending',
    CONFIRMED: 'confirmed',
    CANCELLED: 'cancelled',
    COMPLETED: 'completed'
};
const USER_ROLES = {
    FARM_OWNER: 'farm_owner',
    GUEST: 'guest',
    ADMIN: 'admin'
};
const DEFAULT_PRICE_RANGE = [
    0,
    20000
];
const MAX_FILE_SIZE = 50 * 1024 * 1024 // 50MB
;
const SUPPORTED_IMAGE_FORMATS = [
    'image/jpeg',
    'image/jpg',
    'image/png',
    'image/webp'
];
const PAGINATION = {
    DEFAULT_LIMIT: 10,
    MAX_LIMIT: 100,
    FEATURED_FARMS_LIMIT: 3
};
const FARM_AMENITIES = [
    'Luxury Lodge',
    'Basic Accommodation',
    'Restaurant',
    'Bar',
    'Swimming Pool',
    'Spa',
    'WiFi',
    'Airport Transfer',
    'Professional Guide',
    'Trophy Preparation',
    'Taxidermy'
];
const GAME_SPECIES = [
    'Lion',
    'Leopard',
    'Elephant',
    'Buffalo',
    'Rhino',
    'Kudu',
    'Impala',
    'Springbok',
    'Eland',
    'Sable',
    'Gemsbok',
    'Waterbuck',
    'Bushbuck',
    'Warthog'
];
}}),
"[project]/src/app/farms/create/page.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>CreateFarmPage)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/image.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useAuth$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/useAuth.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/Button.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/Input.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/Card.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$FileUpload$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/FileUpload.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$LocationAutocomplete$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/LocationAutocomplete.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$farmService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/services/farmService.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/constants.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
;
;
;
;
;
;
;
;
;
function CreateFarmPage() {
    const { user, loading: authLoading } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useAuth$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAuth"])();
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRouter"])();
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [error, setError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [uploadedImages, setUploadedImages] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    const [formData, setFormData] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({
        name: '',
        description: '',
        descriptionAfrikaans: '',
        locationData: null,
        province: '',
        sizeHectares: '',
        activityTypes: 'both',
        contactEmail: '',
        contactPhone: '',
        websiteUrl: '',
        rules: '',
        rulesAfrikaans: '',
        pricingInfo: ''
    });
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (!authLoading && !user) {
            router.push('/auth/login');
            return;
        }
        // TODO: Check if user is farm owner using custom claims or user profile
        // For now, allow all authenticated users to create farms
        // Pre-fill contact email with user's email
        if (user?.email) {
            setFormData((prev)=>({
                    ...prev,
                    contactEmail: user.email
                }));
        }
    }, [
        user,
        authLoading,
        router
    ]);
    const handleInputChange = (field, value)=>{
        setFormData((prev)=>({
                ...prev,
                [field]: value
            }));
        setError(null);
    };
    const handleLocationSelect = (locationData)=>{
        setFormData((prev)=>({
                ...prev,
                locationData
            }));
        // Auto-populate province from location data if available
        const mappedProvince = locationData.addressComponents.administrativeAreaLevel1;
        if (mappedProvince && __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SOUTH_AFRICAN_PROVINCES"].includes(mappedProvince)) {
            setFormData((prev)=>({
                    ...prev,
                    province: mappedProvince
                }));
        }
        setError(null);
    };
    const handleImageUpload = (imageUrl)=>{
        setUploadedImages((prev)=>[
                ...prev,
                imageUrl
            ]);
    };
    const handleImageRemove = (imageUrl)=>{
        setUploadedImages((prev)=>prev.filter((url)=>url !== imageUrl));
    };
    const handleSubmit = async (e)=>{
        e.preventDefault();
        setLoading(true);
        setError(null);
        try {
            // Validate required fields
            if (!formData.name || !formData.locationData || !formData.province || !formData.contactEmail) {
                setError('Please fill in all required fields including location');
                setLoading(false);
                return;
            }
            // Validate website URL if provided
            if (formData.websiteUrl && formData.websiteUrl.trim()) {
                const urlPattern = /^(https?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$/;
                if (!urlPattern.test(formData.websiteUrl.trim())) {
                    setError('Please enter a valid website URL');
                    setLoading(false);
                    return;
                }
            }
            const farmData = {
                name: formData.name,
                description: formData.description || undefined,
                descriptionAfrikaans: formData.descriptionAfrikaans || undefined,
                locationData: formData.locationData,
                province: formData.province,
                sizeHectares: formData.sizeHectares ? parseInt(formData.sizeHectares) : undefined,
                activityTypes: formData.activityTypes,
                contactEmail: formData.contactEmail,
                contactPhone: formData.contactPhone || undefined,
                websiteUrl: formData.websiteUrl || undefined,
                rules: formData.rules || undefined,
                rulesAfrikaans: formData.rulesAfrikaans || undefined,
                pricingInfo: formData.pricingInfo || undefined
            };
            const farmId = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$farmService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["enhancedFarmService"].createFarm(farmData, user.uid);
            // Handle farm images - they're already uploaded to Firebase Storage
            // The uploadedImages array contains the Firebase Storage URLs
            if (uploadedImages.length > 0) {
                console.log(`Farm created with ${uploadedImages.length} images uploaded to Firebase Storage`);
            // Images are already stored in Firebase Storage and URLs are in uploadedImages
            // We could optionally store these URLs in a farm_images subcollection
            // For now, the images are uploaded and accessible via their URLs
            }
            router.push(`/farms/${farmId}`);
        } catch (err) {
            setError('An unexpected error occurred');
            console.error('Farm creation error:', err);
        } finally{
            setLoading(false);
        }
    };
    if (authLoading) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "min-h-screen flex items-center justify-center bg-earth-100",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "animate-spin rounded-full h-12 w-12 border-b-2 border-accent-600"
            }, void 0, false, {
                fileName: "[project]/src/app/farms/create/page.tsx",
                lineNumber: 160,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/app/farms/create/page.tsx",
            lineNumber: 159,
            columnNumber: 7
        }, this);
    }
    // TODO: Implement proper role checking with Firebase custom claims
    // For now, allow all authenticated users to create farms
    if (!user) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "min-h-screen flex items-center justify-center bg-earth-100",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "text-center",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-earth-600 mb-4",
                        children: "Please log in to create a farm listing."
                    }, void 0, false, {
                        fileName: "[project]/src/app/farms/create/page.tsx",
                        lineNumber: 171,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                        onClick: ()=>router.push('/auth/login'),
                        children: "Go to Login"
                    }, void 0, false, {
                        fileName: "[project]/src/app/farms/create/page.tsx",
                        lineNumber: 172,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/farms/create/page.tsx",
                lineNumber: 170,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/app/farms/create/page.tsx",
            lineNumber: 169,
            columnNumber: 7
        }, this);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "min-h-screen bg-earth-100 py-8",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "mb-8",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                            className: "text-3xl font-bold text-earth-900",
                            children: "Create Farm Listing"
                        }, void 0, false, {
                            fileName: "[project]/src/app/farms/create/page.tsx",
                            lineNumber: 184,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-earth-600 mt-2",
                            children: "Add your game farm to BvR Safaris and start receiving bookings"
                        }, void 0, false, {
                            fileName: "[project]/src/app/farms/create/page.tsx",
                            lineNumber: 185,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/farms/create/page.tsx",
                    lineNumber: 183,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("form", {
                    onSubmit: handleSubmit,
                    className: "space-y-8",
                    children: [
                        error && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "bg-red-50 border border-red-200 rounded-md p-3",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-red-600 text-sm",
                                children: error
                            }, void 0, false, {
                                fileName: "[project]/src/app/farms/create/page.tsx",
                                lineNumber: 193,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/app/farms/create/page.tsx",
                            lineNumber: 192,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Card"], {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CardHeader"], {
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CardTitle"], {
                                        children: "Basic Information"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/farms/create/page.tsx",
                                        lineNumber: 200,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/app/farms/create/page.tsx",
                                    lineNumber: 199,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CardContent"], {
                                    className: "space-y-6",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                    htmlFor: "name",
                                                    className: "block text-sm font-medium text-earth-700 mb-2",
                                                    children: "Farm Name *"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/farms/create/page.tsx",
                                                    lineNumber: 204,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Input"], {
                                                    id: "name",
                                                    type: "text",
                                                    value: formData.name,
                                                    onChange: (e)=>handleInputChange('name', e.target.value),
                                                    placeholder: "Enter your farm name",
                                                    required: true
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/farms/create/page.tsx",
                                                    lineNumber: 207,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/farms/create/page.tsx",
                                            lineNumber: 203,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "grid grid-cols-1 md:grid-cols-2 gap-6",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                            className: "block text-sm font-medium text-earth-700 mb-2",
                                                            children: "Farm Location *"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/farms/create/page.tsx",
                                                            lineNumber: 219,
                                                            columnNumber: 19
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$LocationAutocomplete$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["LocationAutocomplete"], {
                                                            value: formData.locationData?.formattedAddress || '',
                                                            onLocationSelect: handleLocationSelect,
                                                            placeholder: "Search for your farm location...",
                                                            required: true,
                                                            className: "w-full"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/farms/create/page.tsx",
                                                            lineNumber: 222,
                                                            columnNumber: 19
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                            className: "text-xs text-earth-500 mt-1",
                                                            children: "Start typing to search for your farm's address or nearby landmark"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/farms/create/page.tsx",
                                                            lineNumber: 229,
                                                            columnNumber: 19
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/farms/create/page.tsx",
                                                    lineNumber: 218,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                            htmlFor: "province",
                                                            className: "block text-sm font-medium text-earth-700 mb-2",
                                                            children: "Province *"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/farms/create/page.tsx",
                                                            lineNumber: 235,
                                                            columnNumber: 19
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("select", {
                                                            id: "province",
                                                            value: formData.province,
                                                            onChange: (e)=>handleInputChange('province', e.target.value),
                                                            className: "w-full px-3 py-2 border border-earth-300 rounded-md focus:outline-none focus:ring-2 focus:ring-accent-600 focus:border-transparent",
                                                            required: true,
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                                                    value: "",
                                                                    children: "Select Province"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/farms/create/page.tsx",
                                                                    lineNumber: 245,
                                                                    columnNumber: 21
                                                                }, this),
                                                                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SOUTH_AFRICAN_PROVINCES"].map((province)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                                                        value: province,
                                                                        children: province
                                                                    }, province, false, {
                                                                        fileName: "[project]/src/app/farms/create/page.tsx",
                                                                        lineNumber: 247,
                                                                        columnNumber: 23
                                                                    }, this))
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/farms/create/page.tsx",
                                                            lineNumber: 238,
                                                            columnNumber: 19
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                            className: "text-xs text-earth-500 mt-1",
                                                            children: "Province will be auto-filled from location if available"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/farms/create/page.tsx",
                                                            lineNumber: 250,
                                                            columnNumber: 19
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/farms/create/page.tsx",
                                                    lineNumber: 234,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/farms/create/page.tsx",
                                            lineNumber: 217,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "grid grid-cols-1 md:grid-cols-2 gap-6",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                            htmlFor: "sizeHectares",
                                                            className: "block text-sm font-medium text-earth-700 mb-2",
                                                            children: "Size (Hectares)"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/farms/create/page.tsx",
                                                            lineNumber: 258,
                                                            columnNumber: 19
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Input"], {
                                                            id: "sizeHectares",
                                                            type: "number",
                                                            value: formData.sizeHectares,
                                                            onChange: (e)=>handleInputChange('sizeHectares', e.target.value),
                                                            placeholder: "e.g., 5000"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/farms/create/page.tsx",
                                                            lineNumber: 261,
                                                            columnNumber: 19
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/farms/create/page.tsx",
                                                    lineNumber: 257,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                            htmlFor: "activityTypes",
                                                            className: "block text-sm font-medium text-earth-700 mb-2",
                                                            children: "Activity Types *"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/farms/create/page.tsx",
                                                            lineNumber: 271,
                                                            columnNumber: 19
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("select", {
                                                            id: "activityTypes",
                                                            value: formData.activityTypes,
                                                            onChange: (e)=>handleInputChange('activityTypes', e.target.value),
                                                            className: "w-full px-3 py-2 border border-earth-300 rounded-md focus:outline-none focus:ring-2 focus:ring-accent-600 focus:border-transparent",
                                                            required: true,
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                                                    value: "hunting",
                                                                    children: "Hunting Only"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/farms/create/page.tsx",
                                                                    lineNumber: 281,
                                                                    columnNumber: 21
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                                                    value: "photo_safari",
                                                                    children: "Photo Safari Only"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/farms/create/page.tsx",
                                                                    lineNumber: 282,
                                                                    columnNumber: 21
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                                                    value: "both",
                                                                    children: "Both Hunting & Photo Safari"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/farms/create/page.tsx",
                                                                    lineNumber: 283,
                                                                    columnNumber: 21
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/farms/create/page.tsx",
                                                            lineNumber: 274,
                                                            columnNumber: 19
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/farms/create/page.tsx",
                                                    lineNumber: 270,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/farms/create/page.tsx",
                                            lineNumber: 256,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                    htmlFor: "description",
                                                    className: "block text-sm font-medium text-earth-700 mb-2",
                                                    children: "Description (English)"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/farms/create/page.tsx",
                                                    lineNumber: 289,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("textarea", {
                                                    id: "description",
                                                    value: formData.description,
                                                    onChange: (e)=>handleInputChange('description', e.target.value),
                                                    placeholder: "Describe your farm, facilities, and what makes it special...",
                                                    rows: 4,
                                                    className: "w-full px-3 py-2 border border-earth-300 rounded-md focus:outline-none focus:ring-2 focus:ring-accent-600 focus:border-transparent"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/farms/create/page.tsx",
                                                    lineNumber: 292,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/farms/create/page.tsx",
                                            lineNumber: 288,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                    htmlFor: "descriptionAfrikaans",
                                                    className: "block text-sm font-medium text-earth-700 mb-2",
                                                    children: "Description (Afrikaans)"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/farms/create/page.tsx",
                                                    lineNumber: 303,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("textarea", {
                                                    id: "descriptionAfrikaans",
                                                    value: formData.descriptionAfrikaans,
                                                    onChange: (e)=>handleInputChange('descriptionAfrikaans', e.target.value),
                                                    placeholder: "Beskryf jou plaas, fasiliteite, en wat dit spesiaal maak...",
                                                    rows: 4,
                                                    className: "w-full px-3 py-2 border border-earth-300 rounded-md focus:outline-none focus:ring-2 focus:ring-accent-600 focus:border-transparent"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/farms/create/page.tsx",
                                                    lineNumber: 306,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/farms/create/page.tsx",
                                            lineNumber: 302,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/farms/create/page.tsx",
                                    lineNumber: 202,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/farms/create/page.tsx",
                            lineNumber: 198,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Card"], {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CardHeader"], {
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CardTitle"], {
                                        children: "Contact Information"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/farms/create/page.tsx",
                                        lineNumber: 321,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/app/farms/create/page.tsx",
                                    lineNumber: 320,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CardContent"], {
                                    className: "space-y-6",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "grid grid-cols-1 md:grid-cols-2 gap-6",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                            htmlFor: "contactEmail",
                                                            className: "block text-sm font-medium text-earth-700 mb-2",
                                                            children: "Contact Email *"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/farms/create/page.tsx",
                                                            lineNumber: 326,
                                                            columnNumber: 19
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Input"], {
                                                            id: "contactEmail",
                                                            type: "email",
                                                            value: formData.contactEmail,
                                                            onChange: (e)=>handleInputChange('contactEmail', e.target.value),
                                                            placeholder: "<EMAIL>",
                                                            required: true
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/farms/create/page.tsx",
                                                            lineNumber: 329,
                                                            columnNumber: 19
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/farms/create/page.tsx",
                                                    lineNumber: 325,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                            htmlFor: "contactPhone",
                                                            className: "block text-sm font-medium text-earth-700 mb-2",
                                                            children: "Contact Phone"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/farms/create/page.tsx",
                                                            lineNumber: 340,
                                                            columnNumber: 19
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Input"], {
                                                            id: "contactPhone",
                                                            type: "tel",
                                                            value: formData.contactPhone,
                                                            onChange: (e)=>handleInputChange('contactPhone', e.target.value),
                                                            placeholder: "+27 12 345 6789"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/farms/create/page.tsx",
                                                            lineNumber: 343,
                                                            columnNumber: 19
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/farms/create/page.tsx",
                                                    lineNumber: 339,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/farms/create/page.tsx",
                                            lineNumber: 324,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                    htmlFor: "websiteUrl",
                                                    className: "block text-sm font-medium text-earth-700 mb-2",
                                                    children: "Website URL"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/farms/create/page.tsx",
                                                    lineNumber: 354,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Input"], {
                                                    id: "websiteUrl",
                                                    type: "text",
                                                    value: formData.websiteUrl,
                                                    onChange: (e)=>handleInputChange('websiteUrl', e.target.value),
                                                    placeholder: "www.yourfarm.com"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/farms/create/page.tsx",
                                                    lineNumber: 357,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/farms/create/page.tsx",
                                            lineNumber: 353,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/farms/create/page.tsx",
                                    lineNumber: 323,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/farms/create/page.tsx",
                            lineNumber: 319,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Card"], {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CardHeader"], {
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CardTitle"], {
                                        children: "Additional Information"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/farms/create/page.tsx",
                                        lineNumber: 371,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/app/farms/create/page.tsx",
                                    lineNumber: 370,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CardContent"], {
                                    className: "space-y-6",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                    htmlFor: "pricingInfo",
                                                    className: "block text-sm font-medium text-earth-700 mb-2",
                                                    children: "Pricing Information"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/farms/create/page.tsx",
                                                    lineNumber: 375,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("textarea", {
                                                    id: "pricingInfo",
                                                    value: formData.pricingInfo,
                                                    onChange: (e)=>handleInputChange('pricingInfo', e.target.value),
                                                    placeholder: "Provide general pricing information or mention 'Contact for pricing'...",
                                                    rows: 3,
                                                    className: "w-full px-3 py-2 border border-earth-300 rounded-md focus:outline-none focus:ring-2 focus:ring-accent-600 focus:border-transparent"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/farms/create/page.tsx",
                                                    lineNumber: 378,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/farms/create/page.tsx",
                                            lineNumber: 374,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                    htmlFor: "rules",
                                                    className: "block text-sm font-medium text-earth-700 mb-2",
                                                    children: "Rules & Regulations (English)"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/farms/create/page.tsx",
                                                    lineNumber: 389,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("textarea", {
                                                    id: "rules",
                                                    value: formData.rules,
                                                    onChange: (e)=>handleInputChange('rules', e.target.value),
                                                    placeholder: "List important rules, safety requirements, what to bring, etc...",
                                                    rows: 4,
                                                    className: "w-full px-3 py-2 border border-earth-300 rounded-md focus:outline-none focus:ring-2 focus:ring-accent-600 focus:border-transparent"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/farms/create/page.tsx",
                                                    lineNumber: 392,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/farms/create/page.tsx",
                                            lineNumber: 388,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                    htmlFor: "rulesAfrikaans",
                                                    className: "block text-sm font-medium text-earth-700 mb-2",
                                                    children: "Rules & Regulations (Afrikaans)"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/farms/create/page.tsx",
                                                    lineNumber: 403,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("textarea", {
                                                    id: "rulesAfrikaans",
                                                    value: formData.rulesAfrikaans,
                                                    onChange: (e)=>handleInputChange('rulesAfrikaans', e.target.value),
                                                    placeholder: "Lys belangrike reëls, veiligheidsvereistes, wat om te bring, ens...",
                                                    rows: 4,
                                                    className: "w-full px-3 py-2 border border-earth-300 rounded-md focus:outline-none focus:ring-2 focus:ring-accent-600 focus:border-transparent"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/farms/create/page.tsx",
                                                    lineNumber: 406,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/farms/create/page.tsx",
                                            lineNumber: 402,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/farms/create/page.tsx",
                                    lineNumber: 373,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/farms/create/page.tsx",
                            lineNumber: 369,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Card"], {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CardHeader"], {
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CardTitle"], {
                                        children: "Farm Images"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/farms/create/page.tsx",
                                        lineNumber: 421,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/app/farms/create/page.tsx",
                                    lineNumber: 420,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CardContent"], {
                                    className: "space-y-6",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                className: "block text-sm font-medium text-earth-700 mb-4",
                                                children: "Upload Farm Photos"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/farms/create/page.tsx",
                                                lineNumber: 425,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "text-sm text-earth-600 mb-4",
                                                children: "Add photos of your farm, facilities, wildlife, and accommodations. The first image will be used as the main photo."
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/farms/create/page.tsx",
                                                lineNumber: 428,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",
                                                children: [
                                                    uploadedImages.map((imageUrl, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "relative group",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "relative w-full h-48 rounded-lg border overflow-hidden",
                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                                        src: imageUrl,
                                                                        alt: `Farm image ${index + 1}`,
                                                                        fill: true,
                                                                        className: "object-cover",
                                                                        sizes: "(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/app/farms/create/page.tsx",
                                                                        lineNumber: 437,
                                                                        columnNumber: 25
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/farms/create/page.tsx",
                                                                    lineNumber: 436,
                                                                    columnNumber: 23
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "absolute top-2 left-2",
                                                                    children: index === 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                        className: "bg-accent-600 text-white text-xs px-2 py-1 rounded",
                                                                        children: "Main Photo"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/app/farms/create/page.tsx",
                                                                        lineNumber: 447,
                                                                        columnNumber: 27
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/farms/create/page.tsx",
                                                                    lineNumber: 445,
                                                                    columnNumber: 23
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                                    type: "button",
                                                                    onClick: ()=>handleImageRemove(imageUrl),
                                                                    className: "absolute top-2 right-2 bg-red-600 text-white rounded-full w-6 h-6 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity",
                                                                    children: "×"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/farms/create/page.tsx",
                                                                    lineNumber: 452,
                                                                    columnNumber: 23
                                                                }, this)
                                                            ]
                                                        }, imageUrl, true, {
                                                            fileName: "[project]/src/app/farms/create/page.tsx",
                                                            lineNumber: 435,
                                                            columnNumber: 21
                                                        }, this)),
                                                    uploadedImages.length < 10 && user && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$FileUpload$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ImageUpload"], {
                                                        bucket: "farm-images",
                                                        farmId: user.uid,
                                                        onUpload: handleImageUpload,
                                                        maxSize: 10,
                                                        className: "h-48"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/farms/create/page.tsx",
                                                        lineNumber: 464,
                                                        columnNumber: 21
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/farms/create/page.tsx",
                                                lineNumber: 432,
                                                columnNumber: 17
                                            }, this),
                                            uploadedImages.length >= 10 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "text-sm text-earth-500 mt-2",
                                                children: "Maximum of 10 images allowed. Remove an image to add more."
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/farms/create/page.tsx",
                                                lineNumber: 475,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/farms/create/page.tsx",
                                        lineNumber: 424,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/app/farms/create/page.tsx",
                                    lineNumber: 423,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/farms/create/page.tsx",
                            lineNumber: 419,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex gap-4",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                                    type: "submit",
                                    variant: "primary",
                                    isLoading: loading,
                                    className: "flex-1",
                                    children: loading ? 'Creating Farm...' : 'Create Farm Listing'
                                }, void 0, false, {
                                    fileName: "[project]/src/app/farms/create/page.tsx",
                                    lineNumber: 485,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                                    type: "button",
                                    variant: "outline",
                                    onClick: ()=>router.push('/dashboard'),
                                    className: "flex-1",
                                    children: "Cancel"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/farms/create/page.tsx",
                                    lineNumber: 494,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/farms/create/page.tsx",
                            lineNumber: 484,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/farms/create/page.tsx",
                    lineNumber: 190,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/app/farms/create/page.tsx",
            lineNumber: 182,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/app/farms/create/page.tsx",
        lineNumber: 181,
        columnNumber: 5
    }, this);
}
}}),

};

//# sourceMappingURL=src_f44e077c._.js.map