{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/src/personal/bvr-safaris/src/components/ui/LoadingSpinner.tsx"], "sourcesContent": ["import { cn } from '@/lib/utils'\n\ninterface LoadingSpinnerProps {\n  size?: 'sm' | 'md' | 'lg'\n  className?: string\n}\n\nexport function LoadingSpinner({ size = 'md', className }: LoadingSpinnerProps) {\n  const sizeClasses = {\n    sm: 'w-4 h-4',\n    md: 'w-6 h-6',\n    lg: 'w-12 h-12'\n  }\n\n  return (\n    <div className={cn('flex items-center justify-center', className)}>\n      <div\n        className={cn(\n          'animate-spin rounded-full border-2 border-white border-t-transparent',\n          sizeClasses[size]\n        )}\n      />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAOO,SAAS,eAAe,EAAE,OAAO,IAAI,EAAE,SAAS,EAAuB;IAC5E,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,oCAAoC;kBACrD,cAAA,8OAAC;YACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wEACA,WAAW,CAAC,KAAK;;;;;;;;;;;AAK3B", "debugId": null}}, {"offset": {"line": 41, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/src/personal/bvr-safaris/src/lib/firebase/heroImages.ts"], "sourcesContent": ["/**\n * Hero image service for fetching random background images from local banner_images directory\n */\n\n// Available banner images in the public/banner_images/ directory\nconst BANNER_IMAGES = [\n  'IMG_6015-min.JPG',\n  'IMG_6077-min.JPG',\n  'IMG_6207-min.JPG',\n  'IMG_6297-min.JPG',\n  'IMG_6333-min.JPG',\n  'IMG_6395-min.JPG',\n  'IMG_6498-min.JPG',\n  'IMG_6610-min.JPG',\n  'IMG_6632-min.JPG',\n  'IMG_6695-min.JPG',\n  'IMG_6738-min.JPG',\n  'IMG_6744-min.JPG',\n  'IMG_6784-min.JPG'\n]\n\n// Default banner image path (using first banner image as default)\nexport const DEFAULT_HERO_IMAGE = `/banner_images/${BANNER_IMAGES[0]}`\n\nexport interface HeroImageMetadata {\n  name: string\n  fullPath: string\n  downloadURL: string\n}\n\n/**\n * Fetches a random hero image URL from local banner_images directory\n * @returns Promise<string> - Path to a random hero image, or default banner if none available\n */\nexport async function getRandomHeroImageUrl(): Promise<string> {\n  try {\n    // Check if there are any images available\n    if (!BANNER_IMAGES || BANNER_IMAGES.length === 0) {\n      console.warn('No hero images found in banner_images directory, using default banner')\n      return DEFAULT_HERO_IMAGE\n    }\n\n    // Pick a random image from the available items\n    const randomIndex = Math.floor(Math.random() * BANNER_IMAGES.length)\n    const randomImageName = BANNER_IMAGES[randomIndex]\n\n    // Return the path to the selected image\n    const imagePath = `/banner_images/${randomImageName}`\n\n    return imagePath\n  } catch (error) {\n    // Fallback to default banner on error\n    console.error('Error selecting random hero image from local directory, using default banner:', error)\n    return DEFAULT_HERO_IMAGE\n  }\n}\n\n/**\n * Gets the default hero image URL (immediate, no async needed)\n * @returns string - Path to the default banner image\n */\nexport function getDefaultHeroImageUrl(): string {\n  return DEFAULT_HERO_IMAGE\n}\n\n/**\n * Fetches all available hero images with metadata\n * @returns Promise<HeroImageMetadata[]> - Array of hero image metadata\n */\nexport async function getAllHeroImages(): Promise<HeroImageMetadata[]> {\n  try {\n    if (!BANNER_IMAGES || BANNER_IMAGES.length === 0) {\n      return []\n    }\n\n    // Create metadata for all available images\n    const heroImages: HeroImageMetadata[] = BANNER_IMAGES.map((imageName) => {\n      const imagePath = `/banner_images/${imageName}`\n      return {\n        name: imageName,\n        fullPath: imagePath,\n        downloadURL: imagePath\n      }\n    })\n\n    return heroImages\n  } catch (error) {\n    console.error('Error fetching all hero images from local directory:', error)\n    return []\n  }\n}\n\n/**\n * Preloads a random hero image for better performance\n * @returns Promise<string | null> - Path of the preloaded image\n */\nexport async function preloadRandomHeroImage(): Promise<string | null> {\n  const imageUrl = await getRandomHeroImageUrl()\n\n  if (imageUrl) {\n    // Create an image element to preload the image\n    const img = new Image()\n    img.src = imageUrl\n\n    // Return the URL after initiating preload\n    return imageUrl\n  }\n\n  return null\n}\n"], "names": [], "mappings": "AAAA;;CAEC,GAED,iEAAiE;;;;;;;;AACjE,MAAM,gBAAgB;IACpB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAGM,MAAM,qBAAqB,CAAC,eAAe,EAAE,aAAa,CAAC,EAAE,EAAE;AAY/D,eAAe;IACpB,IAAI;QACF,0CAA0C;QAC1C,IAAI,CAAC,iBAAiB,cAAc,MAAM,KAAK,GAAG;YAChD,QAAQ,IAAI,CAAC;YACb,OAAO;QACT;QAEA,+CAA+C;QAC/C,MAAM,cAAc,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,cAAc,MAAM;QACnE,MAAM,kBAAkB,aAAa,CAAC,YAAY;QAElD,wCAAwC;QACxC,MAAM,YAAY,CAAC,eAAe,EAAE,iBAAiB;QAErD,OAAO;IACT,EAAE,OAAO,OAAO;QACd,sCAAsC;QACtC,QAAQ,KAAK,CAAC,iFAAiF;QAC/F,OAAO;IACT;AACF;AAMO,SAAS;IACd,OAAO;AACT;AAMO,eAAe;IACpB,IAAI;QACF,IAAI,CAAC,iBAAiB,cAAc,MAAM,KAAK,GAAG;YAChD,OAAO,EAAE;QACX;QAEA,2CAA2C;QAC3C,MAAM,aAAkC,cAAc,GAAG,CAAC,CAAC;YACzD,MAAM,YAAY,CAAC,eAAe,EAAE,WAAW;YAC/C,OAAO;gBACL,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;QACF;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wDAAwD;QACtE,OAAO,EAAE;IACX;AACF;AAMO,eAAe;IACpB,MAAM,WAAW,MAAM;IAEvB,IAAI,UAAU;QACZ,+CAA+C;QAC/C,MAAM,MAAM,IAAI;QAChB,IAAI,GAAG,GAAG;QAEV,0CAA0C;QAC1C,OAAO;IACT;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 126, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/src/personal/bvr-safaris/src/components/ui/HeroImageLoader.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport Image from 'next/image'\nimport { LoadingSpinner } from './LoadingSpinner'\nimport { getRandomHeroImageUrl, getDefaultHeroImageUrl } from '@/lib/firebase/heroImages'\nimport { cn } from '@/lib/utils'\n\ninterface HeroImageLoaderProps {\n  className?: string\n  children?: React.ReactNode\n  priority?: boolean\n  onImageLoad?: (imageUrl: string) => void\n}\n\nexport function HeroImageLoader({ \n  className, \n  children, \n  priority = false,\n  onImageLoad \n}: HeroImageLoaderProps) {\n  const [imageUrl, setImageUrl] = useState<string>(getDefaultHeroImageUrl())\n  const [isLoading, setIsLoading] = useState(true)\n\n  useEffect(() => {\n    const loadRandomImage = async () => {\n      try {\n        setIsLoading(true)\n        const randomImageUrl = await getRandomHeroImageUrl()\n        setImageUrl(randomImageUrl)\n        onImageLoad?.(randomImageUrl)\n      } catch (error) {\n        console.error('Error loading hero image:', error)\n        setImageUrl(getDefaultHeroImageUrl())\n      } finally {\n        setIsLoading(false)\n      }\n    }\n\n    loadRandomImage()\n  }, [onImageLoad])\n\n  const handleImageLoad = () => {\n    setIsLoading(false)\n  }\n\n  const handleImageError = () => {\n    console.error('Failed to load hero image, falling back to default')\n    setImageUrl(getDefaultHeroImageUrl())\n    setIsLoading(false)\n  }\n\n  return (\n    <div className={cn('relative w-full h-full overflow-hidden', className)}>\n      {/* Loading overlay */}\n      {isLoading && (\n        <div className=\"absolute inset-0 z-10 bg-gradient-to-br from-[var(--primary-green)] to-[var(--secondary-sunset)] flex items-center justify-center\">\n          <div className=\"text-center text-white\">\n            <LoadingSpinner size=\"lg\" className=\"mx-auto mb-4\" />\n            <p className=\"text-sm font-medium\">Loading beautiful safari imagery...</p>\n          </div>\n        </div>\n      )}\n\n      {/* Hero Image */}\n      <Image\n        src={imageUrl}\n        alt=\"Safari landscape\"\n        fill\n        className=\"object-cover\"\n        priority={priority}\n        onLoad={handleImageLoad}\n        onError={handleImageError}\n        sizes=\"100vw\"\n      />\n\n      {/* Dark overlay for text readability */}\n      <div className=\"absolute inset-0 bg-black bg-opacity-40\" />\n\n      {/* Content overlay */}\n      {children && (\n        <div className=\"absolute inset-0 z-20\">\n          {children}\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAeO,SAAS,gBAAgB,EAC9B,SAAS,EACT,QAAQ,EACR,WAAW,KAAK,EAChB,WAAW,EACU;IACrB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,CAAA,GAAA,oIAAA,CAAA,yBAAsB,AAAD;IACtE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,kBAAkB;YACtB,IAAI;gBACF,aAAa;gBACb,MAAM,iBAAiB,MAAM,CAAA,GAAA,oIAAA,CAAA,wBAAqB,AAAD;gBACjD,YAAY;gBACZ,cAAc;YAChB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,6BAA6B;gBAC3C,YAAY,CAAA,GAAA,oIAAA,CAAA,yBAAsB,AAAD;YACnC,SAAU;gBACR,aAAa;YACf;QACF;QAEA;IACF,GAAG;QAAC;KAAY;IAEhB,MAAM,kBAAkB;QACtB,aAAa;IACf;IAEA,MAAM,mBAAmB;QACvB,QAAQ,KAAK,CAAC;QACd,YAAY,CAAA,GAAA,oIAAA,CAAA,yBAAsB,AAAD;QACjC,aAAa;IACf;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;;YAE1D,2BACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,0IAAA,CAAA,iBAAc;4BAAC,MAAK;4BAAK,WAAU;;;;;;sCACpC,8OAAC;4BAAE,WAAU;sCAAsB;;;;;;;;;;;;;;;;;0BAMzC,8OAAC,6HAAA,CAAA,UAAK;gBACJ,KAAK;gBACL,KAAI;gBACJ,IAAI;gBACJ,WAAU;gBACV,UAAU;gBACV,QAAQ;gBACR,SAAS;gBACT,OAAM;;;;;;0BAIR,8OAAC;gBAAI,WAAU;;;;;;YAGd,0BACC,8OAAC;gBAAI,WAAU;0BACZ;;;;;;;;;;;;AAKX", "debugId": null}}, {"offset": {"line": 248, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/src/personal/bvr-safaris/src/components/ui/Input.tsx"], "sourcesContent": ["import { InputHTMLAttributes, forwardRef } from 'react'\nimport { cn } from '@/lib/utils'\n\nexport interface InputProps extends InputHTMLAttributes<HTMLInputElement> {\n  label?: string\n  error?: string\n}\n\nconst Input = forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, label, error, ...props }, ref) => {\n    return (\n      <div className=\"w-full\">\n        {label && (\n          <label className=\"block mb-2 font-semibold text-[var(--primary-brown)] font-[var(--font-ui)]\">\n            {label}\n          </label>\n        )}\n        <input\n          type={type}\n          className={cn(\n            `\n            w-full px-4 py-2 border-2 border-[var(--medium-gray)] \n            rounded-[var(--radius-md)] font-[var(--font-ui)] \n            transition-colors duration-300\n            focus:outline-none focus:border-[var(--primary-brown)] \n            focus:ring-0 focus:shadow-[0_0_0_3px_rgba(139,69,19,0.1)]\n            placeholder:text-[var(--medium-gray)]\n            `,\n            error && 'border-red-500 focus:border-red-500',\n            className\n          )}\n          ref={ref}\n          {...props}\n        />\n        {error && (\n          <p className=\"mt-1 text-sm text-red-600\">{error}</p>\n        )}\n      </div>\n    )\n  }\n)\n\nInput.displayName = 'Input'\n\nexport { Input }"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAOA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACrB,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE;IAC5C,qBACE,8OAAC;QAAI,WAAU;;YACZ,uBACC,8OAAC;gBAAM,WAAU;0BACd;;;;;;0BAGL,8OAAC;gBACC,MAAM;gBACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,CAAC;;;;;;;YAOD,CAAC,EACD,SAAS,uCACT;gBAEF,KAAK;gBACJ,GAAG,KAAK;;;;;;YAEV,uBACC,8OAAC;gBAAE,WAAU;0BAA6B;;;;;;;;;;;;AAIlD;AAGF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 309, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/src/personal/bvr-safaris/src/lib/types/location.ts"], "sourcesContent": ["/**\n * Location-related types for BVR Safaris geolocation functionality\n */\n\nimport { GeoPoint } from 'firebase/firestore'\n\n// Google Places API response types\nexport interface LocationSearchResult {\n  placeId: string\n  description: string\n  mainText: string\n  secondaryText: string\n  types: string[]\n  structuredFormatting?: {\n    mainText: string\n    secondaryText?: string\n  }\n}\n\nexport interface AddressComponent {\n  longName: string\n  shortName: string\n  types: string[]\n}\n\nexport interface PlaceDetails {\n  placeId: string\n  formattedAddress: string\n  coordinates: {\n    lat: number\n    lng: number\n  }\n  addressComponents: AddressComponent[]\n  types: string[]\n  viewport?: {\n    northeast: { lat: number; lng: number }\n    southwest: { lat: number; lng: number }\n  }\n  name?: string\n  businessStatus?: string\n}\n\n// Enhanced location data structure for farms\nexport interface LocationData {\n  placeId: string\n  formattedAddress: string\n  coordinates: GeoPoint // Firestore GeoPoint for geo queries\n  addressComponents: {\n    streetNumber?: string\n    route?: string\n    locality?: string // City/Town\n    sublocality?: string // Suburb/Area\n    administrativeAreaLevel1: string // Province\n    administrativeAreaLevel2?: string // District/Municipality\n    postalCode?: string\n    country: string // Should be \"South Africa\"\n  }\n  placeTypes: string[]\n  viewport?: {\n    northeast: GeoPoint\n    southwest: GeoPoint\n  }\n  name?: string // Business name if applicable\n}\n\n// Search parameters for location-based queries\nexport interface LocationSearchParams {\n  center?: { lat: number; lng: number }\n  radius?: number // in kilometers\n  bounds?: {\n    northeast: { lat: number; lng: number }\n    southwest: { lat: number; lng: number }\n  }\n  formattedAddress?: string\n  text?: string // fallback text search\n}\n\n// Distance calculation result\nexport interface DistanceResult {\n  distance: number // in kilometers\n  duration?: number // in minutes (if available)\n}\n\n// Geocoding result\nexport interface GeocodeResult {\n  coordinates: { lat: number; lng: number }\n  formattedAddress: string\n  addressComponents: AddressComponent[]\n  placeId?: string\n  types: string[]\n}\n\n// Location service configuration\nexport interface LocationServiceConfig {\n  apiKey: string\n  countryRestriction?: string // Default: 'za' for South Africa\n  language?: string // Default: 'en'\n  region?: string // Default: 'za'\n}\n\n// Cache entry for location data\nexport interface LocationCacheEntry {\n  data: any\n  timestamp: number\n  ttl: number // time to live in milliseconds\n}\n\n// Error types for location services\nexport enum LocationErrorType {\n  API_ERROR = 'API_ERROR',\n  NETWORK_ERROR = 'NETWORK_ERROR',\n  INVALID_REQUEST = 'INVALID_REQUEST',\n  QUOTA_EXCEEDED = 'QUOTA_EXCEEDED',\n  PERMISSION_DENIED = 'PERMISSION_DENIED',\n  UNKNOWN_ERROR = 'UNKNOWN_ERROR'\n}\n\nexport interface LocationError {\n  type: LocationErrorType\n  message: string\n  originalError?: any\n}\n\n// Utility types for component props\nexport interface LocationAutocompleteProps {\n  value?: string\n  onLocationSelect: (location: PlaceDetails) => void\n  onInputChange?: (input: string) => void\n  placeholder?: string\n  countryRestriction?: string\n  types?: string[]\n  className?: string\n  disabled?: boolean\n  required?: boolean\n  error?: string\n}\n\nexport interface LocationDisplayProps {\n  locationData: LocationData\n  showFullAddress?: boolean\n  showDistance?: boolean\n  userLocation?: { lat: number; lng: number }\n  className?: string\n}\n\nexport interface DistanceFilterProps {\n  value?: number\n  onChange: (radius: number) => void\n  options?: number[] // Available radius options in km\n  className?: string\n}\n\n// Search filter types with location support\nexport interface EnhancedSearchFilters {\n  query?: string\n  location?: LocationSearchParams\n  provinces?: string[]\n  activities?: string[]\n  priceRange?: [number, number]\n  sortBy?: 'distance' | 'rating' | 'created' | 'name' | 'price'\n  limit?: number\n}\n\n// Farm with distance information\nexport interface FarmWithDistance {\n  id: string\n  name: string\n  locationData?: LocationData\n  distance?: number\n  [key: string]: any // Other farm properties\n}\n\n// Browser geolocation types\nexport interface BrowserLocationOptions {\n  enableHighAccuracy?: boolean\n  timeout?: number\n  maximumAge?: number\n}\n\nexport interface BrowserLocationResult {\n  coordinates: { lat: number; lng: number }\n  accuracy: number\n  timestamp: number\n}\n\n// Rate limiting configuration\nexport interface RateLimitConfig {\n  requestsPerSecond: number\n  burstLimit: number\n  windowMs: number\n}\n\n// Service response wrapper\nexport interface ServiceResponse<T> {\n  success: boolean\n  data?: T\n  error?: LocationError\n  cached?: boolean\n}\n\n// Province mapping for South African addresses\nexport const PROVINCE_MAPPING: Record<string, string> = {\n  'Eastern Cape': 'Eastern Cape',\n  'Free State': 'Free State',\n  'Gauteng': 'Gauteng',\n  'KwaZulu-Natal': 'KwaZulu-Natal',\n  'Limpopo': 'Limpopo',\n  'Mpumalanga': 'Mpumalanga',\n  'Northern Cape': 'Northern Cape',\n  'North West': 'North West',\n  'Western Cape': 'Western Cape'\n}\n\n// Common place types for South African locations\nexport const SOUTH_AFRICAN_PLACE_TYPES = {\n  ESTABLISHMENT: 'establishment',\n  GEOCODE: 'geocode',\n  LOCALITY: 'locality',\n  SUBLOCALITY: 'sublocality',\n  ADMINISTRATIVE_AREA_LEVEL_1: 'administrative_area_level_1',\n  ADMINISTRATIVE_AREA_LEVEL_2: 'administrative_area_level_2',\n  COUNTRY: 'country',\n  POSTAL_CODE: 'postal_code'\n} as const\n\n// Default configuration values\nexport const DEFAULT_LOCATION_CONFIG = {\n  COUNTRY_RESTRICTION: 'za',\n  LANGUAGE: 'en',\n  REGION: 'za',\n  CACHE_TTL: 5 * 60 * 1000, // 5 minutes\n  DEBOUNCE_DELAY: 300, // milliseconds\n  DEFAULT_RADIUS_OPTIONS: [5, 10, 25, 50, 100], // kilometers\n  MAX_AUTOCOMPLETE_RESULTS: 5,\n  GEOLOCATION_TIMEOUT: 10000, // 10 seconds\n  GEOLOCATION_MAX_AGE: 60000 // 1 minute\n} as const\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;;AA0GM,IAAA,AAAK,2CAAA;;;;;;;WAAA;;AA6FL,MAAM,mBAA2C;IACtD,gBAAgB;IAChB,cAAc;IACd,WAAW;IACX,iBAAiB;IACjB,WAAW;IACX,cAAc;IACd,iBAAiB;IACjB,cAAc;IACd,gBAAgB;AAClB;AAGO,MAAM,4BAA4B;IACvC,eAAe;IACf,SAAS;IACT,UAAU;IACV,aAAa;IACb,6BAA6B;IAC7B,6BAA6B;IAC7B,SAAS;IACT,aAAa;AACf;AAGO,MAAM,0BAA0B;IACrC,qBAAqB;IACrB,UAAU;IACV,QAAQ;IACR,WAAW,IAAI,KAAK;IACpB,gBAAgB;IAChB,wBAAwB;QAAC;QAAG;QAAI;QAAI;QAAI;KAAI;IAC5C,0BAA0B;IAC1B,qBAAqB;IACrB,qBAAqB,MAAM,WAAW;AACxC", "debugId": null}}, {"offset": {"line": 370, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/src/personal/bvr-safaris/src/lib/services/location/googlePlaces.ts"], "sourcesContent": ["/**\n * Google Places API service for BVR Safaris\n * Handles autocomplete, place details, and geocoding functionality\n */\n\nimport {\n  LocationSearchResult,\n  PlaceDetails,\n  GeocodeResult,\n  LocationServiceConfig,\n  LocationError,\n  LocationErrorType,\n  ServiceResponse,\n  DEFAULT_LOCATION_CONFIG\n} from '@/lib/types/location'\n\nexport class GooglePlacesService {\n  private apiKey: string\n  private config: LocationServiceConfig\n  private cache = new Map<string, any>()\n  private requestQueue = new Map<string, Promise<any>>()\n\n  constructor(apiKey: string, config?: Partial<LocationServiceConfig>) {\n    this.apiKey = apiKey\n    this.config = {\n      apiKey,\n      countryRestriction: DEFAULT_LOCATION_CONFIG.COUNTRY_RESTRICTION,\n      language: DEFAULT_LOCATION_CONFIG.LANGUAGE,\n      region: DEFAULT_LOCATION_CONFIG.REGION,\n      ...config\n    }\n  }\n\n  /**\n   * Get place predictions for autocomplete\n   */\n  async getPlacePredictions(\n    input: string,\n    options?: {\n      types?: string[]\n      componentRestrictions?: { country: string }\n      locationBias?: { lat: number; lng: number; radius: number }\n      sessionToken?: string\n    }\n  ): Promise<ServiceResponse<LocationSearchResult[]>> {\n    if (!input || input.length < 2) {\n      return { success: true, data: [] }\n    }\n\n    const cacheKey = `predictions_${input}_${JSON.stringify(options)}`\n    \n    // Check cache first\n    const cached = this.getFromCache(cacheKey)\n    if (cached) {\n      return { success: true, data: cached, cached: true }\n    }\n\n    // Check if request is already in progress\n    if (this.requestQueue.has(cacheKey)) {\n      try {\n        const result = await this.requestQueue.get(cacheKey)!\n        return { success: true, data: result }\n      } catch (error) {\n        return this.handleError(error)\n      }\n    }\n\n    // Create new request\n    const requestPromise = this.fetchPlacePredictions(input, options)\n    this.requestQueue.set(cacheKey, requestPromise)\n\n    try {\n      const results = await requestPromise\n      this.setCache(cacheKey, results)\n      this.requestQueue.delete(cacheKey)\n      return { success: true, data: results }\n    } catch (error) {\n      this.requestQueue.delete(cacheKey)\n      return this.handleError(error)\n    }\n  }\n\n  /**\n   * Get detailed place information\n   */\n  async getPlaceDetails(\n    placeId: string,\n    fields?: string[]\n  ): Promise<ServiceResponse<PlaceDetails>> {\n    const cacheKey = `details_${placeId}_${fields?.join(',') || 'default'}`\n    \n    // Check cache first\n    const cached = this.getFromCache(cacheKey)\n    if (cached) {\n      return { success: true, data: cached, cached: true }\n    }\n\n    try {\n      const details = await this.fetchPlaceDetails(placeId, fields)\n      this.setCache(cacheKey, details)\n      return { success: true, data: details }\n    } catch (error) {\n      return this.handleError(error)\n    }\n  }\n\n  /**\n   * Geocode an address to coordinates\n   */\n  async geocodeAddress(address: string): Promise<ServiceResponse<GeocodeResult>> {\n    const cacheKey = `geocode_${address}`\n    \n    // Check cache first\n    const cached = this.getFromCache(cacheKey)\n    if (cached) {\n      return { success: true, data: cached, cached: true }\n    }\n\n    try {\n      const result = await this.fetchGeocode(address)\n      this.setCache(cacheKey, result)\n      return { success: true, data: result }\n    } catch (error) {\n      return this.handleError(error)\n    }\n  }\n\n  /**\n   * Reverse geocode coordinates to address\n   */\n  async reverseGeocode(\n    lat: number, \n    lng: number\n  ): Promise<ServiceResponse<PlaceDetails>> {\n    const cacheKey = `reverse_${lat}_${lng}`\n    \n    // Check cache first\n    const cached = this.getFromCache(cacheKey)\n    if (cached) {\n      return { success: true, data: cached, cached: true }\n    }\n\n    try {\n      const result = await this.fetchReverseGeocode(lat, lng)\n      this.setCache(cacheKey, result)\n      return { success: true, data: result }\n    } catch (error) {\n      return this.handleError(error)\n    }\n  }\n\n  /**\n   * Private method to fetch place predictions from Google API\n   */\n  private async fetchPlacePredictions(\n    input: string,\n    options?: any\n  ): Promise<LocationSearchResult[]> {\n    const params = new URLSearchParams({\n      input,\n      key: this.apiKey,\n      language: this.config.language!,\n      components: `country:${options?.componentRestrictions?.country || this.config.countryRestriction}`,\n    })\n\n    if (options?.types) {\n      params.append('types', options.types.join('|'))\n    }\n\n    if (options?.sessionToken) {\n      params.append('sessiontoken', options.sessionToken)\n    }\n\n    const response = await fetch(\n      `https://maps.googleapis.com/maps/api/place/autocomplete/json?${params}`\n    )\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`)\n    }\n\n    const data = await response.json()\n\n    if (data.status !== 'OK' && data.status !== 'ZERO_RESULTS') {\n      throw new Error(`Google Places API error: ${data.status} - ${data.error_message || 'Unknown error'}`)\n    }\n\n    return (data.predictions || []).map((prediction: any) => ({\n      placeId: prediction.place_id,\n      description: prediction.description,\n      mainText: prediction.structured_formatting?.main_text || prediction.description,\n      secondaryText: prediction.structured_formatting?.secondary_text || '',\n      types: prediction.types || [],\n      structuredFormatting: prediction.structured_formatting\n    }))\n  }\n\n  /**\n   * Private method to fetch place details from Google API\n   */\n  private async fetchPlaceDetails(\n    placeId: string,\n    fields?: string[]\n  ): Promise<PlaceDetails> {\n    const defaultFields = [\n      'place_id',\n      'formatted_address',\n      'geometry',\n      'address_components',\n      'types',\n      'name',\n      'business_status'\n    ]\n\n    const params = new URLSearchParams({\n      place_id: placeId,\n      key: this.apiKey,\n      language: this.config.language!,\n      fields: (fields || defaultFields).join(',')\n    })\n\n    const response = await fetch(\n      `https://maps.googleapis.com/maps/api/place/details/json?${params}`\n    )\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`)\n    }\n\n    const data = await response.json()\n\n    if (data.status !== 'OK') {\n      throw new Error(`Google Places API error: ${data.status} - ${data.error_message || 'Unknown error'}`)\n    }\n\n    const place = data.result\n    return {\n      placeId: place.place_id,\n      formattedAddress: place.formatted_address,\n      coordinates: {\n        lat: place.geometry.location.lat,\n        lng: place.geometry.location.lng\n      },\n      addressComponents: (place.address_components || []).map((component: any) => ({\n        longName: component.long_name,\n        shortName: component.short_name,\n        types: component.types\n      })),\n      types: place.types || [],\n      viewport: place.geometry.viewport ? {\n        northeast: {\n          lat: place.geometry.viewport.northeast.lat,\n          lng: place.geometry.viewport.northeast.lng\n        },\n        southwest: {\n          lat: place.geometry.viewport.southwest.lat,\n          lng: place.geometry.viewport.southwest.lng\n        }\n      } : undefined,\n      name: place.name,\n      businessStatus: place.business_status\n    }\n  }\n\n  /**\n   * Private method to geocode address\n   */\n  private async fetchGeocode(address: string): Promise<GeocodeResult> {\n    const params = new URLSearchParams({\n      address,\n      key: this.apiKey,\n      language: this.config.language!,\n      region: this.config.region!\n    })\n\n    const response = await fetch(\n      `https://maps.googleapis.com/maps/api/geocode/json?${params}`\n    )\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`)\n    }\n\n    const data = await response.json()\n\n    if (data.status !== 'OK') {\n      throw new Error(`Geocoding API error: ${data.status} - ${data.error_message || 'Unknown error'}`)\n    }\n\n    const result = data.results[0]\n    return {\n      coordinates: {\n        lat: result.geometry.location.lat,\n        lng: result.geometry.location.lng\n      },\n      formattedAddress: result.formatted_address,\n      addressComponents: (result.address_components || []).map((component: any) => ({\n        longName: component.long_name,\n        shortName: component.short_name,\n        types: component.types\n      })),\n      placeId: result.place_id,\n      types: result.types || []\n    }\n  }\n\n  /**\n   * Private method to reverse geocode coordinates\n   */\n  private async fetchReverseGeocode(lat: number, lng: number): Promise<PlaceDetails> {\n    const params = new URLSearchParams({\n      latlng: `${lat},${lng}`,\n      key: this.apiKey,\n      language: this.config.language!\n    })\n\n    const response = await fetch(\n      `https://maps.googleapis.com/maps/api/geocode/json?${params}`\n    )\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`)\n    }\n\n    const data = await response.json()\n\n    if (data.status !== 'OK') {\n      throw new Error(`Reverse geocoding API error: ${data.status} - ${data.error_message || 'Unknown error'}`)\n    }\n\n    const result = data.results[0]\n    return {\n      placeId: result.place_id,\n      formattedAddress: result.formatted_address,\n      coordinates: { lat, lng },\n      addressComponents: (result.address_components || []).map((component: any) => ({\n        longName: component.long_name,\n        shortName: component.short_name,\n        types: component.types\n      })),\n      types: result.types || []\n    }\n  }\n\n  /**\n   * Cache management\n   */\n  private getFromCache(key: string): any {\n    const entry = this.cache.get(key)\n    if (!entry) return null\n\n    const { data, timestamp, ttl } = entry\n    if (Date.now() - timestamp > ttl) {\n      this.cache.delete(key)\n      return null\n    }\n\n    return data\n  }\n\n  private setCache(key: string, data: any, ttl = DEFAULT_LOCATION_CONFIG.CACHE_TTL): void {\n    this.cache.set(key, {\n      data,\n      timestamp: Date.now(),\n      ttl\n    })\n\n    // Clean up old entries periodically\n    if (this.cache.size > 100) {\n      this.cleanupCache()\n    }\n  }\n\n  private cleanupCache(): void {\n    const now = Date.now()\n    for (const [key, entry] of this.cache.entries()) {\n      if (now - entry.timestamp > entry.ttl) {\n        this.cache.delete(key)\n      }\n    }\n  }\n\n  /**\n   * Error handling\n   */\n  private handleError(error: any): ServiceResponse<any> {\n    let locationError: LocationError\n\n    if (error.message?.includes('quota') || error.message?.includes('OVER_QUERY_LIMIT')) {\n      locationError = {\n        type: LocationErrorType.QUOTA_EXCEEDED,\n        message: 'API quota exceeded. Please try again later.',\n        originalError: error\n      }\n    } else if (error.message?.includes('PERMISSION_DENIED')) {\n      locationError = {\n        type: LocationErrorType.PERMISSION_DENIED,\n        message: 'Permission denied. Please check API key configuration.',\n        originalError: error\n      }\n    } else if (error.message?.includes('INVALID_REQUEST')) {\n      locationError = {\n        type: LocationErrorType.INVALID_REQUEST,\n        message: 'Invalid request parameters.',\n        originalError: error\n      }\n    } else if (error.name === 'TypeError' || error.message?.includes('fetch')) {\n      locationError = {\n        type: LocationErrorType.NETWORK_ERROR,\n        message: 'Network error. Please check your internet connection.',\n        originalError: error\n      }\n    } else {\n      locationError = {\n        type: LocationErrorType.UNKNOWN_ERROR,\n        message: error.message || 'An unknown error occurred.',\n        originalError: error\n      }\n    }\n\n    return { success: false, error: locationError }\n  }\n\n  /**\n   * Clear cache\n   */\n  clearCache(): void {\n    this.cache.clear()\n  }\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAED;;AAWO,MAAM;IACH,OAAc;IACd,OAA6B;IAC7B,QAAQ,IAAI,MAAkB;IAC9B,eAAe,IAAI,MAA2B;IAEtD,YAAY,MAAc,EAAE,MAAuC,CAAE;QACnE,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,MAAM,GAAG;YACZ;YACA,oBAAoB,+HAAA,CAAA,0BAAuB,CAAC,mBAAmB;YAC/D,UAAU,+HAAA,CAAA,0BAAuB,CAAC,QAAQ;YAC1C,QAAQ,+HAAA,CAAA,0BAAuB,CAAC,MAAM;YACtC,GAAG,MAAM;QACX;IACF;IAEA;;GAEC,GACD,MAAM,oBACJ,KAAa,EACb,OAKC,EACiD;QAClD,IAAI,CAAC,SAAS,MAAM,MAAM,GAAG,GAAG;YAC9B,OAAO;gBAAE,SAAS;gBAAM,MAAM,EAAE;YAAC;QACnC;QAEA,MAAM,WAAW,CAAC,YAAY,EAAE,MAAM,CAAC,EAAE,KAAK,SAAS,CAAC,UAAU;QAElE,oBAAoB;QACpB,MAAM,SAAS,IAAI,CAAC,YAAY,CAAC;QACjC,IAAI,QAAQ;YACV,OAAO;gBAAE,SAAS;gBAAM,MAAM;gBAAQ,QAAQ;YAAK;QACrD;QAEA,0CAA0C;QAC1C,IAAI,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,WAAW;YACnC,IAAI;gBACF,MAAM,SAAS,MAAM,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC;gBAC3C,OAAO;oBAAE,SAAS;oBAAM,MAAM;gBAAO;YACvC,EAAE,OAAO,OAAO;gBACd,OAAO,IAAI,CAAC,WAAW,CAAC;YAC1B;QACF;QAEA,qBAAqB;QACrB,MAAM,iBAAiB,IAAI,CAAC,qBAAqB,CAAC,OAAO;QACzD,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,UAAU;QAEhC,IAAI;YACF,MAAM,UAAU,MAAM;YACtB,IAAI,CAAC,QAAQ,CAAC,UAAU;YACxB,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC;YACzB,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAQ;QACxC,EAAE,OAAO,OAAO;YACd,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC;YACzB,OAAO,IAAI,CAAC,WAAW,CAAC;QAC1B;IACF;IAEA;;GAEC,GACD,MAAM,gBACJ,OAAe,EACf,MAAiB,EACuB;QACxC,MAAM,WAAW,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAAE,QAAQ,KAAK,QAAQ,WAAW;QAEvE,oBAAoB;QACpB,MAAM,SAAS,IAAI,CAAC,YAAY,CAAC;QACjC,IAAI,QAAQ;YACV,OAAO;gBAAE,SAAS;gBAAM,MAAM;gBAAQ,QAAQ;YAAK;QACrD;QAEA,IAAI;YACF,MAAM,UAAU,MAAM,IAAI,CAAC,iBAAiB,CAAC,SAAS;YACtD,IAAI,CAAC,QAAQ,CAAC,UAAU;YACxB,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAQ;QACxC,EAAE,OAAO,OAAO;YACd,OAAO,IAAI,CAAC,WAAW,CAAC;QAC1B;IACF;IAEA;;GAEC,GACD,MAAM,eAAe,OAAe,EAA2C;QAC7E,MAAM,WAAW,CAAC,QAAQ,EAAE,SAAS;QAErC,oBAAoB;QACpB,MAAM,SAAS,IAAI,CAAC,YAAY,CAAC;QACjC,IAAI,QAAQ;YACV,OAAO;gBAAE,SAAS;gBAAM,MAAM;gBAAQ,QAAQ;YAAK;QACrD;QAEA,IAAI;YACF,MAAM,SAAS,MAAM,IAAI,CAAC,YAAY,CAAC;YACvC,IAAI,CAAC,QAAQ,CAAC,UAAU;YACxB,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAO;QACvC,EAAE,OAAO,OAAO;YACd,OAAO,IAAI,CAAC,WAAW,CAAC;QAC1B;IACF;IAEA;;GAEC,GACD,MAAM,eACJ,GAAW,EACX,GAAW,EAC6B;QACxC,MAAM,WAAW,CAAC,QAAQ,EAAE,IAAI,CAAC,EAAE,KAAK;QAExC,oBAAoB;QACpB,MAAM,SAAS,IAAI,CAAC,YAAY,CAAC;QACjC,IAAI,QAAQ;YACV,OAAO;gBAAE,SAAS;gBAAM,MAAM;gBAAQ,QAAQ;YAAK;QACrD;QAEA,IAAI;YACF,MAAM,SAAS,MAAM,IAAI,CAAC,mBAAmB,CAAC,KAAK;YACnD,IAAI,CAAC,QAAQ,CAAC,UAAU;YACxB,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAO;QACvC,EAAE,OAAO,OAAO;YACd,OAAO,IAAI,CAAC,WAAW,CAAC;QAC1B;IACF;IAEA;;GAEC,GACD,MAAc,sBACZ,KAAa,EACb,OAAa,EACoB;QACjC,MAAM,SAAS,IAAI,gBAAgB;YACjC;YACA,KAAK,IAAI,CAAC,MAAM;YAChB,UAAU,IAAI,CAAC,MAAM,CAAC,QAAQ;YAC9B,YAAY,CAAC,QAAQ,EAAE,SAAS,uBAAuB,WAAW,IAAI,CAAC,MAAM,CAAC,kBAAkB,EAAE;QACpG;QAEA,IAAI,SAAS,OAAO;YAClB,OAAO,MAAM,CAAC,SAAS,QAAQ,KAAK,CAAC,IAAI,CAAC;QAC5C;QAEA,IAAI,SAAS,cAAc;YACzB,OAAO,MAAM,CAAC,gBAAgB,QAAQ,YAAY;QACpD;QAEA,MAAM,WAAW,MAAM,MACrB,CAAC,6DAA6D,EAAE,QAAQ;QAG1E,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,IAAI,KAAK,MAAM,KAAK,QAAQ,KAAK,MAAM,KAAK,gBAAgB;YAC1D,MAAM,IAAI,MAAM,CAAC,yBAAyB,EAAE,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,aAAa,IAAI,iBAAiB;QACtG;QAEA,OAAO,CAAC,KAAK,WAAW,IAAI,EAAE,EAAE,GAAG,CAAC,CAAC,aAAoB,CAAC;gBACxD,SAAS,WAAW,QAAQ;gBAC5B,aAAa,WAAW,WAAW;gBACnC,UAAU,WAAW,qBAAqB,EAAE,aAAa,WAAW,WAAW;gBAC/E,eAAe,WAAW,qBAAqB,EAAE,kBAAkB;gBACnE,OAAO,WAAW,KAAK,IAAI,EAAE;gBAC7B,sBAAsB,WAAW,qBAAqB;YACxD,CAAC;IACH;IAEA;;GAEC,GACD,MAAc,kBACZ,OAAe,EACf,MAAiB,EACM;QACvB,MAAM,gBAAgB;YACpB;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QAED,MAAM,SAAS,IAAI,gBAAgB;YACjC,UAAU;YACV,KAAK,IAAI,CAAC,MAAM;YAChB,UAAU,IAAI,CAAC,MAAM,CAAC,QAAQ;YAC9B,QAAQ,CAAC,UAAU,aAAa,EAAE,IAAI,CAAC;QACzC;QAEA,MAAM,WAAW,MAAM,MACrB,CAAC,wDAAwD,EAAE,QAAQ;QAGrE,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,IAAI,KAAK,MAAM,KAAK,MAAM;YACxB,MAAM,IAAI,MAAM,CAAC,yBAAyB,EAAE,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,aAAa,IAAI,iBAAiB;QACtG;QAEA,MAAM,QAAQ,KAAK,MAAM;QACzB,OAAO;YACL,SAAS,MAAM,QAAQ;YACvB,kBAAkB,MAAM,iBAAiB;YACzC,aAAa;gBACX,KAAK,MAAM,QAAQ,CAAC,QAAQ,CAAC,GAAG;gBAChC,KAAK,MAAM,QAAQ,CAAC,QAAQ,CAAC,GAAG;YAClC;YACA,mBAAmB,CAAC,MAAM,kBAAkB,IAAI,EAAE,EAAE,GAAG,CAAC,CAAC,YAAmB,CAAC;oBAC3E,UAAU,UAAU,SAAS;oBAC7B,WAAW,UAAU,UAAU;oBAC/B,OAAO,UAAU,KAAK;gBACxB,CAAC;YACD,OAAO,MAAM,KAAK,IAAI,EAAE;YACxB,UAAU,MAAM,QAAQ,CAAC,QAAQ,GAAG;gBAClC,WAAW;oBACT,KAAK,MAAM,QAAQ,CAAC,QAAQ,CAAC,SAAS,CAAC,GAAG;oBAC1C,KAAK,MAAM,QAAQ,CAAC,QAAQ,CAAC,SAAS,CAAC,GAAG;gBAC5C;gBACA,WAAW;oBACT,KAAK,MAAM,QAAQ,CAAC,QAAQ,CAAC,SAAS,CAAC,GAAG;oBAC1C,KAAK,MAAM,QAAQ,CAAC,QAAQ,CAAC,SAAS,CAAC,GAAG;gBAC5C;YACF,IAAI;YACJ,MAAM,MAAM,IAAI;YAChB,gBAAgB,MAAM,eAAe;QACvC;IACF;IAEA;;GAEC,GACD,MAAc,aAAa,OAAe,EAA0B;QAClE,MAAM,SAAS,IAAI,gBAAgB;YACjC;YACA,KAAK,IAAI,CAAC,MAAM;YAChB,UAAU,IAAI,CAAC,MAAM,CAAC,QAAQ;YAC9B,QAAQ,IAAI,CAAC,MAAM,CAAC,MAAM;QAC5B;QAEA,MAAM,WAAW,MAAM,MACrB,CAAC,kDAAkD,EAAE,QAAQ;QAG/D,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,IAAI,KAAK,MAAM,KAAK,MAAM;YACxB,MAAM,IAAI,MAAM,CAAC,qBAAqB,EAAE,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,aAAa,IAAI,iBAAiB;QAClG;QAEA,MAAM,SAAS,KAAK,OAAO,CAAC,EAAE;QAC9B,OAAO;YACL,aAAa;gBACX,KAAK,OAAO,QAAQ,CAAC,QAAQ,CAAC,GAAG;gBACjC,KAAK,OAAO,QAAQ,CAAC,QAAQ,CAAC,GAAG;YACnC;YACA,kBAAkB,OAAO,iBAAiB;YAC1C,mBAAmB,CAAC,OAAO,kBAAkB,IAAI,EAAE,EAAE,GAAG,CAAC,CAAC,YAAmB,CAAC;oBAC5E,UAAU,UAAU,SAAS;oBAC7B,WAAW,UAAU,UAAU;oBAC/B,OAAO,UAAU,KAAK;gBACxB,CAAC;YACD,SAAS,OAAO,QAAQ;YACxB,OAAO,OAAO,KAAK,IAAI,EAAE;QAC3B;IACF;IAEA;;GAEC,GACD,MAAc,oBAAoB,GAAW,EAAE,GAAW,EAAyB;QACjF,MAAM,SAAS,IAAI,gBAAgB;YACjC,QAAQ,GAAG,IAAI,CAAC,EAAE,KAAK;YACvB,KAAK,IAAI,CAAC,MAAM;YAChB,UAAU,IAAI,CAAC,MAAM,CAAC,QAAQ;QAChC;QAEA,MAAM,WAAW,MAAM,MACrB,CAAC,kDAAkD,EAAE,QAAQ;QAG/D,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,IAAI,KAAK,MAAM,KAAK,MAAM;YACxB,MAAM,IAAI,MAAM,CAAC,6BAA6B,EAAE,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,aAAa,IAAI,iBAAiB;QAC1G;QAEA,MAAM,SAAS,KAAK,OAAO,CAAC,EAAE;QAC9B,OAAO;YACL,SAAS,OAAO,QAAQ;YACxB,kBAAkB,OAAO,iBAAiB;YAC1C,aAAa;gBAAE;gBAAK;YAAI;YACxB,mBAAmB,CAAC,OAAO,kBAAkB,IAAI,EAAE,EAAE,GAAG,CAAC,CAAC,YAAmB,CAAC;oBAC5E,UAAU,UAAU,SAAS;oBAC7B,WAAW,UAAU,UAAU;oBAC/B,OAAO,UAAU,KAAK;gBACxB,CAAC;YACD,OAAO,OAAO,KAAK,IAAI,EAAE;QAC3B;IACF;IAEA;;GAEC,GACD,AAAQ,aAAa,GAAW,EAAO;QACrC,MAAM,QAAQ,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;QAC7B,IAAI,CAAC,OAAO,OAAO;QAEnB,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,EAAE,GAAG;QACjC,IAAI,KAAK,GAAG,KAAK,YAAY,KAAK;YAChC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;YAClB,OAAO;QACT;QAEA,OAAO;IACT;IAEQ,SAAS,GAAW,EAAE,IAAS,EAAE,MAAM,+HAAA,CAAA,0BAAuB,CAAC,SAAS,EAAQ;QACtF,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK;YAClB;YACA,WAAW,KAAK,GAAG;YACnB;QACF;QAEA,oCAAoC;QACpC,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,KAAK;YACzB,IAAI,CAAC,YAAY;QACnB;IACF;IAEQ,eAAqB;QAC3B,MAAM,MAAM,KAAK,GAAG;QACpB,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,GAAI;YAC/C,IAAI,MAAM,MAAM,SAAS,GAAG,MAAM,GAAG,EAAE;gBACrC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;YACpB;QACF;IACF;IAEA;;GAEC,GACD,AAAQ,YAAY,KAAU,EAAwB;QACpD,IAAI;QAEJ,IAAI,MAAM,OAAO,EAAE,SAAS,YAAY,MAAM,OAAO,EAAE,SAAS,qBAAqB;YACnF,gBAAgB;gBACd,MAAM,+HAAA,CAAA,oBAAiB,CAAC,cAAc;gBACtC,SAAS;gBACT,eAAe;YACjB;QACF,OAAO,IAAI,MAAM,OAAO,EAAE,SAAS,sBAAsB;YACvD,gBAAgB;gBACd,MAAM,+HAAA,CAAA,oBAAiB,CAAC,iBAAiB;gBACzC,SAAS;gBACT,eAAe;YACjB;QACF,OAAO,IAAI,MAAM,OAAO,EAAE,SAAS,oBAAoB;YACrD,gBAAgB;gBACd,MAAM,+HAAA,CAAA,oBAAiB,CAAC,eAAe;gBACvC,SAAS;gBACT,eAAe;YACjB;QACF,OAAO,IAAI,MAAM,IAAI,KAAK,eAAe,MAAM,OAAO,EAAE,SAAS,UAAU;YACzE,gBAAgB;gBACd,MAAM,+HAAA,CAAA,oBAAiB,CAAC,aAAa;gBACrC,SAAS;gBACT,eAAe;YACjB;QACF,OAAO;YACL,gBAAgB;gBACd,MAAM,+HAAA,CAAA,oBAAiB,CAAC,aAAa;gBACrC,SAAS,MAAM,OAAO,IAAI;gBAC1B,eAAe;YACjB;QACF;QAEA,OAAO;YAAE,SAAS;YAAO,OAAO;QAAc;IAChD;IAEA;;GAEC,GACD,aAAmB;QACjB,IAAI,CAAC,KAAK,CAAC,KAAK;IAClB;AACF", "debugId": null}}, {"offset": {"line": 746, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/src/personal/bvr-safaris/src/lib/services/location/distance.ts"], "sourcesContent": ["/**\n * Distance calculation utilities for BVR Safaris geolocation\n * Provides accurate distance calculations and geographic utilities\n */\n\nimport { DistanceResult } from '@/lib/types/location'\n\nexport class DistanceService {\n  /**\n   * Calculate distance between two points using Haversine formula\n   * Returns distance in kilometers\n   */\n  static calculateDistance(\n    point1: { lat: number; lng: number },\n    point2: { lat: number; lng: number }\n  ): number {\n    const R = 6371 // Earth's radius in kilometers\n    const dLat = this.toRadians(point2.lat - point1.lat)\n    const dLng = this.toRadians(point2.lng - point1.lng)\n    \n    const a = \n      Math.sin(dLat / 2) * Math.sin(dLat / 2) +\n      Math.cos(this.toRadians(point1.lat)) * Math.cos(this.toRadians(point2.lat)) *\n      Math.sin(dLng / 2) * Math.sin(dLng / 2)\n    \n    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))\n    const distance = R * c\n    \n    return Math.round(distance * 100) / 100 // Round to 2 decimal places\n  }\n\n  /**\n   * Check if a point is within a specified radius of a center point\n   */\n  static isWithinRadius(\n    center: { lat: number; lng: number },\n    point: { lat: number; lng: number },\n    radiusKm: number\n  ): boolean {\n    const distance = this.calculateDistance(center, point)\n    return distance <= radiusKm\n  }\n\n  /**\n   * Calculate bounding box for efficient geographic queries\n   * Returns northeast and southwest corners of a box around a center point\n   */\n  static getBoundingBox(\n    center: { lat: number; lng: number },\n    radiusKm: number\n  ): {\n    northeast: { lat: number; lng: number }\n    southwest: { lat: number; lng: number }\n  } {\n    const R = 6371 // Earth's radius in kilometers\n    const lat = this.toRadians(center.lat)\n    const lng = this.toRadians(center.lng)\n    \n    // Angular distance in radians on a great circle\n    const angular = radiusKm / R\n    \n    const minLat = lat - angular\n    const maxLat = lat + angular\n    \n    let minLng: number\n    let maxLng: number\n    \n    if (minLat > this.toRadians(-90) && maxLat < this.toRadians(90)) {\n      const deltaLng = Math.asin(Math.sin(angular) / Math.cos(lat))\n      minLng = lng - deltaLng\n      maxLng = lng + deltaLng\n      \n      if (minLng < this.toRadians(-180)) minLng += 2 * Math.PI\n      if (maxLng > this.toRadians(180)) maxLng -= 2 * Math.PI\n    } else {\n      // A pole is within the distance\n      minLat = Math.max(minLat, this.toRadians(-90))\n      maxLat = Math.min(maxLat, this.toRadians(90))\n      minLng = this.toRadians(-180)\n      maxLng = this.toRadians(180)\n    }\n    \n    return {\n      southwest: {\n        lat: this.toDegrees(minLat),\n        lng: this.toDegrees(minLng)\n      },\n      northeast: {\n        lat: this.toDegrees(maxLat),\n        lng: this.toDegrees(maxLng)\n      }\n    }\n  }\n\n  /**\n   * Sort an array of points by distance from a center point\n   */\n  static sortByDistance<T extends { lat: number; lng: number }>(\n    points: T[],\n    center: { lat: number; lng: number }\n  ): (T & { distance: number })[] {\n    return points\n      .map(point => ({\n        ...point,\n        distance: this.calculateDistance(center, point)\n      }))\n      .sort((a, b) => a.distance - b.distance)\n  }\n\n  /**\n   * Filter points within a specified radius\n   */\n  static filterByRadius<T extends { lat: number; lng: number }>(\n    points: T[],\n    center: { lat: number; lng: number },\n    radiusKm: number\n  ): (T & { distance: number })[] {\n    return points\n      .map(point => ({\n        ...point,\n        distance: this.calculateDistance(center, point)\n      }))\n      .filter(point => point.distance <= radiusKm)\n      .sort((a, b) => a.distance - b.distance)\n  }\n\n  /**\n   * Get the center point (centroid) of multiple coordinates\n   */\n  static getCenterPoint(\n    points: { lat: number; lng: number }[]\n  ): { lat: number; lng: number } {\n    if (points.length === 0) {\n      throw new Error('Cannot calculate center of empty points array')\n    }\n\n    if (points.length === 1) {\n      return points[0]\n    }\n\n    let x = 0\n    let y = 0\n    let z = 0\n\n    for (const point of points) {\n      const lat = this.toRadians(point.lat)\n      const lng = this.toRadians(point.lng)\n\n      x += Math.cos(lat) * Math.cos(lng)\n      y += Math.cos(lat) * Math.sin(lng)\n      z += Math.sin(lat)\n    }\n\n    const total = points.length\n    x = x / total\n    y = y / total\n    z = z / total\n\n    const centralLng = Math.atan2(y, x)\n    const centralSquareRoot = Math.sqrt(x * x + y * y)\n    const centralLat = Math.atan2(z, centralSquareRoot)\n\n    return {\n      lat: this.toDegrees(centralLat),\n      lng: this.toDegrees(centralLng)\n    }\n  }\n\n  /**\n   * Calculate the bearing (direction) from one point to another\n   * Returns bearing in degrees (0-360)\n   */\n  static calculateBearing(\n    from: { lat: number; lng: number },\n    to: { lat: number; lng: number }\n  ): number {\n    const dLng = this.toRadians(to.lng - from.lng)\n    const lat1 = this.toRadians(from.lat)\n    const lat2 = this.toRadians(to.lat)\n\n    const y = Math.sin(dLng) * Math.cos(lat2)\n    const x = Math.cos(lat1) * Math.sin(lat2) - Math.sin(lat1) * Math.cos(lat2) * Math.cos(dLng)\n\n    let bearing = this.toDegrees(Math.atan2(y, x))\n    return (bearing + 360) % 360 // Normalize to 0-360\n  }\n\n  /**\n   * Format distance for display\n   */\n  static formatDistance(distanceKm: number): string {\n    if (distanceKm < 1) {\n      return `${Math.round(distanceKm * 1000)}m`\n    } else if (distanceKm < 10) {\n      return `${distanceKm.toFixed(1)}km`\n    } else {\n      return `${Math.round(distanceKm)}km`\n    }\n  }\n\n  /**\n   * Check if a point is within South Africa's approximate bounds\n   */\n  static isWithinSouthAfrica(point: { lat: number; lng: number }): boolean {\n    // Approximate bounds of South Africa\n    const bounds = {\n      north: -22.0,\n      south: -35.0,\n      east: 33.0,\n      west: 16.0\n    }\n\n    return (\n      point.lat >= bounds.south &&\n      point.lat <= bounds.north &&\n      point.lng >= bounds.west &&\n      point.lng <= bounds.east\n    )\n  }\n\n  /**\n   * Get approximate travel time based on distance (rough estimation)\n   * Assumes average speed of 80 km/h for highway travel\n   */\n  static estimateTravelTime(distanceKm: number): DistanceResult {\n    const averageSpeedKmh = 80\n    const durationMinutes = Math.round((distanceKm / averageSpeedKmh) * 60)\n    \n    return {\n      distance: distanceKm,\n      duration: durationMinutes\n    }\n  }\n\n  /**\n   * Format travel time for display\n   */\n  static formatTravelTime(minutes: number): string {\n    if (minutes < 60) {\n      return `${minutes} min`\n    } else {\n      const hours = Math.floor(minutes / 60)\n      const remainingMinutes = minutes % 60\n      if (remainingMinutes === 0) {\n        return `${hours}h`\n      } else {\n        return `${hours}h ${remainingMinutes}m`\n      }\n    }\n  }\n\n  /**\n   * Validate coordinates\n   */\n  static isValidCoordinate(point: { lat: number; lng: number }): boolean {\n    return (\n      typeof point.lat === 'number' &&\n      typeof point.lng === 'number' &&\n      point.lat >= -90 &&\n      point.lat <= 90 &&\n      point.lng >= -180 &&\n      point.lng <= 180 &&\n      !isNaN(point.lat) &&\n      !isNaN(point.lng)\n    )\n  }\n\n  /**\n   * Convert degrees to radians\n   */\n  private static toRadians(degrees: number): number {\n    return degrees * (Math.PI / 180)\n  }\n\n  /**\n   * Convert radians to degrees\n   */\n  private static toDegrees(radians: number): number {\n    return radians * (180 / Math.PI)\n  }\n}\n\n/**\n * Utility functions for working with geographic data\n */\nexport class GeoUtils {\n  /**\n   * Generate a simple geohash for efficient geographic indexing\n   * This is a simplified version - for production, consider using a proper geohash library\n   */\n  static generateSimpleGeoHash(\n    lat: number,\n    lng: number,\n    precision: number = 6\n  ): string {\n    const latRange = [-90, 90]\n    const lngRange = [-180, 180]\n    let hash = ''\n    let isEven = true\n    let bit = 0\n    let ch = 0\n\n    while (hash.length < precision) {\n      if (isEven) {\n        const mid = (lngRange[0] + lngRange[1]) / 2\n        if (lng >= mid) {\n          ch |= (1 << (4 - bit))\n          lngRange[0] = mid\n        } else {\n          lngRange[1] = mid\n        }\n      } else {\n        const mid = (latRange[0] + latRange[1]) / 2\n        if (lat >= mid) {\n          ch |= (1 << (4 - bit))\n          latRange[0] = mid\n        } else {\n          latRange[1] = mid\n        }\n      }\n\n      isEven = !isEven\n      bit++\n\n      if (bit === 5) {\n        hash += this.base32[ch]\n        bit = 0\n        ch = 0\n      }\n    }\n\n    return hash\n  }\n\n  private static base32 = '0123456789bcdefghjkmnpqrstuvwxyz'\n\n  /**\n   * Create a searchable location string from address components\n   */\n  static createSearchableLocation(addressComponents: any): string {\n    const components = []\n    \n    if (addressComponents.route) components.push(addressComponents.route)\n    if (addressComponents.locality) components.push(addressComponents.locality)\n    if (addressComponents.sublocality) components.push(addressComponents.sublocality)\n    if (addressComponents.administrativeAreaLevel2) components.push(addressComponents.administrativeAreaLevel2)\n    if (addressComponents.administrativeAreaLevel1) components.push(addressComponents.administrativeAreaLevel1)\n    if (addressComponents.postalCode) components.push(addressComponents.postalCode)\n    \n    return components.join(' ').toLowerCase()\n  }\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAIM,MAAM;IACX;;;GAGC,GACD,OAAO,kBACL,MAAoC,EACpC,MAAoC,EAC5B;QACR,MAAM,IAAI,KAAK,+BAA+B;;QAC9C,MAAM,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,GAAG,GAAG,OAAO,GAAG;QACnD,MAAM,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,GAAG,GAAG,OAAO,GAAG;QAEnD,MAAM,IACJ,KAAK,GAAG,CAAC,OAAO,KAAK,KAAK,GAAG,CAAC,OAAO,KACrC,KAAK,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,GAAG,KAAK,KAAK,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,GAAG,KACzE,KAAK,GAAG,CAAC,OAAO,KAAK,KAAK,GAAG,CAAC,OAAO;QAEvC,MAAM,IAAI,IAAI,KAAK,KAAK,CAAC,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI;QACrD,MAAM,WAAW,IAAI;QAErB,OAAO,KAAK,KAAK,CAAC,WAAW,OAAO,IAAI,4BAA4B;;IACtE;IAEA;;GAEC,GACD,OAAO,eACL,MAAoC,EACpC,KAAmC,EACnC,QAAgB,EACP;QACT,MAAM,WAAW,IAAI,CAAC,iBAAiB,CAAC,QAAQ;QAChD,OAAO,YAAY;IACrB;IAEA;;;GAGC,GACD,OAAO,eACL,MAAoC,EACpC,QAAgB,EAIhB;QACA,MAAM,IAAI,KAAK,+BAA+B;;QAC9C,MAAM,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,GAAG;QACrC,MAAM,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,GAAG;QAErC,gDAAgD;QAChD,MAAM,UAAU,WAAW;QAE3B,MAAM,SAAS,MAAM;QACrB,MAAM,SAAS,MAAM;QAErB,IAAI;QACJ,IAAI;QAEJ,IAAI,SAAS,IAAI,CAAC,SAAS,CAAC,CAAC,OAAO,SAAS,IAAI,CAAC,SAAS,CAAC,KAAK;YAC/D,MAAM,WAAW,KAAK,IAAI,CAAC,KAAK,GAAG,CAAC,WAAW,KAAK,GAAG,CAAC;YACxD,SAAS,MAAM;YACf,SAAS,MAAM;YAEf,IAAI,SAAS,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,UAAU,IAAI,KAAK,EAAE;YACxD,IAAI,SAAS,IAAI,CAAC,SAAS,CAAC,MAAM,UAAU,IAAI,KAAK,EAAE;QACzD,OAAO;YACL,gCAAgC;YAChC,SAAS,KAAK,GAAG,CAAC,QAAQ,IAAI,CAAC,SAAS,CAAC,CAAC;YAC1C,SAAS,KAAK,GAAG,CAAC,QAAQ,IAAI,CAAC,SAAS,CAAC;YACzC,SAAS,IAAI,CAAC,SAAS,CAAC,CAAC;YACzB,SAAS,IAAI,CAAC,SAAS,CAAC;QAC1B;QAEA,OAAO;YACL,WAAW;gBACT,KAAK,IAAI,CAAC,SAAS,CAAC;gBACpB,KAAK,IAAI,CAAC,SAAS,CAAC;YACtB;YACA,WAAW;gBACT,KAAK,IAAI,CAAC,SAAS,CAAC;gBACpB,KAAK,IAAI,CAAC,SAAS,CAAC;YACtB;QACF;IACF;IAEA;;GAEC,GACD,OAAO,eACL,MAAW,EACX,MAAoC,EACN;QAC9B,OAAO,OACJ,GAAG,CAAC,CAAA,QAAS,CAAC;gBACb,GAAG,KAAK;gBACR,UAAU,IAAI,CAAC,iBAAiB,CAAC,QAAQ;YAC3C,CAAC,GACA,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,QAAQ,GAAG,EAAE,QAAQ;IAC3C;IAEA;;GAEC,GACD,OAAO,eACL,MAAW,EACX,MAAoC,EACpC,QAAgB,EACc;QAC9B,OAAO,OACJ,GAAG,CAAC,CAAA,QAAS,CAAC;gBACb,GAAG,KAAK;gBACR,UAAU,IAAI,CAAC,iBAAiB,CAAC,QAAQ;YAC3C,CAAC,GACA,MAAM,CAAC,CAAA,QAAS,MAAM,QAAQ,IAAI,UAClC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,QAAQ,GAAG,EAAE,QAAQ;IAC3C;IAEA;;GAEC,GACD,OAAO,eACL,MAAsC,EACR;QAC9B,IAAI,OAAO,MAAM,KAAK,GAAG;YACvB,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,OAAO,MAAM,KAAK,GAAG;YACvB,OAAO,MAAM,CAAC,EAAE;QAClB;QAEA,IAAI,IAAI;QACR,IAAI,IAAI;QACR,IAAI,IAAI;QAER,KAAK,MAAM,SAAS,OAAQ;YAC1B,MAAM,MAAM,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG;YACpC,MAAM,MAAM,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG;YAEpC,KAAK,KAAK,GAAG,CAAC,OAAO,KAAK,GAAG,CAAC;YAC9B,KAAK,KAAK,GAAG,CAAC,OAAO,KAAK,GAAG,CAAC;YAC9B,KAAK,KAAK,GAAG,CAAC;QAChB;QAEA,MAAM,QAAQ,OAAO,MAAM;QAC3B,IAAI,IAAI;QACR,IAAI,IAAI;QACR,IAAI,IAAI;QAER,MAAM,aAAa,KAAK,KAAK,CAAC,GAAG;QACjC,MAAM,oBAAoB,KAAK,IAAI,CAAC,IAAI,IAAI,IAAI;QAChD,MAAM,aAAa,KAAK,KAAK,CAAC,GAAG;QAEjC,OAAO;YACL,KAAK,IAAI,CAAC,SAAS,CAAC;YACpB,KAAK,IAAI,CAAC,SAAS,CAAC;QACtB;IACF;IAEA;;;GAGC,GACD,OAAO,iBACL,IAAkC,EAClC,EAAgC,EACxB;QACR,MAAM,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,GAAG,GAAG,KAAK,GAAG;QAC7C,MAAM,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,GAAG;QACpC,MAAM,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,GAAG;QAElC,MAAM,IAAI,KAAK,GAAG,CAAC,QAAQ,KAAK,GAAG,CAAC;QACpC,MAAM,IAAI,KAAK,GAAG,CAAC,QAAQ,KAAK,GAAG,CAAC,QAAQ,KAAK,GAAG,CAAC,QAAQ,KAAK,GAAG,CAAC,QAAQ,KAAK,GAAG,CAAC;QAEvF,IAAI,UAAU,IAAI,CAAC,SAAS,CAAC,KAAK,KAAK,CAAC,GAAG;QAC3C,OAAO,CAAC,UAAU,GAAG,IAAI,IAAI,qBAAqB;;IACpD;IAEA;;GAEC,GACD,OAAO,eAAe,UAAkB,EAAU;QAChD,IAAI,aAAa,GAAG;YAClB,OAAO,GAAG,KAAK,KAAK,CAAC,aAAa,MAAM,CAAC,CAAC;QAC5C,OAAO,IAAI,aAAa,IAAI;YAC1B,OAAO,GAAG,WAAW,OAAO,CAAC,GAAG,EAAE,CAAC;QACrC,OAAO;YACL,OAAO,GAAG,KAAK,KAAK,CAAC,YAAY,EAAE,CAAC;QACtC;IACF;IAEA;;GAEC,GACD,OAAO,oBAAoB,KAAmC,EAAW;QACvE,qCAAqC;QACrC,MAAM,SAAS;YACb,OAAO,CAAC;YACR,OAAO,CAAC;YACR,MAAM;YACN,MAAM;QACR;QAEA,OACE,MAAM,GAAG,IAAI,OAAO,KAAK,IACzB,MAAM,GAAG,IAAI,OAAO,KAAK,IACzB,MAAM,GAAG,IAAI,OAAO,IAAI,IACxB,MAAM,GAAG,IAAI,OAAO,IAAI;IAE5B;IAEA;;;GAGC,GACD,OAAO,mBAAmB,UAAkB,EAAkB;QAC5D,MAAM,kBAAkB;QACxB,MAAM,kBAAkB,KAAK,KAAK,CAAC,AAAC,aAAa,kBAAmB;QAEpE,OAAO;YACL,UAAU;YACV,UAAU;QACZ;IACF;IAEA;;GAEC,GACD,OAAO,iBAAiB,OAAe,EAAU;QAC/C,IAAI,UAAU,IAAI;YAChB,OAAO,GAAG,QAAQ,IAAI,CAAC;QACzB,OAAO;YACL,MAAM,QAAQ,KAAK,KAAK,CAAC,UAAU;YACnC,MAAM,mBAAmB,UAAU;YACnC,IAAI,qBAAqB,GAAG;gBAC1B,OAAO,GAAG,MAAM,CAAC,CAAC;YACpB,OAAO;gBACL,OAAO,GAAG,MAAM,EAAE,EAAE,iBAAiB,CAAC,CAAC;YACzC;QACF;IACF;IAEA;;GAEC,GACD,OAAO,kBAAkB,KAAmC,EAAW;QACrE,OACE,OAAO,MAAM,GAAG,KAAK,YACrB,OAAO,MAAM,GAAG,KAAK,YACrB,MAAM,GAAG,IAAI,CAAC,MACd,MAAM,GAAG,IAAI,MACb,MAAM,GAAG,IAAI,CAAC,OACd,MAAM,GAAG,IAAI,OACb,CAAC,MAAM,MAAM,GAAG,KAChB,CAAC,MAAM,MAAM,GAAG;IAEpB;IAEA;;GAEC,GACD,OAAe,UAAU,OAAe,EAAU;QAChD,OAAO,UAAU,CAAC,KAAK,EAAE,GAAG,GAAG;IACjC;IAEA;;GAEC,GACD,OAAe,UAAU,OAAe,EAAU;QAChD,OAAO,UAAU,CAAC,MAAM,KAAK,EAAE;IACjC;AACF;AAKO,MAAM;IACX;;;GAGC,GACD,OAAO,sBACL,GAAW,EACX,GAAW,EACX,YAAoB,CAAC,EACb;QACR,MAAM,WAAW;YAAC,CAAC;YAAI;SAAG;QAC1B,MAAM,WAAW;YAAC,CAAC;YAAK;SAAI;QAC5B,IAAI,OAAO;QACX,IAAI,SAAS;QACb,IAAI,MAAM;QACV,IAAI,KAAK;QAET,MAAO,KAAK,MAAM,GAAG,UAAW;YAC9B,IAAI,QAAQ;gBACV,MAAM,MAAM,CAAC,QAAQ,CAAC,EAAE,GAAG,QAAQ,CAAC,EAAE,IAAI;gBAC1C,IAAI,OAAO,KAAK;oBACd,MAAO,KAAM,IAAI;oBACjB,QAAQ,CAAC,EAAE,GAAG;gBAChB,OAAO;oBACL,QAAQ,CAAC,EAAE,GAAG;gBAChB;YACF,OAAO;gBACL,MAAM,MAAM,CAAC,QAAQ,CAAC,EAAE,GAAG,QAAQ,CAAC,EAAE,IAAI;gBAC1C,IAAI,OAAO,KAAK;oBACd,MAAO,KAAM,IAAI;oBACjB,QAAQ,CAAC,EAAE,GAAG;gBAChB,OAAO;oBACL,QAAQ,CAAC,EAAE,GAAG;gBAChB;YACF;YAEA,SAAS,CAAC;YACV;YAEA,IAAI,QAAQ,GAAG;gBACb,QAAQ,IAAI,CAAC,MAAM,CAAC,GAAG;gBACvB,MAAM;gBACN,KAAK;YACP;QACF;QAEA,OAAO;IACT;IAEA,OAAe,SAAS,mCAAkC;IAE1D;;GAEC,GACD,OAAO,yBAAyB,iBAAsB,EAAU;QAC9D,MAAM,aAAa,EAAE;QAErB,IAAI,kBAAkB,KAAK,EAAE,WAAW,IAAI,CAAC,kBAAkB,KAAK;QACpE,IAAI,kBAAkB,QAAQ,EAAE,WAAW,IAAI,CAAC,kBAAkB,QAAQ;QAC1E,IAAI,kBAAkB,WAAW,EAAE,WAAW,IAAI,CAAC,kBAAkB,WAAW;QAChF,IAAI,kBAAkB,wBAAwB,EAAE,WAAW,IAAI,CAAC,kBAAkB,wBAAwB;QAC1G,IAAI,kBAAkB,wBAAwB,EAAE,WAAW,IAAI,CAAC,kBAAkB,wBAAwB;QAC1G,IAAI,kBAAkB,UAAU,EAAE,WAAW,IAAI,CAAC,kBAAkB,UAAU;QAE9E,OAAO,WAAW,IAAI,CAAC,KAAK,WAAW;IACzC;AACF", "debugId": null}}, {"offset": {"line": 1002, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/src/personal/bvr-safaris/src/lib/services/location/index.ts"], "sourcesContent": ["/**\n * Location services entry point for BVR Safaris\n * Provides a unified interface for all location-related functionality\n */\n\nimport { GooglePlacesService } from './googlePlaces'\nimport { DistanceService, GeoUtils } from './distance'\nimport {\n  LocationSearchResult,\n  PlaceDetails,\n  GeocodeResult,\n  LocationData,\n  LocationError,\n  LocationErrorType,\n  ServiceResponse,\n  BrowserLocationResult,\n  BrowserLocationOptions,\n  DEFAULT_LOCATION_CONFIG,\n  PROVINCE_MAPPING\n} from '@/lib/types/location'\nimport { GeoPoint } from 'firebase/firestore'\nimport { SouthAfricanProvince } from '@/lib/constants'\n\n/**\n * Main location service class that orchestrates all location functionality\n */\nexport class LocationService {\n  private googlePlaces: GooglePlacesService\n  private static instance: LocationService\n\n  constructor(apiKey: string) {\n    this.googlePlaces = new GooglePlacesService(apiKey)\n  }\n\n  /**\n   * Get singleton instance\n   */\n  static getInstance(): LocationService {\n    if (!LocationService.instance) {\n      const apiKey = process.env.NEXT_PUBLIC_GOOGLE_PLACES_API_KEY\n      if (!apiKey) {\n        throw new Error('Google Places API key not configured')\n      }\n      LocationService.instance = new LocationService(apiKey)\n    }\n    return LocationService.instance\n  }\n\n  /**\n   * Get place predictions for autocomplete\n   */\n  async getPlacePredictions(\n    input: string,\n    options?: {\n      types?: string[]\n      sessionToken?: string\n    }\n  ): Promise<ServiceResponse<LocationSearchResult[]>> {\n    return this.googlePlaces.getPlacePredictions(input, {\n      ...options,\n      componentRestrictions: { country: DEFAULT_LOCATION_CONFIG.COUNTRY_RESTRICTION }\n    })\n  }\n\n  /**\n   * Get detailed place information and convert to LocationData\n   */\n  async getLocationData(placeId: string): Promise<ServiceResponse<LocationData>> {\n    const response = await this.googlePlaces.getPlaceDetails(placeId)\n    \n    if (!response.success || !response.data) {\n      return response as ServiceResponse<LocationData>\n    }\n\n    const placeDetails = response.data\n    const locationData = this.convertToLocationData(placeDetails)\n    \n    return { success: true, data: locationData, cached: response.cached }\n  }\n\n  /**\n   * Geocode an address and return LocationData\n   */\n  async geocodeToLocationData(address: string): Promise<ServiceResponse<LocationData>> {\n    const response = await this.googlePlaces.geocodeAddress(address)\n    \n    if (!response.success || !response.data) {\n      return response as ServiceResponse<LocationData>\n    }\n\n    const geocodeResult = response.data\n    \n    // Get place details if we have a place ID\n    if (geocodeResult.placeId) {\n      return this.getLocationData(geocodeResult.placeId)\n    }\n\n    // Create LocationData from geocode result\n    const locationData: LocationData = {\n      placeId: geocodeResult.placeId || '',\n      formattedAddress: geocodeResult.formattedAddress,\n      coordinates: new GeoPoint(geocodeResult.coordinates.lat, geocodeResult.coordinates.lng),\n      addressComponents: this.parseAddressComponents(geocodeResult.addressComponents),\n      placeTypes: geocodeResult.types\n    }\n\n    return { success: true, data: locationData, cached: response.cached }\n  }\n\n  /**\n   * Get user's current location using browser geolocation\n   */\n  async getCurrentLocation(\n    options?: BrowserLocationOptions\n  ): Promise<ServiceResponse<BrowserLocationResult>> {\n    return new Promise((resolve) => {\n      if (!navigator.geolocation) {\n        resolve({\n          success: false,\n          error: {\n            type: LocationErrorType.PERMISSION_DENIED,\n            message: 'Geolocation is not supported by this browser'\n          }\n        })\n        return\n      }\n\n      const defaultOptions: BrowserLocationOptions = {\n        enableHighAccuracy: true,\n        timeout: DEFAULT_LOCATION_CONFIG.GEOLOCATION_TIMEOUT,\n        maximumAge: DEFAULT_LOCATION_CONFIG.GEOLOCATION_MAX_AGE,\n        ...options\n      }\n\n      navigator.geolocation.getCurrentPosition(\n        (position) => {\n          const result: BrowserLocationResult = {\n            coordinates: {\n              lat: position.coords.latitude,\n              lng: position.coords.longitude\n            },\n            accuracy: position.coords.accuracy,\n            timestamp: position.timestamp\n          }\n          resolve({ success: true, data: result })\n        },\n        (error) => {\n          let errorType: LocationErrorType\n          let message: string\n\n          switch (error.code) {\n            case error.PERMISSION_DENIED:\n              errorType = LocationErrorType.PERMISSION_DENIED\n              message = 'Location access denied by user'\n              break\n            case error.POSITION_UNAVAILABLE:\n              errorType = LocationErrorType.API_ERROR\n              message = 'Location information unavailable'\n              break\n            case error.TIMEOUT:\n              errorType = LocationErrorType.NETWORK_ERROR\n              message = 'Location request timed out'\n              break\n            default:\n              errorType = LocationErrorType.UNKNOWN_ERROR\n              message = 'Unknown location error'\n          }\n\n          resolve({\n            success: false,\n            error: { type: errorType, message, originalError: error }\n          })\n        },\n        defaultOptions\n      )\n    })\n  }\n\n  /**\n   * Convert PlaceDetails to LocationData format\n   */\n  private convertToLocationData(placeDetails: PlaceDetails): LocationData {\n    return {\n      placeId: placeDetails.placeId,\n      formattedAddress: placeDetails.formattedAddress,\n      coordinates: new GeoPoint(placeDetails.coordinates.lat, placeDetails.coordinates.lng),\n      addressComponents: this.parseAddressComponents(placeDetails.addressComponents),\n      placeTypes: placeDetails.types,\n      viewport: placeDetails.viewport ? {\n        northeast: new GeoPoint(placeDetails.viewport.northeast.lat, placeDetails.viewport.northeast.lng),\n        southwest: new GeoPoint(placeDetails.viewport.southwest.lat, placeDetails.viewport.southwest.lng)\n      } : undefined,\n      name: placeDetails.name\n    }\n  }\n\n  /**\n   * Parse address components into structured format\n   */\n  private parseAddressComponents(components: any[]): LocationData['addressComponents'] {\n    const parsed: LocationData['addressComponents'] = {\n      country: 'South Africa',\n      administrativeAreaLevel1: ''\n    }\n\n    for (const component of components) {\n      const types = component.types\n\n      if (types.includes('street_number')) {\n        parsed.streetNumber = component.longName\n      } else if (types.includes('route')) {\n        parsed.route = component.longName\n      } else if (types.includes('locality')) {\n        parsed.locality = component.longName\n      } else if (types.includes('sublocality') || types.includes('sublocality_level_1')) {\n        parsed.sublocality = component.longName\n      } else if (types.includes('administrative_area_level_1')) {\n        parsed.administrativeAreaLevel1 = component.longName\n      } else if (types.includes('administrative_area_level_2')) {\n        parsed.administrativeAreaLevel2 = component.longName\n      } else if (types.includes('postal_code')) {\n        parsed.postalCode = component.longName\n      } else if (types.includes('country')) {\n        parsed.country = component.longName\n      }\n    }\n\n    return parsed\n  }\n\n  /**\n   * Map location data to South African province\n   */\n  static mapToSouthAfricanProvince(locationData: LocationData): SouthAfricanProvince | null {\n    const provinceName = locationData.addressComponents.administrativeAreaLevel1\n    \n    // Direct mapping\n    if (provinceName && PROVINCE_MAPPING[provinceName]) {\n      return PROVINCE_MAPPING[provinceName] as SouthAfricanProvince\n    }\n\n    // Fuzzy matching for common variations\n    const normalizedProvince = provinceName?.toLowerCase().trim()\n    \n    const provinceMap: Record<string, SouthAfricanProvince> = {\n      'eastern cape': 'Eastern Cape',\n      'free state': 'Free State',\n      'gauteng': 'Gauteng',\n      'kwazulu-natal': 'KwaZulu-Natal',\n      'kzn': 'KwaZulu-Natal',\n      'limpopo': 'Limpopo',\n      'mpumalanga': 'Mpumalanga',\n      'northern cape': 'Northern Cape',\n      'north west': 'North West',\n      'northwest': 'North West',\n      'western cape': 'Western Cape'\n    }\n\n    return normalizedProvince ? provinceMap[normalizedProvince] || null : null\n  }\n\n  /**\n   * Create searchable location string\n   */\n  static createSearchableLocation(locationData: LocationData): string {\n    return GeoUtils.createSearchableLocation(locationData.addressComponents)\n  }\n\n  /**\n   * Generate geohash for location\n   */\n  static generateGeoHash(coordinates: { lat: number; lng: number }): string {\n    return GeoUtils.generateSimpleGeoHash(coordinates.lat, coordinates.lng)\n  }\n\n  /**\n   * Clear all caches\n   */\n  clearCache(): void {\n    this.googlePlaces.clearCache()\n  }\n}\n\n// Export distance utilities\nexport { DistanceService, GeoUtils }\n\n// Export types\nexport type {\n  LocationSearchResult,\n  PlaceDetails,\n  LocationData,\n  GeocodeResult,\n  LocationError,\n  ServiceResponse,\n  BrowserLocationResult\n}\n\n// Export error types\nexport { LocationErrorType }\n\n// Create and export default instance\nlet defaultLocationService: LocationService | null = null\n\nexport function getLocationService(): LocationService {\n  if (!defaultLocationService) {\n    defaultLocationService = LocationService.getInstance()\n  }\n  return defaultLocationService\n}\n\n// Utility functions for common operations\nexport const locationUtils = {\n  /**\n   * Format coordinates for display\n   */\n  formatCoordinates(lat: number, lng: number): string {\n    return `${lat.toFixed(6)}, ${lng.toFixed(6)}`\n  },\n\n  /**\n   * Validate South African coordinates\n   */\n  isValidSouthAfricanLocation(lat: number, lng: number): boolean {\n    return DistanceService.isValidCoordinate({ lat, lng }) && \n           DistanceService.isWithinSouthAfrica({ lat, lng })\n  },\n\n  /**\n   * Get display name for location\n   */\n  getLocationDisplayName(locationData: LocationData): string {\n    const { addressComponents } = locationData\n    \n    if (addressComponents.locality && addressComponents.administrativeAreaLevel1) {\n      return `${addressComponents.locality}, ${addressComponents.administrativeAreaLevel1}`\n    } else if (addressComponents.administrativeAreaLevel2 && addressComponents.administrativeAreaLevel1) {\n      return `${addressComponents.administrativeAreaLevel2}, ${addressComponents.administrativeAreaLevel1}`\n    } else if (addressComponents.administrativeAreaLevel1) {\n      return addressComponents.administrativeAreaLevel1\n    } else {\n      return locationData.formattedAddress\n    }\n  },\n\n  /**\n   * Extract city/town from location data\n   */\n  getCityFromLocation(locationData: LocationData): string | null {\n    return locationData.addressComponents.locality || \n           locationData.addressComponents.sublocality || \n           locationData.addressComponents.administrativeAreaLevel2 || \n           null\n  }\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;AAED;AACA;AACA;AAaA;AAAA;;;;;AAMO,MAAM;IACH,aAAiC;IACzC,OAAe,SAAyB;IAExC,YAAY,MAAc,CAAE;QAC1B,IAAI,CAAC,YAAY,GAAG,IAAI,kJAAA,CAAA,sBAAmB,CAAC;IAC9C;IAEA;;GAEC,GACD,OAAO,cAA+B;QACpC,IAAI,CAAC,gBAAgB,QAAQ,EAAE;YAC7B,MAAM;YACN,uCAAa;;YAEb;YACA,gBAAgB,QAAQ,GAAG,IAAI,gBAAgB;QACjD;QACA,OAAO,gBAAgB,QAAQ;IACjC;IAEA;;GAEC,GACD,MAAM,oBACJ,KAAa,EACb,OAGC,EACiD;QAClD,OAAO,IAAI,CAAC,YAAY,CAAC,mBAAmB,CAAC,OAAO;YAClD,GAAG,OAAO;YACV,uBAAuB;gBAAE,SAAS,+HAAA,CAAA,0BAAuB,CAAC,mBAAmB;YAAC;QAChF;IACF;IAEA;;GAEC,GACD,MAAM,gBAAgB,OAAe,EAA0C;QAC7E,MAAM,WAAW,MAAM,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC;QAEzD,IAAI,CAAC,SAAS,OAAO,IAAI,CAAC,SAAS,IAAI,EAAE;YACvC,OAAO;QACT;QAEA,MAAM,eAAe,SAAS,IAAI;QAClC,MAAM,eAAe,IAAI,CAAC,qBAAqB,CAAC;QAEhD,OAAO;YAAE,SAAS;YAAM,MAAM;YAAc,QAAQ,SAAS,MAAM;QAAC;IACtE;IAEA;;GAEC,GACD,MAAM,sBAAsB,OAAe,EAA0C;QACnF,MAAM,WAAW,MAAM,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC;QAExD,IAAI,CAAC,SAAS,OAAO,IAAI,CAAC,SAAS,IAAI,EAAE;YACvC,OAAO;QACT;QAEA,MAAM,gBAAgB,SAAS,IAAI;QAEnC,0CAA0C;QAC1C,IAAI,cAAc,OAAO,EAAE;YACzB,OAAO,IAAI,CAAC,eAAe,CAAC,cAAc,OAAO;QACnD;QAEA,0CAA0C;QAC1C,MAAM,eAA6B;YACjC,SAAS,cAAc,OAAO,IAAI;YAClC,kBAAkB,cAAc,gBAAgB;YAChD,aAAa,IAAI,iKAAA,CAAA,WAAQ,CAAC,cAAc,WAAW,CAAC,GAAG,EAAE,cAAc,WAAW,CAAC,GAAG;YACtF,mBAAmB,IAAI,CAAC,sBAAsB,CAAC,cAAc,iBAAiB;YAC9E,YAAY,cAAc,KAAK;QACjC;QAEA,OAAO;YAAE,SAAS;YAAM,MAAM;YAAc,QAAQ,SAAS,MAAM;QAAC;IACtE;IAEA;;GAEC,GACD,MAAM,mBACJ,OAAgC,EACiB;QACjD,OAAO,IAAI,QAAQ,CAAC;YAClB,IAAI,CAAC,UAAU,WAAW,EAAE;gBAC1B,QAAQ;oBACN,SAAS;oBACT,OAAO;wBACL,MAAM,+HAAA,CAAA,oBAAiB,CAAC,iBAAiB;wBACzC,SAAS;oBACX;gBACF;gBACA;YACF;YAEA,MAAM,iBAAyC;gBAC7C,oBAAoB;gBACpB,SAAS,+HAAA,CAAA,0BAAuB,CAAC,mBAAmB;gBACpD,YAAY,+HAAA,CAAA,0BAAuB,CAAC,mBAAmB;gBACvD,GAAG,OAAO;YACZ;YAEA,UAAU,WAAW,CAAC,kBAAkB,CACtC,CAAC;gBACC,MAAM,SAAgC;oBACpC,aAAa;wBACX,KAAK,SAAS,MAAM,CAAC,QAAQ;wBAC7B,KAAK,SAAS,MAAM,CAAC,SAAS;oBAChC;oBACA,UAAU,SAAS,MAAM,CAAC,QAAQ;oBAClC,WAAW,SAAS,SAAS;gBAC/B;gBACA,QAAQ;oBAAE,SAAS;oBAAM,MAAM;gBAAO;YACxC,GACA,CAAC;gBACC,IAAI;gBACJ,IAAI;gBAEJ,OAAQ,MAAM,IAAI;oBAChB,KAAK,MAAM,iBAAiB;wBAC1B,YAAY,+HAAA,CAAA,oBAAiB,CAAC,iBAAiB;wBAC/C,UAAU;wBACV;oBACF,KAAK,MAAM,oBAAoB;wBAC7B,YAAY,+HAAA,CAAA,oBAAiB,CAAC,SAAS;wBACvC,UAAU;wBACV;oBACF,KAAK,MAAM,OAAO;wBAChB,YAAY,+HAAA,CAAA,oBAAiB,CAAC,aAAa;wBAC3C,UAAU;wBACV;oBACF;wBACE,YAAY,+HAAA,CAAA,oBAAiB,CAAC,aAAa;wBAC3C,UAAU;gBACd;gBAEA,QAAQ;oBACN,SAAS;oBACT,OAAO;wBAAE,MAAM;wBAAW;wBAAS,eAAe;oBAAM;gBAC1D;YACF,GACA;QAEJ;IACF;IAEA;;GAEC,GACD,AAAQ,sBAAsB,YAA0B,EAAgB;QACtE,OAAO;YACL,SAAS,aAAa,OAAO;YAC7B,kBAAkB,aAAa,gBAAgB;YAC/C,aAAa,IAAI,iKAAA,CAAA,WAAQ,CAAC,aAAa,WAAW,CAAC,GAAG,EAAE,aAAa,WAAW,CAAC,GAAG;YACpF,mBAAmB,IAAI,CAAC,sBAAsB,CAAC,aAAa,iBAAiB;YAC7E,YAAY,aAAa,KAAK;YAC9B,UAAU,aAAa,QAAQ,GAAG;gBAChC,WAAW,IAAI,iKAAA,CAAA,WAAQ,CAAC,aAAa,QAAQ,CAAC,SAAS,CAAC,GAAG,EAAE,aAAa,QAAQ,CAAC,SAAS,CAAC,GAAG;gBAChG,WAAW,IAAI,iKAAA,CAAA,WAAQ,CAAC,aAAa,QAAQ,CAAC,SAAS,CAAC,GAAG,EAAE,aAAa,QAAQ,CAAC,SAAS,CAAC,GAAG;YAClG,IAAI;YACJ,MAAM,aAAa,IAAI;QACzB;IACF;IAEA;;GAEC,GACD,AAAQ,uBAAuB,UAAiB,EAAqC;QACnF,MAAM,SAA4C;YAChD,SAAS;YACT,0BAA0B;QAC5B;QAEA,KAAK,MAAM,aAAa,WAAY;YAClC,MAAM,QAAQ,UAAU,KAAK;YAE7B,IAAI,MAAM,QAAQ,CAAC,kBAAkB;gBACnC,OAAO,YAAY,GAAG,UAAU,QAAQ;YAC1C,OAAO,IAAI,MAAM,QAAQ,CAAC,UAAU;gBAClC,OAAO,KAAK,GAAG,UAAU,QAAQ;YACnC,OAAO,IAAI,MAAM,QAAQ,CAAC,aAAa;gBACrC,OAAO,QAAQ,GAAG,UAAU,QAAQ;YACtC,OAAO,IAAI,MAAM,QAAQ,CAAC,kBAAkB,MAAM,QAAQ,CAAC,wBAAwB;gBACjF,OAAO,WAAW,GAAG,UAAU,QAAQ;YACzC,OAAO,IAAI,MAAM,QAAQ,CAAC,gCAAgC;gBACxD,OAAO,wBAAwB,GAAG,UAAU,QAAQ;YACtD,OAAO,IAAI,MAAM,QAAQ,CAAC,gCAAgC;gBACxD,OAAO,wBAAwB,GAAG,UAAU,QAAQ;YACtD,OAAO,IAAI,MAAM,QAAQ,CAAC,gBAAgB;gBACxC,OAAO,UAAU,GAAG,UAAU,QAAQ;YACxC,OAAO,IAAI,MAAM,QAAQ,CAAC,YAAY;gBACpC,OAAO,OAAO,GAAG,UAAU,QAAQ;YACrC;QACF;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,OAAO,0BAA0B,YAA0B,EAA+B;QACxF,MAAM,eAAe,aAAa,iBAAiB,CAAC,wBAAwB;QAE5E,iBAAiB;QACjB,IAAI,gBAAgB,+HAAA,CAAA,mBAAgB,CAAC,aAAa,EAAE;YAClD,OAAO,+HAAA,CAAA,mBAAgB,CAAC,aAAa;QACvC;QAEA,uCAAuC;QACvC,MAAM,qBAAqB,cAAc,cAAc;QAEvD,MAAM,cAAoD;YACxD,gBAAgB;YAChB,cAAc;YACd,WAAW;YACX,iBAAiB;YACjB,OAAO;YACP,WAAW;YACX,cAAc;YACd,iBAAiB;YACjB,cAAc;YACd,aAAa;YACb,gBAAgB;QAClB;QAEA,OAAO,qBAAqB,WAAW,CAAC,mBAAmB,IAAI,OAAO;IACxE;IAEA;;GAEC,GACD,OAAO,yBAAyB,YAA0B,EAAU;QAClE,OAAO,8IAAA,CAAA,WAAQ,CAAC,wBAAwB,CAAC,aAAa,iBAAiB;IACzE;IAEA;;GAEC,GACD,OAAO,gBAAgB,WAAyC,EAAU;QACxE,OAAO,8IAAA,CAAA,WAAQ,CAAC,qBAAqB,CAAC,YAAY,GAAG,EAAE,YAAY,GAAG;IACxE;IAEA;;GAEC,GACD,aAAmB;QACjB,IAAI,CAAC,YAAY,CAAC,UAAU;IAC9B;AACF;;;AAmBA,qCAAqC;AACrC,IAAI,yBAAiD;AAE9C,SAAS;IACd,IAAI,CAAC,wBAAwB;QAC3B,yBAAyB,gBAAgB,WAAW;IACtD;IACA,OAAO;AACT;AAGO,MAAM,gBAAgB;IAC3B;;GAEC,GACD,mBAAkB,GAAW,EAAE,GAAW;QACxC,OAAO,GAAG,IAAI,OAAO,CAAC,GAAG,EAAE,EAAE,IAAI,OAAO,CAAC,IAAI;IAC/C;IAEA;;GAEC,GACD,6BAA4B,GAAW,EAAE,GAAW;QAClD,OAAO,8IAAA,CAAA,kBAAe,CAAC,iBAAiB,CAAC;YAAE;YAAK;QAAI,MAC7C,8IAAA,CAAA,kBAAe,CAAC,mBAAmB,CAAC;YAAE;YAAK;QAAI;IACxD;IAEA;;GAEC,GACD,wBAAuB,YAA0B;QAC/C,MAAM,EAAE,iBAAiB,EAAE,GAAG;QAE9B,IAAI,kBAAkB,QAAQ,IAAI,kBAAkB,wBAAwB,EAAE;YAC5E,OAAO,GAAG,kBAAkB,QAAQ,CAAC,EAAE,EAAE,kBAAkB,wBAAwB,EAAE;QACvF,OAAO,IAAI,kBAAkB,wBAAwB,IAAI,kBAAkB,wBAAwB,EAAE;YACnG,OAAO,GAAG,kBAAkB,wBAAwB,CAAC,EAAE,EAAE,kBAAkB,wBAAwB,EAAE;QACvG,OAAO,IAAI,kBAAkB,wBAAwB,EAAE;YACrD,OAAO,kBAAkB,wBAAwB;QACnD,OAAO;YACL,OAAO,aAAa,gBAAgB;QACtC;IACF;IAEA;;GAEC,GACD,qBAAoB,YAA0B;QAC5C,OAAO,aAAa,iBAAiB,CAAC,QAAQ,IACvC,aAAa,iBAAiB,CAAC,WAAW,IAC1C,aAAa,iBAAiB,CAAC,wBAAwB,IACvD;IACT;AACF", "debugId": null}}, {"offset": {"line": 1303, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/src/personal/bvr-safaris/src/components/ui/LocationAutocomplete.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState, useEffect, useRef, useCallback } from 'react'\nimport { cn } from '@/lib/utils'\nimport { \n  LocationSearchResult, \n  PlaceDetails, \n  LocationAutocompleteProps,\n  DEFAULT_LOCATION_CONFIG \n} from '@/lib/types/location'\nimport { getLocationService } from '@/lib/services/location'\nimport { LoadingSpinner } from './LoadingSpinner'\n\n/**\n * LocationAutocomplete component for Google Places integration\n * Provides address autocomplete with South African location focus\n */\nexport const LocationAutocomplete: React.FC<LocationAutocompleteProps> = ({\n  value = '',\n  onLocationSelect,\n  onInputChange,\n  placeholder = 'Enter location...',\n  countryRestriction = DEFAULT_LOCATION_CONFIG.COUNTRY_RESTRICTION,\n  types = [],\n  className,\n  disabled = false,\n  required = false,\n  error\n}) => {\n  const [inputValue, setInputValue] = useState(value)\n  const [suggestions, setSuggestions] = useState<LocationSearchResult[]>([])\n  const [isLoading, setIsLoading] = useState(false)\n  const [isOpen, setIsOpen] = useState(false)\n  const [selectedIndex, setSelectedIndex] = useState(-1)\n  const [sessionToken] = useState(() => crypto.randomUUID())\n\n  const inputRef = useRef<HTMLInputElement>(null)\n  const dropdownRef = useRef<HTMLDivElement>(null)\n  const debounceRef = useRef<NodeJS.Timeout>()\n\n  const locationService = getLocationService()\n\n  // Debounced search function\n  const debouncedSearch = useCallback(async (query: string) => {\n    if (query.length < 2) {\n      setSuggestions([])\n      setIsOpen(false)\n      return\n    }\n\n    setIsLoading(true)\n    \n    try {\n      const response = await locationService.getPlacePredictions(query, {\n        types: types.length > 0 ? types : undefined,\n        sessionToken\n      })\n\n      if (response.success && response.data) {\n        setSuggestions(response.data.slice(0, DEFAULT_LOCATION_CONFIG.MAX_AUTOCOMPLETE_RESULTS))\n        setIsOpen(true)\n      } else {\n        setSuggestions([])\n        setIsOpen(false)\n      }\n    } catch (error) {\n      console.error('Location search error:', error)\n      setSuggestions([])\n      setIsOpen(false)\n    } finally {\n      setIsLoading(false)\n    }\n  }, [locationService, types, sessionToken])\n\n  // Handle input change\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const newValue = e.target.value\n    setInputValue(newValue)\n    setSelectedIndex(-1)\n    \n    onInputChange?.(newValue)\n\n    // Clear existing debounce\n    if (debounceRef.current) {\n      clearTimeout(debounceRef.current)\n    }\n\n    // Set new debounce\n    debounceRef.current = setTimeout(() => {\n      debouncedSearch(newValue)\n    }, DEFAULT_LOCATION_CONFIG.DEBOUNCE_DELAY)\n  }\n\n  // Handle suggestion selection\n  const handleSuggestionSelect = async (suggestion: LocationSearchResult) => {\n    setInputValue(suggestion.description)\n    setIsOpen(false)\n    setSuggestions([])\n    setIsLoading(true)\n\n    try {\n      const response = await locationService.getLocationData(suggestion.placeId)\n      \n      if (response.success && response.data) {\n        onLocationSelect(response.data)\n      }\n    } catch (error) {\n      console.error('Error getting place details:', error)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  // Handle keyboard navigation\n  const handleKeyDown = (e: React.KeyboardEvent) => {\n    if (!isOpen || suggestions.length === 0) return\n\n    switch (e.key) {\n      case 'ArrowDown':\n        e.preventDefault()\n        setSelectedIndex(prev => \n          prev < suggestions.length - 1 ? prev + 1 : prev\n        )\n        break\n      case 'ArrowUp':\n        e.preventDefault()\n        setSelectedIndex(prev => prev > 0 ? prev - 1 : prev)\n        break\n      case 'Enter':\n        e.preventDefault()\n        if (selectedIndex >= 0 && selectedIndex < suggestions.length) {\n          handleSuggestionSelect(suggestions[selectedIndex])\n        }\n        break\n      case 'Escape':\n        setIsOpen(false)\n        setSelectedIndex(-1)\n        inputRef.current?.blur()\n        break\n    }\n  }\n\n  // Handle click outside\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (\n        dropdownRef.current &&\n        !dropdownRef.current.contains(event.target as Node) &&\n        !inputRef.current?.contains(event.target as Node)\n      ) {\n        setIsOpen(false)\n        setSelectedIndex(-1)\n      }\n    }\n\n    document.addEventListener('mousedown', handleClickOutside)\n    return () => document.removeEventListener('mousedown', handleClickOutside)\n  }, [])\n\n  // Update input value when prop changes\n  useEffect(() => {\n    setInputValue(value)\n  }, [value])\n\n  // Cleanup debounce on unmount\n  useEffect(() => {\n    return () => {\n      if (debounceRef.current) {\n        clearTimeout(debounceRef.current)\n      }\n    }\n  }, [])\n\n  return (\n    <div className={cn('relative w-full', className)}>\n      <div className=\"relative\">\n        <input\n          ref={inputRef}\n          type=\"text\"\n          value={inputValue}\n          onChange={handleInputChange}\n          onKeyDown={handleKeyDown}\n          onFocus={() => {\n            if (suggestions.length > 0) {\n              setIsOpen(true)\n            }\n          }}\n          placeholder={placeholder}\n          disabled={disabled}\n          required={required}\n          className={cn(\n            `\n            w-full px-4 py-2 pr-10 border-2 border-[var(--medium-gray)] \n            rounded-[var(--radius-md)] font-[var(--font-ui)] \n            transition-colors duration-300\n            focus:outline-none focus:border-[var(--primary-brown)] \n            focus:ring-0 focus:shadow-[0_0_0_3px_rgba(139,69,19,0.1)]\n            placeholder:text-[var(--medium-gray)]\n            disabled:bg-gray-100 disabled:cursor-not-allowed\n            `,\n            error && 'border-red-500 focus:border-red-500'\n          )}\n        />\n        \n        {/* Loading spinner */}\n        {isLoading && (\n          <div className=\"absolute right-3 top-1/2 transform -translate-y-1/2\">\n            <LoadingSpinner size=\"sm\" />\n          </div>\n        )}\n\n        {/* Location icon */}\n        {!isLoading && (\n          <div className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-[var(--medium-gray)]\">\n            <svg\n              width=\"16\"\n              height=\"16\"\n              viewBox=\"0 0 24 24\"\n              fill=\"none\"\n              stroke=\"currentColor\"\n              strokeWidth=\"2\"\n              strokeLinecap=\"round\"\n              strokeLinejoin=\"round\"\n            >\n              <path d=\"M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z\" />\n              <circle cx=\"12\" cy=\"10\" r=\"3\" />\n            </svg>\n          </div>\n        )}\n      </div>\n\n      {/* Dropdown */}\n      {isOpen && suggestions.length > 0 && (\n        <div\n          ref={dropdownRef}\n          className=\"absolute z-50 w-full mt-1 bg-white border border-[var(--medium-gray)] rounded-[var(--radius-md)] shadow-lg max-h-60 overflow-y-auto\"\n        >\n          {suggestions.map((suggestion, index) => (\n            <div\n              key={suggestion.placeId}\n              onClick={() => handleSuggestionSelect(suggestion)}\n              className={cn(\n                'px-4 py-3 cursor-pointer border-b border-gray-100 last:border-b-0 transition-colors',\n                'hover:bg-[var(--light-brown)] hover:bg-opacity-10',\n                selectedIndex === index && 'bg-[var(--light-brown)] bg-opacity-20'\n              )}\n            >\n              <div className=\"flex items-start space-x-3\">\n                <div className=\"flex-shrink-0 mt-1 text-[var(--medium-gray)]\">\n                  <svg\n                    width=\"14\"\n                    height=\"14\"\n                    viewBox=\"0 0 24 24\"\n                    fill=\"none\"\n                    stroke=\"currentColor\"\n                    strokeWidth=\"2\"\n                    strokeLinecap=\"round\"\n                    strokeLinejoin=\"round\"\n                  >\n                    <path d=\"M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z\" />\n                    <circle cx=\"12\" cy=\"10\" r=\"3\" />\n                  </svg>\n                </div>\n                <div className=\"flex-1 min-w-0\">\n                  <div className=\"font-medium text-[var(--primary-brown)] truncate\">\n                    {suggestion.mainText}\n                  </div>\n                  {suggestion.secondaryText && (\n                    <div className=\"text-sm text-[var(--medium-gray)] truncate\">\n                      {suggestion.secondaryText}\n                    </div>\n                  )}\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n      )}\n\n      {/* Error message */}\n      {error && (\n        <p className=\"mt-1 text-sm text-red-600\">{error}</p>\n      )}\n    </div>\n  )\n}\n\n/**\n * Simple location display component\n */\nexport const LocationDisplay: React.FC<{\n  locationData: any\n  showFullAddress?: boolean\n  className?: string\n}> = ({ locationData, showFullAddress = false, className }) => {\n  if (!locationData) return null\n\n  const displayText = showFullAddress\n    ? locationData.formattedAddress\n    : `${locationData.addressComponents?.locality || ''}, ${locationData.addressComponents?.administrativeAreaLevel1 || ''}`.replace(/^,\\s*/, '')\n\n  return (\n    <div className={cn('flex items-center space-x-2 text-[var(--medium-gray)]', className)}>\n      <svg\n        width=\"14\"\n        height=\"14\"\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke=\"currentColor\"\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n      >\n        <path d=\"M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z\" />\n        <circle cx=\"12\" cy=\"10\" r=\"3\" />\n      </svg>\n      <span className=\"text-sm\">{displayText}</span>\n    </div>\n  )\n}\n\n/**\n * Distance filter component for location-based searches\n */\nexport const DistanceFilter: React.FC<{\n  value?: number\n  onChange: (radius: number) => void\n  options?: number[]\n  className?: string\n  label?: string\n}> = ({\n  value,\n  onChange,\n  options = DEFAULT_LOCATION_CONFIG.DEFAULT_RADIUS_OPTIONS,\n  className,\n  label = 'Distance'\n}) => {\n  return (\n    <div className={cn('w-full', className)}>\n      {label && (\n        <label className=\"block mb-2 font-semibold text-[var(--primary-brown)] font-[var(--font-ui)]\">\n          {label}\n        </label>\n      )}\n      <select\n        value={value || ''}\n        onChange={(e) => onChange(Number(e.target.value))}\n        className={cn(\n          `\n          w-full px-4 py-2 border-2 border-[var(--medium-gray)]\n          rounded-[var(--radius-md)] font-[var(--font-ui)]\n          transition-colors duration-300\n          focus:outline-none focus:border-[var(--primary-brown)]\n          focus:ring-0 focus:shadow-[0_0_0_3px_rgba(139,69,19,0.1)]\n          bg-white\n          `\n        )}\n      >\n        <option value=\"\">Any distance</option>\n        {options.map(distance => (\n          <option key={distance} value={distance}>\n            Within {distance}km\n          </option>\n        ))}\n      </select>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AACA;AAMA;AAAA;AACA;AAXA;;;;;;;AAiBO,MAAM,uBAA4D,CAAC,EACxE,QAAQ,EAAE,EACV,gBAAgB,EAChB,aAAa,EACb,cAAc,mBAAmB,EACjC,qBAAqB,+HAAA,CAAA,0BAAuB,CAAC,mBAAmB,EAChE,QAAQ,EAAE,EACV,SAAS,EACT,WAAW,KAAK,EAChB,WAAW,KAAK,EAChB,KAAK,EACN;IACC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B,EAAE;IACzE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IACpD,MAAM,CAAC,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,IAAM,OAAO,UAAU;IAEvD,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAC1C,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC3C,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD;IAEzB,MAAM,kBAAkB,CAAA,GAAA,2JAAA,CAAA,qBAAkB,AAAD;IAEzC,4BAA4B;IAC5B,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACzC,IAAI,MAAM,MAAM,GAAG,GAAG;YACpB,eAAe,EAAE;YACjB,UAAU;YACV;QACF;QAEA,aAAa;QAEb,IAAI;YACF,MAAM,WAAW,MAAM,gBAAgB,mBAAmB,CAAC,OAAO;gBAChE,OAAO,MAAM,MAAM,GAAG,IAAI,QAAQ;gBAClC;YACF;YAEA,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;gBACrC,eAAe,SAAS,IAAI,CAAC,KAAK,CAAC,GAAG,+HAAA,CAAA,0BAAuB,CAAC,wBAAwB;gBACtF,UAAU;YACZ,OAAO;gBACL,eAAe,EAAE;gBACjB,UAAU;YACZ;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,eAAe,EAAE;YACjB,UAAU;QACZ,SAAU;YACR,aAAa;QACf;IACF,GAAG;QAAC;QAAiB;QAAO;KAAa;IAEzC,sBAAsB;IACtB,MAAM,oBAAoB,CAAC;QACzB,MAAM,WAAW,EAAE,MAAM,CAAC,KAAK;QAC/B,cAAc;QACd,iBAAiB,CAAC;QAElB,gBAAgB;QAEhB,0BAA0B;QAC1B,IAAI,YAAY,OAAO,EAAE;YACvB,aAAa,YAAY,OAAO;QAClC;QAEA,mBAAmB;QACnB,YAAY,OAAO,GAAG,WAAW;YAC/B,gBAAgB;QAClB,GAAG,+HAAA,CAAA,0BAAuB,CAAC,cAAc;IAC3C;IAEA,8BAA8B;IAC9B,MAAM,yBAAyB,OAAO;QACpC,cAAc,WAAW,WAAW;QACpC,UAAU;QACV,eAAe,EAAE;QACjB,aAAa;QAEb,IAAI;YACF,MAAM,WAAW,MAAM,gBAAgB,eAAe,CAAC,WAAW,OAAO;YAEzE,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;gBACrC,iBAAiB,SAAS,IAAI;YAChC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD,SAAU;YACR,aAAa;QACf;IACF;IAEA,6BAA6B;IAC7B,MAAM,gBAAgB,CAAC;QACrB,IAAI,CAAC,UAAU,YAAY,MAAM,KAAK,GAAG;QAEzC,OAAQ,EAAE,GAAG;YACX,KAAK;gBACH,EAAE,cAAc;gBAChB,iBAAiB,CAAA,OACf,OAAO,YAAY,MAAM,GAAG,IAAI,OAAO,IAAI;gBAE7C;YACF,KAAK;gBACH,EAAE,cAAc;gBAChB,iBAAiB,CAAA,OAAQ,OAAO,IAAI,OAAO,IAAI;gBAC/C;YACF,KAAK;gBACH,EAAE,cAAc;gBAChB,IAAI,iBAAiB,KAAK,gBAAgB,YAAY,MAAM,EAAE;oBAC5D,uBAAuB,WAAW,CAAC,cAAc;gBACnD;gBACA;YACF,KAAK;gBACH,UAAU;gBACV,iBAAiB,CAAC;gBAClB,SAAS,OAAO,EAAE;gBAClB;QACJ;IACF;IAEA,uBAAuB;IACvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB,CAAC;YAC1B,IACE,YAAY,OAAO,IACnB,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,KAC1C,CAAC,SAAS,OAAO,EAAE,SAAS,MAAM,MAAM,GACxC;gBACA,UAAU;gBACV,iBAAiB,CAAC;YACpB;QACF;QAEA,SAAS,gBAAgB,CAAC,aAAa;QACvC,OAAO,IAAM,SAAS,mBAAmB,CAAC,aAAa;IACzD,GAAG,EAAE;IAEL,uCAAuC;IACvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,cAAc;IAChB,GAAG;QAAC;KAAM;IAEV,8BAA8B;IAC9B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,OAAO;YACL,IAAI,YAAY,OAAO,EAAE;gBACvB,aAAa,YAAY,OAAO;YAClC;QACF;IACF,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;;0BACpC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,KAAK;wBACL,MAAK;wBACL,OAAO;wBACP,UAAU;wBACV,WAAW;wBACX,SAAS;4BACP,IAAI,YAAY,MAAM,GAAG,GAAG;gCAC1B,UAAU;4BACZ;wBACF;wBACA,aAAa;wBACb,UAAU;wBACV,UAAU;wBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,CAAC;;;;;;;;YAQD,CAAC,EACD,SAAS;;;;;;oBAKZ,2BACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,0IAAA,CAAA,iBAAc;4BAAC,MAAK;;;;;;;;;;;oBAKxB,CAAC,2BACA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,OAAM;4BACN,QAAO;4BACP,SAAQ;4BACR,MAAK;4BACL,QAAO;4BACP,aAAY;4BACZ,eAAc;4BACd,gBAAe;;8CAEf,8OAAC;oCAAK,GAAE;;;;;;8CACR,8OAAC;oCAAO,IAAG;oCAAK,IAAG;oCAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;YAOjC,UAAU,YAAY,MAAM,GAAG,mBAC9B,8OAAC;gBACC,KAAK;gBACL,WAAU;0BAET,YAAY,GAAG,CAAC,CAAC,YAAY,sBAC5B,8OAAC;wBAEC,SAAS,IAAM,uBAAuB;wBACtC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uFACA,qDACA,kBAAkB,SAAS;kCAG7B,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,OAAM;wCACN,QAAO;wCACP,SAAQ;wCACR,MAAK;wCACL,QAAO;wCACP,aAAY;wCACZ,eAAc;wCACd,gBAAe;;0DAEf,8OAAC;gDAAK,GAAE;;;;;;0DACR,8OAAC;gDAAO,IAAG;gDAAK,IAAG;gDAAK,GAAE;;;;;;;;;;;;;;;;;8CAG9B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACZ,WAAW,QAAQ;;;;;;wCAErB,WAAW,aAAa,kBACvB,8OAAC;4CAAI,WAAU;sDACZ,WAAW,aAAa;;;;;;;;;;;;;;;;;;uBA9B5B,WAAW,OAAO;;;;;;;;;;YAyC9B,uBACC,8OAAC;gBAAE,WAAU;0BAA6B;;;;;;;;;;;;AAIlD;AAKO,MAAM,kBAIR,CAAC,EAAE,YAAY,EAAE,kBAAkB,KAAK,EAAE,SAAS,EAAE;IACxD,IAAI,CAAC,cAAc,OAAO;IAE1B,MAAM,cAAc,kBAChB,aAAa,gBAAgB,GAC7B,GAAG,aAAa,iBAAiB,EAAE,YAAY,GAAG,EAAE,EAAE,aAAa,iBAAiB,EAAE,4BAA4B,IAAI,CAAC,OAAO,CAAC,SAAS;IAE5I,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,yDAAyD;;0BAC1E,8OAAC;gBACC,OAAM;gBACN,QAAO;gBACP,SAAQ;gBACR,MAAK;gBACL,QAAO;gBACP,aAAY;gBACZ,eAAc;gBACd,gBAAe;;kCAEf,8OAAC;wBAAK,GAAE;;;;;;kCACR,8OAAC;wBAAO,IAAG;wBAAK,IAAG;wBAAK,GAAE;;;;;;;;;;;;0BAE5B,8OAAC;gBAAK,WAAU;0BAAW;;;;;;;;;;;;AAGjC;AAKO,MAAM,iBAMR,CAAC,EACJ,KAAK,EACL,QAAQ,EACR,UAAU,+HAAA,CAAA,0BAAuB,CAAC,sBAAsB,EACxD,SAAS,EACT,QAAQ,UAAU,EACnB;IACC,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,UAAU;;YAC1B,uBACC,8OAAC;gBAAM,WAAU;0BACd;;;;;;0BAGL,8OAAC;gBACC,OAAO,SAAS;gBAChB,UAAU,CAAC,IAAM,SAAS,OAAO,EAAE,MAAM,CAAC,KAAK;gBAC/C,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,CAAC;;;;;;;UAOD,CAAC;;kCAGH,8OAAC;wBAAO,OAAM;kCAAG;;;;;;oBAChB,QAAQ,GAAG,CAAC,CAAA,yBACX,8OAAC;4BAAsB,OAAO;;gCAAU;gCAC9B;gCAAS;;2BADN;;;;;;;;;;;;;;;;;AAOvB", "debugId": null}}, {"offset": {"line": 1760, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/src/personal/bvr-safaris/src/components/features/SearchBar.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Input } from '@/components/ui/Input'\nimport { Button } from '@/components/ui/Button'\nimport { LocationAutocomplete, DistanceFilter } from '@/components/ui/LocationAutocomplete'\nimport { LocationData } from '@/lib/types/location'\n\nexport interface SearchFilters {\n  query: string\n  locationData: LocationData | null\n  radius?: number\n}\n\nexport interface SearchBarProps {\n  onSearch?: (filters: SearchFilters) => void\n  placeholder?: string\n  className?: string\n  showDistanceFilter?: boolean\n}\n\nexport function SearchBar({\n  onSearch,\n  placeholder = \"Search farms, activities, or species...\",\n  className,\n  showDistanceFilter = true\n}: SearchBarProps) {\n  const [query, setQuery] = useState('')\n  const [locationData, setLocationData] = useState<LocationData | null>(null)\n  const [radius, setRadius] = useState<number | undefined>(undefined)\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault()\n    onSearch?.({\n      query,\n      locationData,\n      radius\n    })\n  }\n\n  const handleLocationSelect = (location: LocationData) => {\n    setLocationData(location)\n  }\n\n  return (\n    <form onSubmit={handleSubmit} className={className}>\n      <div className=\"flex flex-col gap-4 p-6 bg-white rounded-lg shadow-lg\">\n        {/* Main search row */}\n        <div className=\"flex flex-col md:flex-row gap-4\">\n          <div className=\"flex-1\">\n            <Input\n              type=\"text\"\n              placeholder={placeholder}\n              value={query}\n              onChange={(e) => setQuery(e.target.value)}\n              className=\"border-earth-300 focus:border-accent-600\"\n            />\n          </div>\n\n          <div className=\"flex-1 md:max-w-sm\">\n            <LocationAutocomplete\n              value={locationData?.formattedAddress || ''}\n              onLocationSelect={handleLocationSelect}\n              placeholder=\"Search location...\"\n              className=\"border-earth-300 focus:border-accent-600\"\n            />\n          </div>\n\n          <Button\n            type=\"submit\"\n            variant=\"primary\"\n            size=\"lg\"\n            className=\"md:px-8 whitespace-nowrap\"\n          >\n            🔍 Search\n          </Button>\n        </div>\n\n        {/* Distance filter row */}\n        {showDistanceFilter && locationData && (\n          <div className=\"flex flex-col md:flex-row gap-4 pt-2 border-t border-earth-200\">\n            <div className=\"md:max-w-xs\">\n              <DistanceFilter\n                value={radius}\n                onChange={setRadius}\n                label=\"Search radius\"\n                className=\"w-full\"\n              />\n            </div>\n\n            <div className=\"flex-1 flex items-end\">\n              <p className=\"text-sm text-earth-600\">\n                {locationData.addressComponents.locality && (\n                  <>Searching near <strong>{locationData.addressComponents.locality}</strong></>\n                )}\n                {radius && <> within <strong>{radius}km</strong></>}\n              </p>\n            </div>\n          </div>\n        )}\n      </div>\n    </form>\n  )\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAqBO,SAAS,UAAU,EACxB,QAAQ,EACR,cAAc,yCAAyC,EACvD,SAAS,EACT,qBAAqB,IAAI,EACV;IACf,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;IACtE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;IAEzD,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,WAAW;YACT;YACA;YACA;QACF;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,gBAAgB;IAClB;IAEA,qBACE,8OAAC;QAAK,UAAU;QAAc,WAAW;kBACvC,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;gCACJ,MAAK;gCACL,aAAa;gCACb,OAAO;gCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;gCACxC,WAAU;;;;;;;;;;;sCAId,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,gJAAA,CAAA,uBAAoB;gCACnB,OAAO,cAAc,oBAAoB;gCACzC,kBAAkB;gCAClB,aAAY;gCACZ,WAAU;;;;;;;;;;;sCAId,8OAAC,kIAAA,CAAA,SAAM;4BACL,MAAK;4BACL,SAAQ;4BACR,MAAK;4BACL,WAAU;sCACX;;;;;;;;;;;;gBAMF,sBAAsB,8BACrB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,gJAAA,CAAA,iBAAc;gCACb,OAAO;gCACP,UAAU;gCACV,OAAM;gCACN,WAAU;;;;;;;;;;;sCAId,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;;oCACV,aAAa,iBAAiB,CAAC,QAAQ,kBACtC;;4CAAE;0DAAe,8OAAC;0DAAQ,aAAa,iBAAiB,CAAC,QAAQ;;;;;;;;oCAElE,wBAAU;;4CAAE;0DAAQ,8OAAC;;oDAAQ;oDAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQrD", "debugId": null}}, {"offset": {"line": 1937, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/src/personal/bvr-safaris/src/components/ui/Card.tsx"], "sourcesContent": ["import { HTMLAttributes, forwardRef } from 'react'\nimport Image from 'next/image'\nimport { cn } from '@/lib/utils'\n\nexport interface CardProps extends HTMLAttributes<HTMLDivElement> {\n  hover?: boolean\n}\n\nconst Card = forwardRef<HTMLDivElement, CardProps>(\n  ({ className, hover = true, children, ...props }, ref) => {\n    return (\n      <div\n        ref={ref}\n        className={cn(\n          `\n          bg-white rounded-[var(--radius-lg)] shadow-[var(--shadow-md)] \n          overflow-hidden transition-all duration-300\n          `,\n          hover && 'hover:transform hover:-translate-y-1 hover:shadow-[var(--shadow-xl)]',\n          className\n        )}\n        {...props}\n      >\n        {children}\n      </div>\n    )\n  }\n)\n\nCard.displayName = 'Card'\n\nconst CardContent = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn('p-[var(--space-lg)]', className)}\n      {...props}\n    />\n  )\n)\n\nCardContent.displayName = 'CardContent'\n\nconst CardHeader = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn('px-[var(--space-lg)] pt-[var(--space-lg)]', className)}\n      {...props}\n    />\n  )\n)\n\nCardHeader.displayName = 'CardHeader'\n\nconst CardTitle = forwardRef<HTMLHeadingElement, HTMLAttributes<HTMLHeadingElement>>(\n  ({ className, ...props }, ref) => (\n    <h3\n      ref={ref}\n      className={cn(\n        'text-xl font-bold mb-2 text-[var(--primary-brown)] font-[var(--font-display)]',\n        className\n      )}\n      {...props}\n    />\n  )\n)\n\nCardTitle.displayName = 'CardTitle'\n\nconst CardImage = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement> & {\n  src?: string\n  alt?: string\n  children?: React.ReactNode\n}>(\n  ({ className, src, alt, children, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(\n        'relative w-full h-48 bg-gradient-to-br from-[var(--primary-green)] to-[var(--secondary-sunset)] flex items-center justify-center text-white text-5xl',\n        className\n      )}\n      {...props}\n    >\n      {src ? (\n        <Image src={src} alt={alt || ''} fill className=\"object-cover\" />\n      ) : (\n        children\n      )}\n    </div>\n  )\n)\n\nCardImage.displayName = 'CardImage'\n\nexport { Card, CardContent, CardHeader, CardTitle, CardImage }"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;AACA;;;;;AAMA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACpB,CAAC,EAAE,SAAS,EAAE,QAAQ,IAAI,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IAChD,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,CAAC;;;UAGD,CAAC,EACD,SAAS,wEACT;QAED,GAAG,KAAK;kBAER;;;;;;AAGP;AAGF,KAAK,WAAW,GAAG;AAEnB,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAKf,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAC1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAKf,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACzB,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iFACA;QAED,GAAG,KAAK;;;;;;AAKf,UAAU,WAAW,GAAG;AAExB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAKzB,CAAC,EAAE,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAC5C,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wJACA;QAED,GAAG,KAAK;kBAER,oBACC,8OAAC,6HAAA,CAAA,UAAK;YAAC,KAAK;YAAK,KAAK,OAAO;YAAI,IAAI;YAAC,WAAU;;;;;mBAEhD;;;;;;AAMR,UAAU,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 2025, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/src/personal/bvr-safaris/src/components/ui/Badge.tsx"], "sourcesContent": ["import { HTMLAttributes, forwardRef } from 'react'\nimport { cn } from '@/lib/utils'\n\nexport interface BadgeProps extends HTMLAttributes<HTMLSpanElement> {\n  variant?: 'hunting' | 'photo' | 'location' | 'default'\n}\n\nconst Badge = forwardRef<HTMLSpanElement, BadgeProps>(\n  ({ className, variant = 'default', ...props }, ref) => {\n    const variants = {\n      hunting: 'bg-[var(--hunting-accent)] text-white',\n      photo: 'bg-[var(--photo-accent)] text-white',\n      location: 'bg-[var(--secondary-stone)] text-white',\n      default: 'bg-[var(--medium-gray)] text-white'\n    }\n\n    return (\n      <span\n        ref={ref}\n        className={cn(\n          'inline-flex items-center px-2 py-1 rounded-[var(--radius-sm)] text-sm font-semibold',\n          variants[variant],\n          className\n        )}\n        {...props}\n      />\n    )\n  }\n)\n\nBadge.displayName = 'Badge'\n\nexport { Badge }"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAMA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACrB,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,GAAG,OAAO,EAAE;IAC7C,MAAM,WAAW;QACf,SAAS;QACT,OAAO;QACP,UAAU;QACV,SAAS;IACX;IAEA,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uFACA,QAAQ,CAAC,QAAQ,EACjB;QAED,GAAG,KAAK;;;;;;AAGf;AAGF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 2059, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/src/personal/bvr-safaris/src/components/features/FarmCard.tsx"], "sourcesContent": ["import Link from 'next/link'\nimport { Card, CardContent, CardImage, CardTitle } from '@/components/ui/Card'\nimport { Badge } from '@/components/ui/Badge'\n\nexport interface FarmCardProps {\n  id: string\n  name: string\n  location: string\n  province: string\n  description: string\n  imageUrl?: string\n  activities: ('hunting' | 'photo_safari')[]\n  priceRange?: string\n  rating?: number\n  reviewCount?: number\n}\n\nexport function FarmCard({\n  id,\n  name,\n  location,\n  province,\n  description,\n  imageUrl,\n  activities,\n  priceRange,\n  rating,\n  reviewCount\n}: FarmCardProps) {\n  return (\n    <Link href={`/farms/${id}`}>\n      <Card className=\"h-full cursor-pointer\">\n        <CardImage src={imageUrl} alt={`${name} farm`} />\n        \n        <CardContent className=\"flex flex-col h-full\">\n          <div className=\"flex-1\">\n            <div className=\"flex items-start justify-between mb-3\">\n              <CardTitle className=\"text-lg leading-tight\">{name}</CardTitle>\n              {rating && (\n                <div className=\"flex items-center text-sm text-earth-600\">\n                  <span className=\"text-accent-600 mr-1\">★</span>\n                  <span>{rating}</span>\n                  {reviewCount && <span className=\"ml-1\">({reviewCount})</span>}\n                </div>\n              )}\n            </div>\n\n            <div className=\"text-earth-600 text-sm mb-3\">\n              {location}, {province}\n            </div>\n\n            <p className=\"text-earth-700 text-sm mb-4 line-clamp-3\">\n              {description}\n            </p>\n\n            <div className=\"flex flex-wrap gap-2 mb-4\">\n              {activities.map((activity) => (\n                <Badge \n                  key={activity} \n                  variant={activity === 'hunting' ? 'hunting' : 'photo'}\n                >\n                  {activity === 'hunting' ? 'Hunting' : 'Photo Safari'}\n                </Badge>\n              ))}\n            </div>\n          </div>\n\n          {priceRange && (\n            <div className=\"text-earth-900 font-semibold text-lg\">\n              {priceRange}\n            </div>\n          )}\n        </CardContent>\n      </Card>\n    </Link>\n  )\n}"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAeO,SAAS,SAAS,EACvB,EAAE,EACF,IAAI,EACJ,QAAQ,EACR,QAAQ,EACR,WAAW,EACX,QAAQ,EACR,UAAU,EACV,UAAU,EACV,MAAM,EACN,WAAW,EACG;IACd,qBACE,8OAAC,4JAAA,CAAA,UAAI;QAAC,MAAM,CAAC,OAAO,EAAE,IAAI;kBACxB,cAAA,8OAAC,gIAAA,CAAA,OAAI;YAAC,WAAU;;8BACd,8OAAC,gIAAA,CAAA,YAAS;oBAAC,KAAK;oBAAU,KAAK,GAAG,KAAK,KAAK,CAAC;;;;;;8BAE7C,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAyB;;;;;;wCAC7C,wBACC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAuB;;;;;;8DACvC,8OAAC;8DAAM;;;;;;gDACN,6BAAe,8OAAC;oDAAK,WAAU;;wDAAO;wDAAE;wDAAY;;;;;;;;;;;;;;;;;;;8CAK3D,8OAAC;oCAAI,WAAU;;wCACZ;wCAAS;wCAAG;;;;;;;8CAGf,8OAAC;oCAAE,WAAU;8CACV;;;;;;8CAGH,8OAAC;oCAAI,WAAU;8CACZ,WAAW,GAAG,CAAC,CAAC,yBACf,8OAAC,iIAAA,CAAA,QAAK;4CAEJ,SAAS,aAAa,YAAY,YAAY;sDAE7C,aAAa,YAAY,YAAY;2CAHjC;;;;;;;;;;;;;;;;wBASZ,4BACC,8OAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;;;;;;;;;;;;AAOf", "debugId": null}}, {"offset": {"line": 2216, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/src/personal/bvr-safaris/src/components/features/FeaturedFarmsCarousel.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { FarmCard } from './FarmCard'\nimport { ChevronLeft, ChevronRight } from 'lucide-react'\nimport { GameFarm } from '@/lib/types/firestore'\n\ninterface FarmWithStats extends GameFarm {\n  rating?: number\n  reviewCount?: number\n  activities: ('hunting' | 'photo_safari')[]\n  imageUrl?: string\n}\n\ninterface FeaturedFarmsCarouselProps {\n  farms: FarmWithStats[]\n  loading?: boolean\n  error?: string | null\n  onRetry?: () => void\n}\n\nexport function FeaturedFarmsCarousel({ \n  farms, \n  loading = false, \n  error = null, \n  onRetry \n}: FeaturedFarmsCarouselProps) {\n  const [currentIndex, setCurrentIndex] = useState(0)\n  const [isAutoPlaying, setIsAutoPlaying] = useState(true)\n\n  // Auto-play functionality\n  useEffect(() => {\n    if (!isAutoPlaying || farms.length <= 1) return\n\n    const interval = setInterval(() => {\n      setCurrentIndex((prevIndex) => \n        prevIndex === farms.length - 1 ? 0 : prevIndex + 1\n      )\n    }, 5000) // Change slide every 5 seconds\n\n    return () => clearInterval(interval)\n  }, [isAutoPlaying, farms.length])\n\n  const goToPrevious = () => {\n    setIsAutoPlaying(false)\n    setCurrentIndex(currentIndex === 0 ? farms.length - 1 : currentIndex - 1)\n    // Resume auto-play after 10 seconds\n    setTimeout(() => setIsAutoPlaying(true), 10000)\n  }\n\n  const goToNext = () => {\n    setIsAutoPlaying(false)\n    setCurrentIndex(currentIndex === farms.length - 1 ? 0 : currentIndex + 1)\n    // Resume auto-play after 10 seconds\n    setTimeout(() => setIsAutoPlaying(true), 10000)\n  }\n\n  const goToSlide = (index: number) => {\n    setIsAutoPlaying(false)\n    setCurrentIndex(index)\n    // Resume auto-play after 10 seconds\n    setTimeout(() => setIsAutoPlaying(true), 10000)\n  }\n\n  if (loading) {\n    return (\n      <div className=\"text-center py-16\">\n        <p className=\"text-cream\" style={{\n          fontFamily: 'var(--font-body)',\n          fontSize: 'var(--text-secondary-body)'\n        }}>Loading featured farms...</p>\n      </div>\n    )\n  }\n\n  if (error) {\n    return (\n      <div className=\"text-center py-16\">\n        <p className=\"text-red-400 mb-4\" style={{\n          fontFamily: 'var(--font-body)',\n          fontSize: 'var(--text-secondary-body)'\n        }}>{error}</p>\n        {onRetry && (\n          <button\n            onClick={onRetry}\n            className=\"mt-4 px-4 py-2 rounded-lg hover:opacity-80 transition-opacity\"\n            style={{\n              backgroundColor: 'var(--deep-brown)',\n              color: 'var(--cream)',\n              fontFamily: 'var(--font-body)',\n              fontSize: 'var(--text-secondary-body)',\n              borderRadius: '12px'\n            }}\n          >\n            Try Again\n          </button>\n        )}\n      </div>\n    )\n  }\n\n  if (farms.length === 0) {\n    return null\n  }\n\n  return (\n    <div className=\"relative\">\n      {/* Carousel Container */}\n      <div className=\"relative overflow-hidden rounded-lg\">\n        <div \n          className=\"flex transition-transform duration-500 ease-in-out\"\n          style={{ transform: `translateX(-${currentIndex * 100}%)` }}\n        >\n          {farms.map((farm) => (\n            <div key={farm.id} className=\"w-full flex-shrink-0 px-4\">\n              <div className=\"bg-white/10 rounded-lg p-4 backdrop-blur-sm\">\n                <FarmCard\n                  id={farm.id}\n                  name={farm.name}\n                  location={farm.location}\n                  province={farm.province}\n                  description={farm.description || ''}\n                  imageUrl={farm.imageUrl}\n                  activities={farm.activities}\n                  priceRange={farm.pricingInfo || 'Contact for pricing'}\n                  rating={farm.rating}\n                  reviewCount={farm.reviewCount}\n                />\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n\n      {/* Navigation Arrows */}\n      {farms.length > 1 && (\n        <>\n          <button\n            onClick={goToPrevious}\n            className=\"absolute left-4 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white p-2 rounded-full transition-all duration-200 z-10\"\n            aria-label=\"Previous farm\"\n          >\n            <ChevronLeft className=\"w-6 h-6\" />\n          </button>\n          \n          <button\n            onClick={goToNext}\n            className=\"absolute right-4 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white p-2 rounded-full transition-all duration-200 z-10\"\n            aria-label=\"Next farm\"\n          >\n            <ChevronRight className=\"w-6 h-6\" />\n          </button>\n        </>\n      )}\n\n      {/* Dots Indicator */}\n      {farms.length > 1 && (\n        <div className=\"flex justify-center mt-6 space-x-2\">\n          {farms.map((_, index) => (\n            <button\n              key={index}\n              onClick={() => goToSlide(index)}\n              className={`w-3 h-3 rounded-full transition-all duration-200 ${\n                index === currentIndex \n                  ? 'bg-cream' \n                  : 'bg-cream/30 hover:bg-cream/50'\n              }`}\n              aria-label={`Go to farm ${index + 1}`}\n            />\n          ))}\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAJA;;;;;AAqBO,SAAS,sBAAsB,EACpC,KAAK,EACL,UAAU,KAAK,EACf,QAAQ,IAAI,EACZ,OAAO,EACoB;IAC3B,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,0BAA0B;IAC1B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,iBAAiB,MAAM,MAAM,IAAI,GAAG;QAEzC,MAAM,WAAW,YAAY;YAC3B,gBAAgB,CAAC,YACf,cAAc,MAAM,MAAM,GAAG,IAAI,IAAI,YAAY;QAErD,GAAG,MAAM,+BAA+B;;QAExC,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC;QAAe,MAAM,MAAM;KAAC;IAEhC,MAAM,eAAe;QACnB,iBAAiB;QACjB,gBAAgB,iBAAiB,IAAI,MAAM,MAAM,GAAG,IAAI,eAAe;QACvE,oCAAoC;QACpC,WAAW,IAAM,iBAAiB,OAAO;IAC3C;IAEA,MAAM,WAAW;QACf,iBAAiB;QACjB,gBAAgB,iBAAiB,MAAM,MAAM,GAAG,IAAI,IAAI,eAAe;QACvE,oCAAoC;QACpC,WAAW,IAAM,iBAAiB,OAAO;IAC3C;IAEA,MAAM,YAAY,CAAC;QACjB,iBAAiB;QACjB,gBAAgB;QAChB,oCAAoC;QACpC,WAAW,IAAM,iBAAiB,OAAO;IAC3C;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAE,WAAU;gBAAa,OAAO;oBAC/B,YAAY;oBACZ,UAAU;gBACZ;0BAAG;;;;;;;;;;;IAGT;IAEA,IAAI,OAAO;QACT,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAE,WAAU;oBAAoB,OAAO;wBACtC,YAAY;wBACZ,UAAU;oBACZ;8BAAI;;;;;;gBACH,yBACC,8OAAC;oBACC,SAAS;oBACT,WAAU;oBACV,OAAO;wBACL,iBAAiB;wBACjB,OAAO;wBACP,YAAY;wBACZ,UAAU;wBACV,cAAc;oBAChB;8BACD;;;;;;;;;;;;IAMT;IAEA,IAAI,MAAM,MAAM,KAAK,GAAG;QACtB,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,WAAU;oBACV,OAAO;wBAAE,WAAW,CAAC,YAAY,EAAE,eAAe,IAAI,EAAE,CAAC;oBAAC;8BAEzD,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC;4BAAkB,WAAU;sCAC3B,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,0IAAA,CAAA,WAAQ;oCACP,IAAI,KAAK,EAAE;oCACX,MAAM,KAAK,IAAI;oCACf,UAAU,KAAK,QAAQ;oCACvB,UAAU,KAAK,QAAQ;oCACvB,aAAa,KAAK,WAAW,IAAI;oCACjC,UAAU,KAAK,QAAQ;oCACvB,YAAY,KAAK,UAAU;oCAC3B,YAAY,KAAK,WAAW,IAAI;oCAChC,QAAQ,KAAK,MAAM;oCACnB,aAAa,KAAK,WAAW;;;;;;;;;;;2BAZzB,KAAK,EAAE;;;;;;;;;;;;;;;YAqBtB,MAAM,MAAM,GAAG,mBACd;;kCACE,8OAAC;wBACC,SAAS;wBACT,WAAU;wBACV,cAAW;kCAEX,cAAA,8OAAC,oNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;kCAGzB,8OAAC;wBACC,SAAS;wBACT,WAAU;wBACV,cAAW;kCAEX,cAAA,8OAAC,sNAAA,CAAA,eAAY;4BAAC,WAAU;;;;;;;;;;;;;YAM7B,MAAM,MAAM,GAAG,mBACd,8OAAC;gBAAI,WAAU;0BACZ,MAAM,GAAG,CAAC,CAAC,GAAG,sBACb,8OAAC;wBAEC,SAAS,IAAM,UAAU;wBACzB,WAAW,CAAC,iDAAiD,EAC3D,UAAU,eACN,aACA,iCACJ;wBACF,cAAY,CAAC,WAAW,EAAE,QAAQ,GAAG;uBAPhC;;;;;;;;;;;;;;;;AAcnB", "debugId": null}}, {"offset": {"line": 2440, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/src/personal/bvr-safaris/src/lib/firebase/firestore.ts"], "sourcesContent": ["import {\n  collection,\n  doc,\n  getDoc,\n  getDocs,\n  addDoc,\n  setDoc,\n  updateDoc,\n  deleteDoc,\n  query,\n  where,\n  orderBy,\n  limit,\n  Timestamp,\n  DocumentSnapshot,\n  QueryDocumentSnapshot,\n  DocumentData,\n  Query\n} from 'firebase/firestore'\nimport { db } from './client'\nimport {\n  UserProfile,\n  GameFarm,\n  Booking,\n  Review,\n  FarmImage,\n  GameSpecies,\n  FarmAmenity,\n  FirestoreDocument\n} from '@/lib/types/firestore'\n\n// Helper function to convert Firestore document to typed object\nexport function docToData<T extends FirestoreDocument>(\n  doc: QueryDocumentSnapshot<DocumentData> | DocumentSnapshot<DocumentData>\n): T | null {\n  if (!doc.exists()) return null\n  \n  const data = doc.data()\n  return {\n    id: doc.id,\n    ...data,\n    // Convert Firestore Timestamps to Date objects for easier handling\n    createdAt: data?.createdAt?.toDate?.() || data?.createdAt,\n    updatedAt: data?.updatedAt?.toDate?.() || data?.updatedAt,\n  } as T\n}\n\n\n\n// User Profile operations\nexport const userProfileService = {\n  async get(userId: string): Promise<UserProfile | null> {\n    const docRef = doc(db, 'users', userId)\n    const docSnap = await getDoc(docRef)\n    return docToData<UserProfile>(docSnap)\n  },\n\n  async create(userId: string, data: Omit<UserProfile, 'id' | 'createdAt' | 'updatedAt'>): Promise<void> {\n    const docRef = doc(db, 'users', userId)\n    const now = new Date()\n    await setDoc(docRef, {\n      ...data,\n      createdAt: Timestamp.fromDate(now),\n      updatedAt: Timestamp.fromDate(now)\n    })\n  },\n\n  async update(userId: string, data: Partial<UserProfile>): Promise<void> {\n    const docRef = doc(db, 'users', userId)\n    await updateDoc(docRef, {\n      ...data,\n      updatedAt: Timestamp.now()\n    })\n  }\n}\n\n// Game Farm operations\nexport const farmService = {\n  async getAll(filters?: {\n    isActive?: boolean\n    featured?: boolean\n    province?: string\n    activityType?: string\n    limit?: number\n  }): Promise<GameFarm[]> {\n    let q: Query<DocumentData> = collection(db, 'farms')\n\n    if (filters?.isActive !== undefined) {\n      q = query(q, where('isActive', '==', filters.isActive))\n    }\n    if (filters?.featured !== undefined) {\n      q = query(q, where('featured', '==', filters.featured))\n    }\n    if (filters?.province) {\n      q = query(q, where('province', '==', filters.province))\n    }\n    if (filters?.activityType) {\n      q = query(q, where('activityTypes', '==', filters.activityType))\n    }\n\n    q = query(q, orderBy('createdAt', 'desc'))\n\n    if (filters?.limit) {\n      q = query(q, limit(filters.limit))\n    }\n\n    const querySnapshot = await getDocs(q)\n    return querySnapshot.docs.map(doc => docToData<GameFarm>(doc)!).filter(Boolean)\n  },\n\n  async get(farmId: string): Promise<GameFarm | null> {\n    const docRef = doc(db, 'farms', farmId)\n    const docSnap = await getDoc(docRef)\n    return docToData<GameFarm>(docSnap)\n  },\n\n  async getByOwner(ownerId: string): Promise<GameFarm[]> {\n    const q = query(\n      collection(db, 'farms'),\n      where('ownerId', '==', ownerId),\n      orderBy('createdAt', 'desc')\n    )\n    const querySnapshot = await getDocs(q)\n    return querySnapshot.docs.map(doc => docToData<GameFarm>(doc)!).filter(Boolean)\n  },\n\n  async getActive(limitCount?: number): Promise<GameFarm[]> {\n    let q = query(\n      collection(db, 'farms'),\n      where('isActive', '==', true),\n      orderBy('createdAt', 'desc')\n    )\n\n    if (limitCount) {\n      q = query(q, limit(limitCount))\n    }\n\n    const querySnapshot = await getDocs(q)\n    return querySnapshot.docs.map(doc => docToData<GameFarm>(doc)!).filter(Boolean)\n  },\n\n  async create(data: Omit<GameFarm, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {\n    const now = new Date()\n    const docRef = await addDoc(collection(db, 'farms'), {\n      ...data,\n      createdAt: Timestamp.fromDate(now),\n      updatedAt: Timestamp.fromDate(now)\n    })\n    return docRef.id\n  },\n\n  async update(farmId: string, data: Partial<GameFarm>): Promise<void> {\n    const docRef = doc(db, 'farms', farmId)\n    await updateDoc(docRef, {\n      ...data,\n      updatedAt: Timestamp.now()\n    })\n  },\n\n  async delete(farmId: string): Promise<void> {\n    const docRef = doc(db, 'farms', farmId)\n    await deleteDoc(docRef)\n  },\n\n  // Add farm images to subcollection\n  async addImage(farmId: string, imageData: Omit<FarmImage, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {\n    const now = new Date()\n    const docRef = await addDoc(collection(db, 'farms', farmId, 'images'), {\n      ...imageData,\n      createdAt: Timestamp.fromDate(now),\n      updatedAt: Timestamp.fromDate(now)\n    })\n    return docRef.id\n  },\n\n  // Get farm images\n  async getImages(farmId: string): Promise<FarmImage[]> {\n    const q = query(\n      collection(db, 'farms', farmId, 'images'),\n      orderBy('displayOrder', 'asc'),\n      orderBy('createdAt', 'asc')\n    )\n    const querySnapshot = await getDocs(q)\n    return querySnapshot.docs.map(doc => docToData<FarmImage>(doc)!).filter(Boolean)\n  },\n\n  // Delete farm image\n  async deleteImage(farmId: string, imageId: string): Promise<void> {\n    const docRef = doc(db, 'farms', farmId, 'images', imageId)\n    await deleteDoc(docRef)\n  }\n}\n\n// Booking operations\nexport const bookingService = {\n  async getAll(filters?: {\n    hunterId?: string\n    farmId?: string\n    status?: string\n    limit?: number\n  }): Promise<Booking[]> {\n    let q: Query<DocumentData> = collection(db, 'bookings')\n\n    if (filters?.hunterId) {\n      q = query(q, where('hunterId', '==', filters.hunterId))\n    }\n    if (filters?.farmId) {\n      q = query(q, where('farmId', '==', filters.farmId))\n    }\n    if (filters?.status) {\n      q = query(q, where('status', '==', filters.status))\n    }\n\n    q = query(q, orderBy('createdAt', 'desc'))\n\n    if (filters?.limit) {\n      q = query(q, limit(filters.limit))\n    }\n\n    const querySnapshot = await getDocs(q)\n    return querySnapshot.docs.map(doc => docToData<Booking>(doc)!).filter(Boolean)\n  },\n\n  async get(bookingId: string): Promise<Booking | null> {\n    const docRef = doc(db, 'bookings', bookingId)\n    const docSnap = await getDoc(docRef)\n    return docToData<Booking>(docSnap)\n  },\n\n  async create(data: Omit<Booking, 'id' | 'createdAt' | 'updatedAt' | 'bookingReference'>): Promise<string> {\n    const now = new Date()\n    const bookingReference = `BVR-${now.getFullYear()}${(now.getMonth() + 1).toString().padStart(2, '0')}${now.getDate().toString().padStart(2, '0')}-${Math.random().toString(36).substring(2, 8).toUpperCase()}`\n    \n    const docRef = await addDoc(collection(db, 'bookings'), {\n      ...data,\n      bookingReference,\n      createdAt: Timestamp.fromDate(now),\n      updatedAt: Timestamp.fromDate(now)\n    })\n    return docRef.id\n  },\n\n  async update(bookingId: string, data: Partial<Booking>): Promise<void> {\n    const docRef = doc(db, 'bookings', bookingId)\n    await updateDoc(docRef, {\n      ...data,\n      updatedAt: Timestamp.now()\n    })\n  }\n}\n\n// Review operations (subcollection under farms)\nexport const reviewService = {\n  async getByFarm(farmId: string): Promise<Review[]> {\n    const q = query(\n      collection(db, 'farms', farmId, 'reviews'),\n      where('isPublic', '==', true),\n      orderBy('createdAt', 'desc')\n    )\n    const querySnapshot = await getDocs(q)\n    return querySnapshot.docs.map(doc => docToData<Review>(doc)!).filter(Boolean)\n  },\n\n  async create(farmId: string, data: Omit<Review, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {\n    const now = new Date()\n    const docRef = await addDoc(collection(db, 'farms', farmId, 'reviews'), {\n      ...data,\n      createdAt: Timestamp.fromDate(now),\n      updatedAt: Timestamp.fromDate(now)\n    })\n    return docRef.id\n  },\n\n  async update(farmId: string, reviewId: string, data: Partial<Review>): Promise<void> {\n    const docRef = doc(db, 'farms', farmId, 'reviews', reviewId)\n    await updateDoc(docRef, {\n      ...data,\n      updatedAt: Timestamp.now()\n    })\n  }\n}\n\n// Species operations\nexport const speciesService = {\n  async getAll(): Promise<GameSpecies[]> {\n    const q = query(collection(db, 'species'), orderBy('name'))\n    const querySnapshot = await getDocs(q)\n    return querySnapshot.docs.map(doc => docToData<GameSpecies>(doc)!).filter(Boolean)\n  },\n\n  async get(speciesId: string): Promise<GameSpecies | null> {\n    const docRef = doc(db, 'species', speciesId)\n    const docSnap = await getDoc(docRef)\n    return docToData<GameSpecies>(docSnap)\n  }\n}\n\n// Amenities operations\nexport const amenityService = {\n  async getAll(): Promise<FarmAmenity[]> {\n    const q = query(collection(db, 'amenities'), orderBy('name'))\n    const querySnapshot = await getDocs(q)\n    return querySnapshot.docs.map(doc => docToData<FarmAmenity>(doc)!).filter(Boolean)\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAAA;AAmBA;AAAA;;;AAaO,SAAS,UACd,GAAyE;IAEzE,IAAI,CAAC,IAAI,MAAM,IAAI,OAAO;IAE1B,MAAM,OAAO,IAAI,IAAI;IACrB,OAAO;QACL,IAAI,IAAI,EAAE;QACV,GAAG,IAAI;QACP,mEAAmE;QACnE,WAAW,MAAM,WAAW,cAAc,MAAM;QAChD,WAAW,MAAM,WAAW,cAAc,MAAM;IAClD;AACF;AAKO,MAAM,qBAAqB;IAChC,MAAM,KAAI,MAAc;QACtB,MAAM,SAAS,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,SAAS;QAChC,MAAM,UAAU,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE;QAC7B,OAAO,UAAuB;IAChC;IAEA,MAAM,QAAO,MAAc,EAAE,IAAyD;QACpF,MAAM,SAAS,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,SAAS;QAChC,MAAM,MAAM,IAAI;QAChB,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE,QAAQ;YACnB,GAAG,IAAI;YACP,WAAW,iKAAA,CAAA,YAAS,CAAC,QAAQ,CAAC;YAC9B,WAAW,iKAAA,CAAA,YAAS,CAAC,QAAQ,CAAC;QAChC;IACF;IAEA,MAAM,QAAO,MAAc,EAAE,IAA0B;QACrD,MAAM,SAAS,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,SAAS;QAChC,MAAM,CAAA,GAAA,iKAAA,CAAA,YAAS,AAAD,EAAE,QAAQ;YACtB,GAAG,IAAI;YACP,WAAW,iKAAA,CAAA,YAAS,CAAC,GAAG;QAC1B;IACF;AACF;AAGO,MAAM,cAAc;IACzB,MAAM,QAAO,OAMZ;QACC,IAAI,IAAyB,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE;QAE5C,IAAI,SAAS,aAAa,WAAW;YACnC,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,YAAY,MAAM,QAAQ,QAAQ;QACvD;QACA,IAAI,SAAS,aAAa,WAAW;YACnC,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,YAAY,MAAM,QAAQ,QAAQ;QACvD;QACA,IAAI,SAAS,UAAU;YACrB,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,YAAY,MAAM,QAAQ,QAAQ;QACvD;QACA,IAAI,SAAS,cAAc;YACzB,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,iBAAiB,MAAM,QAAQ,YAAY;QAChE;QAEA,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;QAElC,IAAI,SAAS,OAAO;YAClB,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,QAAQ,KAAK;QAClC;QAEA,MAAM,gBAAgB,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;QACpC,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,UAAoB,MAAO,MAAM,CAAC;IACzE;IAEA,MAAM,KAAI,MAAc;QACtB,MAAM,SAAS,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,SAAS;QAChC,MAAM,UAAU,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE;QAC7B,OAAO,UAAoB;IAC7B;IAEA,MAAM,YAAW,OAAe;QAC9B,MAAM,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EACZ,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,UACf,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,WAAW,MAAM,UACvB,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;QAEvB,MAAM,gBAAgB,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;QACpC,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,UAAoB,MAAO,MAAM,CAAC;IACzE;IAEA,MAAM,WAAU,UAAmB;QACjC,IAAI,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EACV,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,UACf,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,YAAY,MAAM,OACxB,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;QAGvB,IAAI,YAAY;YACd,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE;QACrB;QAEA,MAAM,gBAAgB,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;QACpC,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,UAAoB,MAAO,MAAM,CAAC;IACzE;IAEA,MAAM,QAAO,IAAsD;QACjE,MAAM,MAAM,IAAI;QAChB,MAAM,SAAS,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,UAAU;YACnD,GAAG,IAAI;YACP,WAAW,iKAAA,CAAA,YAAS,CAAC,QAAQ,CAAC;YAC9B,WAAW,iKAAA,CAAA,YAAS,CAAC,QAAQ,CAAC;QAChC;QACA,OAAO,OAAO,EAAE;IAClB;IAEA,MAAM,QAAO,MAAc,EAAE,IAAuB;QAClD,MAAM,SAAS,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,SAAS;QAChC,MAAM,CAAA,GAAA,iKAAA,CAAA,YAAS,AAAD,EAAE,QAAQ;YACtB,GAAG,IAAI;YACP,WAAW,iKAAA,CAAA,YAAS,CAAC,GAAG;QAC1B;IACF;IAEA,MAAM,QAAO,MAAc;QACzB,MAAM,SAAS,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,SAAS;QAChC,MAAM,CAAA,GAAA,iKAAA,CAAA,YAAS,AAAD,EAAE;IAClB;IAEA,mCAAmC;IACnC,MAAM,UAAS,MAAc,EAAE,SAA4D;QACzF,MAAM,MAAM,IAAI;QAChB,MAAM,SAAS,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,SAAS,QAAQ,WAAW;YACrE,GAAG,SAAS;YACZ,WAAW,iKAAA,CAAA,YAAS,CAAC,QAAQ,CAAC;YAC9B,WAAW,iKAAA,CAAA,YAAS,CAAC,QAAQ,CAAC;QAChC;QACA,OAAO,OAAO,EAAE;IAClB;IAEA,kBAAkB;IAClB,MAAM,WAAU,MAAc;QAC5B,MAAM,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EACZ,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,SAAS,QAAQ,WAChC,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,gBAAgB,QACxB,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;QAEvB,MAAM,gBAAgB,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;QACpC,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,UAAqB,MAAO,MAAM,CAAC;IAC1E;IAEA,oBAAoB;IACpB,MAAM,aAAY,MAAc,EAAE,OAAe;QAC/C,MAAM,SAAS,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,SAAS,QAAQ,UAAU;QAClD,MAAM,CAAA,GAAA,iKAAA,CAAA,YAAS,AAAD,EAAE;IAClB;AACF;AAGO,MAAM,iBAAiB;IAC5B,MAAM,QAAO,OAKZ;QACC,IAAI,IAAyB,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE;QAE5C,IAAI,SAAS,UAAU;YACrB,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,YAAY,MAAM,QAAQ,QAAQ;QACvD;QACA,IAAI,SAAS,QAAQ;YACnB,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,UAAU,MAAM,QAAQ,MAAM;QACnD;QACA,IAAI,SAAS,QAAQ;YACnB,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,UAAU,MAAM,QAAQ,MAAM;QACnD;QAEA,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;QAElC,IAAI,SAAS,OAAO;YAClB,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,QAAQ,KAAK;QAClC;QAEA,MAAM,gBAAgB,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;QACpC,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,UAAmB,MAAO,MAAM,CAAC;IACxE;IAEA,MAAM,KAAI,SAAiB;QACzB,MAAM,SAAS,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,YAAY;QACnC,MAAM,UAAU,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE;QAC7B,OAAO,UAAmB;IAC5B;IAEA,MAAM,QAAO,IAA0E;QACrF,MAAM,MAAM,IAAI;QAChB,MAAM,mBAAmB,CAAC,IAAI,EAAE,IAAI,WAAW,KAAK,CAAC,IAAI,QAAQ,KAAK,CAAC,EAAE,QAAQ,GAAG,QAAQ,CAAC,GAAG,OAAO,IAAI,OAAO,GAAG,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG,GAAG,WAAW,IAAI;QAE9M,MAAM,SAAS,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,aAAa;YACtD,GAAG,IAAI;YACP;YACA,WAAW,iKAAA,CAAA,YAAS,CAAC,QAAQ,CAAC;YAC9B,WAAW,iKAAA,CAAA,YAAS,CAAC,QAAQ,CAAC;QAChC;QACA,OAAO,OAAO,EAAE;IAClB;IAEA,MAAM,QAAO,SAAiB,EAAE,IAAsB;QACpD,MAAM,SAAS,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,YAAY;QACnC,MAAM,CAAA,GAAA,iKAAA,CAAA,YAAS,AAAD,EAAE,QAAQ;YACtB,GAAG,IAAI;YACP,WAAW,iKAAA,CAAA,YAAS,CAAC,GAAG;QAC1B;IACF;AACF;AAGO,MAAM,gBAAgB;IAC3B,MAAM,WAAU,MAAc;QAC5B,MAAM,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EACZ,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,SAAS,QAAQ,YAChC,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,YAAY,MAAM,OACxB,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;QAEvB,MAAM,gBAAgB,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;QACpC,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,UAAkB,MAAO,MAAM,CAAC;IACvE;IAEA,MAAM,QAAO,MAAc,EAAE,IAAoD;QAC/E,MAAM,MAAM,IAAI;QAChB,MAAM,SAAS,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,SAAS,QAAQ,YAAY;YACtE,GAAG,IAAI;YACP,WAAW,iKAAA,CAAA,YAAS,CAAC,QAAQ,CAAC;YAC9B,WAAW,iKAAA,CAAA,YAAS,CAAC,QAAQ,CAAC;QAChC;QACA,OAAO,OAAO,EAAE;IAClB;IAEA,MAAM,QAAO,MAAc,EAAE,QAAgB,EAAE,IAAqB;QAClE,MAAM,SAAS,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,SAAS,QAAQ,WAAW;QACnD,MAAM,CAAA,GAAA,iKAAA,CAAA,YAAS,AAAD,EAAE,QAAQ;YACtB,GAAG,IAAI;YACP,WAAW,iKAAA,CAAA,YAAS,CAAC,GAAG;QAC1B;IACF;AACF;AAGO,MAAM,iBAAiB;IAC5B,MAAM;QACJ,MAAM,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,YAAY,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;QACnD,MAAM,gBAAgB,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;QACpC,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,UAAuB,MAAO,MAAM,CAAC;IAC5E;IAEA,MAAM,KAAI,SAAiB;QACzB,MAAM,SAAS,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,WAAW;QAClC,MAAM,UAAU,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE;QAC7B,OAAO,UAAuB;IAChC;AACF;AAGO,MAAM,iBAAiB;IAC5B,MAAM;QACJ,MAAM,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,gIAAA,CAAA,KAAE,EAAE,cAAc,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;QACrD,MAAM,gBAAgB,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;QACpC,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,UAAuB,MAAO,MAAM,CAAC;IAC5E;AACF", "debugId": null}}, {"offset": {"line": 2662, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/src/personal/bvr-safaris/src/app/page.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useEffect, useState, useCallback } from 'react'\nimport { HeroImageLoader } from '@/components/ui/HeroImageLoader'\nimport { SearchBar } from '@/components/features/SearchBar'\nimport { FeaturedFarmsCarousel } from '@/components/features/FeaturedFarmsCarousel'\nimport { farmService } from '@/lib/firebase/firestore'\nimport { GameFarm } from '@/lib/types/firestore'\nimport { useRouter } from 'next/navigation'\n\ninterface FarmWithStats extends GameFarm {\n  rating?: number\n  reviewCount?: number\n  activities: ('hunting' | 'photo_safari')[]\n  imageUrl?: string\n}\n\nexport default function Home() {\n  const [featuredFarms, setFeaturedFarms] = useState<FarmWithStats[]>([])\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState<string | null>(null)\n\n  const fetchFeaturedFarms = useCallback(async () => {\n    try {\n      setLoading(true)\n      setError(null)\n\n      const farmsData = await farmService.getAll({\n        featured: true,\n        isActive: true,\n        limit: 3\n      })\n\n      const processedFarms: FarmWithStats[] = farmsData.map((farm) => {\n        // Convert activityTypes to activities array\n        const activities: ('hunting' | 'photo_safari')[] = []\n        if (farm.activityTypes === 'hunting' || farm.activityTypes === 'both') {\n          activities.push('hunting')\n        }\n        if (farm.activityTypes === 'photo_safari' || farm.activityTypes === 'both') {\n          activities.push('photo_safari')\n        }\n\n        return {\n          ...farm,\n          activities,\n          rating: undefined, // TODO: Calculate from reviews subcollection\n          reviewCount: 0, // TODO: Count from reviews subcollection\n          imageUrl: '/globe.svg' // TODO: Get from images subcollection\n        }\n      })\n\n      setFeaturedFarms(processedFarms)\n    } catch (err) {\n      console.error('Error fetching featured farms:', err)\n      setError('Failed to load featured farms')\n    } finally {\n      setLoading(false)\n    }\n  }, [])\n\n  useEffect(() => {\n    fetchFeaturedFarms()\n  }, [fetchFeaturedFarms])\n\n  const router = useRouter()\n\n  const handleSearch = (query: string, location: string) => {\n    const params = new URLSearchParams()\n    if (query) params.set('search', query)\n    if (location) params.set('location', location)\n    router.push(`/farms?${params.toString()}`)\n  }\n\n  return (\n    <div className=\"min-h-screen\">\n      {/* Hero Section - Design Guide Layout with Left-Aligned Text */}\n      <section className=\"relative text-white\" style={{\n        backgroundColor: 'var(--dark-charcoal)',\n        height: '600px',\n        minHeight: '600px'\n      }}>\n        <HeroImageLoader priority className=\"absolute inset-0\">\n          <div className=\"relative z-20 h-full flex items-center\" style={{\n            maxWidth: '995px',\n            margin: '0 auto',\n            padding: '0 60px'\n          }}>\n            {/* Left-aligned text container matching design mockup - moved further left */}\n            <div className=\"text-left max-w-2xl\" style={{ marginLeft: '-150px' }}>\n              {/* Hero Title - Design Guide Typography with Design Mockup Text */}\n              <h1 className=\"hero-title mb-6\" style={{\n                fontFamily: 'var(--font-display)',\n                fontSize: 'var(--text-large-heading)',\n                fontWeight: '400',\n                color: 'var(--cream)',\n                lineHeight: '1.4',\n                letterSpacing: '0em'\n              }}>\n                The wild is calling,<br/>\n                will you answer?\n              </h1>\n\n            </div>\n          </div>\n        </HeroImageLoader>\n      </section>\n\n      {/* Search Bar Section - Standalone Section Below Hero */}\n      <section className=\"py-12\" style={{ backgroundColor: 'var(--off-white)' }}>\n        <div className=\"mx-auto\" style={{ maxWidth: '995px', padding: '0 89px' }}>\n          <div className=\"text-center mb-8\">\n            <h2 className=\"section-header mb-4\" style={{\n              fontFamily: 'var(--font-body)',\n              fontSize: 'var(--text-primary-body)',\n              fontWeight: '400',\n              color: 'var(--deep-brown)',\n              lineHeight: '1.4',\n              letterSpacing: '0em'\n            }}>\n              Search\n            </h2>\n            <p className=\"body-secondary max-w-4xl mx-auto\" style={{\n              fontFamily: 'var(--font-body)',\n              fontSize: 'var(--text-secondary-body)',\n              color: 'var(--forest-green)',\n              lineHeight: '1.5',\n              letterSpacing: '0em'\n            }}>\n              Use our powerful search bar to quickly find the ideal hunting destination tailored to your needs.\n              Simply enter a location, province, or game farm name to explore options in your preferred area. You\n              can also filter your search by the type of game available—whether you&rsquo;re after plains game, dangerous\n              game, or specific species like kudu, impala, or warthog. Refine your results further by selecting\n              hunting methods (rifle, bow, or walk-and-stalk), accommodation type, group size, available dates, or\n              budget range. Our intuitive filters make it easy to compare listings and find exactly what you&rsquo;re looking\n              for, whether it&rsquo;s a weekend getaway or a full-scale hunting safari.\n            </p>\n          </div>\n\n          <div className=\"max-w-4xl mx-auto\">\n            <SearchBar onSearch={handleSearch} />\n          </div>\n        </div>\n      </section>\n\n      {/* Featured Farms Carousel - Design Guide Styling */}\n      {featuredFarms.length > 0 && (\n        <section className=\"py-16\" style={{ backgroundColor: 'var(--dark-charcoal)', minHeight: '593px' }}>\n          <div className=\"mx-auto\" style={{ maxWidth: '995px', padding: '0 89px' }}>\n            <div className=\"text-center mb-12\">\n              <h2 className=\"section-header mb-6\" style={{\n                fontFamily: 'var(--font-display)',\n                fontSize: 'var(--text-section-header)',\n                fontWeight: '700',\n                color: 'var(--cream)',\n                lineHeight: '1.4',\n                letterSpacing: '0em'\n              }}>\n                Featured Listings\n              </h2>\n              <p className=\"body-primary max-w-2xl mx-auto\" style={{\n                fontFamily: 'var(--font-body)',\n                fontSize: 'var(--text-primary-body)',\n                color: 'var(--cream)',\n                lineHeight: '1.5',\n                letterSpacing: '0em'\n              }}>\n                Use area as a carousel of recommended listings or specials.\n              </p>\n            </div>\n\n            <FeaturedFarmsCarousel\n              farms={featuredFarms}\n              loading={loading}\n              error={error}\n              onRetry={fetchFeaturedFarms}\n            />\n          </div>\n        </section>\n      )}\n\n      {/* Why Choose Us Section - Design Guide Styling */}\n      <section className=\"py-16\" style={{ backgroundColor: 'var(--off-white)', minHeight: '792px' }}>\n        <div className=\"mx-auto\" style={{ maxWidth: '995px', padding: '0 89px' }}>\n          <div className=\"text-center mb-12\">\n            <h2 className=\"section-header mb-6\" style={{\n              fontFamily: 'var(--font-display)',\n              fontSize: 'var(--text-section-header)',\n              fontWeight: '700',\n              color: 'var(--forest-green)',\n              lineHeight: '1.4',\n              letterSpacing: '0em'\n            }}>\n              Why choose us?\n            </h2>\n          </div>\n\n          <div className=\"max-w-4xl mx-auto\">\n            <p className=\"body-primary text-center\" style={{\n              fontFamily: 'var(--font-body)',\n              fontSize: 'var(--text-primary-body)',\n              color: 'var(--dark-text)',\n              lineHeight: '1.5',\n              letterSpacing: '0em'\n            }}>\n              At BvR Safaris, we connect passionate hunters with premium hunting\n              destinations across South Africa, offering a curated selection of lodges, farms,\n              and outfitters that meet the highest standards of quality, safety, and ethical\n              hunting practices. Whether you&rsquo;re seeking a self-catering bushveld getaway or\n              a fully guided trophy hunt, our platform simplifies the booking process and\n              ensures transparent pricing, verified reviews, and expert support every step of\n              the way. With a focus on conservation, professionalism, and unforgettable\n              outdoor experiences, we are your trusted partner in planning the perfect\n              hunting escape.\n            </p>\n          </div>\n        </div>\n      </section>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAEA;AARA;;;;;;;;AAiBe,SAAS;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IACtE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACrC,IAAI;YACF,WAAW;YACX,SAAS;YAET,MAAM,YAAY,MAAM,mIAAA,CAAA,cAAW,CAAC,MAAM,CAAC;gBACzC,UAAU;gBACV,UAAU;gBACV,OAAO;YACT;YAEA,MAAM,iBAAkC,UAAU,GAAG,CAAC,CAAC;gBACrD,4CAA4C;gBAC5C,MAAM,aAA6C,EAAE;gBACrD,IAAI,KAAK,aAAa,KAAK,aAAa,KAAK,aAAa,KAAK,QAAQ;oBACrE,WAAW,IAAI,CAAC;gBAClB;gBACA,IAAI,KAAK,aAAa,KAAK,kBAAkB,KAAK,aAAa,KAAK,QAAQ;oBAC1E,WAAW,IAAI,CAAC;gBAClB;gBAEA,OAAO;oBACL,GAAG,IAAI;oBACP;oBACA,QAAQ;oBACR,aAAa;oBACb,UAAU,aAAa,sCAAsC;gBAC/D;YACF;YAEA,iBAAiB;QACnB,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,kCAAkC;YAChD,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAmB;IAEvB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe,CAAC,OAAe;QACnC,MAAM,SAAS,IAAI;QACnB,IAAI,OAAO,OAAO,GAAG,CAAC,UAAU;QAChC,IAAI,UAAU,OAAO,GAAG,CAAC,YAAY;QACrC,OAAO,IAAI,CAAC,CAAC,OAAO,EAAE,OAAO,QAAQ,IAAI;IAC3C;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAQ,WAAU;gBAAsB,OAAO;oBAC9C,iBAAiB;oBACjB,QAAQ;oBACR,WAAW;gBACb;0BACE,cAAA,8OAAC,2IAAA,CAAA,kBAAe;oBAAC,QAAQ;oBAAC,WAAU;8BAClC,cAAA,8OAAC;wBAAI,WAAU;wBAAyC,OAAO;4BAC7D,UAAU;4BACV,QAAQ;4BACR,SAAS;wBACX;kCAEE,cAAA,8OAAC;4BAAI,WAAU;4BAAsB,OAAO;gCAAE,YAAY;4BAAS;sCAEjE,cAAA,8OAAC;gCAAG,WAAU;gCAAkB,OAAO;oCACrC,YAAY;oCACZ,UAAU;oCACV,YAAY;oCACZ,OAAO;oCACP,YAAY;oCACZ,eAAe;gCACjB;;oCAAG;kDACmB,8OAAC;;;;;oCAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUnC,8OAAC;gBAAQ,WAAU;gBAAQ,OAAO;oBAAE,iBAAiB;gBAAmB;0BACtE,cAAA,8OAAC;oBAAI,WAAU;oBAAU,OAAO;wBAAE,UAAU;wBAAS,SAAS;oBAAS;;sCACrE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;oCAAsB,OAAO;wCACzC,YAAY;wCACZ,UAAU;wCACV,YAAY;wCACZ,OAAO;wCACP,YAAY;wCACZ,eAAe;oCACjB;8CAAG;;;;;;8CAGH,8OAAC;oCAAE,WAAU;oCAAmC,OAAO;wCACrD,YAAY;wCACZ,UAAU;wCACV,OAAO;wCACP,YAAY;wCACZ,eAAe;oCACjB;8CAAG;;;;;;;;;;;;sCAWL,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,2IAAA,CAAA,YAAS;gCAAC,UAAU;;;;;;;;;;;;;;;;;;;;;;YAM1B,cAAc,MAAM,GAAG,mBACtB,8OAAC;gBAAQ,WAAU;gBAAQ,OAAO;oBAAE,iBAAiB;oBAAwB,WAAW;gBAAQ;0BAC9F,cAAA,8OAAC;oBAAI,WAAU;oBAAU,OAAO;wBAAE,UAAU;wBAAS,SAAS;oBAAS;;sCACrE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;oCAAsB,OAAO;wCACzC,YAAY;wCACZ,UAAU;wCACV,YAAY;wCACZ,OAAO;wCACP,YAAY;wCACZ,eAAe;oCACjB;8CAAG;;;;;;8CAGH,8OAAC;oCAAE,WAAU;oCAAiC,OAAO;wCACnD,YAAY;wCACZ,UAAU;wCACV,OAAO;wCACP,YAAY;wCACZ,eAAe;oCACjB;8CAAG;;;;;;;;;;;;sCAKL,8OAAC,uJAAA,CAAA,wBAAqB;4BACpB,OAAO;4BACP,SAAS;4BACT,OAAO;4BACP,SAAS;;;;;;;;;;;;;;;;;0BAOjB,8OAAC;gBAAQ,WAAU;gBAAQ,OAAO;oBAAE,iBAAiB;oBAAoB,WAAW;gBAAQ;0BAC1F,cAAA,8OAAC;oBAAI,WAAU;oBAAU,OAAO;wBAAE,UAAU;wBAAS,SAAS;oBAAS;;sCACrE,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAG,WAAU;gCAAsB,OAAO;oCACzC,YAAY;oCACZ,UAAU;oCACV,YAAY;oCACZ,OAAO;oCACP,YAAY;oCACZ,eAAe;gCACjB;0CAAG;;;;;;;;;;;sCAKL,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;gCAA2B,OAAO;oCAC7C,YAAY;oCACZ,UAAU;oCACV,OAAO;oCACP,YAAY;oCACZ,eAAe;gCACjB;0CAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgBf", "debugId": null}}]}