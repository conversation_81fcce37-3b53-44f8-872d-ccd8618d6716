(()=>{var e={};e.id=89,e.ids=[89],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5697:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>u});var t=s(60687),a=s(43210),i=s(16189),n=s(19978),o=s(50944),d=s(2643),l=s(51907);function c(){let[e,r]=(0,a.useState)(""),[s,c]=(0,a.useState)(""),[u,p]=(0,a.useState)(!1),[m,x]=(0,a.useState)(""),[h,f]=(0,a.useState)(!1),[b,v]=(0,a.useState)(!0),w=(0,i.useRouter)(),g=(0,i.useSearchParams)(),j=async r=>{if(r.preventDefault(),p(!0),x(""),e!==s){x("Passwords do not match"),p(!1);return}if(e.length<6){x("Password must be at least 6 characters"),p(!1);return}try{let r=g.get("oobCode");if(!r)return void x("Invalid reset code");await (0,n.R4)(o.j2,r,e),w.push("/auth/login?message=Password updated successfully")}catch(e){console.error("Password reset error:",e),x(e instanceof Error?e.message:"An unexpected error occurred")}finally{p(!1)}};return!b&&(h||m)?m&&!h?(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-earth-100 to-earth-200 px-4",children:(0,t.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-earth-900 mb-2",children:"Invalid Link"}),(0,t.jsx)("p",{className:"text-earth-600 mb-6",children:m})]}),(0,t.jsx)("div",{className:"bg-white rounded-lg shadow-lg p-8 text-center",children:(0,t.jsx)(d.$,{variant:"primary",size:"lg",className:"w-full",onClick:()=>w.push("/auth/forgot-password"),children:"Request New Reset Link"})})]})}):(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-earth-100 to-earth-200 px-4",children:(0,t.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-earth-900 mb-2",children:"Reset Your Password"}),(0,t.jsx)("p",{className:"text-earth-600",children:"Enter your new password below"})]}),(0,t.jsx)("div",{className:"bg-white rounded-lg shadow-lg p-8",children:(0,t.jsxs)("form",{onSubmit:j,className:"space-y-6",children:[m&&(0,t.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-md p-3",children:(0,t.jsx)("p",{className:"text-red-600 text-sm",children:m})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-earth-700 mb-2",children:"New Password"}),(0,t.jsx)(l.p,{id:"password",type:"password",value:e,onChange:e=>r(e.target.value),placeholder:"Enter new password (min. 6 characters)",required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-earth-700 mb-2",children:"Confirm New Password"}),(0,t.jsx)(l.p,{id:"confirmPassword",type:"password",value:s,onChange:e=>c(e.target.value),placeholder:"Confirm new password",required:!0})]}),(0,t.jsx)(d.$,{type:"submit",variant:"primary",size:"lg",className:"w-full",isLoading:u,children:u?"Updating Password...":"Update Password"})]})})]})}):(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-earth-100 to-earth-200 px-4",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-accent-600 mx-auto"}),(0,t.jsx)("p",{className:"mt-4 text-earth-600",children:"Validating reset link..."})]})})}function u(){return(0,t.jsx)(a.Suspense,{fallback:(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-earth-100 to-earth-200 px-4",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-accent-600 mx-auto"}),(0,t.jsx)("p",{className:"mt-4 text-earth-600",children:"Loading..."})]})}),children:(0,t.jsx)(c,{})})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},37366:e=>{"use strict";e.exports=require("dns")},48541:(e,r,s)=>{Promise.resolve().then(s.bind(s,5697))},51907:(e,r,s)=>{"use strict";s.d(r,{p:()=>n});var t=s(60687),a=s(43210),i=s(4780);let n=(0,a.forwardRef)(({className:e,type:r,label:s,error:a,...n},o)=>(0,t.jsxs)("div",{className:"w-full",children:[s&&(0,t.jsx)("label",{className:"block mb-2 font-semibold text-[var(--primary-brown)] font-[var(--font-ui)]",children:s}),(0,t.jsx)("input",{type:r,className:(0,i.cn)(`
            w-full px-4 py-2 border-2 border-[var(--medium-gray)] 
            rounded-[var(--radius-md)] font-[var(--font-ui)] 
            transition-colors duration-300
            focus:outline-none focus:border-[var(--primary-brown)] 
            focus:ring-0 focus:shadow-[0_0_0_3px_rgba(139,69,19,0.1)]
            placeholder:text-[var(--medium-gray)]
            `,a&&"border-red-500 focus:border-red-500",e),ref:o,...n}),a&&(0,t.jsx)("p",{className:"mt-1 text-sm text-red-600",children:a})]}));n.displayName="Input"},55511:e=>{"use strict";e.exports=require("crypto")},59379:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>t});let t=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/src/personal/bvr-safaris/src/app/auth/reset-password/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/src/personal/bvr-safaris/src/app/auth/reset-password/page.tsx","default")},62045:(e,r,s)=>{Promise.resolve().then(s.bind(s,59379))},62887:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>l});var t=s(65239),a=s(48088),i=s(88170),n=s.n(i),o=s(30893),d={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);s.d(r,d);let l={children:["",{children:["auth",{children:["reset-password",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,59379)),"/Users/<USER>/src/personal/bvr-safaris/src/app/auth/reset-password/page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,82893)),"/Users/<USER>/src/personal/bvr-safaris/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/Users/<USER>/src/personal/bvr-safaris/src/app/auth/reset-password/page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},p=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/auth/reset-password/page",pathname:"/auth/reset-password",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>a});var t=s(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,t.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[447,874,658,391],()=>s(62887));module.exports=t})();