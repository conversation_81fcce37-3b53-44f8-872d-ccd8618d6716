(()=>{var e={};e.id=859,e.ids=[859],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4083:(e,r,t)=>{Promise.resolve().then(t.bind(t,49457))},9776:(e,r,t)=>{"use strict";t.d(r,{k:()=>i});var s=t(60687),a=t(4780);function i({size:e="md",className:r}){return(0,s.jsx)("div",{className:(0,a.cn)("flex items-center justify-center",r),children:(0,s.jsx)("div",{className:(0,a.cn)("animate-spin rounded-full border-2 border-white border-t-transparent",{sm:"w-4 h-4",md:"w-6 h-6",lg:"w-12 h-12"}[e])})})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},18439:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var s=t(65239),a=t(48088),i=t(88170),n=t.n(i),o=t(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);t.d(r,l);let d={children:["",{children:["auth",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,81351)),"/Users/<USER>/src/personal/bvr-safaris/src/app/auth/login/page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,82893)),"/Users/<USER>/src/personal/bvr-safaris/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/Users/<USER>/src/personal/bvr-safaris/src/app/auth/login/page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/auth/login/page",pathname:"/auth/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},37366:e=>{"use strict";e.exports=require("dns")},49457:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>p});var s=t(60687),a=t(43210),i=t(85814),n=t.n(i),o=t(16189),l=t(57445),d=t(2643),c=t(51907),u=t(9776);function p(){let[e,r]=(0,a.useState)(""),[t,i]=(0,a.useState)(""),[p,m]=(0,a.useState)(!1),{signIn:x,error:h,clearError:f,user:v,loading:b}=(0,l.As)(),g=(0,o.useRouter)();if(b)return(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-earth-100 to-earth-200",children:(0,s.jsx)(u.k,{size:"lg"})});if(v)return null;let j=async r=>{r.preventDefault(),m(!0);try{await x(e,t),g.push("/dashboard")}catch(e){console.error("Login error:",e)}finally{m(!1)}},y=()=>{f()};return(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-earth-100 to-earth-200 px-4",children:(0,s.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-earth-900 mb-2",children:"Welcome Back"}),(0,s.jsx)("p",{className:"text-earth-600",children:"Sign in to your RvB Safaris account"})]}),(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-8",children:[(0,s.jsxs)("form",{onSubmit:j,className:"space-y-6",children:[h&&(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-md p-3",children:(0,s.jsx)("p",{className:"text-red-600 text-sm",children:h})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-earth-700 mb-2",children:"Email Address"}),(0,s.jsx)(c.p,{id:"email",type:"email",value:e,onChange:e=>{r(e.target.value),y()},placeholder:"Enter your email",required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-earth-700 mb-2",children:"Password"}),(0,s.jsx)(c.p,{id:"password",type:"password",value:t,onChange:e=>{i(e.target.value),y()},placeholder:"Enter your password",required:!0})]}),(0,s.jsx)(d.$,{type:"submit",variant:"primary",size:"lg",className:"w-full",isLoading:p,disabled:!e||!t,children:p?"Signing In...":"Sign In"})]}),(0,s.jsx)("div",{className:"mt-6 text-center",children:(0,s.jsxs)("p",{className:"text-earth-600",children:["Don’t have an account?"," ",(0,s.jsx)(n(),{href:"/auth/register",className:"text-accent-600 hover:text-accent-700 font-medium",children:"Sign up here"})]})}),(0,s.jsx)("div",{className:"mt-4 text-center",children:(0,s.jsx)(n(),{href:"/auth/forgot-password",className:"text-sm text-earth-500 hover:text-earth-700",children:"Forgot your password?"})})]})]})})}},51907:(e,r,t)=>{"use strict";t.d(r,{p:()=>n});var s=t(60687),a=t(43210),i=t(4780);let n=(0,a.forwardRef)(({className:e,type:r,label:t,error:a,...n},o)=>(0,s.jsxs)("div",{className:"w-full",children:[t&&(0,s.jsx)("label",{className:"block mb-2 font-semibold text-[var(--primary-brown)] font-[var(--font-ui)]",children:t}),(0,s.jsx)("input",{type:r,className:(0,i.cn)(`
            w-full px-4 py-2 border-2 border-[var(--medium-gray)] 
            rounded-[var(--radius-md)] font-[var(--font-ui)] 
            transition-colors duration-300
            focus:outline-none focus:border-[var(--primary-brown)] 
            focus:ring-0 focus:shadow-[0_0_0_3px_rgba(139,69,19,0.1)]
            placeholder:text-[var(--medium-gray)]
            `,a&&"border-red-500 focus:border-red-500",e),ref:o,...n}),a&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:a})]}));n.displayName="Input"},55511:e=>{"use strict";e.exports=require("crypto")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67291:(e,r,t)=>{Promise.resolve().then(t.bind(t,81351))},70440:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});var s=t(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81351:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/src/personal/bvr-safaris/src/app/auth/login/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/src/personal/bvr-safaris/src/app/auth/login/page.tsx","default")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,874,658,391],()=>t(18439));module.exports=s})();