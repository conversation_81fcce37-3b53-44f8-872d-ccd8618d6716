(()=>{var e={};e.id=270,e.ids=[270],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10974:(e,r,t)=>{"use strict";t.d(r,{cn:()=>i});var s=t(75986),a=t(8974);function i(...e){return(0,a.QP)((0,s.$)(e))}},14649:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,85814,23))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29852:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>o});var s=t(37413),a=t(4536),i=t.n(a),n=t(83853);function o(){return(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-earth-100 to-earth-200 px-4",children:(0,s.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"mx-auto w-16 h-16 bg-accent-100 rounded-full flex items-center justify-center mb-6",children:(0,s.jsx)("svg",{className:"w-8 h-8 text-accent-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})})}),(0,s.jsx)("h1",{className:"text-3xl font-bold text-earth-900 mb-2",children:"Check Your Email"}),(0,s.jsx)("p",{className:"text-earth-600 mb-6",children:"We’ve sent you a verification link. Please check your email and click the link to activate your account."})]}),(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-8 text-center",children:[(0,s.jsx)("p",{className:"text-earth-700 mb-6",children:"Didn’t receive the email? Check your spam folder or request a new verification email."}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)(n.$,{variant:"primary",size:"lg",className:"w-full",children:"Resend Verification Email"}),(0,s.jsx)(i(),{href:"/auth/login",children:(0,s.jsx)(n.$,{variant:"outline",size:"lg",className:"w-full",children:"Back to Login"})})]})]})]})})}},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},37366:e=>{"use strict";e.exports=require("dns")},55511:e=>{"use strict";e.exports=require("crypto")},60561:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>d,pages:()=>u,routeModule:()=>p,tree:()=>l});var s=t(65239),a=t(48088),i=t(88170),n=t.n(i),o=t(30893),c={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>o[e]);t.d(r,c);let l={children:["",{children:["auth",{children:["verify-email",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,29852)),"/Users/<USER>/src/personal/bvr-safaris/src/app/auth/verify-email/page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,82893)),"/Users/<USER>/src/personal/bvr-safaris/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,u=["/Users/<USER>/src/personal/bvr-safaris/src/app/auth/verify-email/page.tsx"],d={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/auth/verify-email/page",pathname:"/auth/verify-email",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});var s=t(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},78201:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,4536,23))},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83853:(e,r,t)=>{"use strict";t.d(r,{$:()=>n});var s=t(37413),a=t(61120),i=t(10974);let n=(0,a.forwardRef)(({className:e,variant:r="primary",size:t="md",isLoading:a,children:n,disabled:o,...c},l)=>{let u=`
      inline-flex items-center justify-center rounded-[var(--radius-md)] 
      font-[var(--font-ui)] font-semibold transition-all duration-300 
      focus:outline-none focus:ring-2 focus:ring-offset-2
      disabled:opacity-50 disabled:cursor-not-allowed
      hover:transform hover:-translate-y-0.5 hover:shadow-[var(--shadow-lg)]
    `,d={primary:`
        bg-[var(--primary-brown)] text-white 
        hover:bg-[#A0522D] focus:ring-[var(--primary-brown)]
      `,secondary:`
        bg-[var(--secondary-sky)] text-white 
        hover:bg-[#357AE0] focus:ring-[var(--secondary-sky)]
      `,outline:`
        bg-transparent border-2 border-[var(--primary-brown)] text-[var(--primary-brown)]
        hover:bg-[var(--primary-brown)] hover:text-white focus:ring-[var(--primary-brown)]
      `,hunting:`
        bg-[var(--hunting-accent)] text-white 
        hover:bg-[#A67C00] focus:ring-[var(--hunting-accent)]
      `,photo:`
        bg-[var(--photo-accent)] text-white 
        hover:bg-[#3557C7] focus:ring-[var(--photo-accent)]
      `};return(0,s.jsxs)("button",{className:(0,i.cn)(u,d[r],{sm:"px-3 py-1.5 text-sm",md:"px-6 py-2 text-base",lg:"px-8 py-3 text-lg"}[t],a&&"cursor-wait",e),disabled:o||a,ref:l,...c,children:[a&&(0,s.jsxs)("svg",{className:"mr-2 h-4 w-4 animate-spin",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,s.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,s.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),n]})});n.displayName="Button"},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,874,658,17,391],()=>t(60561));module.exports=s})();