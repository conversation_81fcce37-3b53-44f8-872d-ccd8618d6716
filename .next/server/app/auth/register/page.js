(()=>{var e={};e.id=983,e.ids=[983],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9776:(e,r,s)=>{"use strict";s.d(r,{k:()=>i});var t=s(60687),a=s(4780);function i({size:e="md",className:r}){return(0,t.jsx)("div",{className:(0,a.cn)("flex items-center justify-center",r),children:(0,t.jsx)("div",{className:(0,a.cn)("animate-spin rounded-full border-2 border-white border-t-transparent",{sm:"w-4 h-4",md:"w-6 h-6",lg:"w-12 h-12"}[e])})})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},23743:(e,r,s)=>{Promise.resolve().then(s.bind(s,24507))},24507:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>u});var t=s(60687),a=s(43210),i=s(85814),n=s.n(i),o=s(16189),d=s(57445),l=s(2643),c=s(51907),m=s(9776);function u(){let[e,r]=(0,a.useState)({email:"",password:"",confirmPassword:"",firstName:"",lastName:"",role:"guest",phone:""}),[s,i]=(0,a.useState)(!1),{signUp:u,error:p,clearError:h,user:x,loading:f}=(0,d.As)(),b=(0,o.useRouter)();if(f)return(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-earth-100 to-earth-200",children:(0,t.jsx)(m.k,{size:"lg"})});if(x)return null;let v=e=>{r(r=>({...r,[e.target.name]:e.target.value})),h()},g=async r=>{if(r.preventDefault(),i(!0),e.password!==e.confirmPassword||e.password.length<6)return void i(!1);try{await u(e.email,e.password,{firstName:e.firstName,lastName:e.lastName,phone:e.phone||void 0,role:e.role}),b.push("/dashboard")}catch(e){console.error("Registration error:",e)}finally{i(!1)}};return(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-earth-100 to-earth-200 px-4 py-8",children:(0,t.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-earth-900 mb-2",children:"Join BvR Safaris"}),(0,t.jsx)("p",{className:"text-earth-600",children:"Create your account to start your safari adventure"})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-8",children:[(0,t.jsxs)("form",{onSubmit:g,className:"space-y-6",children:[p&&(0,t.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-md p-3",children:(0,t.jsx)("p",{className:"text-red-600 text-sm",children:p})}),e.password!==e.confirmPassword&&e.confirmPassword&&(0,t.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-md p-3",children:(0,t.jsx)("p",{className:"text-red-600 text-sm",children:"Passwords do not match"})}),e.password.length>0&&e.password.length<6&&(0,t.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-md p-3",children:(0,t.jsx)("p",{className:"text-red-600 text-sm",children:"Password must be at least 6 characters"})}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"firstName",className:"block text-sm font-medium text-earth-700 mb-2",children:"First Name"}),(0,t.jsx)(c.p,{id:"firstName",name:"firstName",type:"text",value:e.firstName,onChange:v,placeholder:"First name",required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"lastName",className:"block text-sm font-medium text-earth-700 mb-2",children:"Last Name"}),(0,t.jsx)(c.p,{id:"lastName",name:"lastName",type:"text",value:e.lastName,onChange:v,placeholder:"Last name",required:!0})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-earth-700 mb-2",children:"Email Address"}),(0,t.jsx)(c.p,{id:"email",name:"email",type:"email",value:e.email,onChange:v,placeholder:"Enter your email",required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"phone",className:"block text-sm font-medium text-earth-700 mb-2",children:"Phone Number"}),(0,t.jsx)(c.p,{id:"phone",name:"phone",type:"tel",value:e.phone,onChange:v,placeholder:"Enter your phone number"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"role",className:"block text-sm font-medium text-earth-700 mb-2",children:"I am a..."}),(0,t.jsxs)("select",{id:"role",name:"role",value:e.role,onChange:v,className:"w-full px-3 py-2 border border-earth-300 rounded-md focus:outline-none focus:ring-2 focus:ring-accent-500 focus:border-transparent",required:!0,children:[(0,t.jsx)("option",{value:"guest",children:"Guest (Hunter/Photo Safari Enthusiast)"}),(0,t.jsx)("option",{value:"farm_owner",children:"Farm Owner"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-earth-700 mb-2",children:"Password"}),(0,t.jsx)(c.p,{id:"password",name:"password",type:"password",value:e.password,onChange:v,placeholder:"Create a password (min. 6 characters)",required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-earth-700 mb-2",children:"Confirm Password"}),(0,t.jsx)(c.p,{id:"confirmPassword",name:"confirmPassword",type:"password",value:e.confirmPassword,onChange:v,placeholder:"Confirm your password",required:!0})]}),(0,t.jsx)(l.$,{type:"submit",variant:"primary",size:"lg",className:"w-full",isLoading:s,disabled:!e.email||!e.password||!e.confirmPassword||!e.firstName||!e.lastName||e.password!==e.confirmPassword||e.password.length<6,children:s?"Creating Account...":"Create Account"})]}),(0,t.jsx)("div",{className:"mt-6 text-center",children:(0,t.jsxs)("p",{className:"text-earth-600",children:["Already have an account?"," ",(0,t.jsx)(n(),{href:"/auth/login",className:"text-accent-600 hover:text-accent-700 font-medium",children:"Sign in here"})]})})]})]})})}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},37366:e=>{"use strict";e.exports=require("dns")},50135:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>l});var t=s(65239),a=s(48088),i=s(88170),n=s.n(i),o=s(30893),d={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);s.d(r,d);let l={children:["",{children:["auth",{children:["register",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,59401)),"/Users/<USER>/src/personal/bvr-safaris/src/app/auth/register/page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,82893)),"/Users/<USER>/src/personal/bvr-safaris/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/Users/<USER>/src/personal/bvr-safaris/src/app/auth/register/page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},u=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/auth/register/page",pathname:"/auth/register",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},51907:(e,r,s)=>{"use strict";s.d(r,{p:()=>n});var t=s(60687),a=s(43210),i=s(4780);let n=(0,a.forwardRef)(({className:e,type:r,label:s,error:a,...n},o)=>(0,t.jsxs)("div",{className:"w-full",children:[s&&(0,t.jsx)("label",{className:"block mb-2 font-semibold text-[var(--primary-brown)] font-[var(--font-ui)]",children:s}),(0,t.jsx)("input",{type:r,className:(0,i.cn)(`
            w-full px-4 py-2 border-2 border-[var(--medium-gray)] 
            rounded-[var(--radius-md)] font-[var(--font-ui)] 
            transition-colors duration-300
            focus:outline-none focus:border-[var(--primary-brown)] 
            focus:ring-0 focus:shadow-[0_0_0_3px_rgba(139,69,19,0.1)]
            placeholder:text-[var(--medium-gray)]
            `,a&&"border-red-500 focus:border-red-500",e),ref:o,...n}),a&&(0,t.jsx)("p",{className:"mt-1 text-sm text-red-600",children:a})]}));n.displayName="Input"},55511:e=>{"use strict";e.exports=require("crypto")},59401:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>t});let t=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/src/personal/bvr-safaris/src/app/auth/register/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/src/personal/bvr-safaris/src/app/auth/register/page.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>a});var t=s(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,t.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},93479:(e,r,s)=>{Promise.resolve().then(s.bind(s,59401))},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[447,874,658,391],()=>s(50135));module.exports=t})();