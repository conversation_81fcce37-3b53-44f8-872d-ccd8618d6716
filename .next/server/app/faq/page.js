(()=>{var e={};e.id=505,e.ids=[505],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},25856:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>d});var a=t(60687),s=t(43210),i=t(62688);let n=(0,i.A)("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]]),o=(0,i.A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]),c=[{category:"general",question:"What is BvR Safaris?",answer:"BvR Safaris is South Africa's premier platform connecting hunters, photographers, and safari enthusiasts with certified game farms and safari operators. We facilitate bookings and provide comprehensive information about safari experiences."},{category:"general",question:"How do I create an account?",answer:'Click the "Sign Up" button in the top right corner, choose your account type (Hunter/Guest or Farm Owner), and fill in your details. You\'ll receive a verification email to activate your account.'},{category:"booking",question:"How do I book a safari?",answer:"Browse our farm listings, select your preferred farm and dates, choose your activity type (hunting or photo safari), and submit a booking request. The farm owner will review and confirm your booking."},{category:"booking",question:"What payment methods are accepted?",answer:"Payment methods vary by farm operator. Most accept bank transfers, credit cards, and cash payments. Specific payment terms will be provided when your booking is confirmed."},{category:"booking",question:"Can I cancel my booking?",answer:"Cancellation policies vary by farm operator and are specified in your booking confirmation. Generally, cancellations made well in advance may receive partial or full refunds, while last-minute cancellations may incur penalties."},{category:"hunting",question:"Do I need a hunting license?",answer:"Yes, you must have a valid hunting license and any required permits. International hunters need temporary hunting licenses issued by South African authorities. Farm operators can assist with the application process."},{category:"hunting",question:"What hunting equipment is provided?",answer:"Equipment provision varies by farm. Some provide rifles and ammunition for rent, while others require you to bring your own or arrange through licensed dealers. Check with your chosen farm for specific details."},{category:"hunting",question:"Can I bring my own rifle?",answer:"Yes, but you must follow South African firearm import regulations. This includes obtaining temporary import permits and declaring firearms at customs. We recommend consulting with your farm operator for guidance."},{category:"photo",question:"What should I bring for a photo safari?",answer:"Bring your camera equipment, extra batteries, memory cards, and appropriate clothing. Many farms provide vehicles and guides. Check with your chosen farm for specific recommendations and any equipment they provide."},{category:"photo",question:"Are there restrictions on photography?",answer:"Photography policies vary by farm. Some areas may be off-limits, and there may be restrictions on photographing certain activities or facilities. Always ask permission and respect the farm's guidelines."},{category:"safety",question:"How safe are safari activities?",answer:"Safety is our top priority. All listed farms maintain high safety standards and provide experienced guides. However, safari activities involve inherent risks. We strongly recommend comprehensive travel and activity insurance."},{category:"safety",question:"What safety equipment is provided?",answer:"Safety equipment varies by activity and farm. Hunting safaris typically include safety briefings and protective gear. Photo safaris provide safe vehicles and experienced guides. Specific safety measures are detailed in farm descriptions."},{category:"safety",question:"Do I need travel insurance?",answer:"Yes, we strongly recommend comprehensive travel and activity insurance that covers safari activities, medical emergencies, and equipment. Check that your policy covers hunting activities if applicable."}],l=[{key:"general",label:"General"},{key:"booking",label:"Booking"},{key:"hunting",label:"Hunting"},{key:"photo",label:"Photo Safari"},{key:"safety",label:"Safety"}];function d(){let[e,r]=(0,s.useState)("general"),[t,i]=(0,s.useState)(new Set),d=e=>{let r=new Set(t);r.has(e)?r.delete(e):r.add(e),i(r)},p=c.filter(r=>r.category===e);return(0,a.jsx)("div",{className:"min-h-screen bg-earth-100 py-16",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"text-center mb-12",children:[(0,a.jsx)("h1",{className:"text-4xl md:text-5xl font-bold mb-6 text-earth-900",children:"Frequently Asked Questions"}),(0,a.jsx)("p",{className:"text-xl text-earth-700",children:"Find answers to common questions about BvR Safaris and safari experiences."})]}),(0,a.jsx)("div",{className:"flex flex-wrap justify-center mb-8 bg-white rounded-lg p-2 shadow-md",children:l.map(t=>(0,a.jsx)("button",{onClick:()=>r(t.key),className:`px-4 py-2 mx-1 my-1 rounded-md font-medium transition-colors ${e===t.key?"bg-accent-600 text-white":"text-earth-700 hover:bg-earth-100"}`,children:t.label},t.key))}),(0,a.jsx)("div",{className:"space-y-4",children:p.map((e,r)=>(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md overflow-hidden",children:[(0,a.jsxs)("button",{onClick:()=>d(r),className:"w-full px-6 py-4 text-left flex justify-between items-center hover:bg-earth-50 transition-colors",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-earth-900 pr-4",children:e.question}),t.has(r)?(0,a.jsx)(n,{className:"w-5 h-5 text-earth-600 flex-shrink-0"}):(0,a.jsx)(o,{className:"w-5 h-5 text-earth-600 flex-shrink-0"})]}),t.has(r)&&(0,a.jsx)("div",{className:"px-6 pb-4",children:(0,a.jsx)("p",{className:"text-earth-700 leading-relaxed",children:e.answer})})]},r))}),(0,a.jsxs)("div",{className:"mt-12 bg-white rounded-lg shadow-md p-8 text-center",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold mb-4 text-earth-900",children:"Still Have Questions?"}),(0,a.jsx)("p",{className:"text-earth-700 mb-6",children:"Can't find what you're looking for? Our support team is here to help."}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,a.jsx)("a",{href:"/contact",className:"inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-accent-600 hover:bg-accent-700 transition-colors",children:"Contact Support"}),(0,a.jsx)("a",{href:"mailto:<EMAIL>",className:"inline-flex items-center justify-center px-6 py-3 border border-earth-300 text-base font-medium rounded-md text-earth-700 bg-white hover:bg-earth-50 transition-colors",children:"Email Us"})]})]})]})})}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},37366:e=>{"use strict";e.exports=require("dns")},52591:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>p,pages:()=>d,routeModule:()=>u,tree:()=>l});var a=t(65239),s=t(48088),i=t(88170),n=t.n(i),o=t(30893),c={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>o[e]);t.d(r,c);let l={children:["",{children:["faq",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,93503)),"/Users/<USER>/src/personal/bvr-safaris/src/app/faq/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,82893)),"/Users/<USER>/src/personal/bvr-safaris/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["/Users/<USER>/src/personal/bvr-safaris/src/app/faq/page.tsx"],p={require:t,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/faq/page",pathname:"/faq",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},55511:e=>{"use strict";e.exports=require("crypto")},62443:(e,r,t)=>{Promise.resolve().then(t.bind(t,25856))},62688:(e,r,t)=>{"use strict";t.d(r,{A:()=>p});var a=t(43210);let s=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,r,t)=>t?t.toUpperCase():r.toLowerCase()),n=e=>{let r=i(e);return r.charAt(0).toUpperCase()+r.slice(1)},o=(...e)=>e.filter((e,r,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===r).join(" ").trim(),c=e=>{for(let r in e)if(r.startsWith("aria-")||"role"===r||"title"===r)return!0};var l={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,a.forwardRef)(({color:e="currentColor",size:r=24,strokeWidth:t=2,absoluteStrokeWidth:s,className:i="",children:n,iconNode:d,...p},u)=>(0,a.createElement)("svg",{ref:u,...l,width:r,height:r,stroke:e,strokeWidth:s?24*Number(t)/Number(r):t,className:o("lucide",i),...!n&&!c(p)&&{"aria-hidden":"true"},...p},[...d.map(([e,r])=>(0,a.createElement)(e,r)),...Array.isArray(n)?n:[n]])),p=(e,r)=>{let t=(0,a.forwardRef)(({className:t,...i},c)=>(0,a.createElement)(d,{ref:c,iconNode:r,className:o(`lucide-${s(n(e))}`,`lucide-${e}`,t),...i}));return t.displayName=n(e),t}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68019:(e,r,t)=>{Promise.resolve().then(t.bind(t,93503))},70440:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});var a=t(31658);let s=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},93503:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/src/personal/bvr-safaris/src/app/faq/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/src/personal/bvr-safaris/src/app/faq/page.tsx","default")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[447,874,658,391],()=>t(52591));module.exports=a})();