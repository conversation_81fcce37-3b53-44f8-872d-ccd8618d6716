(()=>{var e={};e.id=636,e.ids=[636],e.modules={474:(e,r,t)=>{"use strict";t.d(r,{C2:()=>d,QQ:()=>i,eg:()=>l,zi:()=>o});var a=t(75535),s=t(50944);function n(e){if(!e.exists())return null;let r=e.data();return{id:e.id,...r,createdAt:r?.createdAt?.toDate?.()||r?.createdAt,updatedAt:r?.updatedAt?.toDate?.()||r?.updatedAt}}let i={async get(e){let r=(0,a.H9)(s.db,"users",e);return n(await (0,a.x7)(r))},async create(e,r){let t=(0,a.H9)(s.db,"users",e),n=new Date;await (0,a.BN)(t,{...r,createdAt:a.Dc.fromDate(n),updatedAt:a.Dc.fromDate(n)})},async update(e,r){let t=(0,a.H9)(s.db,"users",e);await (0,a.mZ)(t,{...r,updatedAt:a.Dc.now()})}},l={async getAll(e){let r=(0,a.rJ)(s.db,"farms");return e?.isActive!==void 0&&(r=(0,a.P)(r,(0,a._M)("isActive","==",e.isActive))),e?.featured!==void 0&&(r=(0,a.P)(r,(0,a._M)("featured","==",e.featured))),e?.province&&(r=(0,a.P)(r,(0,a._M)("province","==",e.province))),e?.activityType&&(r=(0,a.P)(r,(0,a._M)("activityTypes","==",e.activityType))),r=(0,a.P)(r,(0,a.My)("createdAt","desc")),e?.limit&&(r=(0,a.P)(r,(0,a.AB)(e.limit))),(await (0,a.GG)(r)).docs.map(e=>n(e)).filter(Boolean)},async get(e){let r=(0,a.H9)(s.db,"farms",e);return n(await (0,a.x7)(r))},async getByOwner(e){let r=(0,a.P)((0,a.rJ)(s.db,"farms"),(0,a._M)("ownerId","==",e),(0,a.My)("createdAt","desc"));return(await (0,a.GG)(r)).docs.map(e=>n(e)).filter(Boolean)},async getActive(e){let r=(0,a.P)((0,a.rJ)(s.db,"farms"),(0,a._M)("isActive","==",!0),(0,a.My)("createdAt","desc"));return e&&(r=(0,a.P)(r,(0,a.AB)(e))),(await (0,a.GG)(r)).docs.map(e=>n(e)).filter(Boolean)},async create(e){let r=new Date;return(await (0,a.gS)((0,a.rJ)(s.db,"farms"),{...e,createdAt:a.Dc.fromDate(r),updatedAt:a.Dc.fromDate(r)})).id},async update(e,r){let t=(0,a.H9)(s.db,"farms",e);await (0,a.mZ)(t,{...r,updatedAt:a.Dc.now()})},async delete(e){let r=(0,a.H9)(s.db,"farms",e);await (0,a.kd)(r)},async addImage(e,r){let t=new Date;return(await (0,a.gS)((0,a.rJ)(s.db,"farms",e,"images"),{...r,createdAt:a.Dc.fromDate(t),updatedAt:a.Dc.fromDate(t)})).id},async getImages(e){let r=(0,a.P)((0,a.rJ)(s.db,"farms",e,"images"),(0,a.My)("displayOrder","asc"),(0,a.My)("createdAt","asc"));return(await (0,a.GG)(r)).docs.map(e=>n(e)).filter(Boolean)},async deleteImage(e,r){let t=(0,a.H9)(s.db,"farms",e,"images",r);await (0,a.kd)(t)}},o={async getAll(e){let r=(0,a.rJ)(s.db,"bookings");return e?.hunterId&&(r=(0,a.P)(r,(0,a._M)("hunterId","==",e.hunterId))),e?.farmId&&(r=(0,a.P)(r,(0,a._M)("farmId","==",e.farmId))),e?.status&&(r=(0,a.P)(r,(0,a._M)("status","==",e.status))),r=(0,a.P)(r,(0,a.My)("createdAt","desc")),e?.limit&&(r=(0,a.P)(r,(0,a.AB)(e.limit))),(await (0,a.GG)(r)).docs.map(e=>n(e)).filter(Boolean)},async get(e){let r=(0,a.H9)(s.db,"bookings",e);return n(await (0,a.x7)(r))},async create(e){let r=new Date,t=`BVR-${r.getFullYear()}${(r.getMonth()+1).toString().padStart(2,"0")}${r.getDate().toString().padStart(2,"0")}-${Math.random().toString(36).substring(2,8).toUpperCase()}`;return(await (0,a.gS)((0,a.rJ)(s.db,"bookings"),{...e,bookingReference:t,createdAt:a.Dc.fromDate(r),updatedAt:a.Dc.fromDate(r)})).id},async update(e,r){let t=(0,a.H9)(s.db,"bookings",e);await (0,a.mZ)(t,{...r,updatedAt:a.Dc.now()})}},d={async getByFarm(e){let r=(0,a.P)((0,a.rJ)(s.db,"farms",e,"reviews"),(0,a._M)("isPublic","==",!0),(0,a.My)("createdAt","desc"));return(await (0,a.GG)(r)).docs.map(e=>n(e)).filter(Boolean)},async create(e,r){let t=new Date;return(await (0,a.gS)((0,a.rJ)(s.db,"farms",e,"reviews"),{...r,createdAt:a.Dc.fromDate(t),updatedAt:a.Dc.fromDate(t)})).id},async update(e,r,t){let n=(0,a.H9)(s.db,"farms",e,"reviews",r);await (0,a.mZ)(n,{...t,updatedAt:a.Dc.now()})}}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4633:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var a=t(65239),s=t(48088),n=t(88170),i=t.n(n),l=t(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);t.d(r,o);let d={children:["",{children:["profile",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,75758)),"/Users/<USER>/src/personal/bvr-safaris/src/app/profile/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,82893)),"/Users/<USER>/src/personal/bvr-safaris/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/Users/<USER>/src/personal/bvr-safaris/src/app/profile/page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/profile/page",pathname:"/profile",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28749:(e,r,t)=>{"use strict";t.d(r,{MH:()=>u,Wu:()=>o,ZB:()=>c,Zp:()=>l,aR:()=>d});var a=t(60687),s=t(43210),n=t(30474),i=t(4780);let l=(0,s.forwardRef)(({className:e,hover:r=!0,children:t,...s},n)=>(0,a.jsx)("div",{ref:n,className:(0,i.cn)(`
          bg-white rounded-[var(--radius-lg)] shadow-[var(--shadow-md)] 
          overflow-hidden transition-all duration-300
          `,r&&"hover:transform hover:-translate-y-1 hover:shadow-[var(--shadow-xl)]",e),...s,children:t}));l.displayName="Card";let o=(0,s.forwardRef)(({className:e,...r},t)=>(0,a.jsx)("div",{ref:t,className:(0,i.cn)("p-[var(--space-lg)]",e),...r}));o.displayName="CardContent";let d=(0,s.forwardRef)(({className:e,...r},t)=>(0,a.jsx)("div",{ref:t,className:(0,i.cn)("px-[var(--space-lg)] pt-[var(--space-lg)]",e),...r}));d.displayName="CardHeader";let c=(0,s.forwardRef)(({className:e,...r},t)=>(0,a.jsx)("h3",{ref:t,className:(0,i.cn)("text-xl font-bold mb-2 text-[var(--primary-brown)] font-[var(--font-display)]",e),...r}));c.displayName="CardTitle";let u=(0,s.forwardRef)(({className:e,src:r,alt:t,children:s,...l},o)=>(0,a.jsx)("div",{ref:o,className:(0,i.cn)("relative w-full h-48 bg-gradient-to-br from-[var(--primary-green)] to-[var(--secondary-sunset)] flex items-center justify-center text-white text-5xl",e),...l,children:r?(0,a.jsx)(n.default,{src:r,alt:t||"",fill:!0,className:"object-cover"}):s}));u.displayName="CardImage"},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30474:(e,r,t)=>{"use strict";t.d(r,{default:()=>s.a});var a=t(31261),s=t.n(a)},31261:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(r,{default:function(){return o},getImageProps:function(){return l}});let a=t(14985),s=t(44953),n=t(46533),i=a._(t(1933));function l(e){let{props:r}=(0,s.getImgProps)(e,{defaultLoader:i.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,t]of Object.entries(r))void 0===t&&delete r[e];return{props:r}}let o=n.Image},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},37366:e=>{"use strict";e.exports=require("dns")},51907:(e,r,t)=>{"use strict";t.d(r,{p:()=>i});var a=t(60687),s=t(43210),n=t(4780);let i=(0,s.forwardRef)(({className:e,type:r,label:t,error:s,...i},l)=>(0,a.jsxs)("div",{className:"w-full",children:[t&&(0,a.jsx)("label",{className:"block mb-2 font-semibold text-[var(--primary-brown)] font-[var(--font-ui)]",children:t}),(0,a.jsx)("input",{type:r,className:(0,n.cn)(`
            w-full px-4 py-2 border-2 border-[var(--medium-gray)] 
            rounded-[var(--radius-md)] font-[var(--font-ui)] 
            transition-colors duration-300
            focus:outline-none focus:border-[var(--primary-brown)] 
            focus:ring-0 focus:shadow-[0_0_0_3px_rgba(139,69,19,0.1)]
            placeholder:text-[var(--medium-gray)]
            `,s&&"border-red-500 focus:border-red-500",e),ref:l,...i}),s&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600",children:s})]}));i.displayName="Input"},55511:e=>{"use strict";e.exports=require("crypto")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});var a=t(31658);let s=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75758:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/src/personal/bvr-safaris/src/app/profile/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/src/personal/bvr-safaris/src/app/profile/page.tsx","default")},77118:(e,r,t)=>{Promise.resolve().then(t.bind(t,87032))},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},81846:(e,r,t)=>{Promise.resolve().then(t.bind(t,75758))},87032:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>u});var a=t(60687),s=t(43210),n=t(16189),i=t(57445),l=t(2643),o=t(51907),d=t(28749),c=t(474);function u(){let{user:e,loading:r}=(0,i.As)(),t=(0,n.useRouter)(),[u,p]=(0,s.useState)(null),[m,f]=(0,s.useState)(!0),[x,h]=(0,s.useState)(!1),[b,g]=(0,s.useState)(null),[v,y]=(0,s.useState)(!1),[w,j]=(0,s.useState)({fullName:"",phone:"",bio:"",languagePreference:"en"}),N=(0,s.useCallback)(async()=>{try{f(!0);let r=await c.QQ.get(e.uid);r?(p(r),j({fullName:r.fullName||"",phone:r.phone||"",bio:r.bio||"",languagePreference:r.languagePreference||"en"})):(console.error("Profile not found"),g("Failed to load profile"))}catch(e){console.error("Unexpected error:",e),g("An unexpected error occurred")}finally{f(!1)}},[e]),P=(e,r)=>{j(t=>({...t,[e]:r})),y(!1),g(null)},A=async r=>{r.preventDefault(),h(!0),g(null);try{await c.QQ.update(e.uid,{fullName:w.fullName,phone:w.phone||void 0,bio:w.bio||void 0,languagePreference:w.languagePreference}),y(!0),await N()}catch(e){g("An unexpected error occurred"),console.error("Profile update error:",e)}finally{h(!1)}};return r||m?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-earth-100",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-accent-600"})}):e&&u?(0,a.jsx)("div",{className:"min-h-screen bg-earth-100 py-8",children:(0,a.jsxs)("div",{className:"max-w-2xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-earth-900",children:"Profile Settings"}),(0,a.jsx)("p",{className:"text-earth-600 mt-2",children:"Manage your account information and preferences"})]}),(0,a.jsxs)(d.Zp,{children:[(0,a.jsx)(d.aR,{children:(0,a.jsx)(d.ZB,{children:"Personal Information"})}),(0,a.jsx)(d.Wu,{children:(0,a.jsxs)("form",{onSubmit:A,className:"space-y-6",children:[b&&(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-md p-3",children:(0,a.jsx)("p",{className:"text-red-600 text-sm",children:b})}),v&&(0,a.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-md p-3",children:(0,a.jsx)("p",{className:"text-green-600 text-sm",children:"Profile updated successfully!"})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-earth-700 mb-2",children:"Email Address"}),(0,a.jsx)(o.p,{type:"email",value:u.email,disabled:!0,className:"bg-earth-50"}),(0,a.jsx)("p",{className:"text-xs text-earth-500 mt-1",children:"Email cannot be changed. Contact support if needed."})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-earth-700 mb-2",children:"Account Type"}),(0,a.jsx)(o.p,{type:"text",value:u.role.replace("_"," ").replace(/\b\w/g,e=>e.toUpperCase()),disabled:!0,className:"bg-earth-50"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"fullName",className:"block text-sm font-medium text-earth-700 mb-2",children:"Full Name *"}),(0,a.jsx)(o.p,{id:"fullName",type:"text",value:w.fullName,onChange:e=>P("fullName",e.target.value),placeholder:"Enter your full name",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"phone",className:"block text-sm font-medium text-earth-700 mb-2",children:"Phone Number"}),(0,a.jsx)(o.p,{id:"phone",type:"tel",value:w.phone,onChange:e=>P("phone",e.target.value),placeholder:"Enter your phone number"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"bio",className:"block text-sm font-medium text-earth-700 mb-2",children:"Bio"}),(0,a.jsx)("textarea",{id:"bio",value:w.bio,onChange:e=>P("bio",e.target.value),placeholder:"Tell us about yourself...",rows:4,className:"w-full px-3 py-2 border border-earth-300 rounded-md focus:outline-none focus:ring-2 focus:ring-accent-600 focus:border-transparent"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"language",className:"block text-sm font-medium text-earth-700 mb-2",children:"Language Preference"}),(0,a.jsxs)("select",{id:"language",value:w.languagePreference,onChange:e=>P("languagePreference",e.target.value),className:"w-full px-3 py-2 border border-earth-300 rounded-md focus:outline-none focus:ring-2 focus:ring-accent-600 focus:border-transparent",children:[(0,a.jsx)("option",{value:"en",children:"English"}),(0,a.jsx)("option",{value:"af",children:"Afrikaans"})]})]}),(0,a.jsxs)("div",{className:"flex gap-4",children:[(0,a.jsx)(l.$,{type:"submit",variant:"primary",isLoading:x,className:"flex-1",children:x?"Saving...":"Save Changes"}),(0,a.jsx)(l.$,{type:"button",variant:"outline",onClick:()=>t.push("/dashboard"),className:"flex-1",children:"Cancel"})]})]})})]})]})}):(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-earth-100",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-earth-600 mb-4",children:"Profile not found"}),(0,a.jsx)(l.$,{onClick:()=>t.push("/dashboard"),children:"Go to Dashboard"})]})})}},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[447,874,658,533,391],()=>t(4633));module.exports=a})();