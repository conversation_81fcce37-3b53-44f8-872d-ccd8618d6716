(()=>{var e={};e.id=977,e.ids=[977],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28749:(e,r,s)=>{"use strict";s.d(r,{MH:()=>u,Wu:()=>o,ZB:()=>d,Zp:()=>l,aR:()=>c});var t=s(60687),a=s(43210),i=s(30474),n=s(4780);let l=(0,a.forwardRef)(({className:e,hover:r=!0,children:s,...a},i)=>(0,t.jsx)("div",{ref:i,className:(0,n.cn)(`
          bg-white rounded-[var(--radius-lg)] shadow-[var(--shadow-md)] 
          overflow-hidden transition-all duration-300
          `,r&&"hover:transform hover:-translate-y-1 hover:shadow-[var(--shadow-xl)]",e),...a,children:s}));l.displayName="Card";let o=(0,a.forwardRef)(({className:e,...r},s)=>(0,t.jsx)("div",{ref:s,className:(0,n.cn)("p-[var(--space-lg)]",e),...r}));o.displayName="CardContent";let c=(0,a.forwardRef)(({className:e,...r},s)=>(0,t.jsx)("div",{ref:s,className:(0,n.cn)("px-[var(--space-lg)] pt-[var(--space-lg)]",e),...r}));c.displayName="CardHeader";let d=(0,a.forwardRef)(({className:e,...r},s)=>(0,t.jsx)("h3",{ref:s,className:(0,n.cn)("text-xl font-bold mb-2 text-[var(--primary-brown)] font-[var(--font-display)]",e),...r}));d.displayName="CardTitle";let u=(0,a.forwardRef)(({className:e,src:r,alt:s,children:a,...l},o)=>(0,t.jsx)("div",{ref:o,className:(0,n.cn)("relative w-full h-48 bg-gradient-to-br from-[var(--primary-green)] to-[var(--secondary-sunset)] flex items-center justify-center text-white text-5xl",e),...l,children:r?(0,t.jsx)(i.default,{src:r,alt:s||"",fill:!0,className:"object-cover"}):a}));u.displayName="CardImage"},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30474:(e,r,s)=>{"use strict";s.d(r,{default:()=>a.a});var t=s(31261),a=s.n(t)},31261:(e,r,s)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,r){for(var s in r)Object.defineProperty(e,s,{enumerable:!0,get:r[s]})}(r,{default:function(){return o},getImageProps:function(){return l}});let t=s(14985),a=s(44953),i=s(46533),n=t._(s(1933));function l(e){let{props:r}=(0,a.getImgProps)(e,{defaultLoader:n.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,s]of Object.entries(r))void 0===s&&delete r[e];return{props:r}}let o=i.Image},32455:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c});var t=s(65239),a=s(48088),i=s(88170),n=s.n(i),l=s(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(r,o);let c={children:["",{children:["contact",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,43839)),"/Users/<USER>/src/personal/bvr-safaris/src/app/contact/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,82893)),"/Users/<USER>/src/personal/bvr-safaris/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["/Users/<USER>/src/personal/bvr-safaris/src/app/contact/page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/contact/page",pathname:"/contact",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},37366:e=>{"use strict";e.exports=require("dns")},43839:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>t});let t=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/src/personal/bvr-safaris/src/app/contact/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/src/personal/bvr-safaris/src/app/contact/page.tsx","default")},48730:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(62688).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},51907:(e,r,s)=>{"use strict";s.d(r,{p:()=>n});var t=s(60687),a=s(43210),i=s(4780);let n=(0,a.forwardRef)(({className:e,type:r,label:s,error:a,...n},l)=>(0,t.jsxs)("div",{className:"w-full",children:[s&&(0,t.jsx)("label",{className:"block mb-2 font-semibold text-[var(--primary-brown)] font-[var(--font-ui)]",children:s}),(0,t.jsx)("input",{type:r,className:(0,i.cn)(`
            w-full px-4 py-2 border-2 border-[var(--medium-gray)] 
            rounded-[var(--radius-md)] font-[var(--font-ui)] 
            transition-colors duration-300
            focus:outline-none focus:border-[var(--primary-brown)] 
            focus:ring-0 focus:shadow-[0_0_0_3px_rgba(139,69,19,0.1)]
            placeholder:text-[var(--medium-gray)]
            `,a&&"border-red-500 focus:border-red-500",e),ref:l,...n}),a&&(0,t.jsx)("p",{className:"mt-1 text-sm text-red-600",children:a})]}));n.displayName="Input"},55511:e=>{"use strict";e.exports=require("crypto")},59859:(e,r,s)=>{Promise.resolve().then(s.bind(s,43839))},62688:(e,r,s)=>{"use strict";s.d(r,{A:()=>u});var t=s(43210);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,r,s)=>s?s.toUpperCase():r.toLowerCase()),n=e=>{let r=i(e);return r.charAt(0).toUpperCase()+r.slice(1)},l=(...e)=>e.filter((e,r,s)=>!!e&&""!==e.trim()&&s.indexOf(e)===r).join(" ").trim(),o=e=>{for(let r in e)if(r.startsWith("aria-")||"role"===r||"title"===r)return!0};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,t.forwardRef)(({color:e="currentColor",size:r=24,strokeWidth:s=2,absoluteStrokeWidth:a,className:i="",children:n,iconNode:d,...u},m)=>(0,t.createElement)("svg",{ref:m,...c,width:r,height:r,stroke:e,strokeWidth:a?24*Number(s)/Number(r):s,className:l("lucide",i),...!n&&!o(u)&&{"aria-hidden":"true"},...u},[...d.map(([e,r])=>(0,t.createElement)(e,r)),...Array.isArray(n)?n:[n]])),u=(e,r)=>{let s=(0,t.forwardRef)(({className:s,...i},o)=>(0,t.createElement)(d,{ref:o,iconNode:r,className:l(`lucide-${a(n(e))}`,`lucide-${e}`,s),...i}));return s.displayName=n(e),s}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>a});var t=s(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,t.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91117:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>x});var t=s(60687),a=s(43210),i=s(2643),n=s(51907),l=s(28749),o=s(62688);let c=(0,o.A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]]),d=(0,o.A)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]]);var u=s(97992),m=s(48730);function x(){let[e,r]=(0,a.useState)({name:"",email:"",subject:"",message:""}),[s,o]=(0,a.useState)(!1),[x,p]=(0,a.useState)(!1),h=(e,s)=>{r(r=>({...r,[e]:s}))},f=async e=>{e.preventDefault(),o(!0),await new Promise(e=>setTimeout(e,1e3)),p(!0),o(!1),r({name:"",email:"",subject:"",message:""})};return(0,t.jsx)("div",{className:"min-h-screen bg-earth-100 py-16",children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,t.jsxs)("div",{className:"text-center mb-12",children:[(0,t.jsx)("h1",{className:"text-4xl md:text-5xl font-bold mb-6 text-earth-900",children:"Contact Us"}),(0,t.jsx)("p",{className:"text-xl text-earth-700 max-w-3xl mx-auto",children:"Get in touch with our team. We're here to help you plan your perfect safari experience."})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12",children:[(0,t.jsxs)("div",{className:"space-y-8",children:[(0,t.jsxs)(l.Zp,{children:[(0,t.jsx)(l.aR,{children:(0,t.jsxs)(l.ZB,{className:"flex items-center",children:[(0,t.jsx)(c,{className:"w-5 h-5 mr-2 text-accent-600"}),"Email Us"]})}),(0,t.jsxs)(l.Wu,{children:[(0,t.jsx)("p",{className:"text-earth-700 mb-2",children:"General Inquiries"}),(0,t.jsx)("p",{className:"font-medium text-earth-900",children:"<EMAIL>"}),(0,t.jsx)("p",{className:"text-earth-700 mb-2 mt-4",children:"Booking Support"}),(0,t.jsx)("p",{className:"font-medium text-earth-900",children:"<EMAIL>"})]})]}),(0,t.jsxs)(l.Zp,{children:[(0,t.jsx)(l.aR,{children:(0,t.jsxs)(l.ZB,{className:"flex items-center",children:[(0,t.jsx)(d,{className:"w-5 h-5 mr-2 text-accent-600"}),"Call Us"]})}),(0,t.jsxs)(l.Wu,{children:[(0,t.jsx)("p",{className:"text-earth-700 mb-2",children:"Main Office"}),(0,t.jsx)("p",{className:"font-medium text-earth-900",children:"+27 (0)11 123 4567"}),(0,t.jsx)("p",{className:"text-earth-700 mb-2 mt-4",children:"WhatsApp"}),(0,t.jsx)("p",{className:"font-medium text-earth-900",children:"+27 (0)82 123 4567"})]})]}),(0,t.jsxs)(l.Zp,{children:[(0,t.jsx)(l.aR,{children:(0,t.jsxs)(l.ZB,{className:"flex items-center",children:[(0,t.jsx)(u.A,{className:"w-5 h-5 mr-2 text-accent-600"}),"Visit Us"]})}),(0,t.jsxs)(l.Wu,{children:[(0,t.jsx)("p",{className:"text-earth-700 mb-2",children:"Head Office"}),(0,t.jsxs)("p",{className:"font-medium text-earth-900",children:["123 Safari Street",(0,t.jsx)("br",{}),"Johannesburg, 2000",(0,t.jsx)("br",{}),"South Africa"]})]})]}),(0,t.jsxs)(l.Zp,{children:[(0,t.jsx)(l.aR,{children:(0,t.jsxs)(l.ZB,{className:"flex items-center",children:[(0,t.jsx)(m.A,{className:"w-5 h-5 mr-2 text-accent-600"}),"Business Hours"]})}),(0,t.jsx)(l.Wu,{children:(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-earth-700",children:"Monday - Friday"}),(0,t.jsx)("span",{className:"font-medium text-earth-900",children:"8:00 AM - 6:00 PM"})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-earth-700",children:"Saturday"}),(0,t.jsx)("span",{className:"font-medium text-earth-900",children:"9:00 AM - 4:00 PM"})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-earth-700",children:"Sunday"}),(0,t.jsx)("span",{className:"font-medium text-earth-900",children:"Closed"})]})]})})]})]}),(0,t.jsx)("div",{children:(0,t.jsxs)(l.Zp,{children:[(0,t.jsx)(l.aR,{children:(0,t.jsx)(l.ZB,{children:"Send us a Message"})}),(0,t.jsx)(l.Wu,{children:x?(0,t.jsxs)("div",{className:"text-center py-8",children:[(0,t.jsx)("div",{className:"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,t.jsx)("svg",{className:"w-8 h-8 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})}),(0,t.jsx)("h3",{className:"text-xl font-semibold text-earth-900 mb-2",children:"Message Sent!"}),(0,t.jsx)("p",{className:"text-earth-700",children:"Thank you for contacting us. We'll get back to you within 24 hours."}),(0,t.jsx)(i.$,{onClick:()=>p(!1),variant:"outline",className:"mt-4",children:"Send Another Message"})]}):(0,t.jsxs)("form",{onSubmit:f,className:"space-y-6",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsx)(n.p,{label:"Full Name",value:e.name,onChange:e=>h("name",e.target.value),required:!0}),(0,t.jsx)(n.p,{label:"Email Address",type:"email",value:e.email,onChange:e=>h("email",e.target.value),required:!0})]}),(0,t.jsx)(n.p,{label:"Subject",value:e.subject,onChange:e=>h("subject",e.target.value),required:!0}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-earth-900 mb-2",children:"Message"}),(0,t.jsx)("textarea",{className:"w-full px-3 py-2 border border-earth-300 rounded-md focus:outline-none focus:ring-2 focus:ring-accent-600 focus:border-transparent",rows:6,value:e.message,onChange:e=>h("message",e.target.value),required:!0,placeholder:"Tell us about your safari requirements..."})]}),(0,t.jsx)(i.$,{type:"submit",variant:"primary",size:"lg",className:"w-full",isLoading:s,children:s?"Sending...":"Send Message"})]})})]})})]})]})})}},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96811:(e,r,s)=>{Promise.resolve().then(s.bind(s,91117))},97992:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(62688).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])}};var r=require("../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[447,874,658,533,391],()=>s(32455));module.exports=t})();