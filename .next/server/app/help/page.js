(()=>{var e={};e.id=728,e.ids=[728],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10974:(e,t,r)=>{"use strict";r.d(t,{cn:()=>i});var s=r(75986),a=r(8974);function i(...e){return(0,a.QP)((0,s.$)(e))}},16635:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>v});var s=r(37413),a=r(26373);let i=(0,a.A)("book",[["path",{d:"M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H19a1 1 0 0 1 1 1v18a1 1 0 0 1-1 1H6.5a1 1 0 0 1 0-5H20",key:"k3hazp"}]]);var n=r(78768),l=r(90230),o=r(69117);let c=(0,a.A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]),d=(0,a.A)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]]);var p=r(71091),h=r(83853),m=r(4536),x=r.n(m);let u=[{icon:i,title:"Getting Started",description:"Learn the basics of using BvR Safaris",links:[{title:"Creating Your Account",href:"/help/account-setup"},{title:"How to Search for Farms",href:"/help/searching"},{title:"Understanding Farm Profiles",href:"/help/farm-profiles"},{title:"Platform Overview",href:"/how-it-works"}]},{icon:n.A,title:"Booking & Reservations",description:"Everything about making and managing bookings",links:[{title:"How to Make a Booking",href:"/help/booking-process"},{title:"Payment Methods",href:"/help/payments"},{title:"Cancellation Policies",href:"/help/cancellations"},{title:"Booking Confirmations",href:"/help/confirmations"}]},{icon:l.A,title:"Communication",description:"Connecting with farm operators and support",links:[{title:"Messaging Farm Owners",href:"/help/messaging"},{title:"Pre-Arrival Communication",href:"/help/pre-arrival"},{title:"Emergency Contacts",href:"/help/emergency"},{title:"Language Support",href:"/help/languages"}]},{icon:o.A,title:"Safety & Guidelines",description:"Important safety information and guidelines",links:[{title:"Safari Safety Guidelines",href:"/safety"},{title:"Hunting Regulations",href:"/help/hunting-rules"},{title:"Equipment Requirements",href:"/help/equipment"},{title:"Insurance Recommendations",href:"/help/insurance"}]},{icon:c,title:"Legal & Policies",description:"Terms, privacy, and legal information",links:[{title:"Terms of Service",href:"/terms"},{title:"Privacy Policy",href:"/privacy"},{title:"POPIA Compliance",href:"/help/popia"},{title:"Dispute Resolution",href:"/help/disputes"}]},{icon:d,title:"Contact & Support",description:"Get in touch with our support team",links:[{title:"Contact Information",href:"/contact"},{title:"Support Hours",href:"/help/support-hours"},{title:"Emergency Support",href:"/help/emergency-support"},{title:"Feedback & Suggestions",href:"/help/feedback"}]}],f=[{title:"Track Your Booking",description:"Check the status of your current bookings",action:"Go to Dashboard",href:"/dashboard"},{title:"Contact Support",description:"Get help from our support team",action:"Contact Us",href:"/contact"},{title:"Browse FAQs",description:"Find answers to common questions",action:"View FAQs",href:"/faq"}];function v(){return(0,s.jsxs)("div",{className:"min-h-screen bg-earth-100",children:[(0,s.jsx)("section",{className:"bg-gradient-to-r from-earth-600 to-earth-700 text-white py-16",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:[(0,s.jsx)("h1",{className:"text-4xl md:text-5xl font-bold mb-6",children:"Help Center"}),(0,s.jsx)("p",{className:"text-xl md:text-2xl opacity-90 max-w-3xl mx-auto mb-8",children:"Find answers, get support, and learn how to make the most of your BvR Safaris experience."}),(0,s.jsx)("div",{className:"max-w-2xl mx-auto",children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(n.A,{className:"absolute left-4 top-1/2 transform -translate-y-1/2 text-earth-400 w-5 h-5"}),(0,s.jsx)("input",{type:"text",placeholder:"Search for help articles...",className:"w-full pl-12 pr-4 py-3 rounded-lg text-earth-900 placeholder-earth-500 focus:outline-none focus:ring-2 focus:ring-accent-600"})]})})]})}),(0,s.jsx)("section",{className:"py-12",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-earth-900 mb-8 text-center",children:"Quick Actions"}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:f.map((e,t)=>(0,s.jsx)(p.Zp,{className:"text-center p-6 hover:shadow-lg transition-shadow",children:(0,s.jsxs)(p.Wu,{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-earth-900 mb-2",children:e.title}),(0,s.jsx)("p",{className:"text-earth-700 mb-4",children:e.description}),(0,s.jsx)(x(),{href:e.href,children:(0,s.jsx)(h.$,{variant:"primary",size:"sm",children:e.action})})]})},t))})]})}),(0,s.jsx)("section",{className:"py-12 bg-white",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,s.jsx)("h2",{className:"text-3xl font-bold text-earth-900 mb-12 text-center",children:"Browse Help Topics"}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:u.map((e,t)=>(0,s.jsxs)(p.Zp,{className:"h-full",children:[(0,s.jsxs)(p.aR,{children:[(0,s.jsxs)("div",{className:"flex items-center mb-4",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-accent-100 rounded-lg flex items-center justify-center mr-4",children:(0,s.jsx)(e.icon,{className:"w-6 h-6 text-accent-600"})}),(0,s.jsx)(p.ZB,{className:"text-xl",children:e.title})]}),(0,s.jsx)("p",{className:"text-earth-700",children:e.description})]}),(0,s.jsx)(p.Wu,{children:(0,s.jsx)("ul",{className:"space-y-3",children:e.links.map((e,t)=>(0,s.jsx)("li",{children:(0,s.jsx)(x(),{href:e.href,className:"text-accent-600 hover:text-accent-700 hover:underline transition-colors",children:e.title})},t))})})]},t))})]})}),(0,s.jsx)("section",{className:"py-16 bg-earth-50",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:[(0,s.jsx)("h2",{className:"text-3xl font-bold text-earth-900 mb-6",children:"Still Need Help?"}),(0,s.jsx)("p",{className:"text-lg text-earth-700 mb-8",children:"Our support team is available to assist you with any questions or issues you may have."}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8 mb-8",children:[(0,s.jsx)(p.Zp,{className:"p-6",children:(0,s.jsxs)(p.Wu,{className:"text-center",children:[(0,s.jsx)(l.A,{className:"w-12 h-12 text-accent-600 mx-auto mb-4"}),(0,s.jsx)("h3",{className:"text-xl font-semibold text-earth-900 mb-2",children:"Live Chat"}),(0,s.jsx)("p",{className:"text-earth-700 mb-4",children:"Get instant help from our support team"}),(0,s.jsx)(h.$,{variant:"primary",children:"Start Chat"})]})}),(0,s.jsx)(p.Zp,{className:"p-6",children:(0,s.jsxs)(p.Wu,{className:"text-center",children:[(0,s.jsx)(d,{className:"w-12 h-12 text-accent-600 mx-auto mb-4"}),(0,s.jsx)("h3",{className:"text-xl font-semibold text-earth-900 mb-2",children:"Phone Support"}),(0,s.jsx)("p",{className:"text-earth-700 mb-4",children:"Speak directly with our team"}),(0,s.jsx)(h.$,{variant:"outline",children:"Call Now"})]})})]}),(0,s.jsxs)("div",{className:"bg-white rounded-lg p-6 shadow-md",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-earth-900 mb-4",children:"Support Hours"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-medium text-earth-900",children:"Monday - Friday"}),(0,s.jsx)("p",{className:"text-earth-700",children:"8:00 AM - 6:00 PM SAST"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-medium text-earth-900",children:"Saturday"}),(0,s.jsx)("p",{className:"text-earth-700",children:"9:00 AM - 4:00 PM SAST"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-medium text-earth-900",children:"Sunday"}),(0,s.jsx)("p",{className:"text-earth-700",children:"Emergency Support Only"})]})]})]})]})})]})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},37366:e=>{"use strict";e.exports=require("dns")},41703:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,85814,23)),Promise.resolve().then(r.t.bind(r,46533,23))},55511:e=>{"use strict";e.exports=require("crypto")},58575:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,4536,23)),Promise.resolve().then(r.t.bind(r,49603,23))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},69117:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(26373).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},71091:(e,t,r)=>{"use strict";r.d(t,{Wu:()=>o,ZB:()=>d,Zp:()=>l,aR:()=>c});var s=r(37413),a=r(61120),i=r(53384),n=r(10974);let l=(0,a.forwardRef)(({className:e,hover:t=!0,children:r,...a},i)=>(0,s.jsx)("div",{ref:i,className:(0,n.cn)(`
          bg-white rounded-[var(--radius-lg)] shadow-[var(--shadow-md)] 
          overflow-hidden transition-all duration-300
          `,t&&"hover:transform hover:-translate-y-1 hover:shadow-[var(--shadow-xl)]",e),...a,children:r}));l.displayName="Card";let o=(0,a.forwardRef)(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,n.cn)("p-[var(--space-lg)]",e),...t}));o.displayName="CardContent";let c=(0,a.forwardRef)(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,n.cn)("px-[var(--space-lg)] pt-[var(--space-lg)]",e),...t}));c.displayName="CardHeader";let d=(0,a.forwardRef)(({className:e,...t},r)=>(0,s.jsx)("h3",{ref:r,className:(0,n.cn)("text-xl font-bold mb-2 text-[var(--primary-brown)] font-[var(--font-display)]",e),...t}));d.displayName="CardTitle",(0,a.forwardRef)(({className:e,src:t,alt:r,children:a,...l},o)=>(0,s.jsx)("div",{ref:o,className:(0,n.cn)("relative w-full h-48 bg-gradient-to-br from-[var(--primary-green)] to-[var(--secondary-sunset)] flex items-center justify-center text-white text-5xl",e),...l,children:t?(0,s.jsx)(i.default,{src:t,alt:r||"",fill:!0,className:"object-cover"}):a})).displayName="CardImage"},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},74953:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>p,pages:()=>d,routeModule:()=>h,tree:()=>c});var s=r(65239),a=r(48088),i=r(88170),n=r.n(i),l=r(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);r.d(t,o);let c={children:["",{children:["help",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,16635)),"/Users/<USER>/src/personal/bvr-safaris/src/app/help/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,82893)),"/Users/<USER>/src/personal/bvr-safaris/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["/Users/<USER>/src/personal/bvr-safaris/src/app/help/page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},h=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/help/page",pathname:"/help",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},78768:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(26373).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83853:(e,t,r)=>{"use strict";r.d(t,{$:()=>n});var s=r(37413),a=r(61120),i=r(10974);let n=(0,a.forwardRef)(({className:e,variant:t="primary",size:r="md",isLoading:a,children:n,disabled:l,...o},c)=>{let d=`
      inline-flex items-center justify-center rounded-[var(--radius-md)] 
      font-[var(--font-ui)] font-semibold transition-all duration-300 
      focus:outline-none focus:ring-2 focus:ring-offset-2
      disabled:opacity-50 disabled:cursor-not-allowed
      hover:transform hover:-translate-y-0.5 hover:shadow-[var(--shadow-lg)]
    `,p={primary:`
        bg-[var(--primary-brown)] text-white 
        hover:bg-[#A0522D] focus:ring-[var(--primary-brown)]
      `,secondary:`
        bg-[var(--secondary-sky)] text-white 
        hover:bg-[#357AE0] focus:ring-[var(--secondary-sky)]
      `,outline:`
        bg-transparent border-2 border-[var(--primary-brown)] text-[var(--primary-brown)]
        hover:bg-[var(--primary-brown)] hover:text-white focus:ring-[var(--primary-brown)]
      `,hunting:`
        bg-[var(--hunting-accent)] text-white 
        hover:bg-[#A67C00] focus:ring-[var(--hunting-accent)]
      `,photo:`
        bg-[var(--photo-accent)] text-white 
        hover:bg-[#3557C7] focus:ring-[var(--photo-accent)]
      `};return(0,s.jsxs)("button",{className:(0,i.cn)(d,p[t],{sm:"px-3 py-1.5 text-sm",md:"px-6 py-2 text-base",lg:"px-8 py-3 text-lg"}[r],a&&"cursor-wait",e),disabled:l||a,ref:c,...o,children:[a&&(0,s.jsxs)("svg",{className:"mr-2 h-4 w-4 animate-spin",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,s.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,s.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),n]})});n.displayName="Button"},90230:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(26373).A)("message-circle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]])},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,874,658,533,17,772,391],()=>r(74953));module.exports=s})();