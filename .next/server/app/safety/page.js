(()=>{var e={};e.id=437,e.ids=[437],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10974:(e,s,r)=>{"use strict";r.d(s,{cn:()=>i});var a=r(75986),t=r(8974);function i(...e){return(0,t.QP)((0,a.$)(e))}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19715:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>x,tree:()=>o});var a=r(65239),t=r(48088),i=r(88170),n=r.n(i),l=r(30893),c={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);r.d(s,c);let o={children:["",{children:["safety",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,37318)),"/Users/<USER>/src/personal/bvr-safaris/src/app/safety/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,82893)),"/Users/<USER>/src/personal/bvr-safaris/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["/Users/<USER>/src/personal/bvr-safaris/src/app/safety/page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:t.RouteKind.APP_PAGE,page:"/safety/page",pathname:"/safety",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},37318:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>f});var a=r(37413),t=r(26373);let i=(0,t.A)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]]),n=(0,t.A)("camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]]);var l=r(69117);let c=(0,t.A)("heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]]),o=(0,t.A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]),d=(0,t.A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);var m=r(71091);let x=[{icon:i,title:"Hunting Safety",color:"hunting-accent",guidelines:["Always treat firearms as if they are loaded","Never point a firearm at anything you do not intend to shoot","Keep your finger off the trigger until ready to shoot","Be sure of your target and what is beyond it","Wear appropriate safety gear including eye and ear protection","Follow all instructions from your professional hunter guide","Maintain proper firearm handling and storage protocols","Ensure you have valid hunting licenses and permits"]},{icon:n,title:"Photo Safari Safety",color:"photo-accent",guidelines:["Maintain safe distances from all wildlife","Never exit vehicles without guide permission","Follow all instructions from your guide immediately","Keep noise levels low to avoid disturbing animals","Secure all equipment to prevent dropping items","Wear neutral-colored clothing to blend with environment","Stay hydrated and protect yourself from sun exposure","Respect animal behavior and retreat if animals show stress"]}],p=[{icon:l.A,title:"General Precautions",items:["Inform someone of your travel plans and expected return","Carry emergency contact information at all times","Follow all farm rules and regulations strictly","Stay within designated areas unless accompanied by guides","Report any safety concerns immediately to farm management"]},{icon:c,title:"Health & Medical",items:["Ensure you have comprehensive travel and medical insurance","Bring any required medications and prescriptions","Inform guides of any medical conditions or allergies","Stay hydrated and protect yourself from sun exposure","Be aware of potential disease vectors (malaria, tick-borne diseases)"]},{icon:o,title:"Emergency Preparedness",items:["Know the location of first aid facilities","Understand emergency evacuation procedures","Keep emergency contact numbers readily available","Carry a charged mobile phone when possible","Follow all emergency instructions from guides and staff"]}],h=["Inspect all equipment before use","Use only equipment provided or approved by the farm","Report any damaged or malfunctioning equipment immediately","Follow proper maintenance and storage procedures","Ensure all safety equipment is properly fitted and functional"],u=["Never approach or attempt to feed wild animals","Maintain respectful distances as advised by guides","Move slowly and avoid sudden movements around animals","Never come between animals and their escape routes","Be especially cautious around mothers with young","Understand animal warning signs and behaviors","Follow guide instructions for animal encounters immediately"];function f(){return(0,a.jsxs)("div",{className:"min-h-screen bg-earth-100",children:[(0,a.jsx)("section",{className:"bg-gradient-to-r from-earth-600 to-earth-700 text-white py-16",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:[(0,a.jsx)(l.A,{className:"w-16 h-16 mx-auto mb-6 text-white"}),(0,a.jsx)("h1",{className:"text-4xl md:text-5xl font-bold mb-6",children:"Safety Guidelines"}),(0,a.jsx)("p",{className:"text-xl md:text-2xl opacity-90 max-w-3xl mx-auto",children:"Your safety is our top priority. Please review these important guidelines before your safari experience."})]})}),(0,a.jsx)("section",{className:"py-16",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-earth-900 mb-12 text-center",children:"Activity-Specific Safety"}),(0,a.jsx)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:x.map((e,s)=>(0,a.jsxs)(m.Zp,{className:"h-full",children:[(0,a.jsx)(m.aR,{children:(0,a.jsxs)("div",{className:"flex items-center mb-4",children:[(0,a.jsx)("div",{className:`w-12 h-12 bg-${e.color} bg-opacity-10 rounded-lg flex items-center justify-center mr-4`,children:(0,a.jsx)(e.icon,{className:`w-6 h-6 text-${e.color}`})}),(0,a.jsx)(m.ZB,{className:"text-2xl",children:e.title})]})}),(0,a.jsx)(m.Wu,{children:(0,a.jsx)("ul",{className:"space-y-3",children:e.guidelines.map((s,r)=>(0,a.jsxs)("li",{className:"flex items-start",children:[(0,a.jsx)("div",{className:`w-2 h-2 bg-${e.color} rounded-full mt-2 mr-3 flex-shrink-0`}),(0,a.jsx)("span",{className:"text-earth-700",children:s})]},r))})})]},s))})]})}),(0,a.jsx)("section",{className:"py-16 bg-white",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-earth-900 mb-12 text-center",children:"General Safety Guidelines"}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8",children:p.map((e,s)=>(0,a.jsxs)(m.Zp,{className:"h-full",children:[(0,a.jsx)(m.aR,{children:(0,a.jsxs)("div",{className:"flex items-center mb-4",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-accent-100 rounded-lg flex items-center justify-center mr-4",children:(0,a.jsx)(e.icon,{className:"w-6 h-6 text-accent-600"})}),(0,a.jsx)(m.ZB,{className:"text-xl",children:e.title})]})}),(0,a.jsx)(m.Wu,{children:(0,a.jsx)("ul",{className:"space-y-3",children:e.items.map((e,s)=>(0,a.jsxs)("li",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-accent-600 rounded-full mt-2 mr-3 flex-shrink-0"}),(0,a.jsx)("span",{className:"text-earth-700",children:e})]},s))})})]},s))})]})}),(0,a.jsx)("section",{className:"py-16 bg-earth-50",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"text-center mb-12",children:[(0,a.jsx)(d,{className:"w-16 h-16 mx-auto mb-6 text-accent-600"}),(0,a.jsx)("h2",{className:"text-3xl font-bold text-earth-900 mb-4",children:"Wildlife Safety"}),(0,a.jsx)("p",{className:"text-lg text-earth-700",children:"Respect for wildlife is essential for both your safety and conservation efforts."})]}),(0,a.jsx)(m.Zp,{children:(0,a.jsx)(m.Wu,{className:"p-8",children:(0,a.jsx)("ul",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:u.map((e,s)=>(0,a.jsxs)("li",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-accent-600 rounded-full mt-2 mr-3 flex-shrink-0"}),(0,a.jsx)("span",{className:"text-earth-700",children:e})]},s))})})})]})}),(0,a.jsx)("section",{className:"py-16",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"text-center mb-12",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-earth-900 mb-4",children:"Equipment Safety"}),(0,a.jsx)("p",{className:"text-lg text-earth-700",children:"Proper equipment handling is crucial for a safe safari experience."})]}),(0,a.jsx)(m.Zp,{children:(0,a.jsx)(m.Wu,{className:"p-8",children:(0,a.jsx)("ul",{className:"space-y-4",children:h.map((e,s)=>(0,a.jsxs)("li",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-accent-600 rounded-full mt-2 mr-3 flex-shrink-0"}),(0,a.jsx)("span",{className:"text-earth-700",children:e})]},s))})})})]})}),(0,a.jsx)("section",{className:"py-16 bg-red-50",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"text-center mb-8",children:[(0,a.jsx)(o,{className:"w-16 h-16 mx-auto mb-6 text-red-600"}),(0,a.jsx)("h2",{className:"text-3xl font-bold text-red-900 mb-4",children:"Emergency Information"})]}),(0,a.jsx)(m.Zp,{className:"border-red-200",children:(0,a.jsx)(m.Wu,{className:"p-8",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-red-900 mb-4",children:"Emergency Contacts"}),(0,a.jsxs)("div",{className:"space-y-2 text-earth-700",children:[(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Emergency Services:"})," 10111"]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Medical Emergency:"})," 10177"]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"BvR Safaris Emergency:"})," +27 (0)82 123 4567"]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Farm Emergency:"})," Contact your farm operator"]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-red-900 mb-4",children:"In Case of Emergency"}),(0,a.jsxs)("ol",{className:"list-decimal list-inside space-y-2 text-earth-700",children:[(0,a.jsx)("li",{children:"Ensure immediate safety of all persons"}),(0,a.jsx)("li",{children:"Contact emergency services if required"}),(0,a.jsx)("li",{children:"Notify your guide or farm management"}),(0,a.jsx)("li",{children:"Contact BvR Safaris emergency line"}),(0,a.jsx)("li",{children:"Follow all instructions from authorities"})]})]})]})})})]})}),(0,a.jsx)("section",{className:"py-8 bg-earth-700 text-white",children:(0,a.jsx)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:(0,a.jsxs)("p",{className:"text-sm opacity-90",children:[(0,a.jsx)("strong",{children:"Important:"})," These guidelines are for general information only. Always follow specific instructions from your farm operator and professional guides. Safari activities involve inherent risks, and participants engage at their own risk. Comprehensive insurance coverage is strongly recommended."]})})})]})}},37366:e=>{"use strict";e.exports=require("dns")},43969:(e,s,r)=>{Promise.resolve().then(r.t.bind(r,49603,23))},55511:e=>{"use strict";e.exports=require("crypto")},57121:(e,s,r)=>{Promise.resolve().then(r.t.bind(r,46533,23))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},69117:(e,s,r)=>{"use strict";r.d(s,{A:()=>a});let a=(0,r(26373).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},70440:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});var a=r(31658);let t=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},71091:(e,s,r)=>{"use strict";r.d(s,{Wu:()=>c,ZB:()=>d,Zp:()=>l,aR:()=>o});var a=r(37413),t=r(61120),i=r(53384),n=r(10974);let l=(0,t.forwardRef)(({className:e,hover:s=!0,children:r,...t},i)=>(0,a.jsx)("div",{ref:i,className:(0,n.cn)(`
          bg-white rounded-[var(--radius-lg)] shadow-[var(--shadow-md)] 
          overflow-hidden transition-all duration-300
          `,s&&"hover:transform hover:-translate-y-1 hover:shadow-[var(--shadow-xl)]",e),...t,children:r}));l.displayName="Card";let c=(0,t.forwardRef)(({className:e,...s},r)=>(0,a.jsx)("div",{ref:r,className:(0,n.cn)("p-[var(--space-lg)]",e),...s}));c.displayName="CardContent";let o=(0,t.forwardRef)(({className:e,...s},r)=>(0,a.jsx)("div",{ref:r,className:(0,n.cn)("px-[var(--space-lg)] pt-[var(--space-lg)]",e),...s}));o.displayName="CardHeader";let d=(0,t.forwardRef)(({className:e,...s},r)=>(0,a.jsx)("h3",{ref:r,className:(0,n.cn)("text-xl font-bold mb-2 text-[var(--primary-brown)] font-[var(--font-display)]",e),...s}));d.displayName="CardTitle",(0,t.forwardRef)(({className:e,src:s,alt:r,children:t,...l},c)=>(0,a.jsx)("div",{ref:c,className:(0,n.cn)("relative w-full h-48 bg-gradient-to-br from-[var(--primary-green)] to-[var(--secondary-sunset)] flex items-center justify-center text-white text-5xl",e),...l,children:s?(0,a.jsx)(i.default,{src:s,alt:r||"",fill:!0,className:"object-cover"}):t})).displayName="CardImage"},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),a=s.X(0,[447,874,658,533,17,772,391],()=>r(19715));module.exports=a})();