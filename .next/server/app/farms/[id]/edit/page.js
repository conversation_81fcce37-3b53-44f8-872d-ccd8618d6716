(()=>{var e={};e.id=239,e.ids=[239],e.modules={474:(e,t,r)=>{"use strict";r.d(t,{C2:()=>c,QQ:()=>n,eg:()=>l,zi:()=>o});var a=r(75535),s=r(50944);function i(e){if(!e.exists())return null;let t=e.data();return{id:e.id,...t,createdAt:t?.createdAt?.toDate?.()||t?.createdAt,updatedAt:t?.updatedAt?.toDate?.()||t?.updatedAt}}let n={async get(e){let t=(0,a.H9)(s.db,"users",e);return i(await (0,a.x7)(t))},async create(e,t){let r=(0,a.H9)(s.db,"users",e),i=new Date;await (0,a.BN)(r,{...t,createdAt:a.Dc.fromDate(i),updatedAt:a.Dc.fromDate(i)})},async update(e,t){let r=(0,a.H9)(s.db,"users",e);await (0,a.mZ)(r,{...t,updatedAt:a.Dc.now()})}},l={async getAll(e){let t=(0,a.rJ)(s.db,"farms");return e?.isActive!==void 0&&(t=(0,a.P)(t,(0,a._M)("isActive","==",e.isActive))),e?.featured!==void 0&&(t=(0,a.P)(t,(0,a._M)("featured","==",e.featured))),e?.province&&(t=(0,a.P)(t,(0,a._M)("province","==",e.province))),e?.activityType&&(t=(0,a.P)(t,(0,a._M)("activityTypes","==",e.activityType))),t=(0,a.P)(t,(0,a.My)("createdAt","desc")),e?.limit&&(t=(0,a.P)(t,(0,a.AB)(e.limit))),(await (0,a.GG)(t)).docs.map(e=>i(e)).filter(Boolean)},async get(e){let t=(0,a.H9)(s.db,"farms",e);return i(await (0,a.x7)(t))},async getByOwner(e){let t=(0,a.P)((0,a.rJ)(s.db,"farms"),(0,a._M)("ownerId","==",e),(0,a.My)("createdAt","desc"));return(await (0,a.GG)(t)).docs.map(e=>i(e)).filter(Boolean)},async getActive(e){let t=(0,a.P)((0,a.rJ)(s.db,"farms"),(0,a._M)("isActive","==",!0),(0,a.My)("createdAt","desc"));return e&&(t=(0,a.P)(t,(0,a.AB)(e))),(await (0,a.GG)(t)).docs.map(e=>i(e)).filter(Boolean)},async create(e){let t=new Date;return(await (0,a.gS)((0,a.rJ)(s.db,"farms"),{...e,createdAt:a.Dc.fromDate(t),updatedAt:a.Dc.fromDate(t)})).id},async update(e,t){let r=(0,a.H9)(s.db,"farms",e);await (0,a.mZ)(r,{...t,updatedAt:a.Dc.now()})},async delete(e){let t=(0,a.H9)(s.db,"farms",e);await (0,a.kd)(t)},async addImage(e,t){let r=new Date;return(await (0,a.gS)((0,a.rJ)(s.db,"farms",e,"images"),{...t,createdAt:a.Dc.fromDate(r),updatedAt:a.Dc.fromDate(r)})).id},async getImages(e){let t=(0,a.P)((0,a.rJ)(s.db,"farms",e,"images"),(0,a.My)("displayOrder","asc"),(0,a.My)("createdAt","asc"));return(await (0,a.GG)(t)).docs.map(e=>i(e)).filter(Boolean)},async deleteImage(e,t){let r=(0,a.H9)(s.db,"farms",e,"images",t);await (0,a.kd)(r)}},o={async getAll(e){let t=(0,a.rJ)(s.db,"bookings");return e?.hunterId&&(t=(0,a.P)(t,(0,a._M)("hunterId","==",e.hunterId))),e?.farmId&&(t=(0,a.P)(t,(0,a._M)("farmId","==",e.farmId))),e?.status&&(t=(0,a.P)(t,(0,a._M)("status","==",e.status))),t=(0,a.P)(t,(0,a.My)("createdAt","desc")),e?.limit&&(t=(0,a.P)(t,(0,a.AB)(e.limit))),(await (0,a.GG)(t)).docs.map(e=>i(e)).filter(Boolean)},async get(e){let t=(0,a.H9)(s.db,"bookings",e);return i(await (0,a.x7)(t))},async create(e){let t=new Date,r=`BVR-${t.getFullYear()}${(t.getMonth()+1).toString().padStart(2,"0")}${t.getDate().toString().padStart(2,"0")}-${Math.random().toString(36).substring(2,8).toUpperCase()}`;return(await (0,a.gS)((0,a.rJ)(s.db,"bookings"),{...e,bookingReference:r,createdAt:a.Dc.fromDate(t),updatedAt:a.Dc.fromDate(t)})).id},async update(e,t){let r=(0,a.H9)(s.db,"bookings",e);await (0,a.mZ)(r,{...t,updatedAt:a.Dc.now()})}},c={async getByFarm(e){let t=(0,a.P)((0,a.rJ)(s.db,"farms",e,"reviews"),(0,a._M)("isPublic","==",!0),(0,a.My)("createdAt","desc"));return(await (0,a.GG)(t)).docs.map(e=>i(e)).filter(Boolean)},async create(e,t){let r=new Date;return(await (0,a.gS)((0,a.rJ)(s.db,"farms",e,"reviews"),{...t,createdAt:a.Dc.fromDate(r),updatedAt:a.Dc.fromDate(r)})).id},async update(e,t,r){let i=(0,a.H9)(s.db,"farms",e,"reviews",t);await (0,a.mZ)(i,{...r,updatedAt:a.Dc.now()})}}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13861:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28559:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},28749:(e,t,r)=>{"use strict";r.d(t,{MH:()=>u,Wu:()=>o,ZB:()=>d,Zp:()=>l,aR:()=>c});var a=r(60687),s=r(43210),i=r(30474),n=r(4780);let l=(0,s.forwardRef)(({className:e,hover:t=!0,children:r,...s},i)=>(0,a.jsx)("div",{ref:i,className:(0,n.cn)(`
          bg-white rounded-[var(--radius-lg)] shadow-[var(--shadow-md)] 
          overflow-hidden transition-all duration-300
          `,t&&"hover:transform hover:-translate-y-1 hover:shadow-[var(--shadow-xl)]",e),...s,children:r}));l.displayName="Card";let o=(0,s.forwardRef)(({className:e,...t},r)=>(0,a.jsx)("div",{ref:r,className:(0,n.cn)("p-[var(--space-lg)]",e),...t}));o.displayName="CardContent";let c=(0,s.forwardRef)(({className:e,...t},r)=>(0,a.jsx)("div",{ref:r,className:(0,n.cn)("px-[var(--space-lg)] pt-[var(--space-lg)]",e),...t}));c.displayName="CardHeader";let d=(0,s.forwardRef)(({className:e,...t},r)=>(0,a.jsx)("h3",{ref:r,className:(0,n.cn)("text-xl font-bold mb-2 text-[var(--primary-brown)] font-[var(--font-display)]",e),...t}));d.displayName="CardTitle";let u=(0,s.forwardRef)(({className:e,src:t,alt:r,children:s,...l},o)=>(0,a.jsx)("div",{ref:o,className:(0,n.cn)("relative w-full h-48 bg-gradient-to-br from-[var(--primary-green)] to-[var(--secondary-sunset)] flex items-center justify-center text-white text-5xl",e),...l,children:t?(0,a.jsx)(i.default,{src:t,alt:r||"",fill:!0,className:"object-cover"}):s}));u.displayName="CardImage"},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30474:(e,t,r)=>{"use strict";r.d(t,{default:()=>s.a});var a=r(31261),s=r.n(a)},31261:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return o},getImageProps:function(){return l}});let a=r(14985),s=r(44953),i=r(46533),n=a._(r(1933));function l(e){let{props:t}=(0,s.getImgProps)(e,{defaultLoader:n.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,r]of Object.entries(t))void 0===r&&delete t[e];return{props:t}}let o=i.Image},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},37366:e=>{"use strict";e.exports=require("dns")},41351:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c});var a=r(65239),s=r(48088),i=r(88170),n=r.n(i),l=r(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);r.d(t,o);let c={children:["",{children:["farms",{children:["[id]",{children:["edit",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,58033)),"/Users/<USER>/src/personal/bvr-safaris/src/app/farms/[id]/edit/page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,82893)),"/Users/<USER>/src/personal/bvr-safaris/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["/Users/<USER>/src/personal/bvr-safaris/src/app/farms/[id]/edit/page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/farms/[id]/edit/page",pathname:"/farms/[id]/edit",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},50358:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>b});var a=r(60687),s=r(43210),i=r(16189),n=r(2643),l=r(51907),o=r(28749),c=r(71170),d=r(474),u=r(57445),m=r(66420),p=r(28559),h=r(13861);let f=(0,r(62688).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]]);var x=r(85814),v=r.n(x);function b(){let{user:e,loading:t}=(0,u.As)(),r=(0,i.useRouter)(),x=(0,i.useParams)().id,[b,g]=(0,s.useState)(null),[y,j]=(0,s.useState)(!0),[w,N]=(0,s.useState)(!1),[A,k]=(0,s.useState)(null),[P,C]=(0,s.useState)(null),[D,_]=(0,s.useState)({name:"",description:"",descriptionAfrikaans:"",location:"",province:"",sizeHectares:"",activityTypes:"both",contactEmail:"",contactPhone:"",websiteUrl:"",rules:"",rulesAfrikaans:"",pricingInfo:"",isActive:!0,featured:!1}),S=(0,s.useCallback)(async()=>{try{j(!0),k(null);let t=await d.eg.get(x);if(!t)throw Error("Farm not found");if(t.ownerId!==e.uid)throw Error("Access denied - you do not own this farm");g(t),_({name:t.name,description:t.description||"",descriptionAfrikaans:t.descriptionAfrikaans||"",location:t.location,province:t.province,sizeHectares:t.sizeHectares?.toString()||"",activityTypes:t.activityTypes,contactEmail:t.contactEmail,contactPhone:t.contactPhone||"",websiteUrl:t.websiteUrl||"",rules:t.rules||"",rulesAfrikaans:t.rulesAfrikaans||"",pricingInfo:t.pricingInfo||"",isActive:t.isActive,featured:t.featured})}catch(e){console.error("Error fetching farm:",e),k("Failed to load farm details")}finally{j(!1)}},[x,e]),I=(e,t)=>{_(r=>({...r,[e]:t})),C(null)},M=async t=>{if(t.preventDefault(),e&&b)try{N(!0),k(null);let e={name:D.name,description:D.description||void 0,descriptionAfrikaans:D.descriptionAfrikaans||void 0,location:D.location,province:D.province,sizeHectares:D.sizeHectares?parseInt(D.sizeHectares):void 0,activityTypes:D.activityTypes,contactEmail:D.contactEmail,contactPhone:D.contactPhone||void 0,websiteUrl:D.websiteUrl||void 0,rules:D.rules||void 0,rulesAfrikaans:D.rulesAfrikaans||void 0,pricingInfo:D.pricingInfo||void 0,isActive:D.isActive,featured:D.featured};await d.eg.update(x,e),C("Farm details updated successfully!"),await S()}catch(e){console.error("Error updating farm:",e),k("Failed to update farm details")}finally{N(!1)}};return t||y?(0,a.jsx)("div",{className:"min-h-screen bg-earth-100 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-accent-600 mx-auto"}),(0,a.jsx)("p",{className:"mt-4 text-earth-600",children:"Loading farm details..."})]})}):e?A&&!b?(0,a.jsx)("div",{className:"min-h-screen bg-earth-100 py-8",children:(0,a.jsx)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-earth-900 mb-4",children:"Farm Not Found"}),(0,a.jsx)("p",{className:"text-earth-600 mb-6",children:A}),(0,a.jsx)(v(),{href:"/dashboard",children:(0,a.jsxs)(n.$,{variant:"primary",children:[(0,a.jsx)(p.A,{className:"w-4 h-4 mr-2"}),"Back to Dashboard"]})})]})})}):(0,a.jsx)("div",{className:"min-h-screen bg-earth-100 py-8",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsxs)("div",{className:"flex items-center gap-4 mb-4",children:[(0,a.jsx)(v(),{href:"/dashboard",children:(0,a.jsxs)(n.$,{variant:"outline",size:"sm",children:[(0,a.jsx)(p.A,{className:"w-4 h-4 mr-2"}),"Back to Dashboard"]})}),(0,a.jsx)(v(),{href:`/farms/${x}`,children:(0,a.jsxs)(n.$,{variant:"outline",size:"sm",children:[(0,a.jsx)(h.A,{className:"w-4 h-4 mr-2"}),"View Public Page"]})})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-earth-900",children:"Edit Farm Listing"}),(0,a.jsx)("p",{className:"text-earth-600 mt-2",children:"Update your farm details and settings"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(c.E,{variant:D.isActive?"hunting":"default",children:D.isActive?"Active":"Inactive"}),D.featured&&(0,a.jsx)(c.E,{variant:"photo",children:"Featured"})]})]})]}),(0,a.jsxs)("form",{onSubmit:M,className:"space-y-8",children:[A&&(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-md p-3",children:(0,a.jsx)("p",{className:"text-red-600 text-sm",children:A})}),P&&(0,a.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-md p-3",children:(0,a.jsx)("p",{className:"text-green-600 text-sm",children:P})}),(0,a.jsxs)(o.Zp,{children:[(0,a.jsx)(o.aR,{children:(0,a.jsx)(o.ZB,{children:"Basic Information"})}),(0,a.jsxs)(o.Wu,{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-earth-700 mb-2",children:"Farm Name *"}),(0,a.jsx)(l.p,{id:"name",type:"text",value:D.name,onChange:e=>I("name",e.target.value),placeholder:"Enter your farm name",required:!0})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"location",className:"block text-sm font-medium text-earth-700 mb-2",children:"Location/City *"}),(0,a.jsx)(l.p,{id:"location",type:"text",value:D.location,onChange:e=>I("location",e.target.value),placeholder:"e.g., Lephalale",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"province",className:"block text-sm font-medium text-earth-700 mb-2",children:"Province *"}),(0,a.jsxs)("select",{id:"province",value:D.province,onChange:e=>I("province",e.target.value),className:"w-full px-3 py-2 border border-earth-300 rounded-md focus:outline-none focus:ring-2 focus:ring-accent-600 focus:border-transparent",required:!0,children:[(0,a.jsx)("option",{value:"",children:"Select Province"}),m.L0.map(e=>(0,a.jsx)("option",{value:e,children:e},e))]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"sizeHectares",className:"block text-sm font-medium text-earth-700 mb-2",children:"Farm Size (Hectares)"}),(0,a.jsx)(l.p,{id:"sizeHectares",type:"number",value:D.sizeHectares,onChange:e=>I("sizeHectares",e.target.value),placeholder:"e.g., 5000"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"activityTypes",className:"block text-sm font-medium text-earth-700 mb-2",children:"Activity Types *"}),(0,a.jsxs)("select",{id:"activityTypes",value:D.activityTypes,onChange:e=>I("activityTypes",e.target.value),className:"w-full px-3 py-2 border border-earth-300 rounded-md focus:outline-none focus:ring-2 focus:ring-accent-600 focus:border-transparent",required:!0,children:[(0,a.jsx)("option",{value:"hunting",children:"Hunting Only"}),(0,a.jsx)("option",{value:"photo_safari",children:"Photo Safari Only"}),(0,a.jsx)("option",{value:"both",children:"Both Hunting & Photo Safari"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"description",className:"block text-sm font-medium text-earth-700 mb-2",children:"Description"}),(0,a.jsx)("textarea",{id:"description",value:D.description,onChange:e=>I("description",e.target.value),placeholder:"Describe your farm, facilities, and what makes it special...",rows:4,className:"w-full px-3 py-2 border border-earth-300 rounded-md focus:outline-none focus:ring-2 focus:ring-accent-600 focus:border-transparent"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"pricingInfo",className:"block text-sm font-medium text-earth-700 mb-2",children:"Pricing Information"}),(0,a.jsx)(l.p,{id:"pricingInfo",type:"text",value:D.pricingInfo,onChange:e=>I("pricingInfo",e.target.value),placeholder:"e.g., From R2,500 per day"})]})]})]}),(0,a.jsxs)(o.Zp,{children:[(0,a.jsx)(o.aR,{children:(0,a.jsx)(o.ZB,{children:"Contact Information"})}),(0,a.jsxs)(o.Wu,{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"contactEmail",className:"block text-sm font-medium text-earth-700 mb-2",children:"Contact Email *"}),(0,a.jsx)(l.p,{id:"contactEmail",type:"email",value:D.contactEmail,onChange:e=>I("contactEmail",e.target.value),placeholder:"<EMAIL>",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"contactPhone",className:"block text-sm font-medium text-earth-700 mb-2",children:"Contact Phone"}),(0,a.jsx)(l.p,{id:"contactPhone",type:"tel",value:D.contactPhone,onChange:e=>I("contactPhone",e.target.value),placeholder:"+27 12 345 6789"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"websiteUrl",className:"block text-sm font-medium text-earth-700 mb-2",children:"Website URL"}),(0,a.jsx)(l.p,{id:"websiteUrl",type:"text",value:D.websiteUrl,onChange:e=>I("websiteUrl",e.target.value),placeholder:"www.yourfarm.com"})]})]})]}),(0,a.jsxs)(o.Zp,{children:[(0,a.jsx)(o.aR,{children:(0,a.jsx)(o.ZB,{children:"Farm Settings"})}),(0,a.jsxs)(o.Wu,{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-earth-700",children:"Active Status"}),(0,a.jsx)("p",{className:"text-sm text-earth-600",children:"Control whether your farm is visible to customers"})]}),(0,a.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:D.isActive,onChange:e=>I("isActive",e.target.checked),className:"sr-only peer"}),(0,a.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-accent-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-accent-600"})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-earth-700",children:"Featured Listing"}),(0,a.jsx)("p",{className:"text-sm text-earth-600",children:"Featured farms appear at the top of search results"})]}),(0,a.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:D.featured,onChange:e=>I("featured",e.target.checked),className:"sr-only peer"}),(0,a.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-accent-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-accent-600"})]})]})]})]}),(0,a.jsxs)("div",{className:"flex justify-end gap-4",children:[(0,a.jsx)(v(),{href:"/dashboard",children:(0,a.jsx)(n.$,{variant:"outline",type:"button",children:"Cancel"})}),(0,a.jsx)(n.$,{type:"submit",variant:"primary",disabled:w,className:"min-w-[120px]",children:w?(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Saving..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(f,{className:"w-4 h-4 mr-2"}),"Save Changes"]})})]})]})]})}):(r.push("/auth/login"),null)}},51907:(e,t,r)=>{"use strict";r.d(t,{p:()=>n});var a=r(60687),s=r(43210),i=r(4780);let n=(0,s.forwardRef)(({className:e,type:t,label:r,error:s,...n},l)=>(0,a.jsxs)("div",{className:"w-full",children:[r&&(0,a.jsx)("label",{className:"block mb-2 font-semibold text-[var(--primary-brown)] font-[var(--font-ui)]",children:r}),(0,a.jsx)("input",{type:t,className:(0,i.cn)(`
            w-full px-4 py-2 border-2 border-[var(--medium-gray)] 
            rounded-[var(--radius-md)] font-[var(--font-ui)] 
            transition-colors duration-300
            focus:outline-none focus:border-[var(--primary-brown)] 
            focus:ring-0 focus:shadow-[0_0_0_3px_rgba(139,69,19,0.1)]
            placeholder:text-[var(--medium-gray)]
            `,s&&"border-red-500 focus:border-red-500",e),ref:l,...n}),s&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600",children:s})]}));n.displayName="Input"},55511:e=>{"use strict";e.exports=require("crypto")},58033:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/src/personal/bvr-safaris/src/app/farms/[id]/edit/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/src/personal/bvr-safaris/src/app/farms/[id]/edit/page.tsx","default")},62688:(e,t,r)=>{"use strict";r.d(t,{A:()=>u});var a=r(43210);let s=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),n=e=>{let t=i(e);return t.charAt(0).toUpperCase()+t.slice(1)},l=(...e)=>e.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim(),o=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,a.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:s,className:i="",children:n,iconNode:d,...u},m)=>(0,a.createElement)("svg",{ref:m,...c,width:t,height:t,stroke:e,strokeWidth:s?24*Number(r)/Number(t):r,className:l("lucide",i),...!n&&!o(u)&&{"aria-hidden":"true"},...u},[...d.map(([e,t])=>(0,a.createElement)(e,t)),...Array.isArray(n)?n:[n]])),u=(e,t)=>{let r=(0,a.forwardRef)(({className:r,...i},o)=>(0,a.createElement)(d,{ref:o,iconNode:t,className:l(`lucide-${s(n(e))}`,`lucide-${e}`,r),...i}));return r.displayName=n(e),r}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66420:(e,t,r)=>{"use strict";r.d(t,{L0:()=>a,QC:()=>i,lc:()=>s});let a=["Eastern Cape","Free State","Gauteng","KwaZulu-Natal","Limpopo","Mpumalanga","Northern Cape","North West","Western Cape"],s=["Luxury Lodge","Basic Accommodation","Restaurant","Bar","Swimming Pool","Spa","WiFi","Airport Transfer","Professional Guide","Trophy Preparation","Taxidermy"],i=["Lion","Leopard","Elephant","Buffalo","Rhino","Kudu","Impala","Springbok","Eland","Sable","Gemsbok","Waterbuck","Bushbuck","Warthog"]},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});var a=r(31658);let s=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},71170:(e,t,r)=>{"use strict";r.d(t,{E:()=>n});var a=r(60687),s=r(43210),i=r(4780);let n=(0,s.forwardRef)(({className:e,variant:t="default",...r},s)=>(0,a.jsx)("span",{ref:s,className:(0,i.cn)("inline-flex items-center px-2 py-1 rounded-[var(--radius-sm)] text-sm font-semibold",{hunting:"bg-[var(--hunting-accent)] text-white",photo:"bg-[var(--photo-accent)] text-white",location:"bg-[var(--secondary-stone)] text-white",default:"bg-[var(--medium-gray)] text-white"}[t],e),...r}));n.displayName="Badge"},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91403:(e,t,r)=>{Promise.resolve().then(r.bind(r,50358))},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96979:(e,t,r)=>{Promise.resolve().then(r.bind(r,58033))}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[447,874,658,533,391],()=>r(41351));module.exports=a})();