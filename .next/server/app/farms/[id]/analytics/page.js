(()=>{var t={};t.id=673,t.ids=[673],t.modules={22:(t,e,r)=>{var n=r(75254),o=r(20623),i=r(48169),a=r(40542),c=r(45058);t.exports=function(t){return"function"==typeof t?t:null==t?i:"object"==typeof t?a(t)?o(t[0],t[1]):n(t):c(t)}},474:(t,e,r)=>{"use strict";r.d(e,{C2:()=>l,QQ:()=>a,eg:()=>c,zi:()=>u});var n=r(75535),o=r(50944);function i(t){if(!t.exists())return null;let e=t.data();return{id:t.id,...e,createdAt:e?.createdAt?.toDate?.()||e?.createdAt,updatedAt:e?.updatedAt?.toDate?.()||e?.updatedAt}}let a={async get(t){let e=(0,n.H9)(o.db,"users",t);return i(await (0,n.x7)(e))},async create(t,e){let r=(0,n.H9)(o.db,"users",t),i=new Date;await (0,n.BN)(r,{...e,createdAt:n.Dc.fromDate(i),updatedAt:n.Dc.fromDate(i)})},async update(t,e){let r=(0,n.H9)(o.db,"users",t);await (0,n.mZ)(r,{...e,updatedAt:n.Dc.now()})}},c={async getAll(t){let e=(0,n.rJ)(o.db,"farms");return t?.isActive!==void 0&&(e=(0,n.P)(e,(0,n._M)("isActive","==",t.isActive))),t?.featured!==void 0&&(e=(0,n.P)(e,(0,n._M)("featured","==",t.featured))),t?.province&&(e=(0,n.P)(e,(0,n._M)("province","==",t.province))),t?.activityType&&(e=(0,n.P)(e,(0,n._M)("activityTypes","==",t.activityType))),e=(0,n.P)(e,(0,n.My)("createdAt","desc")),t?.limit&&(e=(0,n.P)(e,(0,n.AB)(t.limit))),(await (0,n.GG)(e)).docs.map(t=>i(t)).filter(Boolean)},async get(t){let e=(0,n.H9)(o.db,"farms",t);return i(await (0,n.x7)(e))},async getByOwner(t){let e=(0,n.P)((0,n.rJ)(o.db,"farms"),(0,n._M)("ownerId","==",t),(0,n.My)("createdAt","desc"));return(await (0,n.GG)(e)).docs.map(t=>i(t)).filter(Boolean)},async getActive(t){let e=(0,n.P)((0,n.rJ)(o.db,"farms"),(0,n._M)("isActive","==",!0),(0,n.My)("createdAt","desc"));return t&&(e=(0,n.P)(e,(0,n.AB)(t))),(await (0,n.GG)(e)).docs.map(t=>i(t)).filter(Boolean)},async create(t){let e=new Date;return(await (0,n.gS)((0,n.rJ)(o.db,"farms"),{...t,createdAt:n.Dc.fromDate(e),updatedAt:n.Dc.fromDate(e)})).id},async update(t,e){let r=(0,n.H9)(o.db,"farms",t);await (0,n.mZ)(r,{...e,updatedAt:n.Dc.now()})},async delete(t){let e=(0,n.H9)(o.db,"farms",t);await (0,n.kd)(e)},async addImage(t,e){let r=new Date;return(await (0,n.gS)((0,n.rJ)(o.db,"farms",t,"images"),{...e,createdAt:n.Dc.fromDate(r),updatedAt:n.Dc.fromDate(r)})).id},async getImages(t){let e=(0,n.P)((0,n.rJ)(o.db,"farms",t,"images"),(0,n.My)("displayOrder","asc"),(0,n.My)("createdAt","asc"));return(await (0,n.GG)(e)).docs.map(t=>i(t)).filter(Boolean)},async deleteImage(t,e){let r=(0,n.H9)(o.db,"farms",t,"images",e);await (0,n.kd)(r)}},u={async getAll(t){let e=(0,n.rJ)(o.db,"bookings");return t?.hunterId&&(e=(0,n.P)(e,(0,n._M)("hunterId","==",t.hunterId))),t?.farmId&&(e=(0,n.P)(e,(0,n._M)("farmId","==",t.farmId))),t?.status&&(e=(0,n.P)(e,(0,n._M)("status","==",t.status))),e=(0,n.P)(e,(0,n.My)("createdAt","desc")),t?.limit&&(e=(0,n.P)(e,(0,n.AB)(t.limit))),(await (0,n.GG)(e)).docs.map(t=>i(t)).filter(Boolean)},async get(t){let e=(0,n.H9)(o.db,"bookings",t);return i(await (0,n.x7)(e))},async create(t){let e=new Date,r=`BVR-${e.getFullYear()}${(e.getMonth()+1).toString().padStart(2,"0")}${e.getDate().toString().padStart(2,"0")}-${Math.random().toString(36).substring(2,8).toUpperCase()}`;return(await (0,n.gS)((0,n.rJ)(o.db,"bookings"),{...t,bookingReference:r,createdAt:n.Dc.fromDate(e),updatedAt:n.Dc.fromDate(e)})).id},async update(t,e){let r=(0,n.H9)(o.db,"bookings",t);await (0,n.mZ)(r,{...e,updatedAt:n.Dc.now()})}},l={async getByFarm(t){let e=(0,n.P)((0,n.rJ)(o.db,"farms",t,"reviews"),(0,n._M)("isPublic","==",!0),(0,n.My)("createdAt","desc"));return(await (0,n.GG)(e)).docs.map(t=>i(t)).filter(Boolean)},async create(t,e){let r=new Date;return(await (0,n.gS)((0,n.rJ)(o.db,"farms",t,"reviews"),{...e,createdAt:n.Dc.fromDate(r),updatedAt:n.Dc.fromDate(r)})).id},async update(t,e,r){let i=(0,n.H9)(o.db,"farms",t,"reviews",e);await (0,n.mZ)(i,{...r,updatedAt:n.Dc.now()})}}},658:(t,e,r)=>{t.exports=r(41547)(r(85718),"Map")},1566:(t,e,r)=>{var n=r(89167),o=r(658),i=r(30401),a=r(34772),c=r(17830),u=r(29395),l=r(12290),s="[object Map]",f="[object Promise]",p="[object Set]",h="[object WeakMap]",d="[object DataView]",y=l(n),v=l(o),m=l(i),b=l(a),g=l(c),x=u;(n&&x(new n(new ArrayBuffer(1)))!=d||o&&x(new o)!=s||i&&x(i.resolve())!=f||a&&x(new a)!=p||c&&x(new c)!=h)&&(x=function(t){var e=u(t),r="[object Object]"==e?t.constructor:void 0,n=r?l(r):"";if(n)switch(n){case y:return d;case v:return s;case m:return f;case b:return p;case g:return h}return e}),t.exports=x},1707:(t,e,r)=>{var n=r(35142),o=r(46436);t.exports=function(t,e){e=n(e,t);for(var r=0,i=e.length;null!=t&&r<i;)t=t[o(e[r++])];return r&&r==i?t:void 0}},1944:t=>{t.exports=function(){return!1}},2408:t=>{t.exports=function(t){var e=-1,r=Array(t.size);return t.forEach(function(t){r[++e]=t}),r}},2896:(t,e,r)=>{var n=r(81488),o=r(59467);t.exports=function(t,e){return null!=t&&o(t,e,n)}},2984:(t,e,r)=>{var n=r(49227);t.exports=function(t,e,r){for(var o=-1,i=t.length;++o<i;){var a=t[o],c=e(a);if(null!=c&&(void 0===u?c==c&&!n(c):r(c,u)))var u=c,l=a}return l}},3105:t=>{t.exports=function(t){return t.split("")}},3295:t=>{"use strict";t.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4999:(t,e,r)=>{t.exports=r(85718).Uint8Array},5231:(t,e,r)=>{var n=r(29395),o=r(55048);t.exports=function(t){if(!o(t))return!1;var e=n(t);return"[object Function]"==e||"[object GeneratorFunction]"==e||"[object AsyncFunction]"==e||"[object Proxy]"==e}},5359:t=>{t.exports=function(t){var e=null==t?0:t.length;return e?t[e-1]:void 0}},5566:(t,e,r)=>{var n=r(41011),o=r(34117),i=r(66713),a=r(42403);t.exports=function(t){return function(e){var r=o(e=a(e))?i(e):void 0,c=r?r[0]:e.charAt(0),u=r?n(r,1).join(""):e.slice(1);return c[t]()+u}}},6053:t=>{var e=/\s/;t.exports=function(t){for(var r=t.length;r--&&e.test(t.charAt(r)););return r}},6330:t=>{t.exports=function(){return[]}},7383:(t,e,r)=>{var n=r(67009),o=r(32269),i=r(38428),a=r(55048);t.exports=function(t,e,r){if(!a(r))return!1;var c=typeof e;return("number"==c?!!(o(r)&&i(e,r.length)):"string"==c&&e in r)&&n(r[e],t)}},7651:(t,e,r)=>{var n=r(82038),o=r(52931),i=r(32269);t.exports=function(t){return i(t)?n(t):o(t)}},8336:(t,e,r)=>{var n=r(45803);t.exports=function(t,e){var r=t.__data__;return n(e)?r["string"==typeof e?"string":"hash"]:r.map}},8852:(t,e,r)=>{var n=r(1707);t.exports=function(t){return function(e){return n(e,t)}}},10034:(t,e,r)=>{var n=r(2984),o=r(22),i=r(46063);t.exports=function(t,e){return t&&t.length?n(t,o(e,2),i):void 0}},10090:(t,e,r)=>{var n=r(80458),o=r(89624),i=r(47282),a=i&&i.isTypedArray;t.exports=a?o(a):n},10653:(t,e,r)=>{var n=r(21456),o=r(63979),i=r(7651);t.exports=function(t){return n(t,i,o)}},10663:t=>{t.exports="object"==typeof global&&global&&global.Object===Object&&global},10846:t=>{"use strict";t.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11117:t=>{"use strict";var e=Object.prototype.hasOwnProperty,r="~";function n(){}function o(t,e,r){this.fn=t,this.context=e,this.once=r||!1}function i(t,e,n,i,a){if("function"!=typeof n)throw TypeError("The listener must be a function");var c=new o(n,i||t,a),u=r?r+e:e;return t._events[u]?t._events[u].fn?t._events[u]=[t._events[u],c]:t._events[u].push(c):(t._events[u]=c,t._eventsCount++),t}function a(t,e){0==--t._eventsCount?t._events=new n:delete t._events[e]}function c(){this._events=new n,this._eventsCount=0}Object.create&&(n.prototype=Object.create(null),new n().__proto__||(r=!1)),c.prototype.eventNames=function(){var t,n,o=[];if(0===this._eventsCount)return o;for(n in t=this._events)e.call(t,n)&&o.push(r?n.slice(1):n);return Object.getOwnPropertySymbols?o.concat(Object.getOwnPropertySymbols(t)):o},c.prototype.listeners=function(t){var e=r?r+t:t,n=this._events[e];if(!n)return[];if(n.fn)return[n.fn];for(var o=0,i=n.length,a=Array(i);o<i;o++)a[o]=n[o].fn;return a},c.prototype.listenerCount=function(t){var e=r?r+t:t,n=this._events[e];return n?n.fn?1:n.length:0},c.prototype.emit=function(t,e,n,o,i,a){var c=r?r+t:t;if(!this._events[c])return!1;var u,l,s=this._events[c],f=arguments.length;if(s.fn){switch(s.once&&this.removeListener(t,s.fn,void 0,!0),f){case 1:return s.fn.call(s.context),!0;case 2:return s.fn.call(s.context,e),!0;case 3:return s.fn.call(s.context,e,n),!0;case 4:return s.fn.call(s.context,e,n,o),!0;case 5:return s.fn.call(s.context,e,n,o,i),!0;case 6:return s.fn.call(s.context,e,n,o,i,a),!0}for(l=1,u=Array(f-1);l<f;l++)u[l-1]=arguments[l];s.fn.apply(s.context,u)}else{var p,h=s.length;for(l=0;l<h;l++)switch(s[l].once&&this.removeListener(t,s[l].fn,void 0,!0),f){case 1:s[l].fn.call(s[l].context);break;case 2:s[l].fn.call(s[l].context,e);break;case 3:s[l].fn.call(s[l].context,e,n);break;case 4:s[l].fn.call(s[l].context,e,n,o);break;default:if(!u)for(p=1,u=Array(f-1);p<f;p++)u[p-1]=arguments[p];s[l].fn.apply(s[l].context,u)}}return!0},c.prototype.on=function(t,e,r){return i(this,t,e,r,!1)},c.prototype.once=function(t,e,r){return i(this,t,e,r,!0)},c.prototype.removeListener=function(t,e,n,o){var i=r?r+t:t;if(!this._events[i])return this;if(!e)return a(this,i),this;var c=this._events[i];if(c.fn)c.fn!==e||o&&!c.once||n&&c.context!==n||a(this,i);else{for(var u=0,l=[],s=c.length;u<s;u++)(c[u].fn!==e||o&&!c[u].once||n&&c[u].context!==n)&&l.push(c[u]);l.length?this._events[i]=1===l.length?l[0]:l:a(this,i)}return this},c.prototype.removeAllListeners=function(t){var e;return t?(e=r?r+t:t,this._events[e]&&a(this,e)):(this._events=new n,this._eventsCount=0),this},c.prototype.off=c.prototype.removeListener,c.prototype.addListener=c.prototype.on,c.prefixed=r,c.EventEmitter=c,t.exports=c},11424:(t,e,r)=>{var n=r(47603);t.exports=r(66400)(n)},11539:(t,e,r)=>{var n=r(37643),o=r(55048),i=r(49227),a=0/0,c=/^[-+]0x[0-9a-f]+$/i,u=/^0b[01]+$/i,l=/^0o[0-7]+$/i,s=parseInt;t.exports=function(t){if("number"==typeof t)return t;if(i(t))return a;if(o(t)){var e="function"==typeof t.valueOf?t.valueOf():t;t=o(e)?e+"":e}if("string"!=typeof t)return 0===t?t:+t;t=n(t);var r=u.test(t);return r||l.test(t)?s(t.slice(2),r?2:8):c.test(t)?a:+t}},12290:t=>{var e=Function.prototype.toString;t.exports=function(t){if(null!=t){try{return e.call(t)}catch(t){}try{return t+""}catch(t){}}return""}},12344:(t,e,r)=>{t.exports=r(65984)()},13861:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},14675:t=>{t.exports=function(t){return function(){return t}}},15451:(t,e,r)=>{var n=r(29395),o=r(27467);t.exports=function(t){return o(t)&&"[object Arguments]"==n(t)}},15871:(t,e,r)=>{var n=r(36341),o=r(27467);t.exports=function t(e,r,i,a,c){return e===r||(null!=e&&null!=r&&(o(e)||o(r))?n(e,r,i,a,t,c):e!=e&&r!=r)}},15883:(t,e,r)=>{var n=r(2984),o=r(46063),i=r(48169);t.exports=function(t){return t&&t.length?n(t,i,o):void 0}},15909:(t,e,r)=>{var n=r(87506),o=r(66930),i=r(658);t.exports=function(){this.size=0,this.__data__={hash:new n,map:new(i||o),string:new n}}},16854:t=>{t.exports=function(t){return this.__data__.has(t)}},17518:(t,e,r)=>{var n=r(21367),o=r(1707),i=r(22),a=r(54765),c=r(43378),u=r(89624),l=r(65727),s=r(48169),f=r(40542);t.exports=function(t,e,r){e=e.length?n(e,function(t){return f(t)?function(e){return o(e,1===t.length?t[0]:t)}:t}):[s];var p=-1;return e=n(e,u(i)),c(a(t,function(t,r,o){return{criteria:n(e,function(e){return e(t)}),index:++p,value:t}}),function(t,e){return l(t,e,r)})}},17830:(t,e,r)=>{t.exports=r(41547)(r(85718),"WeakMap")},18234:(t,e,r)=>{var n=r(91290),o=r(22),i=r(84482),a=Math.max;t.exports=function(t,e,r){var c=null==t?0:t.length;if(!c)return -1;var u=null==r?0:i(r);return u<0&&(u=a(c+u,0)),n(t,o(e,3),u)}},19121:t=>{"use strict";t.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:t=>{"use strict";t.exports=require("process")},19976:(t,e,r)=>{var n=r(8336);t.exports=function(t,e){var r=n(this,t),o=r.size;return r.set(t,e),this.size+=+(r.size!=o),this}},20540:(t,e,r)=>{var n=r(55048),o=r(70151),i=r(11539),a=Math.max,c=Math.min;t.exports=function(t,e,r){var u,l,s,f,p,h,d=0,y=!1,v=!1,m=!0;if("function"!=typeof t)throw TypeError("Expected a function");function b(e){var r=u,n=l;return u=l=void 0,d=e,f=t.apply(n,r)}function g(t){var r=t-h,n=t-d;return void 0===h||r>=e||r<0||v&&n>=s}function x(){var t,r,n,i=o();if(g(i))return O(i);p=setTimeout(x,(t=i-h,r=i-d,n=e-t,v?c(n,s-r):n))}function O(t){return(p=void 0,m&&u)?b(t):(u=l=void 0,f)}function w(){var t,r=o(),n=g(r);if(u=arguments,l=this,h=r,n){if(void 0===p)return d=t=h,p=setTimeout(x,e),y?b(t):f;if(v)return clearTimeout(p),p=setTimeout(x,e),b(h)}return void 0===p&&(p=setTimeout(x,e)),f}return e=i(e)||0,n(r)&&(y=!!r.leading,s=(v="maxWait"in r)?a(i(r.maxWait)||0,e):s,m="trailing"in r?!!r.trailing:m),w.cancel=function(){void 0!==p&&clearTimeout(p),d=0,u=h=l=p=void 0},w.flush=function(){return void 0===p?f:O(o())},w}},20623:(t,e,r)=>{var n=r(15871),o=r(40491),i=r(2896),a=r(67619),c=r(34883),u=r(41132),l=r(46436);t.exports=function(t,e){return a(t)&&c(e)?u(l(t),e):function(r){var a=o(r,t);return void 0===a&&a===e?i(r,t):n(e,a,3)}}},21367:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length,o=Array(n);++r<n;)o[r]=e(t[r],r,t);return o}},21456:(t,e,r)=>{var n=r(41693),o=r(40542);t.exports=function(t,e,r){var i=e(t);return o(t)?i:n(i,r(t))}},21592:(t,e,r)=>{var n=r(42205),o=r(61837);t.exports=function(t,e){return n(o(t,e),1)}},21630:(t,e,r)=>{var n=r(10653),o=Object.prototype.hasOwnProperty;t.exports=function(t,e,r,i,a,c){var u=1&r,l=n(t),s=l.length;if(s!=n(e).length&&!u)return!1;for(var f=s;f--;){var p=l[f];if(!(u?p in e:o.call(e,p)))return!1}var h=c.get(t),d=c.get(e);if(h&&d)return h==e&&d==t;var y=!0;c.set(t,e),c.set(e,t);for(var v=u;++f<s;){var m=t[p=l[f]],b=e[p];if(i)var g=u?i(b,m,p,e,t,c):i(m,b,p,t,e,c);if(!(void 0===g?m===b||a(m,b,r,i,c):g)){y=!1;break}v||(v="constructor"==p)}if(y&&!v){var x=t.constructor,O=e.constructor;x!=O&&"constructor"in t&&"constructor"in e&&!("function"==typeof x&&x instanceof x&&"function"==typeof O&&O instanceof O)&&(y=!1)}return c.delete(t),c.delete(e),y}},21820:t=>{"use strict";t.exports=require("os")},22964:(t,e,r)=>{t.exports=r(23729)(r(18234))},23729:(t,e,r)=>{var n=r(22),o=r(32269),i=r(7651);t.exports=function(t){return function(e,r,a){var c=Object(e);if(!o(e)){var u=n(r,3);e=i(e),r=function(t){return u(c[t],t,c)}}var l=t(e,r,a);return l>-1?c[u?e[l]:l]:void 0}}},23928:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(62688).A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},25118:t=>{t.exports=function(t){return this.__data__.has(t)}},27006:(t,e,r)=>{var n=r(46328),o=r(99525),i=r(58276);t.exports=function(t,e,r,a,c,u){var l=1&r,s=t.length,f=e.length;if(s!=f&&!(l&&f>s))return!1;var p=u.get(t),h=u.get(e);if(p&&h)return p==e&&h==t;var d=-1,y=!0,v=2&r?new n:void 0;for(u.set(t,e),u.set(e,t);++d<s;){var m=t[d],b=e[d];if(a)var g=l?a(b,m,d,e,t,u):a(m,b,d,t,e,u);if(void 0!==g){if(g)continue;y=!1;break}if(v){if(!o(e,function(t,e){if(!i(v,e)&&(m===t||c(m,t,r,a,u)))return v.push(e)})){y=!1;break}}else if(!(m===b||c(m,b,r,a,u))){y=!1;break}}return u.delete(t),u.delete(e),y}},27467:t=>{t.exports=function(t){return null!=t&&"object"==typeof t}},27669:t=>{t.exports=function(){this.__data__=[],this.size=0}},27910:t=>{"use strict";t.exports=require("stream")},28354:t=>{"use strict";t.exports=require("util")},28559:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},28749:(t,e,r)=>{"use strict";r.d(e,{MH:()=>f,Wu:()=>u,ZB:()=>s,Zp:()=>c,aR:()=>l});var n=r(60687),o=r(43210),i=r(30474),a=r(4780);let c=(0,o.forwardRef)(({className:t,hover:e=!0,children:r,...o},i)=>(0,n.jsx)("div",{ref:i,className:(0,a.cn)(`
          bg-white rounded-[var(--radius-lg)] shadow-[var(--shadow-md)] 
          overflow-hidden transition-all duration-300
          `,e&&"hover:transform hover:-translate-y-1 hover:shadow-[var(--shadow-xl)]",t),...o,children:r}));c.displayName="Card";let u=(0,o.forwardRef)(({className:t,...e},r)=>(0,n.jsx)("div",{ref:r,className:(0,a.cn)("p-[var(--space-lg)]",t),...e}));u.displayName="CardContent";let l=(0,o.forwardRef)(({className:t,...e},r)=>(0,n.jsx)("div",{ref:r,className:(0,a.cn)("px-[var(--space-lg)] pt-[var(--space-lg)]",t),...e}));l.displayName="CardHeader";let s=(0,o.forwardRef)(({className:t,...e},r)=>(0,n.jsx)("h3",{ref:r,className:(0,a.cn)("text-xl font-bold mb-2 text-[var(--primary-brown)] font-[var(--font-display)]",t),...e}));s.displayName="CardTitle";let f=(0,o.forwardRef)(({className:t,src:e,alt:r,children:o,...c},u)=>(0,n.jsx)("div",{ref:u,className:(0,a.cn)("relative w-full h-48 bg-gradient-to-br from-[var(--primary-green)] to-[var(--secondary-sunset)] flex items-center justify-center text-white text-5xl",t),...c,children:e?(0,n.jsx)(i.default,{src:e,alt:r||"",fill:!0,className:"object-cover"}):o}));f.displayName="CardImage"},28837:(t,e,r)=>{var n=r(57797),o=Array.prototype.splice;t.exports=function(t){var e=this.__data__,r=n(e,t);return!(r<0)&&(r==e.length-1?e.pop():o.call(e,r,1),--this.size,!0)}},28977:(t,e,r)=>{var n=r(11539),o=1/0;t.exports=function(t){return t?(t=n(t))===o||t===-o?(t<0?-1:1)*17976931348623157e292:t==t?t:0:0===t?t:0}},29021:t=>{"use strict";t.exports=require("fs")},29205:(t,e,r)=>{var n=r(8336);t.exports=function(t){var e=n(this,t).delete(t);return this.size-=!!e,e}},29294:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29395:(t,e,r)=>{var n=r(79474),o=r(70222),i=r(84713),a=n?n.toStringTag:void 0;t.exports=function(t){return null==t?void 0===t?"[object Undefined]":"[object Null]":a&&a in Object(t)?o(t):i(t)}},29508:(t,e,r)=>{var n=r(8336);t.exports=function(t){return n(this,t).get(t)}},30316:(t,e,r)=>{var n=r(67554);t.exports=function(t,e){var r=!0;return n(t,function(t,n,o){return r=!!e(t,n,o)}),r}},30401:(t,e,r)=>{t.exports=r(41547)(r(85718),"Promise")},30474:(t,e,r)=>{"use strict";r.d(e,{default:()=>o.a});var n=r(31261),o=r.n(n)},30854:(t,e,r)=>{var n=r(66930),o=r(658),i=r(95746);t.exports=function(t,e){var r=this.__data__;if(r instanceof n){var a=r.__data__;if(!o||a.length<199)return a.push([t,e]),this.size=++r.size,this;r=this.__data__=new i(a)}return r.set(t,e),this.size=r.size,this}},31261:(t,e,r)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var r in e)Object.defineProperty(t,r,{enumerable:!0,get:e[r]})}(e,{default:function(){return u},getImageProps:function(){return c}});let n=r(14985),o=r(44953),i=r(46533),a=n._(r(1933));function c(t){let{props:e}=(0,o.getImgProps)(t,{defaultLoader:a.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[t,r]of Object.entries(e))void 0===r&&delete e[t];return{props:e}}let u=i.Image},32269:(t,e,r)=>{var n=r(5231),o=r(69619);t.exports=function(t){return null!=t&&o(t.length)&&!n(t)}},33873:t=>{"use strict";t.exports=require("path")},34117:t=>{var e=RegExp("[\\u200d\ud800-\udfff\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff\\ufe0e\\ufe0f]");t.exports=function(t){return e.test(t)}},34452:t=>{"use strict";t.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},34631:t=>{"use strict";t.exports=require("tls")},34746:t=>{t.exports=function(t){return this.__data__.get(t)}},34772:(t,e,r)=>{t.exports=r(41547)(r(85718),"Set")},34821:t=>{t.exports=function(t,e){for(var r=-1,n=Array(t);++r<t;)n[r]=e(r);return n}},34883:(t,e,r)=>{var n=r(55048);t.exports=function(t){return t==t&&!n(t)}},34990:(t,e,r)=>{t.exports=r(87321)()},35142:(t,e,r)=>{var n=r(40542),o=r(67619),i=r(51449),a=r(42403);t.exports=function(t,e){return n(t)?t:o(t,e)?[t]:i(a(t))}},35163:(t,e,r)=>{var n=r(15451),o=r(27467),i=Object.prototype,a=i.hasOwnProperty,c=i.propertyIsEnumerable;t.exports=n(function(){return arguments}())?n:function(t){return o(t)&&a.call(t,"callee")&&!c.call(t,"callee")}},35697:(t,e,r)=>{var n=r(79474),o=r(4999),i=r(67009),a=r(27006),c=r(59774),u=r(2408),l=n?n.prototype:void 0,s=l?l.valueOf:void 0;t.exports=function(t,e,r,n,l,f,p){switch(r){case"[object DataView]":if(t.byteLength!=e.byteLength||t.byteOffset!=e.byteOffset)break;t=t.buffer,e=e.buffer;case"[object ArrayBuffer]":if(t.byteLength!=e.byteLength||!f(new o(t),new o(e)))break;return!0;case"[object Boolean]":case"[object Date]":case"[object Number]":return i(+t,+e);case"[object Error]":return t.name==e.name&&t.message==e.message;case"[object RegExp]":case"[object String]":return t==e+"";case"[object Map]":var h=c;case"[object Set]":var d=1&n;if(h||(h=u),t.size!=e.size&&!d)break;var y=p.get(t);if(y)return y==e;n|=2,p.set(t,e);var v=a(h(t),h(e),n,l,f,p);return p.delete(t),v;case"[object Symbol]":if(s)return s.call(t)==s.call(e)}return!1}},35800:(t,e,r)=>{var n=r(57797);t.exports=function(t){return n(this.__data__,t)>-1}},36315:(t,e,r)=>{var n=r(22),o=r(92662);t.exports=function(t,e){return t&&t.length?o(t,n(e,2)):[]}},36341:(t,e,r)=>{var n=r(67200),o=r(27006),i=r(35697),a=r(21630),c=r(1566),u=r(40542),l=r(80329),s=r(10090),f="[object Arguments]",p="[object Array]",h="[object Object]",d=Object.prototype.hasOwnProperty;t.exports=function(t,e,r,y,v,m){var b=u(t),g=u(e),x=b?p:c(t),O=g?p:c(e);x=x==f?h:x,O=O==f?h:O;var w=x==h,j=O==h,S=x==O;if(S&&l(t)){if(!l(e))return!1;b=!0,w=!1}if(S&&!w)return m||(m=new n),b||s(t)?o(t,e,r,y,v,m):i(t,e,x,r,y,v,m);if(!(1&r)){var A=w&&d.call(t,"__wrapped__"),P=j&&d.call(e,"__wrapped__");if(A||P){var k=A?t.value():t,E=P?e.value():e;return m||(m=new n),v(k,E,r,y,m)}}return!!S&&(m||(m=new n),a(t,e,r,y,v,m))}},36959:t=>{t.exports=function(){}},37366:t=>{"use strict";t.exports=require("dns")},37456:t=>{t.exports=function(t){return null==t}},37575:(t,e,r)=>{var n=r(66930);t.exports=function(){this.__data__=new n,this.size=0}},37643:(t,e,r)=>{var n=r(6053),o=/^\s+/;t.exports=function(t){return t?t.slice(0,n(t)+1).replace(o,""):t}},38404:(t,e,r)=>{var n=r(29395),o=r(65932),i=r(27467),a=Object.prototype,c=Function.prototype.toString,u=a.hasOwnProperty,l=c.call(Object);t.exports=function(t){if(!i(t)||"[object Object]"!=n(t))return!1;var e=o(t);if(null===e)return!0;var r=u.call(e,"constructor")&&e.constructor;return"function"==typeof r&&r instanceof r&&c.call(r)==l}},38428:t=>{var e=/^(?:0|[1-9]\d*)$/;t.exports=function(t,r){var n=typeof t;return!!(r=null==r?0x1fffffffffffff:r)&&("number"==n||"symbol"!=n&&e.test(t))&&t>-1&&t%1==0&&t<r}},39672:(t,e,r)=>{var n=r(58141);t.exports=function(t,e){var r=this.__data__;return this.size+=+!this.has(t),r[t]=n&&void 0===e?"__lodash_hash_undefined__":e,this}},39774:t=>{t.exports=function(t){return t!=t}},39839:(t,e,r)=>{"use strict";r.r(e),r.d(e,{GlobalError:()=>a.a,__next_app__:()=>f,pages:()=>s,routeModule:()=>p,tree:()=>l});var n=r(65239),o=r(48088),i=r(88170),a=r.n(i),c=r(30893),u={};for(let t in c)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(t)&&(u[t]=()=>c[t]);r.d(e,u);let l={children:["",{children:["farms",{children:["[id]",{children:["analytics",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,94389)),"/Users/<USER>/src/personal/bvr-safaris/src/app/farms/[id]/analytics/page.tsx"]}]},{}]},{}]},{metadata:{icon:[async t=>(await Promise.resolve().then(r.bind(r,70440))).default(t)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,82893)),"/Users/<USER>/src/personal/bvr-safaris/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async t=>(await Promise.resolve().then(r.bind(r,70440))).default(t)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,s=["/Users/<USER>/src/personal/bvr-safaris/src/app/farms/[id]/analytics/page.tsx"],f={require:r,loadChunk:()=>Promise.resolve()},p=new n.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/farms/[id]/analytics/page",pathname:"/farms/[id]/analytics",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},40228:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},40491:(t,e,r)=>{var n=r(1707);t.exports=function(t,e,r){var o=null==t?void 0:n(t,e);return void 0===o?r:o}},40542:t=>{t.exports=Array.isArray},41011:(t,e,r)=>{var n=r(41353);t.exports=function(t,e,r){var o=t.length;return r=void 0===r?o:r,!e&&r>=o?t:n(t,e,r)}},41132:t=>{t.exports=function(t,e){return function(r){return null!=r&&r[t]===e&&(void 0!==e||t in Object(r))}}},41157:(t,e,r)=>{var n=r(91928);t.exports=function(t,e,r){"__proto__"==e&&n?n(t,e,{configurable:!0,enumerable:!0,value:r,writable:!0}):t[e]=r}},41353:t=>{t.exports=function(t,e,r){var n=-1,o=t.length;e<0&&(e=-e>o?0:o+e),(r=r>o?o:r)<0&&(r+=o),o=e>r?0:r-e>>>0,e>>>=0;for(var i=Array(o);++n<o;)i[n]=t[n+e];return i}},41547:(t,e,r)=>{var n=r(61548),o=r(90851);t.exports=function(t,e){var r=o(t,e);return n(r)?r:void 0}},41693:t=>{t.exports=function(t,e){for(var r=-1,n=e.length,o=t.length;++r<n;)t[o+r]=e[r];return t}},42082:t=>{t.exports=function(t){return function(e){return null==e?void 0:e[t]}}},42205:(t,e,r)=>{var n=r(41693),o=r(85450);t.exports=function t(e,r,i,a,c){var u=-1,l=e.length;for(i||(i=o),c||(c=[]);++u<l;){var s=e[u];r>0&&i(s)?r>1?t(s,r-1,i,a,c):n(c,s):a||(c[c.length]=s)}return c}},42403:(t,e,r)=>{var n=r(80195);t.exports=function(t){return null==t?"":n(t)}},43378:t=>{t.exports=function(t,e){var r=t.length;for(t.sort(e);r--;)t[r]=t[r].value;return t}},45058:(t,e,r)=>{var n=r(42082),o=r(8852),i=r(67619),a=r(46436);t.exports=function(t){return i(t)?n(a(t)):o(t)}},45603:(t,e,r)=>{var n=r(20540),o=r(55048);t.exports=function(t,e,r){var i=!0,a=!0;if("function"!=typeof t)throw TypeError("Expected a function");return o(r)&&(i="leading"in r?!!r.leading:i,a="trailing"in r?!!r.trailing:a),n(t,e,{leading:i,maxWait:e,trailing:a})}},45803:t=>{t.exports=function(t){var e=typeof t;return"string"==e||"number"==e||"symbol"==e||"boolean"==e?"__proto__"!==t:null===t}},46063:t=>{t.exports=function(t,e){return t<e}},46229:(t,e,r)=>{var n=r(48169),o=r(66354),i=r(11424);t.exports=function(t,e){return i(o(t,e,n),t+"")}},46328:(t,e,r)=>{var n=r(95746),o=r(89185),i=r(16854);function a(t){var e=-1,r=null==t?0:t.length;for(this.__data__=new n;++e<r;)this.add(t[e])}a.prototype.add=a.prototype.push=o,a.prototype.has=i,t.exports=a},46436:(t,e,r)=>{var n=r(49227),o=1/0;t.exports=function(t){if("string"==typeof t||n(t))return t;var e=t+"";return"0"==e&&1/t==-o?"-0":e}},47212:(t,e,r)=>{var n=r(87270),o=r(30316),i=r(22),a=r(40542),c=r(7383);t.exports=function(t,e,r){var u=a(t)?n:o;return r&&c(t,e,r)&&(e=void 0),u(t,i(e,3))}},47282:(t,e,r)=>{t=r.nmd(t);var n=r(10663),o=e&&!e.nodeType&&e,i=o&&t&&!t.nodeType&&t,a=i&&i.exports===o&&n.process,c=function(){try{var t=i&&i.require&&i.require("util").types;if(t)return t;return a&&a.binding&&a.binding("util")}catch(t){}}();t.exports=c},47603:(t,e,r)=>{var n=r(14675),o=r(91928),i=r(48169);t.exports=o?function(t,e){return o(t,"toString",{configurable:!0,enumerable:!1,value:n(e),writable:!0})}:i},48169:t=>{t.exports=function(t){return t}},48385:t=>{var e="\ud800-\udfff",r="[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]",n="\ud83c[\udffb-\udfff]",o="[^"+e+"]",i="(?:\ud83c[\udde6-\uddff]){2}",a="[\ud800-\udbff][\udc00-\udfff]",c="(?:"+r+"|"+n+")?",u="[\\ufe0e\\ufe0f]?",l="(?:\\u200d(?:"+[o,i,a].join("|")+")"+u+c+")*",s=RegExp(n+"(?="+n+")|"+("(?:"+[o+r+"?",r,i,a,"["+e+"]"].join("|"))+")"+(u+c+l),"g");t.exports=function(t){return t.match(s)||[]}},49227:(t,e,r)=>{var n=r(29395),o=r(27467);t.exports=function(t){return"symbol"==typeof t||o(t)&&"[object Symbol]"==n(t)}},51449:(t,e,r)=>{var n=r(85745),o=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,i=/\\(\\)?/g;t.exports=n(function(t){var e=[];return 46===t.charCodeAt(0)&&e.push(""),t.replace(o,function(t,r,n,o){e.push(n?o.replace(i,"$1"):r||t)}),e})},52599:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length,o=0,i=[];++r<n;){var a=t[r];e(a,r,t)&&(i[o++]=a)}return i}},52823:(t,e,r)=>{var n=r(85406),o=function(){var t=/[^.]+$/.exec(n&&n.keys&&n.keys.IE_PROTO||"");return t?"Symbol(src)_1."+t:""}();t.exports=function(t){return!!o&&o in t}},52931:(t,e,r)=>{var n=r(77834),o=r(89605),i=Object.prototype.hasOwnProperty;t.exports=function(t){if(!n(t))return o(t);var e=[];for(var r in Object(t))i.call(t,r)&&"constructor"!=r&&e.push(r);return e}},54765:(t,e,r)=>{var n=r(67554),o=r(32269);t.exports=function(t,e){var r=-1,i=o(t)?Array(t.length):[];return n(t,function(t,n,o){i[++r]=e(t,n,o)}),i}},55048:t=>{t.exports=function(t){var e=typeof t;return null!=t&&("object"==e||"function"==e)}},55511:t=>{"use strict";t.exports=require("crypto")},56506:(t,e,r)=>{var n=r(32269);t.exports=function(t,e){return function(r,o){if(null==r)return r;if(!n(r))return t(r,o);for(var i=r.length,a=e?i:-1,c=Object(r);(e?a--:++a<i)&&!1!==o(c[a],a,c););return r}}},57088:(t,e,r)=>{var n=r(2984),o=r(99180),i=r(22);t.exports=function(t,e){return t&&t.length?n(t,i(e,2),o):void 0}},57797:(t,e,r)=>{var n=r(67009);t.exports=function(t,e){for(var r=t.length;r--;)if(n(t[r][0],e))return r;return -1}},58141:(t,e,r)=>{t.exports=r(41547)(Object,"create")},58276:t=>{t.exports=function(t,e){return t.has(e)}},58701:(t,e,r)=>{Promise.resolve().then(r.bind(r,61415))},58744:(t,e,r)=>{var n=r(57797);t.exports=function(t,e){var r=this.__data__,o=n(r,t);return o<0?(++this.size,r.push([t,e])):r[o][1]=e,this}},59467:(t,e,r)=>{var n=r(35142),o=r(35163),i=r(40542),a=r(38428),c=r(69619),u=r(46436);t.exports=function(t,e,r){e=n(e,t);for(var l=-1,s=e.length,f=!1;++l<s;){var p=u(e[l]);if(!(f=null!=t&&r(t,p)))break;t=t[p]}return f||++l!=s?f:!!(s=null==t?0:t.length)&&c(s)&&a(p,s)&&(i(t)||o(t))}},59774:t=>{t.exports=function(t){var e=-1,r=Array(t.size);return t.forEach(function(t,n){r[++e]=[n,t]}),r}},60909:(t,e,r)=>{Promise.resolve().then(r.bind(r,94389))},61320:(t,e,r)=>{var n=r(8336);t.exports=function(t){return n(this,t).has(t)}},61415:(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>yU});var n={};r.r(n),r.d(n,{scaleBand:()=>nk,scaleDiverging:()=>function t(){var e=id(cT()(o5));return e.copy=function(){return ck(e,t())},nO.apply(e,arguments)},scaleDivergingLog:()=>function t(){var e=ij(cT()).domain([.1,1,10]);return e.copy=function(){return ck(e,t()).base(e.base())},nO.apply(e,arguments)},scaleDivergingPow:()=>c_,scaleDivergingSqrt:()=>cC,scaleDivergingSymlog:()=>function t(){var e=iP(cT());return e.copy=function(){return ck(e,t()).constant(e.constant())},nO.apply(e,arguments)},scaleIdentity:()=>function t(e){var r;function n(t){return null==t||isNaN(t*=1)?r:t}return n.invert=n,n.domain=n.range=function(t){return arguments.length?(e=Array.from(t,o2),n):e.slice()},n.unknown=function(t){return arguments.length?(r=t,n):r},n.copy=function(){return t(e).unknown(r)},e=arguments.length?Array.from(e,o2):[0,1],id(n)},scaleImplicit:()=>nA,scaleLinear:()=>iy,scaleLog:()=>function t(){let e=ij(o9()).domain([1,10]);return e.copy=()=>o7(e,t()).base(e.base()),nx.apply(e,arguments),e},scaleOrdinal:()=>nP,scalePoint:()=>nE,scalePow:()=>i_,scaleQuantile:()=>function t(){var e,r=[],n=[],o=[];function i(){var t=0,e=Math.max(1,n.length);for(o=Array(e-1);++t<e;)o[t-1]=function(t,e,r=of){if(!(!(n=t.length)||isNaN(e*=1))){if(e<=0||n<2)return+r(t[0],0,t);if(e>=1)return+r(t[n-1],n-1,t);var n,o=(n-1)*e,i=Math.floor(o),a=+r(t[i],i,t);return a+(r(t[i+1],i+1,t)-a)*(o-i)}}(r,t/e);return a}function a(t){return null==t||isNaN(t*=1)?e:n[oh(o,t)]}return a.invertExtent=function(t){var e=n.indexOf(t);return e<0?[NaN,NaN]:[e>0?o[e-1]:r[0],e<o.length?o[e]:r[r.length-1]]},a.domain=function(t){if(!arguments.length)return r.slice();for(let e of(r=[],t))null==e||isNaN(e*=1)||r.push(e);return r.sort(oc),i()},a.range=function(t){return arguments.length?(n=Array.from(t),i()):n.slice()},a.unknown=function(t){return arguments.length?(e=t,a):e},a.quantiles=function(){return o.slice()},a.copy=function(){return t().domain(r).range(n).unknown(e)},nx.apply(a,arguments)},scaleQuantize:()=>function t(){var e,r=0,n=1,o=1,i=[.5],a=[0,1];function c(t){return null!=t&&t<=t?a[oh(i,t,0,o)]:e}function u(){var t=-1;for(i=Array(o);++t<o;)i[t]=((t+1)*n-(t-o)*r)/(o+1);return c}return c.domain=function(t){return arguments.length?([r,n]=t,r*=1,n*=1,u()):[r,n]},c.range=function(t){return arguments.length?(o=(a=Array.from(t)).length-1,u()):a.slice()},c.invertExtent=function(t){var e=a.indexOf(t);return e<0?[NaN,NaN]:e<1?[r,i[0]]:e>=o?[i[o-1],n]:[i[e-1],i[e]]},c.unknown=function(t){return arguments.length&&(e=t),c},c.thresholds=function(){return i.slice()},c.copy=function(){return t().domain([r,n]).range(a).unknown(e)},nx.apply(id(c),arguments)},scaleRadial:()=>function t(){var e,r=it(),n=[0,1],o=!1;function i(t){var n,i=Math.sign(n=r(t))*Math.sqrt(Math.abs(n));return isNaN(i)?e:o?Math.round(i):i}return i.invert=function(t){return r.invert(iD(t))},i.domain=function(t){return arguments.length?(r.domain(t),i):r.domain()},i.range=function(t){return arguments.length?(r.range((n=Array.from(t,o2)).map(iD)),i):n.slice()},i.rangeRound=function(t){return i.range(t).round(!0)},i.round=function(t){return arguments.length?(o=!!t,i):o},i.clamp=function(t){return arguments.length?(r.clamp(t),i):r.clamp()},i.unknown=function(t){return arguments.length?(e=t,i):e},i.copy=function(){return t(r.domain(),n).round(o).clamp(r.clamp()).unknown(e)},nx.apply(i,arguments),id(i)},scaleSequential:()=>function t(){var e=id(cP()(o5));return e.copy=function(){return ck(e,t())},nO.apply(e,arguments)},scaleSequentialLog:()=>function t(){var e=ij(cP()).domain([1,10]);return e.copy=function(){return ck(e,t()).base(e.base())},nO.apply(e,arguments)},scaleSequentialPow:()=>cE,scaleSequentialQuantile:()=>function t(){var e=[],r=o5;function n(t){if(null!=t&&!isNaN(t*=1))return r((oh(e,t,1)-1)/(e.length-1))}return n.domain=function(t){if(!arguments.length)return e.slice();for(let r of(e=[],t))null==r||isNaN(r*=1)||e.push(r);return e.sort(oc),n},n.interpolator=function(t){return arguments.length?(r=t,n):r},n.range=function(){return e.map((t,n)=>r(n/(e.length-1)))},n.quantiles=function(t){return Array.from({length:t+1},(r,n)=>(function(t,e,r){if(!(!(n=(t=Float64Array.from(function*(t,e){if(void 0===e)for(let e of t)null!=e&&(e*=1)>=e&&(yield e);else{let r=-1;for(let n of t)null!=(n=e(n,++r,t))&&(n*=1)>=n&&(yield n)}}(t,void 0))).length)||isNaN(e*=1))){if(e<=0||n<2)return iI(t);if(e>=1)return iN(t);var n,o=(n-1)*e,i=Math.floor(o),a=iN((function t(e,r,n=0,o=1/0,i){if(r=Math.floor(r),n=Math.floor(Math.max(0,n)),o=Math.floor(Math.min(e.length-1,o)),!(n<=r&&r<=o))return e;for(i=void 0===i?iB:function(t=oc){if(t===oc)return iB;if("function"!=typeof t)throw TypeError("compare is not a function");return(e,r)=>{let n=t(e,r);return n||0===n?n:(0===t(r,r))-(0===t(e,e))}}(i);o>n;){if(o-n>600){let a=o-n+1,c=r-n+1,u=Math.log(a),l=.5*Math.exp(2*u/3),s=.5*Math.sqrt(u*l*(a-l)/a)*(c-a/2<0?-1:1),f=Math.max(n,Math.floor(r-c*l/a+s)),p=Math.min(o,Math.floor(r+(a-c)*l/a+s));t(e,r,f,p,i)}let a=e[r],c=n,u=o;for(iR(e,n,r),i(e[o],a)>0&&iR(e,n,o);c<u;){for(iR(e,c,u),++c,--u;0>i(e[c],a);)++c;for(;i(e[u],a)>0;)--u}0===i(e[n],a)?iR(e,n,u):iR(e,++u,o),u<=r&&(n=u+1),r<=u&&(o=u-1)}return e})(t,i).subarray(0,i+1));return a+(iI(t.subarray(i+1))-a)*(o-i)}})(e,n/t))},n.copy=function(){return t(r).domain(e)},nO.apply(n,arguments)},scaleSequentialSqrt:()=>cM,scaleSequentialSymlog:()=>function t(){var e=iP(cP());return e.copy=function(){return ck(e,t()).constant(e.constant())},nO.apply(e,arguments)},scaleSqrt:()=>iC,scaleSymlog:()=>function t(){var e=iP(o9());return e.copy=function(){return o7(e,t()).constant(e.constant())},nx.apply(e,arguments)},scaleThreshold:()=>function t(){var e,r=[.5],n=[0,1],o=1;function i(t){return null!=t&&t<=t?n[oh(r,t,0,o)]:e}return i.domain=function(t){return arguments.length?(o=Math.min((r=Array.from(t)).length,n.length-1),i):r.slice()},i.range=function(t){return arguments.length?(n=Array.from(t),o=Math.min(r.length,n.length-1),i):n.slice()},i.invertExtent=function(t){var e=n.indexOf(t);return[r[e-1],r[e]]},i.unknown=function(t){return arguments.length?(e=t,i):e},i.copy=function(){return t().domain(r).range(n).unknown(e)},nx.apply(i,arguments)},scaleTime:()=>cS,scaleUtc:()=>cA,tickFormat:()=>ih});var o=r(60687),i=r(43210),a=r.n(i),c=r(16189),u=r(2643),l=r(28749),s=r(474),f=r(57445),p=r(28559),h=r(13861),d=r(40228),y=r(23928);let v=(0,r(62688).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]]);var m=r(64398),b=r(49384),g=r(45603),x=r.n(g),O=r(63866),w=r.n(O),j=r(77822),S=r.n(j),A=r(40491),P=r.n(A),k=r(93490),E=r.n(k),M=function(t){return 0===t?0:t>0?1:-1},T=function(t){return w()(t)&&t.indexOf("%")===t.length-1},_=function(t){return E()(t)&&!S()(t)},C=function(t){return _(t)||w()(t)},D=0,N=function(t){var e=++D;return"".concat(t||"").concat(e)},I=function(t,e){var r,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,o=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(!_(t)&&!w()(t))return n;if(T(t)){var i=t.indexOf("%");r=e*parseFloat(t.slice(0,i))/100}else r=+t;return S()(r)&&(r=n),o&&r>e&&(r=e),r},B=function(t){if(!t)return null;var e=Object.keys(t);return e&&e.length?t[e[0]]:null},R=function(t){if(!Array.isArray(t))return!1;for(var e=t.length,r={},n=0;n<e;n++)if(r[t[n]])return!0;else r[t[n]]=!0;return!1},L=function(t,e){return _(t)&&_(e)?function(r){return t+r*(e-t)}:function(){return e}};function z(t,e,r){return t&&t.length?t.find(function(t){return t&&("function"==typeof e?e(t):P()(t,e))===r}):null}var U=function(t,e){return _(t)&&_(e)?t-e:w()(t)&&w()(e)?t.localeCompare(e):t instanceof Date&&e instanceof Date?t.getTime()-e.getTime():String(t).localeCompare(String(e))},F=function(t,e){for(var r=arguments.length,n=Array(r>2?r-2:0),o=2;o<r;o++)n[o-2]=arguments[o]},$=r(37456),q=r.n($),W=r(5231),H=r.n(W),G=r(55048),V=r.n(G),X=r(93780);function K(t,e){for(var r in t)if(({}).hasOwnProperty.call(t,r)&&(!({}).hasOwnProperty.call(e,r)||t[r]!==e[r]))return!1;for(var n in e)if(({}).hasOwnProperty.call(e,n)&&!({}).hasOwnProperty.call(t,n))return!1;return!0}function Y(t){return(Y="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var Z=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],J=["points","pathLength"],Q={svg:["viewBox","children"],polygon:J,polyline:J},tt=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],te=function(t,e){if(!t||"function"==typeof t||"boolean"==typeof t)return null;var r=t;if((0,i.isValidElement)(t)&&(r=t.props),!V()(r))return null;var n={};return Object.keys(r).forEach(function(t){tt.includes(t)&&(n[t]=e||function(e){return r[t](r,e)})}),n},tr=function(t,e,r){if(!V()(t)||"object"!==Y(t))return null;var n=null;return Object.keys(t).forEach(function(o){var i=t[o];tt.includes(o)&&"function"==typeof i&&(n||(n={}),n[o]=function(t){return i(e,r,t),null})}),n},tn=["children"],to=["children"];function ti(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function ta(t){return(ta="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var tc={click:"onClick",mousedown:"onMouseDown",mouseup:"onMouseUp",mouseover:"onMouseOver",mousemove:"onMouseMove",mouseout:"onMouseOut",mouseenter:"onMouseEnter",mouseleave:"onMouseLeave",touchcancel:"onTouchCancel",touchend:"onTouchEnd",touchmove:"onTouchMove",touchstart:"onTouchStart",contextmenu:"onContextMenu",dblclick:"onDoubleClick"},tu=function(t){return"string"==typeof t?t:t?t.displayName||t.name||"Component":""},tl=null,ts=null,tf=function t(e){if(e===tl&&Array.isArray(ts))return ts;var r=[];return i.Children.forEach(e,function(e){q()(e)||((0,X.isFragment)(e)?r=r.concat(t(e.props.children)):r.push(e))}),ts=r,tl=e,r};function tp(t,e){var r=[],n=[];return n=Array.isArray(e)?e.map(function(t){return tu(t)}):[tu(e)],tf(t).forEach(function(t){var e=P()(t,"type.displayName")||P()(t,"type.name");-1!==n.indexOf(e)&&r.push(t)}),r}function th(t,e){var r=tp(t,e);return r&&r[0]}var td=function(t){if(!t||!t.props)return!1;var e=t.props,r=e.width,n=e.height;return!!_(r)&&!(r<=0)&&!!_(n)&&!(n<=0)},ty=["a","altGlyph","altGlyphDef","altGlyphItem","animate","animateColor","animateMotion","animateTransform","circle","clipPath","color-profile","cursor","defs","desc","ellipse","feBlend","feColormatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","filter","font","font-face","font-face-format","font-face-name","font-face-url","foreignObject","g","glyph","glyphRef","hkern","image","line","lineGradient","marker","mask","metadata","missing-glyph","mpath","path","pattern","polygon","polyline","radialGradient","rect","script","set","stop","style","svg","switch","symbol","text","textPath","title","tref","tspan","use","view","vkern"],tv=function(t,e,r,n){var o,i=null!=(o=null==Q?void 0:Q[n])?o:[];return e.startsWith("data-")||!H()(t)&&(n&&i.includes(e)||Z.includes(e))||r&&tt.includes(e)},tm=function(t,e,r){if(!t||"function"==typeof t||"boolean"==typeof t)return null;var n=t;if((0,i.isValidElement)(t)&&(n=t.props),!V()(n))return null;var o={};return Object.keys(n).forEach(function(t){var i;tv(null==(i=n)?void 0:i[t],t,e,r)&&(o[t]=n[t])}),o},tb=function t(e,r){if(e===r)return!0;var n=i.Children.count(e);if(n!==i.Children.count(r))return!1;if(0===n)return!0;if(1===n)return tg(Array.isArray(e)?e[0]:e,Array.isArray(r)?r[0]:r);for(var o=0;o<n;o++){var a=e[o],c=r[o];if(Array.isArray(a)||Array.isArray(c)){if(!t(a,c))return!1}else if(!tg(a,c))return!1}return!0},tg=function(t,e){if(q()(t)&&q()(e))return!0;if(!q()(t)&&!q()(e)){var r=t.props||{},n=r.children,o=ti(r,tn),i=e.props||{},a=i.children,c=ti(i,to);if(n&&a)return K(o,c)&&tb(n,a);if(!n&&!a)return K(o,c)}return!1},tx=function(t,e){var r=[],n={};return tf(t).forEach(function(t,o){var i;if((i=t)&&i.type&&w()(i.type)&&ty.indexOf(i.type)>=0)r.push(t);else if(t){var a=tu(t.type),c=e[a]||{},u=c.handler,l=c.once;if(u&&(!l||!n[a])){var s=u(t,a,o);r.push(s),n[a]=!0}}}),r},tO=function(t){var e=t&&t.type;return e&&tc[e]?tc[e]:null};function tw(t){return(tw="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function tj(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function tS(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?tj(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=tw(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=tw(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==tw(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):tj(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function tA(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var tP=(0,i.forwardRef)(function(t,e){var r,n=t.aspect,o=t.initialDimension,c=void 0===o?{width:-1,height:-1}:o,u=t.width,l=void 0===u?"100%":u,s=t.height,f=void 0===s?"100%":s,p=t.minWidth,h=void 0===p?0:p,d=t.minHeight,y=t.maxHeight,v=t.children,m=t.debounce,g=void 0===m?0:m,O=t.id,w=t.className,j=t.onResize,S=t.style,A=(0,i.useRef)(null),P=(0,i.useRef)();P.current=j,(0,i.useImperativeHandle)(e,function(){return Object.defineProperty(A.current,"current",{get:function(){return console.warn("The usage of ref.current.current is deprecated and will no longer be supported."),A.current},configurable:!0})});var k=function(t){if(Array.isArray(t))return t}(r=(0,i.useState)({containerWidth:c.width,containerHeight:c.height}))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{i=(r=r.call(t)).next,!1;for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(r,2)||function(t,e){if(t){if("string"==typeof t)return tA(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return tA(t,e)}}(r,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),E=k[0],M=k[1],_=(0,i.useCallback)(function(t,e){M(function(r){var n=Math.round(t),o=Math.round(e);return r.containerWidth===n&&r.containerHeight===o?r:{containerWidth:n,containerHeight:o}})},[]);(0,i.useEffect)(function(){var t=function(t){var e,r=t[0].contentRect,n=r.width,o=r.height;_(n,o),null==(e=P.current)||e.call(P,n,o)};g>0&&(t=x()(t,g,{trailing:!0,leading:!1}));var e=new ResizeObserver(t),r=A.current.getBoundingClientRect();return _(r.width,r.height),e.observe(A.current),function(){e.disconnect()}},[_,g]);var C=(0,i.useMemo)(function(){var t=E.containerWidth,e=E.containerHeight;if(t<0||e<0)return null;F(T(l)||T(f),"The width(%s) and height(%s) are both fixed numbers,\n       maybe you don't need to use a ResponsiveContainer.",l,f),F(!n||n>0,"The aspect(%s) must be greater than zero.",n);var r=T(l)?t:l,o=T(f)?e:f;n&&n>0&&(r?o=r/n:o&&(r=o*n),y&&o>y&&(o=y)),F(r>0||o>0,"The width(%s) and height(%s) of chart should be greater than 0,\n       please check the style of container, or the props width(%s) and height(%s),\n       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the\n       height and width.",r,o,l,f,h,d,n);var c=!Array.isArray(v)&&tu(v.type).endsWith("Chart");return a().Children.map(v,function(t){return a().isValidElement(t)?(0,i.cloneElement)(t,tS({width:r,height:o},c?{style:tS({height:"100%",width:"100%",maxHeight:o,maxWidth:r},t.props.style)}:{})):t})},[n,v,f,y,d,h,E,l]);return a().createElement("div",{id:O?"".concat(O):void 0,className:(0,b.A)("recharts-responsive-container",w),style:tS(tS({},void 0===S?{}:S),{},{width:l,height:f,minWidth:h,minHeight:d,maxHeight:y}),ref:A},C)}),tk=r(34990),tE=r.n(tk),tM=r(85938),tT=r.n(tM);function t_(t,e){if(!t)throw Error("Invariant failed")}var tC=["children","width","height","viewBox","className","style","title","desc"];function tD(){return(tD=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function tN(t){var e=t.children,r=t.width,n=t.height,o=t.viewBox,i=t.className,c=t.style,u=t.title,l=t.desc,s=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,tC),f=o||{width:r,height:n,x:0,y:0},p=(0,b.A)("recharts-surface",i);return a().createElement("svg",tD({},tm(s,!0,"svg"),{className:p,width:r,height:n,style:c,viewBox:"".concat(f.x," ").concat(f.y," ").concat(f.width," ").concat(f.height)}),a().createElement("title",null,u),a().createElement("desc",null,l),e)}var tI=["children","className"];function tB(){return(tB=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var tR=a().forwardRef(function(t,e){var r=t.children,n=t.className,o=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,tI),i=(0,b.A)("recharts-layer",n);return a().createElement("g",tB({className:i},tm(o,!0),{ref:e}),r)});function tL(t){return(tL="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function tz(){return(tz=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function tU(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function tF(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function t$(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?tF(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=tL(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=tL(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==tL(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):tF(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function tq(t){return Array.isArray(t)&&C(t[0])&&C(t[1])?t.join(" ~ "):t}var tW=function(t){var e=t.separator,r=void 0===e?" : ":e,n=t.contentStyle,o=t.itemStyle,i=void 0===o?{}:o,c=t.labelStyle,u=t.payload,l=t.formatter,s=t.itemSorter,f=t.wrapperClassName,p=t.labelClassName,h=t.label,d=t.labelFormatter,y=t.accessibilityLayer,v=t$({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},void 0===n?{}:n),m=t$({margin:0},void 0===c?{}:c),g=!q()(h),x=g?h:"",O=(0,b.A)("recharts-default-tooltip",f),w=(0,b.A)("recharts-tooltip-label",p);return g&&d&&null!=u&&(x=d(h,u)),a().createElement("div",tz({className:O,style:v},void 0!==y&&y?{role:"status","aria-live":"assertive"}:{}),a().createElement("p",{className:w,style:m},a().isValidElement(x)?x:"".concat(x)),function(){if(u&&u.length){var t=(s?tT()(u,s):u).map(function(t,e){if("none"===t.type)return null;var n=t$({display:"block",paddingTop:4,paddingBottom:4,color:t.color||"#000"},i),o=t.formatter||l||tq,c=t.value,s=t.name,f=c,p=s;if(o&&null!=f&&null!=p){var h=o(c,s,t,e,u);if(Array.isArray(h)){var d=function(t){if(Array.isArray(t))return t}(h)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{i=(r=r.call(t)).next,!1;for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(h,2)||function(t,e){if(t){if("string"==typeof t)return tU(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return tU(t,e)}}(h,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();f=d[0],p=d[1]}else f=h}return a().createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(e),style:n},C(p)?a().createElement("span",{className:"recharts-tooltip-item-name"},p):null,C(p)?a().createElement("span",{className:"recharts-tooltip-item-separator"},r):null,a().createElement("span",{className:"recharts-tooltip-item-value"},f),a().createElement("span",{className:"recharts-tooltip-item-unit"},t.unit||""))});return a().createElement("ul",{className:"recharts-tooltip-item-list",style:{padding:0,margin:0}},t)}return null}())};function tH(t){return(tH="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function tG(t,e,r){var n;return(n=function(t,e){if("object"!=tH(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=tH(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==tH(n)?n:n+"")in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var tV="recharts-tooltip-wrapper",tX={visibility:"hidden"};function tK(t){var e=t.allowEscapeViewBox,r=t.coordinate,n=t.key,o=t.offsetTopLeft,i=t.position,a=t.reverseDirection,c=t.tooltipDimension,u=t.viewBox,l=t.viewBoxDimension;if(i&&_(i[n]))return i[n];var s=r[n]-c-o,f=r[n]+o;return e[n]?a[n]?s:f:a[n]?s<u[n]?Math.max(f,u[n]):Math.max(s,u[n]):f+c>u[n]+l?Math.max(s,u[n]):Math.max(f,u[n])}function tY(t){return(tY="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function tZ(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function tJ(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?tZ(Object(r),!0).forEach(function(e){t2(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):tZ(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function tQ(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(tQ=function(){return!!t})()}function t0(t){return(t0=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function t1(t,e){return(t1=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function t2(t,e,r){return(e=t3(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function t3(t){var e=function(t,e){if("object"!=tY(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=tY(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==tY(e)?e:e+""}var t5=function(t){var e;function r(){var t,e,n;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");for(var o=arguments.length,i=Array(o),a=0;a<o;a++)i[a]=arguments[a];return e=r,n=[].concat(i),e=t0(e),t2(t=function(t,e){if(e&&("object"===tY(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,tQ()?Reflect.construct(e,n||[],t0(this).constructor):e.apply(this,n)),"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0},lastBoundingBox:{width:-1,height:-1}}),t2(t,"handleKeyDown",function(e){if("Escape"===e.key){var r,n,o,i;t.setState({dismissed:!0,dismissedAtCoordinate:{x:null!=(r=null==(n=t.props.coordinate)?void 0:n.x)?r:0,y:null!=(o=null==(i=t.props.coordinate)?void 0:i.y)?o:0}})}}),t}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&t1(r,t),e=[{key:"updateBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var t=this.wrapperNode.getBoundingClientRect();(Math.abs(t.width-this.state.lastBoundingBox.width)>1||Math.abs(t.height-this.state.lastBoundingBox.height)>1)&&this.setState({lastBoundingBox:{width:t.width,height:t.height}})}else(-1!==this.state.lastBoundingBox.width||-1!==this.state.lastBoundingBox.height)&&this.setState({lastBoundingBox:{width:-1,height:-1}})}},{key:"componentDidMount",value:function(){document.addEventListener("keydown",this.handleKeyDown),this.updateBBox()}},{key:"componentWillUnmount",value:function(){document.removeEventListener("keydown",this.handleKeyDown)}},{key:"componentDidUpdate",value:function(){var t,e;this.props.active&&this.updateBBox(),this.state.dismissed&&((null==(t=this.props.coordinate)?void 0:t.x)!==this.state.dismissedAtCoordinate.x||(null==(e=this.props.coordinate)?void 0:e.y)!==this.state.dismissedAtCoordinate.y)&&(this.state.dismissed=!1)}},{key:"render",value:function(){var t,e,r,n,o,i,c,u,l,s,f,p,h,d,y,v,m,g,x,O=this,w=this.props,j=w.active,S=w.allowEscapeViewBox,A=w.animationDuration,P=w.animationEasing,k=w.children,E=w.coordinate,M=w.hasPayload,T=w.isAnimationActive,C=w.offset,D=w.position,N=w.reverseDirection,I=w.useTranslate3d,B=w.viewBox,R=w.wrapperStyle,L=(p=(t={allowEscapeViewBox:S,coordinate:E,offsetTopLeft:C,position:D,reverseDirection:N,tooltipBox:this.state.lastBoundingBox,useTranslate3d:I,viewBox:B}).allowEscapeViewBox,h=t.coordinate,d=t.offsetTopLeft,y=t.position,v=t.reverseDirection,m=t.tooltipBox,g=t.useTranslate3d,x=t.viewBox,m.height>0&&m.width>0&&h?(r=(e={translateX:s=tK({allowEscapeViewBox:p,coordinate:h,key:"x",offsetTopLeft:d,position:y,reverseDirection:v,tooltipDimension:m.width,viewBox:x,viewBoxDimension:x.width}),translateY:f=tK({allowEscapeViewBox:p,coordinate:h,key:"y",offsetTopLeft:d,position:y,reverseDirection:v,tooltipDimension:m.height,viewBox:x,viewBoxDimension:x.height}),useTranslate3d:g}).translateX,n=e.translateY,l={transform:e.useTranslate3d?"translate3d(".concat(r,"px, ").concat(n,"px, 0)"):"translate(".concat(r,"px, ").concat(n,"px)")}):l=tX,{cssProperties:l,cssClasses:(i=(o={translateX:s,translateY:f,coordinate:h}).coordinate,c=o.translateX,u=o.translateY,(0,b.A)(tV,tG(tG(tG(tG({},"".concat(tV,"-right"),_(c)&&i&&_(i.x)&&c>=i.x),"".concat(tV,"-left"),_(c)&&i&&_(i.x)&&c<i.x),"".concat(tV,"-bottom"),_(u)&&i&&_(i.y)&&u>=i.y),"".concat(tV,"-top"),_(u)&&i&&_(i.y)&&u<i.y)))}),z=L.cssClasses,U=L.cssProperties,F=tJ(tJ({transition:T&&j?"transform ".concat(A,"ms ").concat(P):void 0},U),{},{pointerEvents:"none",visibility:!this.state.dismissed&&j&&M?"visible":"hidden",position:"absolute",top:0,left:0},R);return a().createElement("div",{tabIndex:-1,className:z,style:F,ref:function(t){O.wrapperNode=t}},k)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,t3(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(i.PureComponent),t4={isSsr:!0,get:function(t){return t4[t]},set:function(t,e){if("string"==typeof t)t4[t]=e;else{var r=Object.keys(t);r&&r.length&&r.forEach(function(e){t4[e]=t[e]})}}},t6=r(36315),t8=r.n(t6);function t7(t,e,r){return!0===e?t8()(t,r):H()(e)?t8()(t,e):t}function t9(t){return(t9="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function et(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function ee(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?et(Object(r),!0).forEach(function(e){ei(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):et(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function er(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(er=function(){return!!t})()}function en(t){return(en=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function eo(t,e){return(eo=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function ei(t,e,r){return(e=ea(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function ea(t){var e=function(t,e){if("object"!=t9(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=t9(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==t9(e)?e:e+""}function ec(t){return t.dataKey}var eu=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=en(t),function(t,e){if(e&&("object"===t9(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,er()?Reflect.construct(t,e||[],en(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&eo(r,t),e=[{key:"render",value:function(){var t,e=this,r=this.props,n=r.active,o=r.allowEscapeViewBox,i=r.animationDuration,c=r.animationEasing,u=r.content,l=r.coordinate,s=r.filterNull,f=r.isAnimationActive,p=r.offset,h=r.payload,d=r.payloadUniqBy,y=r.position,v=r.reverseDirection,m=r.useTranslate3d,b=r.viewBox,g=r.wrapperStyle,x=null!=h?h:[];s&&x.length&&(x=t7(h.filter(function(t){return null!=t.value&&(!0!==t.hide||e.props.includeHidden)}),d,ec));var O=x.length>0;return a().createElement(t5,{allowEscapeViewBox:o,animationDuration:i,animationEasing:c,isAnimationActive:f,active:n,coordinate:l,hasPayload:O,offset:p,position:y,reverseDirection:v,useTranslate3d:m,viewBox:b,wrapperStyle:g},(t=ee(ee({},this.props),{},{payload:x}),a().isValidElement(u)?a().cloneElement(u,t):"function"==typeof u?a().createElement(u,t):a().createElement(tW,t)))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,ea(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(i.PureComponent);ei(eu,"displayName","Tooltip"),ei(eu,"defaultProps",{accessibilityLayer:!1,allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",contentStyle:{},coordinate:{x:0,y:0},cursor:!0,cursorStyle:{},filterNull:!0,isAnimationActive:!t4.isSsr,itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,viewBox:{x:0,y:0,height:0,width:0},wrapperStyle:{}});var el=r(69433),es=r.n(el);let ef=Math.cos,ep=Math.sin,eh=Math.sqrt,ed=Math.PI,ey=2*ed,ev={draw(t,e){let r=eh(e/ed);t.moveTo(r,0),t.arc(0,0,r,0,ey)}},em=eh(1/3),eb=2*em,eg=ep(ed/10)/ep(7*ed/10),ex=ep(ey/10)*eg,eO=-ef(ey/10)*eg,ew=eh(3),ej=eh(3)/2,eS=1/eh(12),eA=(eS/2+1)*3;function eP(t){return function(){return t}}let ek=Math.PI,eE=2*ek,eM=eE-1e-6;function eT(t){this._+=t[0];for(let e=1,r=t.length;e<r;++e)this._+=arguments[e]+t[e]}class e_{constructor(t){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=null==t?eT:function(t){let e=Math.floor(t);if(!(e>=0))throw Error(`invalid digits: ${t}`);if(e>15)return eT;let r=10**e;return function(t){this._+=t[0];for(let e=1,n=t.length;e<n;++e)this._+=Math.round(arguments[e]*r)/r+t[e]}}(t)}moveTo(t,e){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+e}`}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(t,e){this._append`L${this._x1=+t},${this._y1=+e}`}quadraticCurveTo(t,e,r,n){this._append`Q${+t},${+e},${this._x1=+r},${this._y1=+n}`}bezierCurveTo(t,e,r,n,o,i){this._append`C${+t},${+e},${+r},${+n},${this._x1=+o},${this._y1=+i}`}arcTo(t,e,r,n,o){if(t*=1,e*=1,r*=1,n*=1,(o*=1)<0)throw Error(`negative radius: ${o}`);let i=this._x1,a=this._y1,c=r-t,u=n-e,l=i-t,s=a-e,f=l*l+s*s;if(null===this._x1)this._append`M${this._x1=t},${this._y1=e}`;else if(f>1e-6)if(Math.abs(s*c-u*l)>1e-6&&o){let p=r-i,h=n-a,d=c*c+u*u,y=Math.sqrt(d),v=Math.sqrt(f),m=o*Math.tan((ek-Math.acos((d+f-(p*p+h*h))/(2*y*v)))/2),b=m/v,g=m/y;Math.abs(b-1)>1e-6&&this._append`L${t+b*l},${e+b*s}`,this._append`A${o},${o},0,0,${+(s*p>l*h)},${this._x1=t+g*c},${this._y1=e+g*u}`}else this._append`L${this._x1=t},${this._y1=e}`}arc(t,e,r,n,o,i){if(t*=1,e*=1,r*=1,i=!!i,r<0)throw Error(`negative radius: ${r}`);let a=r*Math.cos(n),c=r*Math.sin(n),u=t+a,l=e+c,s=1^i,f=i?n-o:o-n;null===this._x1?this._append`M${u},${l}`:(Math.abs(this._x1-u)>1e-6||Math.abs(this._y1-l)>1e-6)&&this._append`L${u},${l}`,r&&(f<0&&(f=f%eE+eE),f>eM?this._append`A${r},${r},0,1,${s},${t-a},${e-c}A${r},${r},0,1,${s},${this._x1=u},${this._y1=l}`:f>1e-6&&this._append`A${r},${r},0,${+(f>=ek)},${s},${this._x1=t+r*Math.cos(o)},${this._y1=e+r*Math.sin(o)}`)}rect(t,e,r,n){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+e}h${r*=1}v${+n}h${-r}Z`}toString(){return this._}}function eC(t){let e=3;return t.digits=function(r){if(!arguments.length)return e;if(null==r)e=null;else{let t=Math.floor(r);if(!(t>=0))throw RangeError(`invalid digits: ${r}`);e=t}return t},()=>new e_(e)}function eD(t){return(eD="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}e_.prototype,eh(3),eh(3);var eN=["type","size","sizeType"];function eI(){return(eI=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function eB(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function eR(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?eB(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=eD(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=eD(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==eD(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):eB(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var eL={symbolCircle:ev,symbolCross:{draw(t,e){let r=eh(e/5)/2;t.moveTo(-3*r,-r),t.lineTo(-r,-r),t.lineTo(-r,-3*r),t.lineTo(r,-3*r),t.lineTo(r,-r),t.lineTo(3*r,-r),t.lineTo(3*r,r),t.lineTo(r,r),t.lineTo(r,3*r),t.lineTo(-r,3*r),t.lineTo(-r,r),t.lineTo(-3*r,r),t.closePath()}},symbolDiamond:{draw(t,e){let r=eh(e/eb),n=r*em;t.moveTo(0,-r),t.lineTo(n,0),t.lineTo(0,r),t.lineTo(-n,0),t.closePath()}},symbolSquare:{draw(t,e){let r=eh(e),n=-r/2;t.rect(n,n,r,r)}},symbolStar:{draw(t,e){let r=eh(.8908130915292852*e),n=ex*r,o=eO*r;t.moveTo(0,-r),t.lineTo(n,o);for(let e=1;e<5;++e){let i=ey*e/5,a=ef(i),c=ep(i);t.lineTo(c*r,-a*r),t.lineTo(a*n-c*o,c*n+a*o)}t.closePath()}},symbolTriangle:{draw(t,e){let r=-eh(e/(3*ew));t.moveTo(0,2*r),t.lineTo(-ew*r,-r),t.lineTo(ew*r,-r),t.closePath()}},symbolWye:{draw(t,e){let r=eh(e/eA),n=r/2,o=r*eS,i=r*eS+r,a=-n;t.moveTo(n,o),t.lineTo(n,i),t.lineTo(a,i),t.lineTo(-.5*n-ej*o,ej*n+-.5*o),t.lineTo(-.5*n-ej*i,ej*n+-.5*i),t.lineTo(-.5*a-ej*i,ej*a+-.5*i),t.lineTo(-.5*n+ej*o,-.5*o-ej*n),t.lineTo(-.5*n+ej*i,-.5*i-ej*n),t.lineTo(-.5*a+ej*i,-.5*i-ej*a),t.closePath()}}},ez=Math.PI/180,eU=function(t,e,r){if("area"===e)return t;switch(r){case"cross":return 5*t*t/9;case"diamond":return .5*t*t/Math.sqrt(3);case"square":return t*t;case"star":var n=18*ez;return 1.25*t*t*(Math.tan(n)-Math.tan(2*n)*Math.pow(Math.tan(n),2));case"triangle":return Math.sqrt(3)*t*t/4;case"wye":return(21-10*Math.sqrt(3))*t*t/8;default:return Math.PI*t*t/4}},eF=function(t){var e,r=t.type,n=void 0===r?"circle":r,o=t.size,i=void 0===o?64:o,c=t.sizeType,u=void 0===c?"area":c,l=eR(eR({},function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,eN)),{},{type:n,size:i,sizeType:u}),s=l.className,f=l.cx,p=l.cy,h=tm(l,!0);return f===+f&&p===+p&&i===+i?a().createElement("path",eI({},h,{className:(0,b.A)("recharts-symbols",s),transform:"translate(".concat(f,", ").concat(p,")"),d:(e=eL["symbol".concat(es()(n))]||ev,(function(t,e){let r=null,n=eC(o);function o(){let o;if(r||(r=o=n()),t.apply(this,arguments).draw(r,+e.apply(this,arguments)),o)return r=null,o+""||null}return t="function"==typeof t?t:eP(t||ev),e="function"==typeof e?e:eP(void 0===e?64:+e),o.type=function(e){return arguments.length?(t="function"==typeof e?e:eP(e),o):t},o.size=function(t){return arguments.length?(e="function"==typeof t?t:eP(+t),o):e},o.context=function(t){return arguments.length?(r=null==t?null:t,o):r},o})().type(e).size(eU(i,u,n))())})):null};function e$(t){return(e$="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function eq(){return(eq=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function eW(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}eF.registerSymbol=function(t,e){eL["symbol".concat(es()(t))]=e};function eH(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(eH=function(){return!!t})()}function eG(t){return(eG=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function eV(t,e){return(eV=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function eX(t,e,r){return(e=eK(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function eK(t){var e=function(t,e){if("object"!=e$(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=e$(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==e$(e)?e:e+""}var eY=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=eG(t),function(t,e){if(e&&("object"===e$(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,eH()?Reflect.construct(t,e||[],eG(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&eV(r,t),e=[{key:"renderIcon",value:function(t){var e=this.props.inactiveColor,r=32/6,n=32/3,o=t.inactive?e:t.color;if("plainline"===t.type)return a().createElement("line",{strokeWidth:4,fill:"none",stroke:o,strokeDasharray:t.payload.strokeDasharray,x1:0,y1:16,x2:32,y2:16,className:"recharts-legend-icon"});if("line"===t.type)return a().createElement("path",{strokeWidth:4,fill:"none",stroke:o,d:"M0,".concat(16,"h").concat(n,"\n            A").concat(r,",").concat(r,",0,1,1,").concat(2*n,",").concat(16,"\n            H").concat(32,"M").concat(2*n,",").concat(16,"\n            A").concat(r,",").concat(r,",0,1,1,").concat(n,",").concat(16),className:"recharts-legend-icon"});if("rect"===t.type)return a().createElement("path",{stroke:"none",fill:o,d:"M0,".concat(4,"h").concat(32,"v").concat(24,"h").concat(-32,"z"),className:"recharts-legend-icon"});if(a().isValidElement(t.legendIcon)){var i=function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?eW(Object(r),!0).forEach(function(e){eX(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):eW(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}({},t);return delete i.legendIcon,a().cloneElement(t.legendIcon,i)}return a().createElement(eF,{fill:o,cx:16,cy:16,size:32,sizeType:"diameter",type:t.type})}},{key:"renderItems",value:function(){var t=this,e=this.props,r=e.payload,n=e.iconSize,o=e.layout,i=e.formatter,c=e.inactiveColor,u={x:0,y:0,width:32,height:32},l={display:"horizontal"===o?"inline-block":"block",marginRight:10},s={display:"inline-block",verticalAlign:"middle",marginRight:4};return r.map(function(e,r){var o=e.formatter||i,f=(0,b.A)(eX(eX({"recharts-legend-item":!0},"legend-item-".concat(r),!0),"inactive",e.inactive));if("none"===e.type)return null;var p=H()(e.value)?null:e.value;F(!H()(e.value),'The name property is also required when using a function for the dataKey of a chart\'s cartesian components. Ex: <Bar name="Name of my Data"/>');var h=e.inactive?c:e.color;return a().createElement("li",eq({className:f,style:l,key:"legend-item-".concat(r)},tr(t.props,e,r)),a().createElement(tN,{width:n,height:n,viewBox:u,style:s},t.renderIcon(e)),a().createElement("span",{className:"recharts-legend-item-text",style:{color:h}},o?o(p,e,r):p))})}},{key:"render",value:function(){var t=this.props,e=t.payload,r=t.layout,n=t.align;return e&&e.length?a().createElement("ul",{className:"recharts-default-legend",style:{padding:0,margin:0,textAlign:"horizontal"===r?n:"left"}},this.renderItems()):null}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,eK(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(i.PureComponent);function eZ(t){return(eZ="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}eX(eY,"displayName","Legend"),eX(eY,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"middle",inactiveColor:"#ccc"});var eJ=["ref"];function eQ(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function e0(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?eQ(Object(r),!0).forEach(function(e){e4(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):eQ(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function e1(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,e6(n.key),n)}}function e2(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(e2=function(){return!!t})()}function e3(t){return(e3=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function e5(t,e){return(e5=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function e4(t,e,r){return(e=e6(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function e6(t){var e=function(t,e){if("object"!=eZ(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=eZ(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==eZ(e)?e:e+""}function e8(t){return t.value}var e7=function(t){var e,r;function n(){var t,e,r;if(!(this instanceof n))throw TypeError("Cannot call a class as a function");for(var o=arguments.length,i=Array(o),a=0;a<o;a++)i[a]=arguments[a];return e=n,r=[].concat(i),e=e3(e),e4(t=function(t,e){if(e&&("object"===eZ(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,e2()?Reflect.construct(e,r||[],e3(this).constructor):e.apply(this,r)),"lastBoundingBox",{width:-1,height:-1}),t}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return n.prototype=Object.create(t&&t.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),t&&e5(n,t),e=[{key:"componentDidMount",value:function(){this.updateBBox()}},{key:"componentDidUpdate",value:function(){this.updateBBox()}},{key:"getBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var t=this.wrapperNode.getBoundingClientRect();return t.height=this.wrapperNode.offsetHeight,t.width=this.wrapperNode.offsetWidth,t}return null}},{key:"updateBBox",value:function(){var t=this.props.onBBoxUpdate,e=this.getBBox();e?(Math.abs(e.width-this.lastBoundingBox.width)>1||Math.abs(e.height-this.lastBoundingBox.height)>1)&&(this.lastBoundingBox.width=e.width,this.lastBoundingBox.height=e.height,t&&t(e)):(-1!==this.lastBoundingBox.width||-1!==this.lastBoundingBox.height)&&(this.lastBoundingBox.width=-1,this.lastBoundingBox.height=-1,t&&t(null))}},{key:"getBBoxSnapshot",value:function(){return this.lastBoundingBox.width>=0&&this.lastBoundingBox.height>=0?e0({},this.lastBoundingBox):{width:0,height:0}}},{key:"getDefaultPosition",value:function(t){var e,r,n=this.props,o=n.layout,i=n.align,a=n.verticalAlign,c=n.margin,u=n.chartWidth,l=n.chartHeight;return t&&(void 0!==t.left&&null!==t.left||void 0!==t.right&&null!==t.right)||(e="center"===i&&"vertical"===o?{left:((u||0)-this.getBBoxSnapshot().width)/2}:"right"===i?{right:c&&c.right||0}:{left:c&&c.left||0}),t&&(void 0!==t.top&&null!==t.top||void 0!==t.bottom&&null!==t.bottom)||(r="middle"===a?{top:((l||0)-this.getBBoxSnapshot().height)/2}:"bottom"===a?{bottom:c&&c.bottom||0}:{top:c&&c.top||0}),e0(e0({},e),r)}},{key:"render",value:function(){var t=this,e=this.props,r=e.content,n=e.width,o=e.height,i=e.wrapperStyle,c=e.payloadUniqBy,u=e.payload,l=e0(e0({position:"absolute",width:n||"auto",height:o||"auto"},this.getDefaultPosition(i)),i);return a().createElement("div",{className:"recharts-legend-wrapper",style:l,ref:function(e){t.wrapperNode=e}},function(t,e){if(a().isValidElement(t))return a().cloneElement(t,e);if("function"==typeof t)return a().createElement(t,e);e.ref;var r=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(e,eJ);return a().createElement(eY,r)}(r,e0(e0({},this.props),{},{payload:t7(u,c,e8)})))}}],r=[{key:"getWithHeight",value:function(t,e){var r=e0(e0({},this.defaultProps),t.props).layout;return"vertical"===r&&_(t.props.height)?{height:t.props.height}:"horizontal"===r?{width:t.props.width||e}:null}}],e&&e1(n.prototype,e),r&&e1(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(i.PureComponent);function e9(){return(e9=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}e4(e7,"displayName","Legend"),e4(e7,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"bottom"});var rt=function(t){var e=t.cx,r=t.cy,n=t.r,o=t.className,i=(0,b.A)("recharts-dot",o);return e===+e&&r===+r&&n===+n?a().createElement("circle",e9({},tm(t,!1),te(t),{className:i,cx:e,cy:r,r:n})):null},re=r(87955),rr=r.n(re),rn=Object.getOwnPropertyNames,ro=Object.getOwnPropertySymbols,ri=Object.prototype.hasOwnProperty;function ra(t,e){return function(r,n,o){return t(r,n,o)&&e(r,n,o)}}function rc(t){return function(e,r,n){if(!e||!r||"object"!=typeof e||"object"!=typeof r)return t(e,r,n);var o=n.cache,i=o.get(e),a=o.get(r);if(i&&a)return i===r&&a===e;o.set(e,r),o.set(r,e);var c=t(e,r,n);return o.delete(e),o.delete(r),c}}function ru(t){return rn(t).concat(ro(t))}var rl=Object.hasOwn||function(t,e){return ri.call(t,e)};function rs(t,e){return t===e||!t&&!e&&t!=t&&e!=e}var rf=Object.getOwnPropertyDescriptor,rp=Object.keys;function rh(t,e,r){var n=t.length;if(e.length!==n)return!1;for(;n-- >0;)if(!r.equals(t[n],e[n],n,n,t,e,r))return!1;return!0}function rd(t,e){return rs(t.getTime(),e.getTime())}function ry(t,e){return t.name===e.name&&t.message===e.message&&t.cause===e.cause&&t.stack===e.stack}function rv(t,e){return t===e}function rm(t,e,r){var n,o,i=t.size;if(i!==e.size)return!1;if(!i)return!0;for(var a=Array(i),c=t.entries(),u=0;(n=c.next())&&!n.done;){for(var l=e.entries(),s=!1,f=0;(o=l.next())&&!o.done;){if(a[f]){f++;continue}var p=n.value,h=o.value;if(r.equals(p[0],h[0],u,f,t,e,r)&&r.equals(p[1],h[1],p[0],h[0],t,e,r)){s=a[f]=!0;break}f++}if(!s)return!1;u++}return!0}function rb(t,e,r){var n=rp(t),o=n.length;if(rp(e).length!==o)return!1;for(;o-- >0;)if(!rA(t,e,r,n[o]))return!1;return!0}function rg(t,e,r){var n,o,i,a=ru(t),c=a.length;if(ru(e).length!==c)return!1;for(;c-- >0;)if(!rA(t,e,r,n=a[c])||(o=rf(t,n),i=rf(e,n),(o||i)&&(!o||!i||o.configurable!==i.configurable||o.enumerable!==i.enumerable||o.writable!==i.writable)))return!1;return!0}function rx(t,e){return rs(t.valueOf(),e.valueOf())}function rO(t,e){return t.source===e.source&&t.flags===e.flags}function rw(t,e,r){var n,o,i=t.size;if(i!==e.size)return!1;if(!i)return!0;for(var a=Array(i),c=t.values();(n=c.next())&&!n.done;){for(var u=e.values(),l=!1,s=0;(o=u.next())&&!o.done;){if(!a[s]&&r.equals(n.value,o.value,n.value,o.value,t,e,r)){l=a[s]=!0;break}s++}if(!l)return!1}return!0}function rj(t,e){var r=t.length;if(e.length!==r)return!1;for(;r-- >0;)if(t[r]!==e[r])return!1;return!0}function rS(t,e){return t.hostname===e.hostname&&t.pathname===e.pathname&&t.protocol===e.protocol&&t.port===e.port&&t.hash===e.hash&&t.username===e.username&&t.password===e.password}function rA(t,e,r,n){return("_owner"===n||"__o"===n||"__v"===n)&&(!!t.$$typeof||!!e.$$typeof)||rl(e,n)&&r.equals(t[n],e[n],n,n,t,e,r)}var rP=Array.isArray,rk="function"==typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView:null,rE=Object.assign,rM=Object.prototype.toString.call.bind(Object.prototype.toString),rT=r_();function r_(t){void 0===t&&(t={});var e,r,n,o,i,a,c,u,l,s,f,p,h,d=t.circular,y=t.createInternalComparator,v=t.createState,m=t.strict,b=(r=(e=function(t){var e=t.circular,r=t.createCustomConfig,n=t.strict,o={areArraysEqual:n?rg:rh,areDatesEqual:rd,areErrorsEqual:ry,areFunctionsEqual:rv,areMapsEqual:n?ra(rm,rg):rm,areNumbersEqual:rs,areObjectsEqual:n?rg:rb,arePrimitiveWrappersEqual:rx,areRegExpsEqual:rO,areSetsEqual:n?ra(rw,rg):rw,areTypedArraysEqual:n?rg:rj,areUrlsEqual:rS};if(r&&(o=rE({},o,r(o))),e){var i=rc(o.areArraysEqual),a=rc(o.areMapsEqual),c=rc(o.areObjectsEqual),u=rc(o.areSetsEqual);o=rE({},o,{areArraysEqual:i,areMapsEqual:a,areObjectsEqual:c,areSetsEqual:u})}return o}(t)).areArraysEqual,n=e.areDatesEqual,o=e.areErrorsEqual,i=e.areFunctionsEqual,a=e.areMapsEqual,c=e.areNumbersEqual,u=e.areObjectsEqual,l=e.arePrimitiveWrappersEqual,s=e.areRegExpsEqual,f=e.areSetsEqual,p=e.areTypedArraysEqual,h=e.areUrlsEqual,function(t,e,d){if(t===e)return!0;if(null==t||null==e)return!1;var y=typeof t;if(y!==typeof e)return!1;if("object"!==y)return"number"===y?c(t,e,d):"function"===y&&i(t,e,d);var v=t.constructor;if(v!==e.constructor)return!1;if(v===Object)return u(t,e,d);if(rP(t))return r(t,e,d);if(null!=rk&&rk(t))return p(t,e,d);if(v===Date)return n(t,e,d);if(v===RegExp)return s(t,e,d);if(v===Map)return a(t,e,d);if(v===Set)return f(t,e,d);var m=rM(t);return"[object Date]"===m?n(t,e,d):"[object RegExp]"===m?s(t,e,d):"[object Map]"===m?a(t,e,d):"[object Set]"===m?f(t,e,d):"[object Object]"===m?"function"!=typeof t.then&&"function"!=typeof e.then&&u(t,e,d):"[object URL]"===m?h(t,e,d):"[object Error]"===m?o(t,e,d):"[object Arguments]"===m?u(t,e,d):("[object Boolean]"===m||"[object Number]"===m||"[object String]"===m)&&l(t,e,d)}),g=y?y(b):function(t,e,r,n,o,i,a){return b(t,e,a)};return function(t){var e=t.circular,r=t.comparator,n=t.createState,o=t.equals,i=t.strict;if(n)return function(t,a){var c=n(),u=c.cache;return r(t,a,{cache:void 0===u?e?new WeakMap:void 0:u,equals:o,meta:c.meta,strict:i})};if(e)return function(t,e){return r(t,e,{cache:new WeakMap,equals:o,meta:void 0,strict:i})};var a={cache:void 0,equals:o,meta:void 0,strict:i};return function(t,e){return r(t,e,a)}}({circular:void 0!==d&&d,comparator:b,createState:v,equals:g,strict:void 0!==m&&m})}function rC(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=-1;requestAnimationFrame(function n(o){if(r<0&&(r=o),o-r>e)t(o),r=-1;else{var i;i=n,"undefined"!=typeof requestAnimationFrame&&requestAnimationFrame(i)}})}function rD(t){return(rD="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function rN(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function rI(t){return(rI="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function rB(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function rR(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?rB(Object(r),!0).forEach(function(e){rL(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):rB(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function rL(t,e,r){var n;return(n=function(t,e){if("object"!==rI(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==rI(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"===rI(n)?n:String(n))in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}r_({strict:!0}),r_({circular:!0}),r_({circular:!0,strict:!0}),r_({createInternalComparator:function(){return rs}}),r_({strict:!0,createInternalComparator:function(){return rs}}),r_({circular:!0,createInternalComparator:function(){return rs}}),r_({circular:!0,createInternalComparator:function(){return rs},strict:!0});var rz=function(t){return t},rU=function(t,e){return Object.keys(e).reduce(function(r,n){return rR(rR({},r),{},rL({},n,t(n,e[n])))},{})},rF=function(t,e,r){return t.map(function(t){return"".concat(t.replace(/([A-Z])/g,function(t){return"-".concat(t.toLowerCase())})," ").concat(e,"ms ").concat(r)}).join(",")},r$=function(t,e,r,n,o,i,a,c){};function rq(t,e){if(t){if("string"==typeof t)return rW(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return rW(t,e)}}function rW(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var rH=function(t,e){return[0,3*t,3*e-6*t,3*t-3*e+1]},rG=function(t,e){return t.map(function(t,r){return t*Math.pow(e,r)}).reduce(function(t,e){return t+e})},rV=function(t,e){return function(r){return rG(rH(t,e),r)}},rX=function(){for(var t,e,r=arguments.length,n=Array(r),o=0;o<r;o++)n[o]=arguments[o];var i=n[0],a=n[1],c=n[2],u=n[3];if(1===n.length)switch(n[0]){case"linear":i=0,a=0,c=1,u=1;break;case"ease":i=.25,a=.1,c=.25,u=1;break;case"ease-in":i=.42,a=0,c=1,u=1;break;case"ease-out":i=.42,a=0,c=.58,u=1;break;case"ease-in-out":i=0,a=0,c=.58,u=1;break;default:var l=n[0].split("(");if("cubic-bezier"===l[0]&&4===l[1].split(")")[0].split(",").length){var s,f=function(t){if(Array.isArray(t))return t}(s=l[1].split(")")[0].split(",").map(function(t){return parseFloat(t)}))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{i=(r=r.call(t)).next,!1;for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(s,4)||rq(s,4)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();i=f[0],a=f[1],c=f[2],u=f[3]}else r$(!1,"[configBezier]: arguments should be one of oneOf 'linear', 'ease', 'ease-in', 'ease-out', 'ease-in-out','cubic-bezier(x1,y1,x2,y2)', instead received %s",n)}r$([i,c,a,u].every(function(t){return"number"==typeof t&&t>=0&&t<=1}),"[configBezier]: arguments should be x1, y1, x2, y2 of [0, 1] instead received %s",n);var p=rV(i,c),h=rV(a,u),d=(t=i,e=c,function(r){var n;return rG([].concat(function(t){if(Array.isArray(t))return rW(t)}(n=rH(t,e).map(function(t,e){return t*e}).slice(1))||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(n)||rq(n)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),[0]),r)}),y=function(t){for(var e=t>1?1:t,r=e,n=0;n<8;++n){var o,i=p(r)-e,a=d(r);if(1e-4>Math.abs(i-e)||a<1e-4)break;r=(o=r-i/a)>1?1:o<0?0:o}return h(r)};return y.isStepper=!1,y},rK=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.stiff,r=void 0===e?100:e,n=t.damping,o=void 0===n?8:n,i=t.dt,a=void 0===i?17:i,c=function(t,e,n){var i=n+(-(t-e)*r-n*o)*a/1e3,c=n*a/1e3+t;return 1e-4>Math.abs(c-e)&&1e-4>Math.abs(i)?[e,0]:[c,i]};return c.isStepper=!0,c.dt=a,c},rY=function(){for(var t=arguments.length,e=Array(t),r=0;r<t;r++)e[r]=arguments[r];var n=e[0];if("string"==typeof n)switch(n){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return rX(n);case"spring":return rK();default:if("cubic-bezier"===n.split("(")[0])return rX(n);r$(!1,"[configEasing]: first argument should be one of 'ease', 'ease-in', 'ease-out', 'ease-in-out','cubic-bezier(x1,y1,x2,y2)', 'linear' and 'spring', instead  received %s",e)}return"function"==typeof n?n:(r$(!1,"[configEasing]: first argument type should be function or string, instead received %s",e),null)};function rZ(t){return(rZ="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function rJ(t){return function(t){if(Array.isArray(t))return r3(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||r2(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function rQ(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function r0(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?rQ(Object(r),!0).forEach(function(e){r1(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):rQ(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function r1(t,e,r){var n;return(n=function(t,e){if("object"!==rZ(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==rZ(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"===rZ(n)?n:String(n))in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function r2(t,e){if(t){if("string"==typeof t)return r3(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return r3(t,e)}}function r3(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var r5=function(t,e,r){return t+(e-t)*r},r4=function(t){return t.from!==t.to},r6=function t(e,r,n){var o=rU(function(t,r){if(r4(r)){var n,o=function(t){if(Array.isArray(t))return t}(n=e(r.from,r.to,r.velocity))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{i=(r=r.call(t)).next,!1;for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(n,2)||r2(n,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),i=o[0],a=o[1];return r0(r0({},r),{},{from:i,velocity:a})}return r},r);return n<1?rU(function(t,e){return r4(e)?r0(r0({},e),{},{velocity:r5(e.velocity,o[t].velocity,n),from:r5(e.from,o[t].from,n)}):e},r):t(e,o,n-1)};let r8=function(t,e,r,n,o){var i,a,c=[Object.keys(t),Object.keys(e)].reduce(function(t,e){return t.filter(function(t){return e.includes(t)})}),u=c.reduce(function(r,n){return r0(r0({},r),{},r1({},n,[t[n],e[n]]))},{}),l=c.reduce(function(r,n){return r0(r0({},r),{},r1({},n,{from:t[n],velocity:0,to:e[n]}))},{}),s=-1,f=function(){return null};return f=r.isStepper?function(n){i||(i=n);var a=(n-i)/r.dt;l=r6(r,l,a),o(r0(r0(r0({},t),e),rU(function(t,e){return e.from},l))),i=n,Object.values(l).filter(r4).length&&(s=requestAnimationFrame(f))}:function(i){a||(a=i);var c=(i-a)/n,l=rU(function(t,e){return r5.apply(void 0,rJ(e).concat([r(c)]))},u);if(o(r0(r0(r0({},t),e),l)),c<1)s=requestAnimationFrame(f);else{var p=rU(function(t,e){return r5.apply(void 0,rJ(e).concat([r(1)]))},u);o(r0(r0(r0({},t),e),p))}},function(){return requestAnimationFrame(f),function(){cancelAnimationFrame(s)}}};function r7(t){return(r7="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var r9=["children","begin","duration","attributeName","easing","isActive","steps","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart"];function nt(t){return function(t){if(Array.isArray(t))return ne(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return ne(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ne(t,e)}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ne(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function nr(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function nn(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?nr(Object(r),!0).forEach(function(e){no(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):nr(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function no(t,e,r){return(e=ni(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function ni(t){var e=function(t,e){if("object"!==r7(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==r7(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===r7(e)?e:String(e)}function na(t,e){return(na=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function nc(t,e){if(e&&("object"===r7(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return nu(t)}function nu(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function nl(t){return(nl=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}var ns=function(t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");o.prototype=Object.create(t&&t.prototype,{constructor:{value:o,writable:!0,configurable:!0}}),Object.defineProperty(o,"prototype",{writable:!1}),t&&na(o,t);var e,r,n=(e=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}(),function(){var t,r=nl(o);return t=e?Reflect.construct(r,arguments,nl(this).constructor):r.apply(this,arguments),nc(this,t)});function o(t,e){if(!(this instanceof o))throw TypeError("Cannot call a class as a function");var r=n.call(this,t,e),i=r.props,a=i.isActive,c=i.attributeName,u=i.from,l=i.to,s=i.steps,f=i.children,p=i.duration;if(r.handleStyleChange=r.handleStyleChange.bind(nu(r)),r.changeStyle=r.changeStyle.bind(nu(r)),!a||p<=0)return r.state={style:{}},"function"==typeof f&&(r.state={style:l}),nc(r);if(s&&s.length)r.state={style:s[0].style};else if(u){if("function"==typeof f)return r.state={style:u},nc(r);r.state={style:c?no({},c,u):u}}else r.state={style:{}};return r}return r=[{key:"componentDidMount",value:function(){var t=this.props,e=t.isActive,r=t.canBegin;this.mounted=!0,e&&r&&this.runAnimation(this.props)}},{key:"componentDidUpdate",value:function(t){var e=this.props,r=e.isActive,n=e.canBegin,o=e.attributeName,i=e.shouldReAnimate,a=e.to,c=e.from,u=this.state.style;if(n){if(!r){var l={style:o?no({},o,a):a};this.state&&u&&(o&&u[o]!==a||!o&&u!==a)&&this.setState(l);return}if(!rT(t.to,a)||!t.canBegin||!t.isActive){var s=!t.canBegin||!t.isActive;this.manager&&this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var f=s||i?c:t.to;if(this.state&&u){var p={style:o?no({},o,f):f};(o&&u[o]!==f||!o&&u!==f)&&this.setState(p)}this.runAnimation(nn(nn({},this.props),{},{from:f,begin:0}))}}}},{key:"componentWillUnmount",value:function(){this.mounted=!1;var t=this.props.onAnimationEnd;this.unSubscribe&&this.unSubscribe(),this.manager&&(this.manager.stop(),this.manager=null),this.stopJSAnimation&&this.stopJSAnimation(),t&&t()}},{key:"handleStyleChange",value:function(t){this.changeStyle(t)}},{key:"changeStyle",value:function(t){this.mounted&&this.setState({style:t})}},{key:"runJSAnimation",value:function(t){var e=this,r=t.from,n=t.to,o=t.duration,i=t.easing,a=t.begin,c=t.onAnimationEnd,u=t.onAnimationStart,l=r8(r,n,rY(i),o,this.changeStyle);this.manager.start([u,a,function(){e.stopJSAnimation=l()},o,c])}},{key:"runStepAnimation",value:function(t){var e=this,r=t.steps,n=t.begin,o=t.onAnimationStart,i=r[0],a=i.style,c=i.duration;return this.manager.start([o].concat(nt(r.reduce(function(t,n,o){if(0===o)return t;var i=n.duration,a=n.easing,c=void 0===a?"ease":a,u=n.style,l=n.properties,s=n.onAnimationEnd,f=o>0?r[o-1]:n,p=l||Object.keys(u);if("function"==typeof c||"spring"===c)return[].concat(nt(t),[e.runJSAnimation.bind(e,{from:f.style,to:u,duration:i,easing:c}),i]);var h=rF(p,i,c),d=nn(nn(nn({},f.style),u),{},{transition:h});return[].concat(nt(t),[d,i,s]).filter(rz)},[a,Math.max(void 0===c?0:c,n)])),[t.onAnimationEnd]))}},{key:"runAnimation",value:function(t){this.manager||(this.manager=(r=function(){return null},n=!1,o=function t(e){if(!n){if(Array.isArray(e)){if(!e.length)return;var o=function(t){if(Array.isArray(t))return t}(e)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(e)||function(t,e){if(t){if("string"==typeof t)return rN(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return rN(t,e)}}(e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),i=o[0],a=o.slice(1);return"number"==typeof i?void rC(t.bind(null,a),i):(t(i),void rC(t.bind(null,a)))}"object"===rD(e)&&r(e),"function"==typeof e&&e()}},{stop:function(){n=!0},start:function(t){n=!1,o(t)},subscribe:function(t){return r=t,function(){r=function(){return null}}}}));var e,r,n,o,i=t.begin,a=t.duration,c=t.attributeName,u=t.to,l=t.easing,s=t.onAnimationStart,f=t.onAnimationEnd,p=t.steps,h=t.children,d=this.manager;if(this.unSubscribe=d.subscribe(this.handleStyleChange),"function"==typeof l||"function"==typeof h||"spring"===l)return void this.runJSAnimation(t);if(p.length>1)return void this.runStepAnimation(t);var y=c?no({},c,u):u,v=rF(Object.keys(y),a,l);d.start([s,i,nn(nn({},y),{},{transition:v}),a,f])}},{key:"render",value:function(){var t=this.props,e=t.children,r=(t.begin,t.duration),n=(t.attributeName,t.easing,t.isActive),o=(t.steps,t.from,t.to,t.canBegin,t.onAnimationEnd,t.shouldReAnimate,t.onAnimationReStart,function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r,n,o={},i=Object.keys(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||(o[r]=t[r]);return o}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,r9)),c=i.Children.count(e),u=this.state.style;if("function"==typeof e)return e(u);if(!n||0===c||r<=0)return e;var l=function(t){var e=t.props,r=e.style,n=e.className;return(0,i.cloneElement)(t,nn(nn({},o),{},{style:nn(nn({},void 0===r?{}:r),u),className:n}))};return 1===c?l(i.Children.only(e)):a().createElement("div",null,i.Children.map(e,function(t){return l(t)}))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,ni(n.key),n)}}(o.prototype,r),Object.defineProperty(o,"prototype",{writable:!1}),o}(i.PureComponent);function nf(t){return(nf="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function np(){return(np=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function nh(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function nd(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function ny(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?nd(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=nf(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=nf(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==nf(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):nd(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}ns.displayName="Animate",ns.defaultProps={begin:0,duration:1e3,from:"",to:"",attributeName:"",easing:"ease",isActive:!0,canBegin:!0,steps:[],onAnimationEnd:function(){},onAnimationStart:function(){}},ns.propTypes={from:rr().oneOfType([rr().object,rr().string]),to:rr().oneOfType([rr().object,rr().string]),attributeName:rr().string,duration:rr().number,begin:rr().number,easing:rr().oneOfType([rr().string,rr().func]),steps:rr().arrayOf(rr().shape({duration:rr().number.isRequired,style:rr().object.isRequired,easing:rr().oneOfType([rr().oneOf(["ease","ease-in","ease-out","ease-in-out","linear"]),rr().func]),properties:rr().arrayOf("string"),onAnimationEnd:rr().func})),children:rr().oneOfType([rr().node,rr().func]),isActive:rr().bool,canBegin:rr().bool,onAnimationEnd:rr().func,shouldReAnimate:rr().bool,onAnimationStart:rr().func,onAnimationReStart:rr().func};var nv=function(t,e,r,n,o){var i,a=Math.min(Math.abs(r)/2,Math.abs(n)/2),c=n>=0?1:-1,u=r>=0?1:-1,l=+(n>=0&&r>=0||n<0&&r<0);if(a>0&&o instanceof Array){for(var s=[0,0,0,0],f=0;f<4;f++)s[f]=o[f]>a?a:o[f];i="M".concat(t,",").concat(e+c*s[0]),s[0]>0&&(i+="A ".concat(s[0],",").concat(s[0],",0,0,").concat(l,",").concat(t+u*s[0],",").concat(e)),i+="L ".concat(t+r-u*s[1],",").concat(e),s[1]>0&&(i+="A ".concat(s[1],",").concat(s[1],",0,0,").concat(l,",\n        ").concat(t+r,",").concat(e+c*s[1])),i+="L ".concat(t+r,",").concat(e+n-c*s[2]),s[2]>0&&(i+="A ".concat(s[2],",").concat(s[2],",0,0,").concat(l,",\n        ").concat(t+r-u*s[2],",").concat(e+n)),i+="L ".concat(t+u*s[3],",").concat(e+n),s[3]>0&&(i+="A ".concat(s[3],",").concat(s[3],",0,0,").concat(l,",\n        ").concat(t,",").concat(e+n-c*s[3])),i+="Z"}else if(a>0&&o===+o&&o>0){var p=Math.min(a,o);i="M ".concat(t,",").concat(e+c*p,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t+u*p,",").concat(e,"\n            L ").concat(t+r-u*p,",").concat(e,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t+r,",").concat(e+c*p,"\n            L ").concat(t+r,",").concat(e+n-c*p,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t+r-u*p,",").concat(e+n,"\n            L ").concat(t+u*p,",").concat(e+n,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t,",").concat(e+n-c*p," Z")}else i="M ".concat(t,",").concat(e," h ").concat(r," v ").concat(n," h ").concat(-r," Z");return i},nm=function(t,e){if(!t||!e)return!1;var r=t.x,n=t.y,o=e.x,i=e.y,a=e.width,c=e.height;if(Math.abs(a)>0&&Math.abs(c)>0){var u=Math.min(o,o+a),l=Math.max(o,o+a),s=Math.min(i,i+c),f=Math.max(i,i+c);return r>=u&&r<=l&&n>=s&&n<=f}return!1},nb={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},ng=function(t){var e,r=ny(ny({},nb),t),n=(0,i.useRef)(),o=function(t){if(Array.isArray(t))return t}(e=(0,i.useState)(-1))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{i=(r=r.call(t)).next,!1;for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(e,2)||function(t,e){if(t){if("string"==typeof t)return nh(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return nh(t,e)}}(e,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),c=o[0],u=o[1];(0,i.useEffect)(function(){if(n.current&&n.current.getTotalLength)try{var t=n.current.getTotalLength();t&&u(t)}catch(t){}},[]);var l=r.x,s=r.y,f=r.width,p=r.height,h=r.radius,d=r.className,y=r.animationEasing,v=r.animationDuration,m=r.animationBegin,g=r.isAnimationActive,x=r.isUpdateAnimationActive;if(l!==+l||s!==+s||f!==+f||p!==+p||0===f||0===p)return null;var O=(0,b.A)("recharts-rectangle",d);return x?a().createElement(ns,{canBegin:c>0,from:{width:f,height:p,x:l,y:s},to:{width:f,height:p,x:l,y:s},duration:v,animationEasing:y,isActive:x},function(t){var e=t.width,o=t.height,i=t.x,u=t.y;return a().createElement(ns,{canBegin:c>0,from:"0px ".concat(-1===c?1:c,"px"),to:"".concat(c,"px 0px"),attributeName:"strokeDasharray",begin:m,duration:v,isActive:g,easing:y},a().createElement("path",np({},tm(r,!0),{className:O,d:nv(i,u,e,o,h),ref:n})))}):a().createElement("path",np({},tm(r,!0),{className:O,d:nv(l,s,f,p,h)}))};function nx(t,e){switch(arguments.length){case 0:break;case 1:this.range(t);break;default:this.range(e).domain(t)}return this}function nO(t,e){switch(arguments.length){case 0:break;case 1:"function"==typeof t?this.interpolator(t):this.range(t);break;default:this.domain(t),"function"==typeof e?this.interpolator(e):this.range(e)}return this}class nw extends Map{constructor(t,e=nS){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:e}}),null!=t)for(let[e,r]of t)this.set(e,r)}get(t){return super.get(nj(this,t))}has(t){return super.has(nj(this,t))}set(t,e){return super.set(function({_intern:t,_key:e},r){let n=e(r);return t.has(n)?t.get(n):(t.set(n,r),r)}(this,t),e)}delete(t){return super.delete(function({_intern:t,_key:e},r){let n=e(r);return t.has(n)&&(r=t.get(n),t.delete(n)),r}(this,t))}}function nj({_intern:t,_key:e},r){let n=e(r);return t.has(n)?t.get(n):r}function nS(t){return null!==t&&"object"==typeof t?t.valueOf():t}let nA=Symbol("implicit");function nP(){var t=new nw,e=[],r=[],n=nA;function o(o){let i=t.get(o);if(void 0===i){if(n!==nA)return n;t.set(o,i=e.push(o)-1)}return r[i%r.length]}return o.domain=function(r){if(!arguments.length)return e.slice();for(let n of(e=[],t=new nw,r))t.has(n)||t.set(n,e.push(n)-1);return o},o.range=function(t){return arguments.length?(r=Array.from(t),o):r.slice()},o.unknown=function(t){return arguments.length?(n=t,o):n},o.copy=function(){return nP(e,r).unknown(n)},nx.apply(o,arguments),o}function nk(){var t,e,r=nP().unknown(void 0),n=r.domain,o=r.range,i=0,a=1,c=!1,u=0,l=0,s=.5;function f(){var r=n().length,f=a<i,p=f?a:i,h=f?i:a;t=(h-p)/Math.max(1,r-u+2*l),c&&(t=Math.floor(t)),p+=(h-p-t*(r-u))*s,e=t*(1-u),c&&(p=Math.round(p),e=Math.round(e));var d=(function(t,e,r){t*=1,e*=1,r=(o=arguments.length)<2?(e=t,t=0,1):o<3?1:+r;for(var n=-1,o=0|Math.max(0,Math.ceil((e-t)/r)),i=Array(o);++n<o;)i[n]=t+n*r;return i})(r).map(function(e){return p+t*e});return o(f?d.reverse():d)}return delete r.unknown,r.domain=function(t){return arguments.length?(n(t),f()):n()},r.range=function(t){return arguments.length?([i,a]=t,i*=1,a*=1,f()):[i,a]},r.rangeRound=function(t){return[i,a]=t,i*=1,a*=1,c=!0,f()},r.bandwidth=function(){return e},r.step=function(){return t},r.round=function(t){return arguments.length?(c=!!t,f()):c},r.padding=function(t){return arguments.length?(u=Math.min(1,l=+t),f()):u},r.paddingInner=function(t){return arguments.length?(u=Math.min(1,t),f()):u},r.paddingOuter=function(t){return arguments.length?(l=+t,f()):l},r.align=function(t){return arguments.length?(s=Math.max(0,Math.min(1,t)),f()):s},r.copy=function(){return nk(n(),[i,a]).round(c).paddingInner(u).paddingOuter(l).align(s)},nx.apply(f(),arguments)}function nE(){return function t(e){var r=e.copy;return e.padding=e.paddingOuter,delete e.paddingInner,delete e.paddingOuter,e.copy=function(){return t(r())},e}(nk.apply(null,arguments).paddingInner(1))}function nM(t){return(nM="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function nT(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function n_(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?nT(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=nM(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=nM(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==nM(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):nT(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function nC(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var nD={widthCache:{},cacheCount:0},nN={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},nI="recharts_measurement_span",nB=function(t){var e,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(null==t||t4.isSsr)return{width:0,height:0};var n=(Object.keys(e=n_({},r)).forEach(function(t){e[t]||delete e[t]}),e),o=JSON.stringify({text:t,copyStyle:n});if(nD.widthCache[o])return nD.widthCache[o];try{var i=document.getElementById(nI);i||((i=document.createElement("span")).setAttribute("id",nI),i.setAttribute("aria-hidden","true"),document.body.appendChild(i));var a=n_(n_({},nN),n);Object.assign(i.style,a),i.textContent="".concat(t);var c=i.getBoundingClientRect(),u={width:c.width,height:c.height};return nD.widthCache[o]=u,++nD.cacheCount>2e3&&(nD.cacheCount=0,nD.widthCache={}),u}catch(t){return{width:0,height:0}}};function nR(t){return(nR="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function nL(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,e)||function(t,e){if(t){if("string"==typeof t)return nz(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return nz(t,e)}}(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function nz(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function nU(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,function(t){var e=function(t,e){if("object"!=nR(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=nR(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==nR(e)?e:e+""}(n.key),n)}}var nF=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,n$=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,nq=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,nW=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,nH={cm:96/2.54,mm:96/25.4,pt:96/72,pc:16,in:96,Q:96/101.6,px:1},nG=Object.keys(nH),nV=function(){var t,e;function r(t,e){if(!(this instanceof r))throw TypeError("Cannot call a class as a function");this.num=t,this.unit=e,this.num=t,this.unit=e,Number.isNaN(t)&&(this.unit=""),""===e||nq.test(e)||(this.num=NaN,this.unit=""),nG.includes(e)&&(this.num=t*nH[e],this.unit="px")}return t=[{key:"add",value:function(t){return this.unit!==t.unit?new r(NaN,""):new r(this.num+t.num,this.unit)}},{key:"subtract",value:function(t){return this.unit!==t.unit?new r(NaN,""):new r(this.num-t.num,this.unit)}},{key:"multiply",value:function(t){return""!==this.unit&&""!==t.unit&&this.unit!==t.unit?new r(NaN,""):new r(this.num*t.num,this.unit||t.unit)}},{key:"divide",value:function(t){return""!==this.unit&&""!==t.unit&&this.unit!==t.unit?new r(NaN,""):new r(this.num/t.num,this.unit||t.unit)}},{key:"toString",value:function(){return"".concat(this.num).concat(this.unit)}},{key:"isNaN",value:function(){return Number.isNaN(this.num)}}],e=[{key:"parse",value:function(t){var e,n=nL(null!=(e=nW.exec(t))?e:[],3),o=n[1],i=n[2];return new r(parseFloat(o),null!=i?i:"")}}],t&&nU(r.prototype,t),e&&nU(r,e),Object.defineProperty(r,"prototype",{writable:!1}),r}();function nX(t){if(t.includes("NaN"))return"NaN";for(var e=t;e.includes("*")||e.includes("/");){var r,n=nL(null!=(r=nF.exec(e))?r:[],4),o=n[1],i=n[2],a=n[3],c=nV.parse(null!=o?o:""),u=nV.parse(null!=a?a:""),l="*"===i?c.multiply(u):c.divide(u);if(l.isNaN())return"NaN";e=e.replace(nF,l.toString())}for(;e.includes("+")||/.-\d+(?:\.\d+)?/.test(e);){var s,f=nL(null!=(s=n$.exec(e))?s:[],4),p=f[1],h=f[2],d=f[3],y=nV.parse(null!=p?p:""),v=nV.parse(null!=d?d:""),m="+"===h?y.add(v):y.subtract(v);if(m.isNaN())return"NaN";e=e.replace(n$,m.toString())}return e}var nK=/\(([^()]*)\)/;function nY(t){var e=function(t){try{var e;return e=t.replace(/\s+/g,""),e=function(t){for(var e=t;e.includes("(");){var r=nL(nK.exec(e),2)[1];e=e.replace(nK,nX(r))}return e}(e),e=nX(e)}catch(t){return"NaN"}}(t.slice(5,-1));return"NaN"===e?"":e}var nZ=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],nJ=["dx","dy","angle","className","breakAll"];function nQ(){return(nQ=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function n0(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function n1(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,e)||function(t,e){if(t){if("string"==typeof t)return n2(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return n2(t,e)}}(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function n2(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var n3=/[ \f\n\r\t\v\u2028\u2029]+/,n5=function(t){var e=t.children,r=t.breakAll,n=t.style;try{var o=[];q()(e)||(o=r?e.toString().split(""):e.toString().split(n3));var i=o.map(function(t){return{word:t,width:nB(t,n).width}}),a=r?0:nB("\xa0",n).width;return{wordsWithComputedWidth:i,spaceWidth:a}}catch(t){return null}},n4=function(t,e,r,n,o){var i,a=t.maxLines,c=t.children,u=t.style,l=t.breakAll,s=_(a),f=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return t.reduce(function(t,e){var i=e.word,a=e.width,c=t[t.length-1];return c&&(null==n||o||c.width+a+r<Number(n))?(c.words.push(i),c.width+=a+r):t.push({words:[i],width:a}),t},[])},p=f(e);if(!s)return p;for(var h=function(t){var e=f(n5({breakAll:l,style:u,children:c.slice(0,t)+"…"}).wordsWithComputedWidth);return[e.length>a||e.reduce(function(t,e){return t.width>e.width?t:e}).width>Number(n),e]},d=0,y=c.length-1,v=0;d<=y&&v<=c.length-1;){var m=Math.floor((d+y)/2),b=n1(h(m-1),2),g=b[0],x=b[1],O=n1(h(m),1)[0];if(g||O||(d=m+1),g&&O&&(y=m-1),!g&&O){i=x;break}v++}return i||p},n6=function(t){return[{words:q()(t)?[]:t.toString().split(n3)}]},n8=function(t){var e=t.width,r=t.scaleToFit,n=t.children,o=t.style,i=t.breakAll,a=t.maxLines;if((e||r)&&!t4.isSsr){var c=n5({breakAll:i,children:n,style:o});if(!c)return n6(n);var u=c.wordsWithComputedWidth,l=c.spaceWidth;return n4({breakAll:i,children:n,maxLines:a,style:o},u,l,e,r)}return n6(n)},n7="#808080",n9=function(t){var e,r=t.x,n=void 0===r?0:r,o=t.y,c=void 0===o?0:o,u=t.lineHeight,l=void 0===u?"1em":u,s=t.capHeight,f=void 0===s?"0.71em":s,p=t.scaleToFit,h=void 0!==p&&p,d=t.textAnchor,y=t.verticalAnchor,v=t.fill,m=void 0===v?n7:v,g=n0(t,nZ),x=(0,i.useMemo)(function(){return n8({breakAll:g.breakAll,children:g.children,maxLines:g.maxLines,scaleToFit:h,style:g.style,width:g.width})},[g.breakAll,g.children,g.maxLines,h,g.style,g.width]),O=g.dx,w=g.dy,j=g.angle,S=g.className,A=g.breakAll,P=n0(g,nJ);if(!C(n)||!C(c))return null;var k=n+(_(O)?O:0),E=c+(_(w)?w:0);switch(void 0===y?"end":y){case"start":e=nY("calc(".concat(f,")"));break;case"middle":e=nY("calc(".concat((x.length-1)/2," * -").concat(l," + (").concat(f," / 2))"));break;default:e=nY("calc(".concat(x.length-1," * -").concat(l,")"))}var M=[];if(h){var T=x[0].width,D=g.width;M.push("scale(".concat((_(D)?D/T:1)/T,")"))}return j&&M.push("rotate(".concat(j,", ").concat(k,", ").concat(E,")")),M.length&&(P.transform=M.join(" ")),a().createElement("text",nQ({},tm(P,!0),{x:k,y:E,className:(0,b.A)("recharts-text",S),textAnchor:void 0===d?"start":d,fill:m.includes("url")?n7:m}),x.map(function(t,r){var n=t.words.join(A?"":" ");return a().createElement("tspan",{x:k,dy:0===r?e:l,key:"".concat(n,"-").concat(r)},n)}))};let ot=Math.sqrt(50),oe=Math.sqrt(10),or=Math.sqrt(2);function on(t,e,r){let n,o,i,a=(e-t)/Math.max(0,r),c=Math.floor(Math.log10(a)),u=a/Math.pow(10,c),l=u>=ot?10:u>=oe?5:u>=or?2:1;return(c<0?(n=Math.round(t*(i=Math.pow(10,-c)/l)),o=Math.round(e*i),n/i<t&&++n,o/i>e&&--o,i=-i):(n=Math.round(t/(i=Math.pow(10,c)*l)),o=Math.round(e/i),n*i<t&&++n,o*i>e&&--o),o<n&&.5<=r&&r<2)?on(t,e,2*r):[n,o,i]}function oo(t,e,r){if(e*=1,t*=1,!((r*=1)>0))return[];if(t===e)return[t];let n=e<t,[o,i,a]=n?on(e,t,r):on(t,e,r);if(!(i>=o))return[];let c=i-o+1,u=Array(c);if(n)if(a<0)for(let t=0;t<c;++t)u[t]=-((i-t)/a);else for(let t=0;t<c;++t)u[t]=(i-t)*a;else if(a<0)for(let t=0;t<c;++t)u[t]=-((o+t)/a);else for(let t=0;t<c;++t)u[t]=(o+t)*a;return u}function oi(t,e,r){return on(t*=1,e*=1,r*=1)[2]}function oa(t,e,r){e*=1,t*=1,r*=1;let n=e<t,o=n?oi(e,t,r):oi(t,e,r);return(n?-1:1)*(o<0?-(1/o):o)}function oc(t,e){return null==t||null==e?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function ou(t,e){return null==t||null==e?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function ol(t){let e,r,n;function o(t,n,i=0,a=t.length){if(i<a){if(0!==e(n,n))return a;do{let e=i+a>>>1;0>r(t[e],n)?i=e+1:a=e}while(i<a)}return i}return 2!==t.length?(e=oc,r=(e,r)=>oc(t(e),r),n=(e,r)=>t(e)-r):(e=t===oc||t===ou?t:os,r=t,n=t),{left:o,center:function(t,e,r=0,i=t.length){let a=o(t,e,r,i-1);return a>r&&n(t[a-1],e)>-n(t[a],e)?a-1:a},right:function(t,n,o=0,i=t.length){if(o<i){if(0!==e(n,n))return i;do{let e=o+i>>>1;0>=r(t[e],n)?o=e+1:i=e}while(o<i)}return o}}}function os(){return 0}function of(t){return null===t?NaN:+t}let op=ol(oc),oh=op.right;function od(t,e,r){t.prototype=e.prototype=r,r.constructor=t}function oy(t,e){var r=Object.create(t.prototype);for(var n in e)r[n]=e[n];return r}function ov(){}op.left,ol(of).center;var om="\\s*([+-]?\\d+)\\s*",ob="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",og="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",ox=/^#([0-9a-f]{3,8})$/,oO=RegExp(`^rgb\\(${om},${om},${om}\\)$`),ow=RegExp(`^rgb\\(${og},${og},${og}\\)$`),oj=RegExp(`^rgba\\(${om},${om},${om},${ob}\\)$`),oS=RegExp(`^rgba\\(${og},${og},${og},${ob}\\)$`),oA=RegExp(`^hsl\\(${ob},${og},${og}\\)$`),oP=RegExp(`^hsla\\(${ob},${og},${og},${ob}\\)$`),ok={aliceblue:0xf0f8ff,antiquewhite:0xfaebd7,aqua:65535,aquamarine:8388564,azure:0xf0ffff,beige:0xf5f5dc,bisque:0xffe4c4,black:0,blanchedalmond:0xffebcd,blue:255,blueviolet:9055202,brown:0xa52a2a,burlywood:0xdeb887,cadetblue:6266528,chartreuse:8388352,chocolate:0xd2691e,coral:0xff7f50,cornflowerblue:6591981,cornsilk:0xfff8dc,crimson:0xdc143c,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:0xb8860b,darkgray:0xa9a9a9,darkgreen:25600,darkgrey:0xa9a9a9,darkkhaki:0xbdb76b,darkmagenta:9109643,darkolivegreen:5597999,darkorange:0xff8c00,darkorchid:0x9932cc,darkred:9109504,darksalmon:0xe9967a,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:0xff1493,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:0xb22222,floralwhite:0xfffaf0,forestgreen:2263842,fuchsia:0xff00ff,gainsboro:0xdcdcdc,ghostwhite:0xf8f8ff,gold:0xffd700,goldenrod:0xdaa520,gray:8421504,green:32768,greenyellow:0xadff2f,grey:8421504,honeydew:0xf0fff0,hotpink:0xff69b4,indianred:0xcd5c5c,indigo:4915330,ivory:0xfffff0,khaki:0xf0e68c,lavender:0xe6e6fa,lavenderblush:0xfff0f5,lawngreen:8190976,lemonchiffon:0xfffacd,lightblue:0xadd8e6,lightcoral:0xf08080,lightcyan:0xe0ffff,lightgoldenrodyellow:0xfafad2,lightgray:0xd3d3d3,lightgreen:9498256,lightgrey:0xd3d3d3,lightpink:0xffb6c1,lightsalmon:0xffa07a,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:0xb0c4de,lightyellow:0xffffe0,lime:65280,limegreen:3329330,linen:0xfaf0e6,magenta:0xff00ff,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:0xba55d3,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:0xc71585,midnightblue:1644912,mintcream:0xf5fffa,mistyrose:0xffe4e1,moccasin:0xffe4b5,navajowhite:0xffdead,navy:128,oldlace:0xfdf5e6,olive:8421376,olivedrab:7048739,orange:0xffa500,orangered:0xff4500,orchid:0xda70d6,palegoldenrod:0xeee8aa,palegreen:0x98fb98,paleturquoise:0xafeeee,palevioletred:0xdb7093,papayawhip:0xffefd5,peachpuff:0xffdab9,peru:0xcd853f,pink:0xffc0cb,plum:0xdda0dd,powderblue:0xb0e0e6,purple:8388736,rebeccapurple:6697881,red:0xff0000,rosybrown:0xbc8f8f,royalblue:4286945,saddlebrown:9127187,salmon:0xfa8072,sandybrown:0xf4a460,seagreen:3050327,seashell:0xfff5ee,sienna:0xa0522d,silver:0xc0c0c0,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:0xfffafa,springgreen:65407,steelblue:4620980,tan:0xd2b48c,teal:32896,thistle:0xd8bfd8,tomato:0xff6347,turquoise:4251856,violet:0xee82ee,wheat:0xf5deb3,white:0xffffff,whitesmoke:0xf5f5f5,yellow:0xffff00,yellowgreen:0x9acd32};function oE(){return this.rgb().formatHex()}function oM(){return this.rgb().formatRgb()}function oT(t){var e,r;return t=(t+"").trim().toLowerCase(),(e=ox.exec(t))?(r=e[1].length,e=parseInt(e[1],16),6===r?o_(e):3===r?new oN(e>>8&15|e>>4&240,e>>4&15|240&e,(15&e)<<4|15&e,1):8===r?oC(e>>24&255,e>>16&255,e>>8&255,(255&e)/255):4===r?oC(e>>12&15|e>>8&240,e>>8&15|e>>4&240,e>>4&15|240&e,((15&e)<<4|15&e)/255):null):(e=oO.exec(t))?new oN(e[1],e[2],e[3],1):(e=ow.exec(t))?new oN(255*e[1]/100,255*e[2]/100,255*e[3]/100,1):(e=oj.exec(t))?oC(e[1],e[2],e[3],e[4]):(e=oS.exec(t))?oC(255*e[1]/100,255*e[2]/100,255*e[3]/100,e[4]):(e=oA.exec(t))?oU(e[1],e[2]/100,e[3]/100,1):(e=oP.exec(t))?oU(e[1],e[2]/100,e[3]/100,e[4]):ok.hasOwnProperty(t)?o_(ok[t]):"transparent"===t?new oN(NaN,NaN,NaN,0):null}function o_(t){return new oN(t>>16&255,t>>8&255,255&t,1)}function oC(t,e,r,n){return n<=0&&(t=e=r=NaN),new oN(t,e,r,n)}function oD(t,e,r,n){var o;return 1==arguments.length?((o=t)instanceof ov||(o=oT(o)),o)?new oN((o=o.rgb()).r,o.g,o.b,o.opacity):new oN:new oN(t,e,r,null==n?1:n)}function oN(t,e,r,n){this.r=+t,this.g=+e,this.b=+r,this.opacity=+n}function oI(){return`#${oz(this.r)}${oz(this.g)}${oz(this.b)}`}function oB(){let t=oR(this.opacity);return`${1===t?"rgb(":"rgba("}${oL(this.r)}, ${oL(this.g)}, ${oL(this.b)}${1===t?")":`, ${t})`}`}function oR(t){return isNaN(t)?1:Math.max(0,Math.min(1,t))}function oL(t){return Math.max(0,Math.min(255,Math.round(t)||0))}function oz(t){return((t=oL(t))<16?"0":"")+t.toString(16)}function oU(t,e,r,n){return n<=0?t=e=r=NaN:r<=0||r>=1?t=e=NaN:e<=0&&(t=NaN),new o$(t,e,r,n)}function oF(t){if(t instanceof o$)return new o$(t.h,t.s,t.l,t.opacity);if(t instanceof ov||(t=oT(t)),!t)return new o$;if(t instanceof o$)return t;var e=(t=t.rgb()).r/255,r=t.g/255,n=t.b/255,o=Math.min(e,r,n),i=Math.max(e,r,n),a=NaN,c=i-o,u=(i+o)/2;return c?(a=e===i?(r-n)/c+(r<n)*6:r===i?(n-e)/c+2:(e-r)/c+4,c/=u<.5?i+o:2-i-o,a*=60):c=u>0&&u<1?0:a,new o$(a,c,u,t.opacity)}function o$(t,e,r,n){this.h=+t,this.s=+e,this.l=+r,this.opacity=+n}function oq(t){return(t=(t||0)%360)<0?t+360:t}function oW(t){return Math.max(0,Math.min(1,t||0))}function oH(t,e,r){return(t<60?e+(r-e)*t/60:t<180?r:t<240?e+(r-e)*(240-t)/60:e)*255}function oG(t,e,r,n,o){var i=t*t,a=i*t;return((1-3*t+3*i-a)*e+(4-6*i+3*a)*r+(1+3*t+3*i-3*a)*n+a*o)/6}od(ov,oT,{copy(t){return Object.assign(new this.constructor,this,t)},displayable(){return this.rgb().displayable()},hex:oE,formatHex:oE,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return oF(this).formatHsl()},formatRgb:oM,toString:oM}),od(oN,oD,oy(ov,{brighter(t){return t=null==t?1.4285714285714286:Math.pow(1.4285714285714286,t),new oN(this.r*t,this.g*t,this.b*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new oN(this.r*t,this.g*t,this.b*t,this.opacity)},rgb(){return this},clamp(){return new oN(oL(this.r),oL(this.g),oL(this.b),oR(this.opacity))},displayable(){return -.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:oI,formatHex:oI,formatHex8:function(){return`#${oz(this.r)}${oz(this.g)}${oz(this.b)}${oz((isNaN(this.opacity)?1:this.opacity)*255)}`},formatRgb:oB,toString:oB})),od(o$,function(t,e,r,n){return 1==arguments.length?oF(t):new o$(t,e,r,null==n?1:n)},oy(ov,{brighter(t){return t=null==t?1.4285714285714286:Math.pow(1.4285714285714286,t),new o$(this.h,this.s,this.l*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new o$(this.h,this.s,this.l*t,this.opacity)},rgb(){var t=this.h%360+(this.h<0)*360,e=isNaN(t)||isNaN(this.s)?0:this.s,r=this.l,n=r+(r<.5?r:1-r)*e,o=2*r-n;return new oN(oH(t>=240?t-240:t+120,o,n),oH(t,o,n),oH(t<120?t+240:t-120,o,n),this.opacity)},clamp(){return new o$(oq(this.h),oW(this.s),oW(this.l),oR(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){let t=oR(this.opacity);return`${1===t?"hsl(":"hsla("}${oq(this.h)}, ${100*oW(this.s)}%, ${100*oW(this.l)}%${1===t?")":`, ${t})`}`}}));let oV=t=>()=>t;function oX(t,e){var r,n,o=e-t;return o?(r=t,n=o,function(t){return r+t*n}):oV(isNaN(t)?e:t)}let oK=function t(e){var r,n=1==(r=+e)?oX:function(t,e){var n,o,i;return e-t?(n=t,o=e,n=Math.pow(n,i=r),o=Math.pow(o,i)-n,i=1/i,function(t){return Math.pow(n+t*o,i)}):oV(isNaN(t)?e:t)};function o(t,e){var r=n((t=oD(t)).r,(e=oD(e)).r),o=n(t.g,e.g),i=n(t.b,e.b),a=oX(t.opacity,e.opacity);return function(e){return t.r=r(e),t.g=o(e),t.b=i(e),t.opacity=a(e),t+""}}return o.gamma=t,o}(1);function oY(t){return function(e){var r,n,o=e.length,i=Array(o),a=Array(o),c=Array(o);for(r=0;r<o;++r)n=oD(e[r]),i[r]=n.r||0,a[r]=n.g||0,c[r]=n.b||0;return i=t(i),a=t(a),c=t(c),n.opacity=1,function(t){return n.r=i(t),n.g=a(t),n.b=c(t),n+""}}}oY(function(t){var e=t.length-1;return function(r){var n=r<=0?r=0:r>=1?(r=1,e-1):Math.floor(r*e),o=t[n],i=t[n+1],a=n>0?t[n-1]:2*o-i,c=n<e-1?t[n+2]:2*i-o;return oG((r-n/e)*e,a,o,i,c)}}),oY(function(t){var e=t.length;return function(r){var n=Math.floor(((r%=1)<0?++r:r)*e),o=t[(n+e-1)%e],i=t[n%e],a=t[(n+1)%e],c=t[(n+2)%e];return oG((r-n/e)*e,o,i,a,c)}});function oZ(t,e){return t*=1,e*=1,function(r){return t*(1-r)+e*r}}var oJ=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,oQ=RegExp(oJ.source,"g");function o0(t,e){var r,n,o=typeof e;return null==e||"boolean"===o?oV(e):("number"===o?oZ:"string"===o?(n=oT(e))?(e=n,oK):function(t,e){var r,n,o,i,a,c=oJ.lastIndex=oQ.lastIndex=0,u=-1,l=[],s=[];for(t+="",e+="";(o=oJ.exec(t))&&(i=oQ.exec(e));)(a=i.index)>c&&(a=e.slice(c,a),l[u]?l[u]+=a:l[++u]=a),(o=o[0])===(i=i[0])?l[u]?l[u]+=i:l[++u]=i:(l[++u]=null,s.push({i:u,x:oZ(o,i)})),c=oQ.lastIndex;return c<e.length&&(a=e.slice(c),l[u]?l[u]+=a:l[++u]=a),l.length<2?s[0]?(r=s[0].x,function(t){return r(t)+""}):(n=e,function(){return n}):(e=s.length,function(t){for(var r,n=0;n<e;++n)l[(r=s[n]).i]=r.x(t);return l.join("")})}:e instanceof oT?oK:e instanceof Date?function(t,e){var r=new Date;return t*=1,e*=1,function(n){return r.setTime(t*(1-n)+e*n),r}}:!ArrayBuffer.isView(r=e)||r instanceof DataView?Array.isArray(e)?function(t,e){var r,n=e?e.length:0,o=t?Math.min(n,t.length):0,i=Array(o),a=Array(n);for(r=0;r<o;++r)i[r]=o0(t[r],e[r]);for(;r<n;++r)a[r]=e[r];return function(t){for(r=0;r<o;++r)a[r]=i[r](t);return a}}:"function"!=typeof e.valueOf&&"function"!=typeof e.toString||isNaN(e)?function(t,e){var r,n={},o={};for(r in(null===t||"object"!=typeof t)&&(t={}),(null===e||"object"!=typeof e)&&(e={}),e)r in t?n[r]=o0(t[r],e[r]):o[r]=e[r];return function(t){for(r in n)o[r]=n[r](t);return o}}:oZ:function(t,e){e||(e=[]);var r,n=t?Math.min(e.length,t.length):0,o=e.slice();return function(i){for(r=0;r<n;++r)o[r]=t[r]*(1-i)+e[r]*i;return o}})(t,e)}function o1(t,e){return t*=1,e*=1,function(r){return Math.round(t*(1-r)+e*r)}}function o2(t){return+t}var o3=[0,1];function o5(t){return t}function o4(t,e){var r;return(e-=t*=1)?function(r){return(r-t)/e}:(r=isNaN(e)?NaN:.5,function(){return r})}function o6(t,e,r){var n=t[0],o=t[1],i=e[0],a=e[1];return o<n?(n=o4(o,n),i=r(a,i)):(n=o4(n,o),i=r(i,a)),function(t){return i(n(t))}}function o8(t,e,r){var n=Math.min(t.length,e.length)-1,o=Array(n),i=Array(n),a=-1;for(t[n]<t[0]&&(t=t.slice().reverse(),e=e.slice().reverse());++a<n;)o[a]=o4(t[a],t[a+1]),i[a]=r(e[a],e[a+1]);return function(e){var r=oh(t,e,1,n)-1;return i[r](o[r](e))}}function o7(t,e){return e.domain(t.domain()).range(t.range()).interpolate(t.interpolate()).clamp(t.clamp()).unknown(t.unknown())}function o9(){var t,e,r,n,o,i,a=o3,c=o3,u=o0,l=o5;function s(){var t,e,r,u=Math.min(a.length,c.length);return l!==o5&&(t=a[0],e=a[u-1],t>e&&(r=t,t=e,e=r),l=function(r){return Math.max(t,Math.min(e,r))}),n=u>2?o8:o6,o=i=null,f}function f(e){return null==e||isNaN(e*=1)?r:(o||(o=n(a.map(t),c,u)))(t(l(e)))}return f.invert=function(r){return l(e((i||(i=n(c,a.map(t),oZ)))(r)))},f.domain=function(t){return arguments.length?(a=Array.from(t,o2),s()):a.slice()},f.range=function(t){return arguments.length?(c=Array.from(t),s()):c.slice()},f.rangeRound=function(t){return c=Array.from(t),u=o1,s()},f.clamp=function(t){return arguments.length?(l=!!t||o5,s()):l!==o5},f.interpolate=function(t){return arguments.length?(u=t,s()):u},f.unknown=function(t){return arguments.length?(r=t,f):r},function(r,n){return t=r,e=n,s()}}function it(){return o9()(o5,o5)}var ie=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function ir(t){var e;if(!(e=ie.exec(t)))throw Error("invalid format: "+t);return new io({fill:e[1],align:e[2],sign:e[3],symbol:e[4],zero:e[5],width:e[6],comma:e[7],precision:e[8]&&e[8].slice(1),trim:e[9],type:e[10]})}function io(t){this.fill=void 0===t.fill?" ":t.fill+"",this.align=void 0===t.align?">":t.align+"",this.sign=void 0===t.sign?"-":t.sign+"",this.symbol=void 0===t.symbol?"":t.symbol+"",this.zero=!!t.zero,this.width=void 0===t.width?void 0:+t.width,this.comma=!!t.comma,this.precision=void 0===t.precision?void 0:+t.precision,this.trim=!!t.trim,this.type=void 0===t.type?"":t.type+""}function ii(t,e){if((r=(t=e?t.toExponential(e-1):t.toExponential()).indexOf("e"))<0)return null;var r,n=t.slice(0,r);return[n.length>1?n[0]+n.slice(2):n,+t.slice(r+1)]}function ia(t){return(t=ii(Math.abs(t)))?t[1]:NaN}function ic(t,e){var r=ii(t,e);if(!r)return t+"";var n=r[0],o=r[1];return o<0?"0."+Array(-o).join("0")+n:n.length>o+1?n.slice(0,o+1)+"."+n.slice(o+1):n+Array(o-n.length+2).join("0")}ir.prototype=io.prototype,io.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};let iu={"%":(t,e)=>(100*t).toFixed(e),b:t=>Math.round(t).toString(2),c:t=>t+"",d:function(t){return Math.abs(t=Math.round(t))>=1e21?t.toLocaleString("en").replace(/,/g,""):t.toString(10)},e:(t,e)=>t.toExponential(e),f:(t,e)=>t.toFixed(e),g:(t,e)=>t.toPrecision(e),o:t=>Math.round(t).toString(8),p:(t,e)=>ic(100*t,e),r:ic,s:function(t,e){var r=ii(t,e);if(!r)return t+"";var n=r[0],o=r[1],i=o-(cL=3*Math.max(-8,Math.min(8,Math.floor(o/3))))+1,a=n.length;return i===a?n:i>a?n+Array(i-a+1).join("0"):i>0?n.slice(0,i)+"."+n.slice(i):"0."+Array(1-i).join("0")+ii(t,Math.max(0,e+i-1))[0]},X:t=>Math.round(t).toString(16).toUpperCase(),x:t=>Math.round(t).toString(16)};function il(t){return t}var is=Array.prototype.map,ip=["y","z","a","f","p","n","\xb5","m","","k","M","G","T","P","E","Z","Y"];function ih(t,e,r,n){var o,i,a,c=oa(t,e,r);switch((n=ir(null==n?",f":n)).type){case"s":var u=Math.max(Math.abs(t),Math.abs(e));return null!=n.precision||isNaN(a=Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(ia(u)/3)))-ia(Math.abs(c))))||(n.precision=a),cF(n,u);case"":case"e":case"g":case"p":case"r":null!=n.precision||isNaN(a=Math.max(0,ia(Math.abs(Math.max(Math.abs(t),Math.abs(e)))-(o=Math.abs(o=c)))-ia(o))+1)||(n.precision=a-("e"===n.type));break;case"f":case"%":null!=n.precision||isNaN(a=Math.max(0,-ia(Math.abs(c))))||(n.precision=a-("%"===n.type)*2)}return cU(n)}function id(t){var e=t.domain;return t.ticks=function(t){var r=e();return oo(r[0],r[r.length-1],null==t?10:t)},t.tickFormat=function(t,r){var n=e();return ih(n[0],n[n.length-1],null==t?10:t,r)},t.nice=function(r){null==r&&(r=10);var n,o,i=e(),a=0,c=i.length-1,u=i[a],l=i[c],s=10;for(l<u&&(o=u,u=l,l=o,o=a,a=c,c=o);s-- >0;){if((o=oi(u,l,r))===n)return i[a]=u,i[c]=l,e(i);if(o>0)u=Math.floor(u/o)*o,l=Math.ceil(l/o)*o;else if(o<0)u=Math.ceil(u*o)/o,l=Math.floor(l*o)/o;else break;n=o}return t},t}function iy(){var t=it();return t.copy=function(){return o7(t,iy())},nx.apply(t,arguments),id(t)}function iv(t,e){t=t.slice();var r,n=0,o=t.length-1,i=t[n],a=t[o];return a<i&&(r=n,n=o,o=r,r=i,i=a,a=r),t[n]=e.floor(i),t[o]=e.ceil(a),t}function im(t){return Math.log(t)}function ib(t){return Math.exp(t)}function ig(t){return-Math.log(-t)}function ix(t){return-Math.exp(-t)}function iO(t){return isFinite(t)?+("1e"+t):t<0?0:t}function iw(t){return(e,r)=>-t(-e,r)}function ij(t){let e,r,n=t(im,ib),o=n.domain,i=10;function a(){var a,c;return e=(a=i)===Math.E?Math.log:10===a&&Math.log10||2===a&&Math.log2||(a=Math.log(a),t=>Math.log(t)/a),r=10===(c=i)?iO:c===Math.E?Math.exp:t=>Math.pow(c,t),o()[0]<0?(e=iw(e),r=iw(r),t(ig,ix)):t(im,ib),n}return n.base=function(t){return arguments.length?(i=+t,a()):i},n.domain=function(t){return arguments.length?(o(t),a()):o()},n.ticks=t=>{let n,a,c=o(),u=c[0],l=c[c.length-1],s=l<u;s&&([u,l]=[l,u]);let f=e(u),p=e(l),h=null==t?10:+t,d=[];if(!(i%1)&&p-f<h){if(f=Math.floor(f),p=Math.ceil(p),u>0){for(;f<=p;++f)for(n=1;n<i;++n)if(!((a=f<0?n/r(-f):n*r(f))<u)){if(a>l)break;d.push(a)}}else for(;f<=p;++f)for(n=i-1;n>=1;--n)if(!((a=f>0?n/r(-f):n*r(f))<u)){if(a>l)break;d.push(a)}2*d.length<h&&(d=oo(u,l,h))}else d=oo(f,p,Math.min(p-f,h)).map(r);return s?d.reverse():d},n.tickFormat=(t,o)=>{if(null==t&&(t=10),null==o&&(o=10===i?"s":","),"function"!=typeof o&&(i%1||null!=(o=ir(o)).precision||(o.trim=!0),o=cU(o)),t===1/0)return o;let a=Math.max(1,i*t/n.ticks().length);return t=>{let n=t/r(Math.round(e(t)));return n*i<i-.5&&(n*=i),n<=a?o(t):""}},n.nice=()=>o(iv(o(),{floor:t=>r(Math.floor(e(t))),ceil:t=>r(Math.ceil(e(t)))})),n}function iS(t){return function(e){return Math.sign(e)*Math.log1p(Math.abs(e/t))}}function iA(t){return function(e){return Math.sign(e)*Math.expm1(Math.abs(e))*t}}function iP(t){var e=1,r=t(iS(1),iA(e));return r.constant=function(r){return arguments.length?t(iS(e=+r),iA(e)):e},id(r)}function ik(t){return function(e){return e<0?-Math.pow(-e,t):Math.pow(e,t)}}function iE(t){return t<0?-Math.sqrt(-t):Math.sqrt(t)}function iM(t){return t<0?-t*t:t*t}function iT(t){var e=t(o5,o5),r=1;return e.exponent=function(e){return arguments.length?1==(r=+e)?t(o5,o5):.5===r?t(iE,iM):t(ik(r),ik(1/r)):r},id(e)}function i_(){var t=iT(o9());return t.copy=function(){return o7(t,i_()).exponent(t.exponent())},nx.apply(t,arguments),t}function iC(){return i_.apply(null,arguments).exponent(.5)}function iD(t){return Math.sign(t)*t*t}function iN(t,e){let r;if(void 0===e)for(let e of t)null!=e&&(r<e||void 0===r&&e>=e)&&(r=e);else{let n=-1;for(let o of t)null!=(o=e(o,++n,t))&&(r<o||void 0===r&&o>=o)&&(r=o)}return r}function iI(t,e){let r;if(void 0===e)for(let e of t)null!=e&&(r>e||void 0===r&&e>=e)&&(r=e);else{let n=-1;for(let o of t)null!=(o=e(o,++n,t))&&(r>o||void 0===r&&o>=o)&&(r=o)}return r}cU=(cz=function(t){var e,r,n,o=void 0===t.grouping||void 0===t.thousands?il:(e=is.call(t.grouping,Number),r=t.thousands+"",function(t,n){for(var o=t.length,i=[],a=0,c=e[0],u=0;o>0&&c>0&&(u+c+1>n&&(c=Math.max(1,n-u)),i.push(t.substring(o-=c,o+c)),!((u+=c+1)>n));)c=e[a=(a+1)%e.length];return i.reverse().join(r)}),i=void 0===t.currency?"":t.currency[0]+"",a=void 0===t.currency?"":t.currency[1]+"",c=void 0===t.decimal?".":t.decimal+"",u=void 0===t.numerals?il:(n=is.call(t.numerals,String),function(t){return t.replace(/[0-9]/g,function(t){return n[+t]})}),l=void 0===t.percent?"%":t.percent+"",s=void 0===t.minus?"−":t.minus+"",f=void 0===t.nan?"NaN":t.nan+"";function p(t){var e=(t=ir(t)).fill,r=t.align,n=t.sign,p=t.symbol,h=t.zero,d=t.width,y=t.comma,v=t.precision,m=t.trim,b=t.type;"n"===b?(y=!0,b="g"):iu[b]||(void 0===v&&(v=12),m=!0,b="g"),(h||"0"===e&&"="===r)&&(h=!0,e="0",r="=");var g="$"===p?i:"#"===p&&/[boxX]/.test(b)?"0"+b.toLowerCase():"",x="$"===p?a:/[%p]/.test(b)?l:"",O=iu[b],w=/[defgprs%]/.test(b);function j(t){var i,a,l,p=g,j=x;if("c"===b)j=O(t)+j,t="";else{var S=(t*=1)<0||1/t<0;if(t=isNaN(t)?f:O(Math.abs(t),v),m&&(t=function(t){t:for(var e,r=t.length,n=1,o=-1;n<r;++n)switch(t[n]){case".":o=e=n;break;case"0":0===o&&(o=n),e=n;break;default:if(!+t[n])break t;o>0&&(o=0)}return o>0?t.slice(0,o)+t.slice(e+1):t}(t)),S&&0==+t&&"+"!==n&&(S=!1),p=(S?"("===n?n:s:"-"===n||"("===n?"":n)+p,j=("s"===b?ip[8+cL/3]:"")+j+(S&&"("===n?")":""),w){for(i=-1,a=t.length;++i<a;)if(48>(l=t.charCodeAt(i))||l>57){j=(46===l?c+t.slice(i+1):t.slice(i))+j,t=t.slice(0,i);break}}}y&&!h&&(t=o(t,1/0));var A=p.length+t.length+j.length,P=A<d?Array(d-A+1).join(e):"";switch(y&&h&&(t=o(P+t,P.length?d-j.length:1/0),P=""),r){case"<":t=p+t+j+P;break;case"=":t=p+P+t+j;break;case"^":t=P.slice(0,A=P.length>>1)+p+t+j+P.slice(A);break;default:t=P+p+t+j}return u(t)}return v=void 0===v?6:/[gprs]/.test(b)?Math.max(1,Math.min(21,v)):Math.max(0,Math.min(20,v)),j.toString=function(){return t+""},j}return{format:p,formatPrefix:function(t,e){var r=p(((t=ir(t)).type="f",t)),n=3*Math.max(-8,Math.min(8,Math.floor(ia(e)/3))),o=Math.pow(10,-n),i=ip[8+n/3];return function(t){return r(o*t)+i}}}}({thousands:",",grouping:[3],currency:["$",""]})).format,cF=cz.formatPrefix;function iB(t,e){return(null==t||!(t>=t))-(null==e||!(e>=e))||(t<e?-1:+(t>e))}function iR(t,e,r){let n=t[e];t[e]=t[r],t[r]=n}let iL=new Date,iz=new Date;function iU(t,e,r,n){function o(e){return t(e=0==arguments.length?new Date:new Date(+e)),e}return o.floor=e=>(t(e=new Date(+e)),e),o.ceil=r=>(t(r=new Date(r-1)),e(r,1),t(r),r),o.round=t=>{let e=o(t),r=o.ceil(t);return t-e<r-t?e:r},o.offset=(t,r)=>(e(t=new Date(+t),null==r?1:Math.floor(r)),t),o.range=(r,n,i)=>{let a,c=[];if(r=o.ceil(r),i=null==i?1:Math.floor(i),!(r<n)||!(i>0))return c;do c.push(a=new Date(+r)),e(r,i),t(r);while(a<r&&r<n);return c},o.filter=r=>iU(e=>{if(e>=e)for(;t(e),!r(e);)e.setTime(e-1)},(t,n)=>{if(t>=t)if(n<0)for(;++n<=0;)for(;e(t,-1),!r(t););else for(;--n>=0;)for(;e(t,1),!r(t););}),r&&(o.count=(e,n)=>(iL.setTime(+e),iz.setTime(+n),t(iL),t(iz),Math.floor(r(iL,iz))),o.every=t=>isFinite(t=Math.floor(t))&&t>0?t>1?o.filter(n?e=>n(e)%t==0:e=>o.count(0,e)%t==0):o:null),o}let iF=iU(()=>{},(t,e)=>{t.setTime(+t+e)},(t,e)=>e-t);iF.every=t=>isFinite(t=Math.floor(t))&&t>0?t>1?iU(e=>{e.setTime(Math.floor(e/t)*t)},(e,r)=>{e.setTime(+e+r*t)},(e,r)=>(r-e)/t):iF:null,iF.range;let i$=iU(t=>{t.setTime(t-t.getMilliseconds())},(t,e)=>{t.setTime(+t+1e3*e)},(t,e)=>(e-t)/1e3,t=>t.getUTCSeconds());i$.range;let iq=iU(t=>{t.setTime(t-t.getMilliseconds()-1e3*t.getSeconds())},(t,e)=>{t.setTime(+t+6e4*e)},(t,e)=>(e-t)/6e4,t=>t.getMinutes());iq.range;let iW=iU(t=>{t.setUTCSeconds(0,0)},(t,e)=>{t.setTime(+t+6e4*e)},(t,e)=>(e-t)/6e4,t=>t.getUTCMinutes());iW.range;let iH=iU(t=>{t.setTime(t-t.getMilliseconds()-1e3*t.getSeconds()-6e4*t.getMinutes())},(t,e)=>{t.setTime(+t+36e5*e)},(t,e)=>(e-t)/36e5,t=>t.getHours());iH.range;let iG=iU(t=>{t.setUTCMinutes(0,0,0)},(t,e)=>{t.setTime(+t+36e5*e)},(t,e)=>(e-t)/36e5,t=>t.getUTCHours());iG.range;let iV=iU(t=>t.setHours(0,0,0,0),(t,e)=>t.setDate(t.getDate()+e),(t,e)=>(e-t-(e.getTimezoneOffset()-t.getTimezoneOffset())*6e4)/864e5,t=>t.getDate()-1);iV.range;let iX=iU(t=>{t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+e)},(t,e)=>(e-t)/864e5,t=>t.getUTCDate()-1);iX.range;let iK=iU(t=>{t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+e)},(t,e)=>(e-t)/864e5,t=>Math.floor(t/864e5));function iY(t){return iU(e=>{e.setDate(e.getDate()-(e.getDay()+7-t)%7),e.setHours(0,0,0,0)},(t,e)=>{t.setDate(t.getDate()+7*e)},(t,e)=>(e-t-(e.getTimezoneOffset()-t.getTimezoneOffset())*6e4)/6048e5)}iK.range;let iZ=iY(0),iJ=iY(1),iQ=iY(2),i0=iY(3),i1=iY(4),i2=iY(5),i3=iY(6);function i5(t){return iU(e=>{e.setUTCDate(e.getUTCDate()-(e.getUTCDay()+7-t)%7),e.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+7*e)},(t,e)=>(e-t)/6048e5)}iZ.range,iJ.range,iQ.range,i0.range,i1.range,i2.range,i3.range;let i4=i5(0),i6=i5(1),i8=i5(2),i7=i5(3),i9=i5(4),at=i5(5),ae=i5(6);i4.range,i6.range,i8.range,i7.range,i9.range,at.range,ae.range;let ar=iU(t=>{t.setDate(1),t.setHours(0,0,0,0)},(t,e)=>{t.setMonth(t.getMonth()+e)},(t,e)=>e.getMonth()-t.getMonth()+(e.getFullYear()-t.getFullYear())*12,t=>t.getMonth());ar.range;let an=iU(t=>{t.setUTCDate(1),t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCMonth(t.getUTCMonth()+e)},(t,e)=>e.getUTCMonth()-t.getUTCMonth()+(e.getUTCFullYear()-t.getUTCFullYear())*12,t=>t.getUTCMonth());an.range;let ao=iU(t=>{t.setMonth(0,1),t.setHours(0,0,0,0)},(t,e)=>{t.setFullYear(t.getFullYear()+e)},(t,e)=>e.getFullYear()-t.getFullYear(),t=>t.getFullYear());ao.every=t=>isFinite(t=Math.floor(t))&&t>0?iU(e=>{e.setFullYear(Math.floor(e.getFullYear()/t)*t),e.setMonth(0,1),e.setHours(0,0,0,0)},(e,r)=>{e.setFullYear(e.getFullYear()+r*t)}):null,ao.range;let ai=iU(t=>{t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCFullYear(t.getUTCFullYear()+e)},(t,e)=>e.getUTCFullYear()-t.getUTCFullYear(),t=>t.getUTCFullYear());function aa(t,e,r,n,o,i){let a=[[i$,1,1e3],[i$,5,5e3],[i$,15,15e3],[i$,30,3e4],[i,1,6e4],[i,5,3e5],[i,15,9e5],[i,30,18e5],[o,1,36e5],[o,3,108e5],[o,6,216e5],[o,12,432e5],[n,1,864e5],[n,2,1728e5],[r,1,6048e5],[e,1,2592e6],[e,3,7776e6],[t,1,31536e6]];function c(e,r,n){let o=Math.abs(r-e)/n,i=ol(([,,t])=>t).right(a,o);if(i===a.length)return t.every(oa(e/31536e6,r/31536e6,n));if(0===i)return iF.every(Math.max(oa(e,r,n),1));let[c,u]=a[o/a[i-1][2]<a[i][2]/o?i-1:i];return c.every(u)}return[function(t,e,r){let n=e<t;n&&([t,e]=[e,t]);let o=r&&"function"==typeof r.range?r:c(t,e,r),i=o?o.range(t,+e+1):[];return n?i.reverse():i},c]}ai.every=t=>isFinite(t=Math.floor(t))&&t>0?iU(e=>{e.setUTCFullYear(Math.floor(e.getUTCFullYear()/t)*t),e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},(e,r)=>{e.setUTCFullYear(e.getUTCFullYear()+r*t)}):null,ai.range;let[ac,au]=aa(ai,an,i4,iK,iG,iW),[al,as]=aa(ao,ar,iZ,iV,iH,iq);function af(t){if(0<=t.y&&t.y<100){var e=new Date(-1,t.m,t.d,t.H,t.M,t.S,t.L);return e.setFullYear(t.y),e}return new Date(t.y,t.m,t.d,t.H,t.M,t.S,t.L)}function ap(t){if(0<=t.y&&t.y<100){var e=new Date(Date.UTC(-1,t.m,t.d,t.H,t.M,t.S,t.L));return e.setUTCFullYear(t.y),e}return new Date(Date.UTC(t.y,t.m,t.d,t.H,t.M,t.S,t.L))}function ah(t,e,r){return{y:t,m:e,d:r,H:0,M:0,S:0,L:0}}var ad={"-":"",_:" ",0:"0"},ay=/^\s*\d+/,av=/^%/,am=/[\\^$*+?|[\]().{}]/g;function ab(t,e,r){var n=t<0?"-":"",o=(n?-t:t)+"",i=o.length;return n+(i<r?Array(r-i+1).join(e)+o:o)}function ag(t){return t.replace(am,"\\$&")}function ax(t){return RegExp("^(?:"+t.map(ag).join("|")+")","i")}function aO(t){return new Map(t.map((t,e)=>[t.toLowerCase(),e]))}function aw(t,e,r){var n=ay.exec(e.slice(r,r+1));return n?(t.w=+n[0],r+n[0].length):-1}function aj(t,e,r){var n=ay.exec(e.slice(r,r+1));return n?(t.u=+n[0],r+n[0].length):-1}function aS(t,e,r){var n=ay.exec(e.slice(r,r+2));return n?(t.U=+n[0],r+n[0].length):-1}function aA(t,e,r){var n=ay.exec(e.slice(r,r+2));return n?(t.V=+n[0],r+n[0].length):-1}function aP(t,e,r){var n=ay.exec(e.slice(r,r+2));return n?(t.W=+n[0],r+n[0].length):-1}function ak(t,e,r){var n=ay.exec(e.slice(r,r+4));return n?(t.y=+n[0],r+n[0].length):-1}function aE(t,e,r){var n=ay.exec(e.slice(r,r+2));return n?(t.y=+n[0]+(+n[0]>68?1900:2e3),r+n[0].length):-1}function aM(t,e,r){var n=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(e.slice(r,r+6));return n?(t.Z=n[1]?0:-(n[2]+(n[3]||"00")),r+n[0].length):-1}function aT(t,e,r){var n=ay.exec(e.slice(r,r+1));return n?(t.q=3*n[0]-3,r+n[0].length):-1}function a_(t,e,r){var n=ay.exec(e.slice(r,r+2));return n?(t.m=n[0]-1,r+n[0].length):-1}function aC(t,e,r){var n=ay.exec(e.slice(r,r+2));return n?(t.d=+n[0],r+n[0].length):-1}function aD(t,e,r){var n=ay.exec(e.slice(r,r+3));return n?(t.m=0,t.d=+n[0],r+n[0].length):-1}function aN(t,e,r){var n=ay.exec(e.slice(r,r+2));return n?(t.H=+n[0],r+n[0].length):-1}function aI(t,e,r){var n=ay.exec(e.slice(r,r+2));return n?(t.M=+n[0],r+n[0].length):-1}function aB(t,e,r){var n=ay.exec(e.slice(r,r+2));return n?(t.S=+n[0],r+n[0].length):-1}function aR(t,e,r){var n=ay.exec(e.slice(r,r+3));return n?(t.L=+n[0],r+n[0].length):-1}function aL(t,e,r){var n=ay.exec(e.slice(r,r+6));return n?(t.L=Math.floor(n[0]/1e3),r+n[0].length):-1}function az(t,e,r){var n=av.exec(e.slice(r,r+1));return n?r+n[0].length:-1}function aU(t,e,r){var n=ay.exec(e.slice(r));return n?(t.Q=+n[0],r+n[0].length):-1}function aF(t,e,r){var n=ay.exec(e.slice(r));return n?(t.s=+n[0],r+n[0].length):-1}function a$(t,e){return ab(t.getDate(),e,2)}function aq(t,e){return ab(t.getHours(),e,2)}function aW(t,e){return ab(t.getHours()%12||12,e,2)}function aH(t,e){return ab(1+iV.count(ao(t),t),e,3)}function aG(t,e){return ab(t.getMilliseconds(),e,3)}function aV(t,e){return aG(t,e)+"000"}function aX(t,e){return ab(t.getMonth()+1,e,2)}function aK(t,e){return ab(t.getMinutes(),e,2)}function aY(t,e){return ab(t.getSeconds(),e,2)}function aZ(t){var e=t.getDay();return 0===e?7:e}function aJ(t,e){return ab(iZ.count(ao(t)-1,t),e,2)}function aQ(t){var e=t.getDay();return e>=4||0===e?i1(t):i1.ceil(t)}function a0(t,e){return t=aQ(t),ab(i1.count(ao(t),t)+(4===ao(t).getDay()),e,2)}function a1(t){return t.getDay()}function a2(t,e){return ab(iJ.count(ao(t)-1,t),e,2)}function a3(t,e){return ab(t.getFullYear()%100,e,2)}function a5(t,e){return ab((t=aQ(t)).getFullYear()%100,e,2)}function a4(t,e){return ab(t.getFullYear()%1e4,e,4)}function a6(t,e){var r=t.getDay();return ab((t=r>=4||0===r?i1(t):i1.ceil(t)).getFullYear()%1e4,e,4)}function a8(t){var e=t.getTimezoneOffset();return(e>0?"-":(e*=-1,"+"))+ab(e/60|0,"0",2)+ab(e%60,"0",2)}function a7(t,e){return ab(t.getUTCDate(),e,2)}function a9(t,e){return ab(t.getUTCHours(),e,2)}function ct(t,e){return ab(t.getUTCHours()%12||12,e,2)}function ce(t,e){return ab(1+iX.count(ai(t),t),e,3)}function cr(t,e){return ab(t.getUTCMilliseconds(),e,3)}function cn(t,e){return cr(t,e)+"000"}function co(t,e){return ab(t.getUTCMonth()+1,e,2)}function ci(t,e){return ab(t.getUTCMinutes(),e,2)}function ca(t,e){return ab(t.getUTCSeconds(),e,2)}function cc(t){var e=t.getUTCDay();return 0===e?7:e}function cu(t,e){return ab(i4.count(ai(t)-1,t),e,2)}function cl(t){var e=t.getUTCDay();return e>=4||0===e?i9(t):i9.ceil(t)}function cs(t,e){return t=cl(t),ab(i9.count(ai(t),t)+(4===ai(t).getUTCDay()),e,2)}function cf(t){return t.getUTCDay()}function cp(t,e){return ab(i6.count(ai(t)-1,t),e,2)}function ch(t,e){return ab(t.getUTCFullYear()%100,e,2)}function cd(t,e){return ab((t=cl(t)).getUTCFullYear()%100,e,2)}function cy(t,e){return ab(t.getUTCFullYear()%1e4,e,4)}function cv(t,e){var r=t.getUTCDay();return ab((t=r>=4||0===r?i9(t):i9.ceil(t)).getUTCFullYear()%1e4,e,4)}function cm(){return"+0000"}function cb(){return"%"}function cg(t){return+t}function cx(t){return Math.floor(t/1e3)}function cO(t){return new Date(t)}function cw(t){return t instanceof Date?+t:+new Date(+t)}function cj(t,e,r,n,o,i,a,c,u,l){var s=it(),f=s.invert,p=s.domain,h=l(".%L"),d=l(":%S"),y=l("%I:%M"),v=l("%I %p"),m=l("%a %d"),b=l("%b %d"),g=l("%B"),x=l("%Y");function O(t){return(u(t)<t?h:c(t)<t?d:a(t)<t?y:i(t)<t?v:n(t)<t?o(t)<t?m:b:r(t)<t?g:x)(t)}return s.invert=function(t){return new Date(f(t))},s.domain=function(t){return arguments.length?p(Array.from(t,cw)):p().map(cO)},s.ticks=function(e){var r=p();return t(r[0],r[r.length-1],null==e?10:e)},s.tickFormat=function(t,e){return null==e?O:l(e)},s.nice=function(t){var r=p();return t&&"function"==typeof t.range||(t=e(r[0],r[r.length-1],null==t?10:t)),t?p(iv(r,t)):s},s.copy=function(){return o7(s,cj(t,e,r,n,o,i,a,c,u,l))},s}function cS(){return nx.apply(cj(al,as,ao,ar,iZ,iV,iH,iq,i$,cq).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function cA(){return nx.apply(cj(ac,au,ai,an,i4,iX,iG,iW,i$,cW).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function cP(){var t,e,r,n,o,i=0,a=1,c=o5,u=!1;function l(e){return null==e||isNaN(e*=1)?o:c(0===r?.5:(e=(n(e)-t)*r,u?Math.max(0,Math.min(1,e)):e))}function s(t){return function(e){var r,n;return arguments.length?([r,n]=e,c=t(r,n),l):[c(0),c(1)]}}return l.domain=function(o){return arguments.length?([i,a]=o,t=n(i*=1),e=n(a*=1),r=t===e?0:1/(e-t),l):[i,a]},l.clamp=function(t){return arguments.length?(u=!!t,l):u},l.interpolator=function(t){return arguments.length?(c=t,l):c},l.range=s(o0),l.rangeRound=s(o1),l.unknown=function(t){return arguments.length?(o=t,l):o},function(o){return n=o,t=o(i),e=o(a),r=t===e?0:1/(e-t),l}}function ck(t,e){return e.domain(t.domain()).interpolator(t.interpolator()).clamp(t.clamp()).unknown(t.unknown())}function cE(){var t=iT(cP());return t.copy=function(){return ck(t,cE()).exponent(t.exponent())},nO.apply(t,arguments)}function cM(){return cE.apply(null,arguments).exponent(.5)}function cT(){var t,e,r,n,o,i,a,c=0,u=.5,l=1,s=1,f=o5,p=!1;function h(t){return isNaN(t*=1)?a:(t=.5+((t=+i(t))-e)*(s*t<s*e?n:o),f(p?Math.max(0,Math.min(1,t)):t))}function d(t){return function(e){var r,n,o;return arguments.length?([r,n,o]=e,f=function(t,e){void 0===e&&(e=t,t=o0);for(var r=0,n=e.length-1,o=e[0],i=Array(n<0?0:n);r<n;)i[r]=t(o,o=e[++r]);return function(t){var e=Math.max(0,Math.min(n-1,Math.floor(t*=n)));return i[e](t-e)}}(t,[r,n,o]),h):[f(0),f(.5),f(1)]}}return h.domain=function(a){return arguments.length?([c,u,l]=a,t=i(c*=1),e=i(u*=1),r=i(l*=1),n=t===e?0:.5/(e-t),o=e===r?0:.5/(r-e),s=e<t?-1:1,h):[c,u,l]},h.clamp=function(t){return arguments.length?(p=!!t,h):p},h.interpolator=function(t){return arguments.length?(f=t,h):f},h.range=d(o0),h.rangeRound=d(o1),h.unknown=function(t){return arguments.length?(a=t,h):a},function(a){return i=a,t=a(c),e=a(u),r=a(l),n=t===e?0:.5/(e-t),o=e===r?0:.5/(r-e),s=e<t?-1:1,h}}function c_(){var t=iT(cT());return t.copy=function(){return ck(t,c_()).exponent(t.exponent())},nO.apply(t,arguments)}function cC(){return c_.apply(null,arguments).exponent(.5)}function cD(t,e){if((o=t.length)>1)for(var r,n,o,i=1,a=t[e[0]],c=a.length;i<o;++i)for(n=a,a=t[e[i]],r=0;r<c;++r)a[r][1]+=a[r][0]=isNaN(n[r][1])?n[r][0]:n[r][1]}function cN(t){return"object"==typeof t&&"length"in t?t:Array.from(t)}function cI(t){for(var e=t.length,r=Array(e);--e>=0;)r[e]=e;return r}function cB(t,e){return t[e]}function cR(t){let e=[];return e.key=t,e}cq=(c$=function(t){var e=t.dateTime,r=t.date,n=t.time,o=t.periods,i=t.days,a=t.shortDays,c=t.months,u=t.shortMonths,l=ax(o),s=aO(o),f=ax(i),p=aO(i),h=ax(a),d=aO(a),y=ax(c),v=aO(c),m=ax(u),b=aO(u),g={a:function(t){return a[t.getDay()]},A:function(t){return i[t.getDay()]},b:function(t){return u[t.getMonth()]},B:function(t){return c[t.getMonth()]},c:null,d:a$,e:a$,f:aV,g:a5,G:a6,H:aq,I:aW,j:aH,L:aG,m:aX,M:aK,p:function(t){return o[+(t.getHours()>=12)]},q:function(t){return 1+~~(t.getMonth()/3)},Q:cg,s:cx,S:aY,u:aZ,U:aJ,V:a0,w:a1,W:a2,x:null,X:null,y:a3,Y:a4,Z:a8,"%":cb},x={a:function(t){return a[t.getUTCDay()]},A:function(t){return i[t.getUTCDay()]},b:function(t){return u[t.getUTCMonth()]},B:function(t){return c[t.getUTCMonth()]},c:null,d:a7,e:a7,f:cn,g:cd,G:cv,H:a9,I:ct,j:ce,L:cr,m:co,M:ci,p:function(t){return o[+(t.getUTCHours()>=12)]},q:function(t){return 1+~~(t.getUTCMonth()/3)},Q:cg,s:cx,S:ca,u:cc,U:cu,V:cs,w:cf,W:cp,x:null,X:null,y:ch,Y:cy,Z:cm,"%":cb},O={a:function(t,e,r){var n=h.exec(e.slice(r));return n?(t.w=d.get(n[0].toLowerCase()),r+n[0].length):-1},A:function(t,e,r){var n=f.exec(e.slice(r));return n?(t.w=p.get(n[0].toLowerCase()),r+n[0].length):-1},b:function(t,e,r){var n=m.exec(e.slice(r));return n?(t.m=b.get(n[0].toLowerCase()),r+n[0].length):-1},B:function(t,e,r){var n=y.exec(e.slice(r));return n?(t.m=v.get(n[0].toLowerCase()),r+n[0].length):-1},c:function(t,r,n){return S(t,e,r,n)},d:aC,e:aC,f:aL,g:aE,G:ak,H:aN,I:aN,j:aD,L:aR,m:a_,M:aI,p:function(t,e,r){var n=l.exec(e.slice(r));return n?(t.p=s.get(n[0].toLowerCase()),r+n[0].length):-1},q:aT,Q:aU,s:aF,S:aB,u:aj,U:aS,V:aA,w:aw,W:aP,x:function(t,e,n){return S(t,r,e,n)},X:function(t,e,r){return S(t,n,e,r)},y:aE,Y:ak,Z:aM,"%":az};function w(t,e){return function(r){var n,o,i,a=[],c=-1,u=0,l=t.length;for(r instanceof Date||(r=new Date(+r));++c<l;)37===t.charCodeAt(c)&&(a.push(t.slice(u,c)),null!=(o=ad[n=t.charAt(++c)])?n=t.charAt(++c):o="e"===n?" ":"0",(i=e[n])&&(n=i(r,o)),a.push(n),u=c+1);return a.push(t.slice(u,c)),a.join("")}}function j(t,e){return function(r){var n,o,i=ah(1900,void 0,1);if(S(i,t,r+="",0)!=r.length)return null;if("Q"in i)return new Date(i.Q);if("s"in i)return new Date(1e3*i.s+("L"in i?i.L:0));if(!e||"Z"in i||(i.Z=0),"p"in i&&(i.H=i.H%12+12*i.p),void 0===i.m&&(i.m="q"in i?i.q:0),"V"in i){if(i.V<1||i.V>53)return null;"w"in i||(i.w=1),"Z"in i?(n=(o=(n=ap(ah(i.y,0,1))).getUTCDay())>4||0===o?i6.ceil(n):i6(n),n=iX.offset(n,(i.V-1)*7),i.y=n.getUTCFullYear(),i.m=n.getUTCMonth(),i.d=n.getUTCDate()+(i.w+6)%7):(n=(o=(n=af(ah(i.y,0,1))).getDay())>4||0===o?iJ.ceil(n):iJ(n),n=iV.offset(n,(i.V-1)*7),i.y=n.getFullYear(),i.m=n.getMonth(),i.d=n.getDate()+(i.w+6)%7)}else("W"in i||"U"in i)&&("w"in i||(i.w="u"in i?i.u%7:+("W"in i)),o="Z"in i?ap(ah(i.y,0,1)).getUTCDay():af(ah(i.y,0,1)).getDay(),i.m=0,i.d="W"in i?(i.w+6)%7+7*i.W-(o+5)%7:i.w+7*i.U-(o+6)%7);return"Z"in i?(i.H+=i.Z/100|0,i.M+=i.Z%100,ap(i)):af(i)}}function S(t,e,r,n){for(var o,i,a=0,c=e.length,u=r.length;a<c;){if(n>=u)return -1;if(37===(o=e.charCodeAt(a++))){if(!(i=O[(o=e.charAt(a++))in ad?e.charAt(a++):o])||(n=i(t,r,n))<0)return -1}else if(o!=r.charCodeAt(n++))return -1}return n}return g.x=w(r,g),g.X=w(n,g),g.c=w(e,g),x.x=w(r,x),x.X=w(n,x),x.c=w(e,x),{format:function(t){var e=w(t+="",g);return e.toString=function(){return t},e},parse:function(t){var e=j(t+="",!1);return e.toString=function(){return t},e},utcFormat:function(t){var e=w(t+="",x);return e.toString=function(){return t},e},utcParse:function(t){var e=j(t+="",!0);return e.toString=function(){return t},e}}}({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]})).format,c$.parse,cW=c$.utcFormat,c$.utcParse,Array.prototype.slice;var cL,cz,cU,cF,c$,cq,cW,cH,cG,cV=r(90453),cX=r.n(cV),cK=r(15883),cY=r.n(cK),cZ=r(21592),cJ=r.n(cZ),cQ=r(71967),c0=r.n(cQ),c1=!0,c2="[DecimalError] ",c3=c2+"Invalid argument: ",c5=c2+"Exponent out of range: ",c4=Math.floor,c6=Math.pow,c8=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,c7=c4(1286742750677284.5),c9={};function ut(t,e){var r,n,o,i,a,c,u,l,s=t.constructor,f=s.precision;if(!t.s||!e.s)return e.s||(e=new s(t)),c1?us(e,f):e;if(u=t.d,l=e.d,a=t.e,o=e.e,u=u.slice(),i=a-o){for(i<0?(n=u,i=-i,c=l.length):(n=l,o=a,c=u.length),i>(c=(a=Math.ceil(f/7))>c?a+1:c+1)&&(i=c,n.length=1),n.reverse();i--;)n.push(0);n.reverse()}for((c=u.length)-(i=l.length)<0&&(i=c,n=l,l=u,u=n),r=0;i;)r=(u[--i]=u[i]+l[i]+r)/1e7|0,u[i]%=1e7;for(r&&(u.unshift(r),++o),c=u.length;0==u[--c];)u.pop();return e.d=u,e.e=o,c1?us(e,f):e}function ue(t,e,r){if(t!==~~t||t<e||t>r)throw Error(c3+t)}function ur(t){var e,r,n,o=t.length-1,i="",a=t[0];if(o>0){for(i+=a,e=1;e<o;e++)(r=7-(n=t[e]+"").length)&&(i+=uc(r)),i+=n;(r=7-(n=(a=t[e])+"").length)&&(i+=uc(r))}else if(0===a)return"0";for(;a%10==0;)a/=10;return i+a}c9.absoluteValue=c9.abs=function(){var t=new this.constructor(this);return t.s&&(t.s=1),t},c9.comparedTo=c9.cmp=function(t){var e,r,n,o;if(t=new this.constructor(t),this.s!==t.s)return this.s||-t.s;if(this.e!==t.e)return this.e>t.e^this.s<0?1:-1;for(e=0,r=(n=this.d.length)<(o=t.d.length)?n:o;e<r;++e)if(this.d[e]!==t.d[e])return this.d[e]>t.d[e]^this.s<0?1:-1;return n===o?0:n>o^this.s<0?1:-1},c9.decimalPlaces=c9.dp=function(){var t=this.d.length-1,e=(t-this.e)*7;if(t=this.d[t])for(;t%10==0;t/=10)e--;return e<0?0:e},c9.dividedBy=c9.div=function(t){return un(this,new this.constructor(t))},c9.dividedToIntegerBy=c9.idiv=function(t){var e=this.constructor;return us(un(this,new e(t),0,1),e.precision)},c9.equals=c9.eq=function(t){return!this.cmp(t)},c9.exponent=function(){return ui(this)},c9.greaterThan=c9.gt=function(t){return this.cmp(t)>0},c9.greaterThanOrEqualTo=c9.gte=function(t){return this.cmp(t)>=0},c9.isInteger=c9.isint=function(){return this.e>this.d.length-2},c9.isNegative=c9.isneg=function(){return this.s<0},c9.isPositive=c9.ispos=function(){return this.s>0},c9.isZero=function(){return 0===this.s},c9.lessThan=c9.lt=function(t){return 0>this.cmp(t)},c9.lessThanOrEqualTo=c9.lte=function(t){return 1>this.cmp(t)},c9.logarithm=c9.log=function(t){var e,r=this.constructor,n=r.precision,o=n+5;if(void 0===t)t=new r(10);else if((t=new r(t)).s<1||t.eq(cG))throw Error(c2+"NaN");if(this.s<1)throw Error(c2+(this.s?"NaN":"-Infinity"));return this.eq(cG)?new r(0):(c1=!1,e=un(uu(this,o),uu(t,o),o),c1=!0,us(e,n))},c9.minus=c9.sub=function(t){return t=new this.constructor(t),this.s==t.s?uf(this,t):ut(this,(t.s=-t.s,t))},c9.modulo=c9.mod=function(t){var e,r=this.constructor,n=r.precision;if(!(t=new r(t)).s)throw Error(c2+"NaN");return this.s?(c1=!1,e=un(this,t,0,1).times(t),c1=!0,this.minus(e)):us(new r(this),n)},c9.naturalExponential=c9.exp=function(){return uo(this)},c9.naturalLogarithm=c9.ln=function(){return uu(this)},c9.negated=c9.neg=function(){var t=new this.constructor(this);return t.s=-t.s||0,t},c9.plus=c9.add=function(t){return t=new this.constructor(t),this.s==t.s?ut(this,t):uf(this,(t.s=-t.s,t))},c9.precision=c9.sd=function(t){var e,r,n;if(void 0!==t&&!!t!==t&&1!==t&&0!==t)throw Error(c3+t);if(e=ui(this)+1,r=7*(n=this.d.length-1)+1,n=this.d[n]){for(;n%10==0;n/=10)r--;for(n=this.d[0];n>=10;n/=10)r++}return t&&e>r?e:r},c9.squareRoot=c9.sqrt=function(){var t,e,r,n,o,i,a,c=this.constructor;if(this.s<1){if(!this.s)return new c(0);throw Error(c2+"NaN")}for(t=ui(this),c1=!1,0==(o=Math.sqrt(+this))||o==1/0?(((e=ur(this.d)).length+t)%2==0&&(e+="0"),o=Math.sqrt(e),t=c4((t+1)/2)-(t<0||t%2),n=new c(e=o==1/0?"5e"+t:(e=o.toExponential()).slice(0,e.indexOf("e")+1)+t)):n=new c(o.toString()),o=a=(r=c.precision)+3;;)if(n=(i=n).plus(un(this,i,a+2)).times(.5),ur(i.d).slice(0,a)===(e=ur(n.d)).slice(0,a)){if(e=e.slice(a-3,a+1),o==a&&"4999"==e){if(us(i,r+1,0),i.times(i).eq(this)){n=i;break}}else if("9999"!=e)break;a+=4}return c1=!0,us(n,r)},c9.times=c9.mul=function(t){var e,r,n,o,i,a,c,u,l,s=this.constructor,f=this.d,p=(t=new s(t)).d;if(!this.s||!t.s)return new s(0);for(t.s*=this.s,r=this.e+t.e,(u=f.length)<(l=p.length)&&(i=f,f=p,p=i,a=u,u=l,l=a),i=[],n=a=u+l;n--;)i.push(0);for(n=l;--n>=0;){for(e=0,o=u+n;o>n;)c=i[o]+p[n]*f[o-n-1]+e,i[o--]=c%1e7|0,e=c/1e7|0;i[o]=(i[o]+e)%1e7|0}for(;!i[--a];)i.pop();return e?++r:i.shift(),t.d=i,t.e=r,c1?us(t,s.precision):t},c9.toDecimalPlaces=c9.todp=function(t,e){var r=this,n=r.constructor;return(r=new n(r),void 0===t)?r:(ue(t,0,1e9),void 0===e?e=n.rounding:ue(e,0,8),us(r,t+ui(r)+1,e))},c9.toExponential=function(t,e){var r,n=this,o=n.constructor;return void 0===t?r=up(n,!0):(ue(t,0,1e9),void 0===e?e=o.rounding:ue(e,0,8),r=up(n=us(new o(n),t+1,e),!0,t+1)),r},c9.toFixed=function(t,e){var r,n,o=this.constructor;return void 0===t?up(this):(ue(t,0,1e9),void 0===e?e=o.rounding:ue(e,0,8),r=up((n=us(new o(this),t+ui(this)+1,e)).abs(),!1,t+ui(n)+1),this.isneg()&&!this.isZero()?"-"+r:r)},c9.toInteger=c9.toint=function(){var t=this.constructor;return us(new t(this),ui(this)+1,t.rounding)},c9.toNumber=function(){return+this},c9.toPower=c9.pow=function(t){var e,r,n,o,i,a,c=this,u=c.constructor,l=+(t=new u(t));if(!t.s)return new u(cG);if(!(c=new u(c)).s){if(t.s<1)throw Error(c2+"Infinity");return c}if(c.eq(cG))return c;if(n=u.precision,t.eq(cG))return us(c,n);if(a=(e=t.e)>=(r=t.d.length-1),i=c.s,a){if((r=l<0?-l:l)<=0x1fffffffffffff){for(o=new u(cG),e=Math.ceil(n/7+4),c1=!1;r%2&&uh((o=o.times(c)).d,e),0!==(r=c4(r/2));)uh((c=c.times(c)).d,e);return c1=!0,t.s<0?new u(cG).div(o):us(o,n)}}else if(i<0)throw Error(c2+"NaN");return i=i<0&&1&t.d[Math.max(e,r)]?-1:1,c.s=1,c1=!1,o=t.times(uu(c,n+12)),c1=!0,(o=uo(o)).s=i,o},c9.toPrecision=function(t,e){var r,n,o=this,i=o.constructor;return void 0===t?(r=ui(o),n=up(o,r<=i.toExpNeg||r>=i.toExpPos)):(ue(t,1,1e9),void 0===e?e=i.rounding:ue(e,0,8),r=ui(o=us(new i(o),t,e)),n=up(o,t<=r||r<=i.toExpNeg,t)),n},c9.toSignificantDigits=c9.tosd=function(t,e){var r=this.constructor;return void 0===t?(t=r.precision,e=r.rounding):(ue(t,1,1e9),void 0===e?e=r.rounding:ue(e,0,8)),us(new r(this),t,e)},c9.toString=c9.valueOf=c9.val=c9.toJSON=c9[Symbol.for("nodejs.util.inspect.custom")]=function(){var t=ui(this),e=this.constructor;return up(this,t<=e.toExpNeg||t>=e.toExpPos)};var un=function(){function t(t,e){var r,n=0,o=t.length;for(t=t.slice();o--;)r=t[o]*e+n,t[o]=r%1e7|0,n=r/1e7|0;return n&&t.unshift(n),t}function e(t,e,r,n){var o,i;if(r!=n)i=r>n?1:-1;else for(o=i=0;o<r;o++)if(t[o]!=e[o]){i=t[o]>e[o]?1:-1;break}return i}function r(t,e,r){for(var n=0;r--;)t[r]-=n,n=+(t[r]<e[r]),t[r]=1e7*n+t[r]-e[r];for(;!t[0]&&t.length>1;)t.shift()}return function(n,o,i,a){var c,u,l,s,f,p,h,d,y,v,m,b,g,x,O,w,j,S,A=n.constructor,P=n.s==o.s?1:-1,k=n.d,E=o.d;if(!n.s)return new A(n);if(!o.s)throw Error(c2+"Division by zero");for(l=0,u=n.e-o.e,j=E.length,O=k.length,d=(h=new A(P)).d=[];E[l]==(k[l]||0);)++l;if(E[l]>(k[l]||0)&&--u,(b=null==i?i=A.precision:a?i+(ui(n)-ui(o))+1:i)<0)return new A(0);if(b=b/7+2|0,l=0,1==j)for(s=0,E=E[0],b++;(l<O||s)&&b--;l++)g=1e7*s+(k[l]||0),d[l]=g/E|0,s=g%E|0;else{for((s=1e7/(E[0]+1)|0)>1&&(E=t(E,s),k=t(k,s),j=E.length,O=k.length),x=j,v=(y=k.slice(0,j)).length;v<j;)y[v++]=0;(S=E.slice()).unshift(0),w=E[0],E[1]>=1e7/2&&++w;do s=0,(c=e(E,y,j,v))<0?(m=y[0],j!=v&&(m=1e7*m+(y[1]||0)),(s=m/w|0)>1?(s>=1e7&&(s=1e7-1),p=(f=t(E,s)).length,v=y.length,1==(c=e(f,y,p,v))&&(s--,r(f,j<p?S:E,p))):(0==s&&(c=s=1),f=E.slice()),(p=f.length)<v&&f.unshift(0),r(y,f,v),-1==c&&(v=y.length,(c=e(E,y,j,v))<1&&(s++,r(y,j<v?S:E,v))),v=y.length):0===c&&(s++,y=[0]),d[l++]=s,c&&y[0]?y[v++]=k[x]||0:(y=[k[x]],v=1);while((x++<O||void 0!==y[0])&&b--)}return d[0]||d.shift(),h.e=u,us(h,a?i+ui(h)+1:i)}}();function uo(t,e){var r,n,o,i,a,c=0,u=0,l=t.constructor,s=l.precision;if(ui(t)>16)throw Error(c5+ui(t));if(!t.s)return new l(cG);for(null==e?(c1=!1,a=s):a=e,i=new l(.03125);t.abs().gte(.1);)t=t.times(i),u+=5;for(a+=Math.log(c6(2,u))/Math.LN10*2+5|0,r=n=o=new l(cG),l.precision=a;;){if(n=us(n.times(t),a),r=r.times(++c),ur((i=o.plus(un(n,r,a))).d).slice(0,a)===ur(o.d).slice(0,a)){for(;u--;)o=us(o.times(o),a);return l.precision=s,null==e?(c1=!0,us(o,s)):o}o=i}}function ui(t){for(var e=7*t.e,r=t.d[0];r>=10;r/=10)e++;return e}function ua(t,e,r){if(e>t.LN10.sd())throw c1=!0,r&&(t.precision=r),Error(c2+"LN10 precision limit exceeded");return us(new t(t.LN10),e)}function uc(t){for(var e="";t--;)e+="0";return e}function uu(t,e){var r,n,o,i,a,c,u,l,s,f=1,p=t,h=p.d,d=p.constructor,y=d.precision;if(p.s<1)throw Error(c2+(p.s?"NaN":"-Infinity"));if(p.eq(cG))return new d(0);if(null==e?(c1=!1,l=y):l=e,p.eq(10))return null==e&&(c1=!0),ua(d,l);if(d.precision=l+=10,n=(r=ur(h)).charAt(0),!(15e14>Math.abs(i=ui(p))))return u=ua(d,l+2,y).times(i+""),p=uu(new d(n+"."+r.slice(1)),l-10).plus(u),d.precision=y,null==e?(c1=!0,us(p,y)):p;for(;n<7&&1!=n||1==n&&r.charAt(1)>3;)n=(r=ur((p=p.times(t)).d)).charAt(0),f++;for(i=ui(p),n>1?(p=new d("0."+r),i++):p=new d(n+"."+r.slice(1)),c=a=p=un(p.minus(cG),p.plus(cG),l),s=us(p.times(p),l),o=3;;){if(a=us(a.times(s),l),ur((u=c.plus(un(a,new d(o),l))).d).slice(0,l)===ur(c.d).slice(0,l))return c=c.times(2),0!==i&&(c=c.plus(ua(d,l+2,y).times(i+""))),c=un(c,new d(f),l),d.precision=y,null==e?(c1=!0,us(c,y)):c;c=u,o+=2}}function ul(t,e){var r,n,o;for((r=e.indexOf("."))>-1&&(e=e.replace(".","")),(n=e.search(/e/i))>0?(r<0&&(r=n),r+=+e.slice(n+1),e=e.substring(0,n)):r<0&&(r=e.length),n=0;48===e.charCodeAt(n);)++n;for(o=e.length;48===e.charCodeAt(o-1);)--o;if(e=e.slice(n,o)){if(o-=n,t.e=c4((r=r-n-1)/7),t.d=[],n=(r+1)%7,r<0&&(n+=7),n<o){for(n&&t.d.push(+e.slice(0,n)),o-=7;n<o;)t.d.push(+e.slice(n,n+=7));n=7-(e=e.slice(n)).length}else n-=o;for(;n--;)e+="0";if(t.d.push(+e),c1&&(t.e>c7||t.e<-c7))throw Error(c5+r)}else t.s=0,t.e=0,t.d=[0];return t}function us(t,e,r){var n,o,i,a,c,u,l,s,f=t.d;for(a=1,i=f[0];i>=10;i/=10)a++;if((n=e-a)<0)n+=7,o=e,l=f[s=0];else{if((s=Math.ceil((n+1)/7))>=(i=f.length))return t;for(a=1,l=i=f[s];i>=10;i/=10)a++;n%=7,o=n-7+a}if(void 0!==r&&(c=l/(i=c6(10,a-o-1))%10|0,u=e<0||void 0!==f[s+1]||l%i,u=r<4?(c||u)&&(0==r||r==(t.s<0?3:2)):c>5||5==c&&(4==r||u||6==r&&(n>0?o>0?l/c6(10,a-o):0:f[s-1])%10&1||r==(t.s<0?8:7))),e<1||!f[0])return u?(i=ui(t),f.length=1,e=e-i-1,f[0]=c6(10,(7-e%7)%7),t.e=c4(-e/7)||0):(f.length=1,f[0]=t.e=t.s=0),t;if(0==n?(f.length=s,i=1,s--):(f.length=s+1,i=c6(10,7-n),f[s]=o>0?(l/c6(10,a-o)%c6(10,o)|0)*i:0),u)for(;;)if(0==s){1e7==(f[0]+=i)&&(f[0]=1,++t.e);break}else{if(f[s]+=i,1e7!=f[s])break;f[s--]=0,i=1}for(n=f.length;0===f[--n];)f.pop();if(c1&&(t.e>c7||t.e<-c7))throw Error(c5+ui(t));return t}function uf(t,e){var r,n,o,i,a,c,u,l,s,f,p=t.constructor,h=p.precision;if(!t.s||!e.s)return e.s?e.s=-e.s:e=new p(t),c1?us(e,h):e;if(u=t.d,f=e.d,n=e.e,l=t.e,u=u.slice(),a=l-n){for((s=a<0)?(r=u,a=-a,c=f.length):(r=f,n=l,c=u.length),a>(o=Math.max(Math.ceil(h/7),c)+2)&&(a=o,r.length=1),r.reverse(),o=a;o--;)r.push(0);r.reverse()}else{for((s=(o=u.length)<(c=f.length))&&(c=o),o=0;o<c;o++)if(u[o]!=f[o]){s=u[o]<f[o];break}a=0}for(s&&(r=u,u=f,f=r,e.s=-e.s),c=u.length,o=f.length-c;o>0;--o)u[c++]=0;for(o=f.length;o>a;){if(u[--o]<f[o]){for(i=o;i&&0===u[--i];)u[i]=1e7-1;--u[i],u[o]+=1e7}u[o]-=f[o]}for(;0===u[--c];)u.pop();for(;0===u[0];u.shift())--n;return u[0]?(e.d=u,e.e=n,c1?us(e,h):e):new p(0)}function up(t,e,r){var n,o=ui(t),i=ur(t.d),a=i.length;return e?(r&&(n=r-a)>0?i=i.charAt(0)+"."+i.slice(1)+uc(n):a>1&&(i=i.charAt(0)+"."+i.slice(1)),i=i+(o<0?"e":"e+")+o):o<0?(i="0."+uc(-o-1)+i,r&&(n=r-a)>0&&(i+=uc(n))):o>=a?(i+=uc(o+1-a),r&&(n=r-o-1)>0&&(i=i+"."+uc(n))):((n=o+1)<a&&(i=i.slice(0,n)+"."+i.slice(n)),r&&(n=r-a)>0&&(o+1===a&&(i+="."),i+=uc(n))),t.s<0?"-"+i:i}function uh(t,e){if(t.length>e)return t.length=e,!0}function ud(t){if(!t||"object"!=typeof t)throw Error(c2+"Object expected");var e,r,n,o=["precision",1,1e9,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(e=0;e<o.length;e+=3)if(void 0!==(n=t[r=o[e]]))if(c4(n)===n&&n>=o[e+1]&&n<=o[e+2])this[r]=n;else throw Error(c3+r+": "+n);if(void 0!==(n=t[r="LN10"]))if(n==Math.LN10)this[r]=new this(n);else throw Error(c3+r+": "+n);return this}var cH=function t(e){var r,n,o;function i(t){if(!(this instanceof i))return new i(t);if(this.constructor=i,t instanceof i){this.s=t.s,this.e=t.e,this.d=(t=t.d)?t.slice():t;return}if("number"==typeof t){if(0*t!=0)throw Error(c3+t);if(t>0)this.s=1;else if(t<0)t=-t,this.s=-1;else{this.s=0,this.e=0,this.d=[0];return}if(t===~~t&&t<1e7){this.e=0,this.d=[t];return}return ul(this,t.toString())}if("string"!=typeof t)throw Error(c3+t);if(45===t.charCodeAt(0)?(t=t.slice(1),this.s=-1):this.s=1,c8.test(t))ul(this,t);else throw Error(c3+t)}if(i.prototype=c9,i.ROUND_UP=0,i.ROUND_DOWN=1,i.ROUND_CEIL=2,i.ROUND_FLOOR=3,i.ROUND_HALF_UP=4,i.ROUND_HALF_DOWN=5,i.ROUND_HALF_EVEN=6,i.ROUND_HALF_CEIL=7,i.ROUND_HALF_FLOOR=8,i.clone=t,i.config=i.set=ud,void 0===e&&(e={}),e)for(r=0,o=["precision","rounding","toExpNeg","toExpPos","LN10"];r<o.length;)e.hasOwnProperty(n=o[r++])||(e[n]=this[n]);return i.config(e),i}({precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"});cG=new cH(1);let uy=cH;function uv(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var um=function(t){return t},ub={},ug=function(t){return t===ub},ux=function(t){return function e(){return 0==arguments.length||1==arguments.length&&ug(arguments.length<=0?void 0:arguments[0])?e:t.apply(void 0,arguments)}},uO=function(t){return function t(e,r){return 1===e?r:ux(function(){for(var n=arguments.length,o=Array(n),i=0;i<n;i++)o[i]=arguments[i];var a=o.filter(function(t){return t!==ub}).length;return a>=e?r.apply(void 0,o):t(e-a,ux(function(){for(var t=arguments.length,e=Array(t),n=0;n<t;n++)e[n]=arguments[n];var i=o.map(function(t){return ug(t)?e.shift():t});return r.apply(void 0,((function(t){if(Array.isArray(t))return uv(t)})(i)||function(t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}(i)||function(t,e){if(t){if("string"==typeof t)return uv(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return uv(t,e)}}(i)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()).concat(e))}))})}(t.length,t)},uw=function(t,e){for(var r=[],n=t;n<e;++n)r[n-t]=n;return r},uj=uO(function(t,e){return Array.isArray(e)?e.map(t):Object.keys(e).map(function(t){return e[t]}).map(t)}),uS=function(){for(var t=arguments.length,e=Array(t),r=0;r<t;r++)e[r]=arguments[r];if(!e.length)return um;var n=e.reverse(),o=n[0],i=n.slice(1);return function(){return i.reduce(function(t,e){return e(t)},o.apply(void 0,arguments))}},uA=function(t){return Array.isArray(t)?t.reverse():t.split("").reverse.join("")},uP=function(t){var e=null,r=null;return function(){for(var n=arguments.length,o=Array(n),i=0;i<n;i++)o[i]=arguments[i];return e&&o.every(function(t,r){return t===e[r]})?r:(e=o,r=t.apply(void 0,o))}};uO(function(t,e,r){var n=+t;return n+r*(e-n)}),uO(function(t,e,r){var n=e-t;return(r-t)/(n=n||1/0)}),uO(function(t,e,r){var n=e-t;return Math.max(0,Math.min(1,(r-t)/(n=n||1/0)))});let uk={rangeStep:function(t,e,r){for(var n=new uy(t),o=0,i=[];n.lt(e)&&o<1e5;)i.push(n.toNumber()),n=n.add(r),o++;return i},getDigitCount:function(t){var e;return 0===t?1:Math.floor(new uy(t).abs().log(10).toNumber())+1}};function uE(t){return function(t){if(Array.isArray(t))return u_(t)}(t)||function(t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}(t)||uT(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function uM(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t)){var r=[],n=!0,o=!1,i=void 0;try{for(var a,c=t[Symbol.iterator]();!(n=(a=c.next()).done)&&(r.push(a.value),!e||r.length!==e);n=!0);}catch(t){o=!0,i=t}finally{try{n||null==c.return||c.return()}finally{if(o)throw i}}return r}}(t,e)||uT(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function uT(t,e){if(t){if("string"==typeof t)return u_(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return u_(t,e)}}function u_(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function uC(t){var e=uM(t,2),r=e[0],n=e[1],o=r,i=n;return r>n&&(o=n,i=r),[o,i]}function uD(t,e,r){if(t.lte(0))return new uy(0);var n=uk.getDigitCount(t.toNumber()),o=new uy(10).pow(n),i=t.div(o),a=1!==n?.05:.1,c=new uy(Math.ceil(i.div(a).toNumber())).add(r).mul(a).mul(o);return e?c:new uy(Math.ceil(c))}function uN(t,e,r){var n=1,o=new uy(t);if(!o.isint()&&r){var i=Math.abs(t);i<1?(n=new uy(10).pow(uk.getDigitCount(t)-1),o=new uy(Math.floor(o.div(n).toNumber())).mul(n)):i>1&&(o=new uy(Math.floor(t)))}else 0===t?o=new uy(Math.floor((e-1)/2)):r||(o=new uy(Math.floor(t)));var a=Math.floor((e-1)/2);return uS(uj(function(t){return o.add(new uy(t-a).mul(n)).toNumber()}),uw)(0,e)}var uI=uP(function(t){var e=uM(t,2),r=e[0],n=e[1],o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=Math.max(o,2),c=uM(uC([r,n]),2),u=c[0],l=c[1];if(u===-1/0||l===1/0){var s=l===1/0?[u].concat(uE(uw(0,o-1).map(function(){return 1/0}))):[].concat(uE(uw(0,o-1).map(function(){return-1/0})),[l]);return r>n?uA(s):s}if(u===l)return uN(u,o,i);var f=function t(e,r,n,o){var i,a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0;if(!Number.isFinite((r-e)/(n-1)))return{step:new uy(0),tickMin:new uy(0),tickMax:new uy(0)};var c=uD(new uy(r).sub(e).div(n-1),o,a),u=Math.ceil((i=e<=0&&r>=0?new uy(0):(i=new uy(e).add(r).div(2)).sub(new uy(i).mod(c))).sub(e).div(c).toNumber()),l=Math.ceil(new uy(r).sub(i).div(c).toNumber()),s=u+l+1;return s>n?t(e,r,n,o,a+1):(s<n&&(l=r>0?l+(n-s):l,u=r>0?u:u+(n-s)),{step:c,tickMin:i.sub(new uy(u).mul(c)),tickMax:i.add(new uy(l).mul(c))})}(u,l,a,i),p=f.step,h=f.tickMin,d=f.tickMax,y=uk.rangeStep(h,d.add(new uy(.1).mul(p)),p);return r>n?uA(y):y});uP(function(t){var e=uM(t,2),r=e[0],n=e[1],o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=Math.max(o,2),c=uM(uC([r,n]),2),u=c[0],l=c[1];if(u===-1/0||l===1/0)return[r,n];if(u===l)return uN(u,o,i);var s=uD(new uy(l).sub(u).div(a-1),i,0),f=uS(uj(function(t){return new uy(u).add(new uy(t).mul(s)).toNumber()}),uw)(0,a).filter(function(t){return t>=u&&t<=l});return r>n?uA(f):f});var uB=uP(function(t,e){var r=uM(t,2),n=r[0],o=r[1],i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=uM(uC([n,o]),2),c=a[0],u=a[1];if(c===-1/0||u===1/0)return[n,o];if(c===u)return[c];var l=Math.max(e,2),s=uD(new uy(u).sub(c).div(l-1),i,0),f=[].concat(uE(uk.rangeStep(new uy(c),new uy(u).sub(new uy(.99).mul(s)),s)),[u]);return n>o?uA(f):f}),uR=["offset","layout","width","dataKey","data","dataPointFormatter","xAxis","yAxis"];function uL(t){return(uL="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function uz(){return(uz=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function uU(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function uF(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(uF=function(){return!!t})()}function u$(t){return(u$=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function uq(t,e){return(uq=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function uW(t,e,r){return(e=uH(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function uH(t){var e=function(t,e){if("object"!=uL(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=uL(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==uL(e)?e:e+""}var uG=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=u$(t),function(t,e){if(e&&("object"===uL(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,uF()?Reflect.construct(t,e||[],u$(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&uq(r,t),e=[{key:"render",value:function(){var t=this.props,e=t.offset,r=t.layout,n=t.width,o=t.dataKey,i=t.data,c=t.dataPointFormatter,u=t.xAxis,l=t.yAxis,s=tm(function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,uR),!1);"x"===this.props.direction&&"number"!==u.type&&t_(!1);var f=i.map(function(t){var i,f,p=c(t,o),h=p.x,d=p.y,y=p.value,v=p.errorVal;if(!v)return null;var m=[];if(Array.isArray(v)){var b=function(t){if(Array.isArray(t))return t}(v)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{i=(r=r.call(t)).next,!1;for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(v,2)||function(t,e){if(t){if("string"==typeof t)return uU(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return uU(t,e)}}(v,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();i=b[0],f=b[1]}else i=f=v;if("vertical"===r){var g=u.scale,x=d+e,O=x+n,w=x-n,j=g(y-i),S=g(y+f);m.push({x1:S,y1:O,x2:S,y2:w}),m.push({x1:j,y1:x,x2:S,y2:x}),m.push({x1:j,y1:O,x2:j,y2:w})}else if("horizontal"===r){var A=l.scale,P=h+e,k=P-n,E=P+n,M=A(y-i),T=A(y+f);m.push({x1:k,y1:T,x2:E,y2:T}),m.push({x1:P,y1:M,x2:P,y2:T}),m.push({x1:k,y1:M,x2:E,y2:M})}return a().createElement(tR,uz({className:"recharts-errorBar",key:"bar-".concat(m.map(function(t){return"".concat(t.x1,"-").concat(t.x2,"-").concat(t.y1,"-").concat(t.y2)}))},s),m.map(function(t){return a().createElement("line",uz({},t,{key:"line-".concat(t.x1,"-").concat(t.x2,"-").concat(t.y1,"-").concat(t.y2)}))}))});return a().createElement(tR,{className:"recharts-errorBars"},f)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,uH(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(a().Component);function uV(t){return(uV="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function uX(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function uK(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?uX(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=uV(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=uV(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==uV(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):uX(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}uW(uG,"defaultProps",{stroke:"black",strokeWidth:1.5,width:5,offset:0,layout:"horizontal"}),uW(uG,"displayName","ErrorBar");var uY=function(t){var e,r=t.children,n=t.formattedGraphicalItems,o=t.legendWidth,i=t.legendContent,a=th(r,e7);if(!a)return null;var c=e7.defaultProps,u=void 0!==c?uK(uK({},c),a.props):{};return e=a.props&&a.props.payload?a.props&&a.props.payload:"children"===i?(n||[]).reduce(function(t,e){var r=e.item,n=e.props,o=n.sectors||n.data||[];return t.concat(o.map(function(t){return{type:a.props.iconType||r.props.legendType,value:t.name,color:t.fill,payload:t}}))},[]):(n||[]).map(function(t){var e=t.item,r=e.type.defaultProps,n=void 0!==r?uK(uK({},r),e.props):{},o=n.dataKey,i=n.name,a=n.legendType;return{inactive:n.hide,dataKey:o,type:u.iconType||a||"square",color:u6(e),value:i||o,payload:n}}),uK(uK(uK({},u),e7.getWithHeight(a,o)),{},{payload:e,item:a})};function uZ(t){return(uZ="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function uJ(t){return function(t){if(Array.isArray(t))return uQ(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return uQ(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return uQ(t,e)}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function uQ(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function u0(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function u1(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?u0(Object(r),!0).forEach(function(e){u2(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):u0(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function u2(t,e,r){var n;return(n=function(t,e){if("object"!=uZ(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=uZ(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==uZ(n)?n:n+"")in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function u3(t,e,r){return q()(t)||q()(e)?r:C(e)?P()(t,e,r):H()(e)?e(t):r}function u5(t,e,r,n){var o=cJ()(t,function(t){return u3(t,e)});if("number"===r){var i=o.filter(function(t){return _(t)||parseFloat(t)});return i.length?[cY()(i),cX()(i)]:[1/0,-1/0]}return(n?o.filter(function(t){return!q()(t)}):o).map(function(t){return C(t)||t instanceof Date?t:""})}var u4=function(t){var e,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2?arguments[2]:void 0,o=arguments.length>3?arguments[3]:void 0,i=-1,a=null!=(e=null==r?void 0:r.length)?e:0;if(a<=1)return 0;if(o&&"angleAxis"===o.axisType&&1e-6>=Math.abs(Math.abs(o.range[1]-o.range[0])-360))for(var c=o.range,u=0;u<a;u++){var l=u>0?n[u-1].coordinate:n[a-1].coordinate,s=n[u].coordinate,f=u>=a-1?n[0].coordinate:n[u+1].coordinate,p=void 0;if(M(s-l)!==M(f-s)){var h=[];if(M(f-s)===M(c[1]-c[0])){p=f;var d=s+c[1]-c[0];h[0]=Math.min(d,(d+l)/2),h[1]=Math.max(d,(d+l)/2)}else{p=l;var y=f+c[1]-c[0];h[0]=Math.min(s,(y+s)/2),h[1]=Math.max(s,(y+s)/2)}var v=[Math.min(s,(p+s)/2),Math.max(s,(p+s)/2)];if(t>v[0]&&t<=v[1]||t>=h[0]&&t<=h[1]){i=n[u].index;break}}else{var m=Math.min(l,f),b=Math.max(l,f);if(t>(m+s)/2&&t<=(b+s)/2){i=n[u].index;break}}}else for(var g=0;g<a;g++)if(0===g&&t<=(r[g].coordinate+r[g+1].coordinate)/2||g>0&&g<a-1&&t>(r[g].coordinate+r[g-1].coordinate)/2&&t<=(r[g].coordinate+r[g+1].coordinate)/2||g===a-1&&t>(r[g].coordinate+r[g-1].coordinate)/2){i=r[g].index;break}return i},u6=function(t){var e,r,n=t.type.displayName,o=null!=(e=t.type)&&e.defaultProps?u1(u1({},t.type.defaultProps),t.props):t.props,i=o.stroke,a=o.fill;switch(n){case"Line":r=i;break;case"Area":case"Radar":r=i&&"none"!==i?i:a;break;default:r=a}return r},u8=function(t){var e=t.barSize,r=t.totalSize,n=t.stackGroups,o=void 0===n?{}:n;if(!o)return{};for(var i={},a=Object.keys(o),c=0,u=a.length;c<u;c++)for(var l=o[a[c]].stackGroups,s=Object.keys(l),f=0,p=s.length;f<p;f++){var h=l[s[f]],d=h.items,y=h.cateAxisId,v=d.filter(function(t){return tu(t.type).indexOf("Bar")>=0});if(v&&v.length){var m=v[0].type.defaultProps,b=void 0!==m?u1(u1({},m),v[0].props):v[0].props,g=b.barSize,x=b[y];i[x]||(i[x]=[]);var O=q()(g)?e:g;i[x].push({item:v[0],stackList:v.slice(1),barSize:q()(O)?void 0:I(O,r,0)})}}return i},u7=function(t){var e,r=t.barGap,n=t.barCategoryGap,o=t.bandSize,i=t.sizeList,a=void 0===i?[]:i,c=t.maxBarSize,u=a.length;if(u<1)return null;var l=I(r,o,0,!0),s=[];if(a[0].barSize===+a[0].barSize){var f=!1,p=o/u,h=a.reduce(function(t,e){return t+e.barSize||0},0);(h+=(u-1)*l)>=o&&(h-=(u-1)*l,l=0),h>=o&&p>0&&(f=!0,p*=.9,h=u*p);var d={offset:((o-h)/2|0)-l,size:0};e=a.reduce(function(t,e){var r={item:e.item,position:{offset:d.offset+d.size+l,size:f?p:e.barSize}},n=[].concat(uJ(t),[r]);return d=n[n.length-1].position,e.stackList&&e.stackList.length&&e.stackList.forEach(function(t){n.push({item:t,position:d})}),n},s)}else{var y=I(n,o,0,!0);o-2*y-(u-1)*l<=0&&(l=0);var v=(o-2*y-(u-1)*l)/u;v>1&&(v>>=0);var m=c===+c?Math.min(v,c):v;e=a.reduce(function(t,e,r){var n=[].concat(uJ(t),[{item:e.item,position:{offset:y+(v+l)*r+(v-m)/2,size:m}}]);return e.stackList&&e.stackList.length&&e.stackList.forEach(function(t){n.push({item:t,position:n[n.length-1].position})}),n},s)}return e},u9=function(t,e,r,n){var o=r.children,i=r.width,a=r.margin,c=uY({children:o,legendWidth:i-(a.left||0)-(a.right||0)});if(c){var u=n||{},l=u.width,s=u.height,f=c.align,p=c.verticalAlign,h=c.layout;if(("vertical"===h||"horizontal"===h&&"middle"===p)&&"center"!==f&&_(t[f]))return u1(u1({},t),{},u2({},f,t[f]+(l||0)));if(("horizontal"===h||"vertical"===h&&"center"===f)&&"middle"!==p&&_(t[p]))return u1(u1({},t),{},u2({},p,t[p]+(s||0)))}return t},lt=function(t,e,r,n,o){var i=tp(e.props.children,uG).filter(function(t){var e;return e=t.props.direction,!!q()(o)||("horizontal"===n?"yAxis"===o:"vertical"===n||"x"===e?"xAxis"===o:"y"!==e||"yAxis"===o)});if(i&&i.length){var a=i.map(function(t){return t.props.dataKey});return t.reduce(function(t,e){var n=u3(e,r);if(q()(n))return t;var o=Array.isArray(n)?[cY()(n),cX()(n)]:[n,n],i=a.reduce(function(t,r){var n=u3(e,r,0),i=o[0]-Math.abs(Array.isArray(n)?n[0]:n),a=o[1]+Math.abs(Array.isArray(n)?n[1]:n);return[Math.min(i,t[0]),Math.max(a,t[1])]},[1/0,-1/0]);return[Math.min(i[0],t[0]),Math.max(i[1],t[1])]},[1/0,-1/0])}return null},le=function(t,e,r,n,o){var i=e.map(function(e){return lt(t,e,r,o,n)}).filter(function(t){return!q()(t)});return i&&i.length?i.reduce(function(t,e){return[Math.min(t[0],e[0]),Math.max(t[1],e[1])]},[1/0,-1/0]):null},lr=function(t,e,r,n,o){var i=e.map(function(e){var i=e.props.dataKey;return"number"===r&&i&&lt(t,e,i,n)||u5(t,i,r,o)});if("number"===r)return i.reduce(function(t,e){return[Math.min(t[0],e[0]),Math.max(t[1],e[1])]},[1/0,-1/0]);var a={};return i.reduce(function(t,e){for(var r=0,n=e.length;r<n;r++)a[e[r]]||(a[e[r]]=!0,t.push(e[r]));return t},[])},ln=function(t,e){return"horizontal"===t&&"xAxis"===e||"vertical"===t&&"yAxis"===e||"centric"===t&&"angleAxis"===e||"radial"===t&&"radiusAxis"===e},lo=function(t,e,r,n){if(n)return t.map(function(t){return t.coordinate});var o,i,a=t.map(function(t){return t.coordinate===e&&(o=!0),t.coordinate===r&&(i=!0),t.coordinate});return o||a.push(e),i||a.push(r),a},li=function(t,e,r){if(!t)return null;var n=t.scale,o=t.duplicateDomain,i=t.type,a=t.range,c="scaleBand"===t.realScaleType?n.bandwidth()/2:2,u=(e||r)&&"category"===i&&n.bandwidth?n.bandwidth()/c:0;return(u="angleAxis"===t.axisType&&(null==a?void 0:a.length)>=2?2*M(a[0]-a[1])*u:u,e&&(t.ticks||t.niceTicks))?(t.ticks||t.niceTicks).map(function(t){return{coordinate:n(o?o.indexOf(t):t)+u,value:t,offset:u}}).filter(function(t){return!S()(t.coordinate)}):t.isCategorical&&t.categoricalDomain?t.categoricalDomain.map(function(t,e){return{coordinate:n(t)+u,value:t,index:e,offset:u}}):n.ticks&&!r?n.ticks(t.tickCount).map(function(t){return{coordinate:n(t)+u,value:t,offset:u}}):n.domain().map(function(t,e){return{coordinate:n(t)+u,value:o?o[t]:t,index:e,offset:u}})},la=new WeakMap,lc=function(t,e){if("function"!=typeof e)return t;la.has(t)||la.set(t,new WeakMap);var r=la.get(t);if(r.has(e))return r.get(e);var n=function(){t.apply(void 0,arguments),e.apply(void 0,arguments)};return r.set(e,n),n},lu=function(t,e,r){var o=t.scale,i=t.type,a=t.layout,c=t.axisType;if("auto"===o)return"radial"===a&&"radiusAxis"===c?{scale:nk(),realScaleType:"band"}:"radial"===a&&"angleAxis"===c?{scale:iy(),realScaleType:"linear"}:"category"===i&&e&&(e.indexOf("LineChart")>=0||e.indexOf("AreaChart")>=0||e.indexOf("ComposedChart")>=0&&!r)?{scale:nE(),realScaleType:"point"}:"category"===i?{scale:nk(),realScaleType:"band"}:{scale:iy(),realScaleType:"linear"};if(w()(o)){var u="scale".concat(es()(o));return{scale:(n[u]||nE)(),realScaleType:n[u]?u:"point"}}return H()(o)?{scale:o}:{scale:nE(),realScaleType:"point"}},ll=function(t){var e=t.domain();if(e&&!(e.length<=2)){var r=e.length,n=t.range(),o=Math.min(n[0],n[1])-1e-4,i=Math.max(n[0],n[1])+1e-4,a=t(e[0]),c=t(e[r-1]);(a<o||a>i||c<o||c>i)&&t.domain([e[0],e[r-1]])}},ls=function(t,e){if(!t)return null;for(var r=0,n=t.length;r<n;r++)if(t[r].item===e)return t[r].position;return null},lf=function(t,e){if(!e||2!==e.length||!_(e[0])||!_(e[1]))return t;var r=Math.min(e[0],e[1]),n=Math.max(e[0],e[1]),o=[t[0],t[1]];return(!_(t[0])||t[0]<r)&&(o[0]=r),(!_(t[1])||t[1]>n)&&(o[1]=n),o[0]>n&&(o[0]=n),o[1]<r&&(o[1]=r),o},lp={sign:function(t){var e=t.length;if(!(e<=0))for(var r=0,n=t[0].length;r<n;++r)for(var o=0,i=0,a=0;a<e;++a){var c=S()(t[a][r][1])?t[a][r][0]:t[a][r][1];c>=0?(t[a][r][0]=o,t[a][r][1]=o+c,o=t[a][r][1]):(t[a][r][0]=i,t[a][r][1]=i+c,i=t[a][r][1])}},expand:function(t,e){if((n=t.length)>0){for(var r,n,o,i=0,a=t[0].length;i<a;++i){for(o=r=0;r<n;++r)o+=t[r][i][1]||0;if(o)for(r=0;r<n;++r)t[r][i][1]/=o}cD(t,e)}},none:cD,silhouette:function(t,e){if((r=t.length)>0){for(var r,n=0,o=t[e[0]],i=o.length;n<i;++n){for(var a=0,c=0;a<r;++a)c+=t[a][n][1]||0;o[n][1]+=o[n][0]=-c/2}cD(t,e)}},wiggle:function(t,e){if((o=t.length)>0&&(n=(r=t[e[0]]).length)>0){for(var r,n,o,i=0,a=1;a<n;++a){for(var c=0,u=0,l=0;c<o;++c){for(var s=t[e[c]],f=s[a][1]||0,p=(f-(s[a-1][1]||0))/2,h=0;h<c;++h){var d=t[e[h]];p+=(d[a][1]||0)-(d[a-1][1]||0)}u+=f,l+=p*f}r[a-1][1]+=r[a-1][0]=i,u&&(i-=l/u)}r[a-1][1]+=r[a-1][0]=i,cD(t,e)}},positive:function(t){var e=t.length;if(!(e<=0))for(var r=0,n=t[0].length;r<n;++r)for(var o=0,i=0;i<e;++i){var a=S()(t[i][r][1])?t[i][r][0]:t[i][r][1];a>=0?(t[i][r][0]=o,t[i][r][1]=o+a,o=t[i][r][1]):(t[i][r][0]=0,t[i][r][1]=0)}}},lh=function(t,e,r){var n=e.map(function(t){return t.props.dataKey}),o=lp[r];return(function(){var t=eP([]),e=cI,r=cD,n=cB;function o(o){var i,a,c=Array.from(t.apply(this,arguments),cR),u=c.length,l=-1;for(let t of o)for(i=0,++l;i<u;++i)(c[i][l]=[0,+n(t,c[i].key,l,o)]).data=t;for(i=0,a=cN(e(c));i<u;++i)c[a[i]].index=i;return r(c,a),c}return o.keys=function(e){return arguments.length?(t="function"==typeof e?e:eP(Array.from(e)),o):t},o.value=function(t){return arguments.length?(n="function"==typeof t?t:eP(+t),o):n},o.order=function(t){return arguments.length?(e=null==t?cI:"function"==typeof t?t:eP(Array.from(t)),o):e},o.offset=function(t){return arguments.length?(r=null==t?cD:t,o):r},o})().keys(n).value(function(t,e){return+u3(t,e,0)}).order(cI).offset(o)(t)},ld=function(t,e,r,n,o,i){if(!t)return null;var a=(i?e.reverse():e).reduce(function(t,e){var o,i=null!=(o=e.type)&&o.defaultProps?u1(u1({},e.type.defaultProps),e.props):e.props,a=i.stackId;if(i.hide)return t;var c=i[r],u=t[c]||{hasStack:!1,stackGroups:{}};if(C(a)){var l=u.stackGroups[a]||{numericAxisId:r,cateAxisId:n,items:[]};l.items.push(e),u.hasStack=!0,u.stackGroups[a]=l}else u.stackGroups[N("_stackId_")]={numericAxisId:r,cateAxisId:n,items:[e]};return u1(u1({},t),{},u2({},c,u))},{});return Object.keys(a).reduce(function(e,i){var c=a[i];return c.hasStack&&(c.stackGroups=Object.keys(c.stackGroups).reduce(function(e,i){var a=c.stackGroups[i];return u1(u1({},e),{},u2({},i,{numericAxisId:r,cateAxisId:n,items:a.items,stackedData:lh(t,a.items,o)}))},{})),u1(u1({},e),{},u2({},i,c))},{})},ly=function(t,e){var r=e.realScaleType,n=e.type,o=e.tickCount,i=e.originalDomain,a=e.allowDecimals,c=r||e.scale;if("auto"!==c&&"linear"!==c)return null;if(o&&"number"===n&&i&&("auto"===i[0]||"auto"===i[1])){var u=t.domain();if(!u.length)return null;var l=uI(u,o,a);return t.domain([cY()(l),cX()(l)]),{niceTicks:l}}return o&&"number"===n?{niceTicks:uB(t.domain(),o,a)}:null};function lv(t){var e=t.axis,r=t.ticks,n=t.bandSize,o=t.entry,i=t.index,a=t.dataKey;if("category"===e.type){if(!e.allowDuplicatedCategory&&e.dataKey&&!q()(o[e.dataKey])){var c=z(r,"value",o[e.dataKey]);if(c)return c.coordinate+n/2}return r[i]?r[i].coordinate+n/2:null}var u=u3(o,q()(a)?e.dataKey:a);return q()(u)?null:e.scale(u)}var lm=function(t){var e=t.axis,r=t.ticks,n=t.offset,o=t.bandSize,i=t.entry,a=t.index;if("category"===e.type)return r[a]?r[a].coordinate+n:null;var c=u3(i,e.dataKey,e.domain[a]);return q()(c)?null:e.scale(c)-o/2+n},lb=function(t){var e=t.numericAxis,r=e.scale.domain();if("number"===e.type){var n=Math.min(r[0],r[1]),o=Math.max(r[0],r[1]);return n<=0&&o>=0?0:o<0?o:n}return r[0]},lg=function(t,e){var r,n=(null!=(r=t.type)&&r.defaultProps?u1(u1({},t.type.defaultProps),t.props):t.props).stackId;if(C(n)){var o=e[n];if(o){var i=o.items.indexOf(t);return i>=0?o.stackedData[i]:null}}return null},lx=function(t,e,r){return Object.keys(t).reduce(function(n,o){var i=t[o].stackedData.reduce(function(t,n){var o=n.slice(e,r+1).reduce(function(t,e){return[cY()(e.concat([t[0]]).filter(_)),cX()(e.concat([t[1]]).filter(_))]},[1/0,-1/0]);return[Math.min(t[0],o[0]),Math.max(t[1],o[1])]},[1/0,-1/0]);return[Math.min(i[0],n[0]),Math.max(i[1],n[1])]},[1/0,-1/0]).map(function(t){return t===1/0||t===-1/0?0:t})},lO=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,lw=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,lj=function(t,e,r){if(H()(t))return t(e,r);if(!Array.isArray(t))return e;var n=[];if(_(t[0]))n[0]=r?t[0]:Math.min(t[0],e[0]);else if(lO.test(t[0])){var o=+lO.exec(t[0])[1];n[0]=e[0]-o}else H()(t[0])?n[0]=t[0](e[0]):n[0]=e[0];if(_(t[1]))n[1]=r?t[1]:Math.max(t[1],e[1]);else if(lw.test(t[1])){var i=+lw.exec(t[1])[1];n[1]=e[1]+i}else H()(t[1])?n[1]=t[1](e[1]):n[1]=e[1];return n},lS=function(t,e,r){if(t&&t.scale&&t.scale.bandwidth){var n=t.scale.bandwidth();if(!r||n>0)return n}if(t&&e&&e.length>=2){for(var o=tT()(e,function(t){return t.coordinate}),i=1/0,a=1,c=o.length;a<c;a++){var u=o[a],l=o[a-1];i=Math.min((u.coordinate||0)-(l.coordinate||0),i)}return i===1/0?0:i}return r?void 0:0},lA=function(t,e,r){return!t||!t.length||c0()(t,P()(r,"type.defaultProps.domain"))?e:t},lP=function(t,e){var r=t.type.defaultProps?u1(u1({},t.type.defaultProps),t.props):t.props,n=r.dataKey,o=r.name,i=r.unit,a=r.formatter,c=r.tooltipType,u=r.chartType,l=r.hide;return u1(u1({},tm(t,!1)),{},{dataKey:n,unit:i,formatter:a,name:o||n,color:u6(t),value:u3(e,n),type:c,payload:e,chartType:u,hide:l})};function lk(t){return(lk="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function lE(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function lM(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?lE(Object(r),!0).forEach(function(e){lT(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):lE(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function lT(t,e,r){var n;return(n=function(t,e){if("object"!=lk(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=lk(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==lk(n)?n:n+"")in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var l_=["Webkit","Moz","O","ms"],lC=function(t,e){if(!t)return null;var r=t.replace(/(\w)/,function(t){return t.toUpperCase()}),n=l_.reduce(function(t,n){return lM(lM({},t),{},lT({},n+r,e))},{});return n[t]=e,n};function lD(t){return(lD="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function lN(){return(lN=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function lI(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function lB(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?lI(Object(r),!0).forEach(function(e){lF(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):lI(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function lR(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,l$(n.key),n)}}function lL(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(lL=function(){return!!t})()}function lz(t){return(lz=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function lU(t,e){return(lU=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function lF(t,e,r){return(e=l$(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function l$(t){var e=function(t,e){if("object"!=lD(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=lD(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==lD(e)?e:e+""}var lq=function(t){var e=t.data,r=t.startIndex,n=t.endIndex,o=t.x,i=t.width,a=t.travellerWidth;if(!e||!e.length)return{};var c=e.length,u=nE().domain(tE()(0,c)).range([o,o+i-a]),l=u.domain().map(function(t){return u(t)});return{isTextActive:!1,isSlideMoving:!1,isTravellerMoving:!1,isTravellerFocused:!1,startX:u(r),endX:u(n),scale:u,scaleValues:l}},lW=function(t){return t.changedTouches&&!!t.changedTouches.length},lH=function(t){var e,r;function n(t){var e,r,o;if(!(this instanceof n))throw TypeError("Cannot call a class as a function");return r=n,o=[t],r=lz(r),lF(e=function(t,e){if(e&&("object"===lD(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,lL()?Reflect.construct(r,o||[],lz(this).constructor):r.apply(this,o)),"handleDrag",function(t){e.leaveTimer&&(clearTimeout(e.leaveTimer),e.leaveTimer=null),e.state.isTravellerMoving?e.handleTravellerMove(t):e.state.isSlideMoving&&e.handleSlideDrag(t)}),lF(e,"handleTouchMove",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&e.handleDrag(t.changedTouches[0])}),lF(e,"handleDragEnd",function(){e.setState({isTravellerMoving:!1,isSlideMoving:!1},function(){var t=e.props,r=t.endIndex,n=t.onDragEnd,o=t.startIndex;null==n||n({endIndex:r,startIndex:o})}),e.detachDragEndListener()}),lF(e,"handleLeaveWrapper",function(){(e.state.isTravellerMoving||e.state.isSlideMoving)&&(e.leaveTimer=window.setTimeout(e.handleDragEnd,e.props.leaveTimeOut))}),lF(e,"handleEnterSlideOrTraveller",function(){e.setState({isTextActive:!0})}),lF(e,"handleLeaveSlideOrTraveller",function(){e.setState({isTextActive:!1})}),lF(e,"handleSlideDragStart",function(t){var r=lW(t)?t.changedTouches[0]:t;e.setState({isTravellerMoving:!1,isSlideMoving:!0,slideMoveStartX:r.pageX}),e.attachDragEndListener()}),e.travellerDragStartHandlers={startX:e.handleTravellerDragStart.bind(e,"startX"),endX:e.handleTravellerDragStart.bind(e,"endX")},e.state={},e}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return n.prototype=Object.create(t&&t.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),t&&lU(n,t),e=[{key:"componentWillUnmount",value:function(){this.leaveTimer&&(clearTimeout(this.leaveTimer),this.leaveTimer=null),this.detachDragEndListener()}},{key:"getIndex",value:function(t){var e=t.startX,r=t.endX,o=this.state.scaleValues,i=this.props,a=i.gap,c=i.data.length-1,u=Math.min(e,r),l=Math.max(e,r),s=n.getIndexInRange(o,u),f=n.getIndexInRange(o,l);return{startIndex:s-s%a,endIndex:f===c?c:f-f%a}}},{key:"getTextOfTick",value:function(t){var e=this.props,r=e.data,n=e.tickFormatter,o=e.dataKey,i=u3(r[t],o,t);return H()(n)?n(i,t):i}},{key:"attachDragEndListener",value:function(){window.addEventListener("mouseup",this.handleDragEnd,!0),window.addEventListener("touchend",this.handleDragEnd,!0),window.addEventListener("mousemove",this.handleDrag,!0)}},{key:"detachDragEndListener",value:function(){window.removeEventListener("mouseup",this.handleDragEnd,!0),window.removeEventListener("touchend",this.handleDragEnd,!0),window.removeEventListener("mousemove",this.handleDrag,!0)}},{key:"handleSlideDrag",value:function(t){var e=this.state,r=e.slideMoveStartX,n=e.startX,o=e.endX,i=this.props,a=i.x,c=i.width,u=i.travellerWidth,l=i.startIndex,s=i.endIndex,f=i.onChange,p=t.pageX-r;p>0?p=Math.min(p,a+c-u-o,a+c-u-n):p<0&&(p=Math.max(p,a-n,a-o));var h=this.getIndex({startX:n+p,endX:o+p});(h.startIndex!==l||h.endIndex!==s)&&f&&f(h),this.setState({startX:n+p,endX:o+p,slideMoveStartX:t.pageX})}},{key:"handleTravellerDragStart",value:function(t,e){var r=lW(e)?e.changedTouches[0]:e;this.setState({isSlideMoving:!1,isTravellerMoving:!0,movingTravellerId:t,brushMoveStartX:r.pageX}),this.attachDragEndListener()}},{key:"handleTravellerMove",value:function(t){var e=this.state,r=e.brushMoveStartX,n=e.movingTravellerId,o=e.endX,i=e.startX,a=this.state[n],c=this.props,u=c.x,l=c.width,s=c.travellerWidth,f=c.onChange,p=c.gap,h=c.data,d={startX:this.state.startX,endX:this.state.endX},y=t.pageX-r;y>0?y=Math.min(y,u+l-s-a):y<0&&(y=Math.max(y,u-a)),d[n]=a+y;var v=this.getIndex(d),m=v.startIndex,b=v.endIndex,g=function(){var t=h.length-1;return"startX"===n&&(o>i?m%p==0:b%p==0)||!!(o<i)&&b===t||"endX"===n&&(o>i?b%p==0:m%p==0)||!!(o>i)&&b===t};this.setState(lF(lF({},n,a+y),"brushMoveStartX",t.pageX),function(){f&&g()&&f(v)})}},{key:"handleTravellerMoveKeyboard",value:function(t,e){var r=this,n=this.state,o=n.scaleValues,i=n.startX,a=n.endX,c=this.state[e],u=o.indexOf(c);if(-1!==u){var l=u+t;if(-1!==l&&!(l>=o.length)){var s=o[l];"startX"===e&&s>=a||"endX"===e&&s<=i||this.setState(lF({},e,s),function(){r.props.onChange(r.getIndex({startX:r.state.startX,endX:r.state.endX}))})}}}},{key:"renderBackground",value:function(){var t=this.props,e=t.x,r=t.y,n=t.width,o=t.height,i=t.fill,c=t.stroke;return a().createElement("rect",{stroke:c,fill:i,x:e,y:r,width:n,height:o})}},{key:"renderPanorama",value:function(){var t=this.props,e=t.x,r=t.y,n=t.width,o=t.height,c=t.data,u=t.children,l=t.padding,s=i.Children.only(u);return s?a().cloneElement(s,{x:e,y:r,width:n,height:o,margin:l,compact:!0,data:c}):null}},{key:"renderTravellerLayer",value:function(t,e){var r,o,i=this,c=this.props,u=c.y,l=c.travellerWidth,s=c.height,f=c.traveller,p=c.ariaLabel,h=c.data,d=c.startIndex,y=c.endIndex,v=Math.max(t,this.props.x),m=lB(lB({},tm(this.props,!1)),{},{x:v,y:u,width:l,height:s}),b=p||"Min value: ".concat(null==(r=h[d])?void 0:r.name,", Max value: ").concat(null==(o=h[y])?void 0:o.name);return a().createElement(tR,{tabIndex:0,role:"slider","aria-label":b,"aria-valuenow":t,className:"recharts-brush-traveller",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.travellerDragStartHandlers[e],onTouchStart:this.travellerDragStartHandlers[e],onKeyDown:function(t){["ArrowLeft","ArrowRight"].includes(t.key)&&(t.preventDefault(),t.stopPropagation(),i.handleTravellerMoveKeyboard("ArrowRight"===t.key?1:-1,e))},onFocus:function(){i.setState({isTravellerFocused:!0})},onBlur:function(){i.setState({isTravellerFocused:!1})},style:{cursor:"col-resize"}},n.renderTraveller(f,m))}},{key:"renderSlide",value:function(t,e){var r=this.props,n=r.y,o=r.height,i=r.stroke,c=r.travellerWidth,u=Math.min(t,e)+c,l=Math.max(Math.abs(e-t)-c,0);return a().createElement("rect",{className:"recharts-brush-slide",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.handleSlideDragStart,onTouchStart:this.handleSlideDragStart,style:{cursor:"move"},stroke:"none",fill:i,fillOpacity:.2,x:u,y:n,width:l,height:o})}},{key:"renderText",value:function(){var t=this.props,e=t.startIndex,r=t.endIndex,n=t.y,o=t.height,i=t.travellerWidth,c=t.stroke,u=this.state,l=u.startX,s=u.endX,f={pointerEvents:"none",fill:c};return a().createElement(tR,{className:"recharts-brush-texts"},a().createElement(n9,lN({textAnchor:"end",verticalAnchor:"middle",x:Math.min(l,s)-5,y:n+o/2},f),this.getTextOfTick(e)),a().createElement(n9,lN({textAnchor:"start",verticalAnchor:"middle",x:Math.max(l,s)+i+5,y:n+o/2},f),this.getTextOfTick(r)))}},{key:"render",value:function(){var t=this.props,e=t.data,r=t.className,n=t.children,o=t.x,i=t.y,c=t.width,u=t.height,l=t.alwaysShowText,s=this.state,f=s.startX,p=s.endX,h=s.isTextActive,d=s.isSlideMoving,y=s.isTravellerMoving,v=s.isTravellerFocused;if(!e||!e.length||!_(o)||!_(i)||!_(c)||!_(u)||c<=0||u<=0)return null;var m=(0,b.A)("recharts-brush",r),g=1===a().Children.count(n),x=lC("userSelect","none");return a().createElement(tR,{className:m,onMouseLeave:this.handleLeaveWrapper,onTouchMove:this.handleTouchMove,style:x},this.renderBackground(),g&&this.renderPanorama(),this.renderSlide(f,p),this.renderTravellerLayer(f,"startX"),this.renderTravellerLayer(p,"endX"),(h||d||y||v||l)&&this.renderText())}}],r=[{key:"renderDefaultTraveller",value:function(t){var e=t.x,r=t.y,n=t.width,o=t.height,i=t.stroke,c=Math.floor(r+o/2)-1;return a().createElement(a().Fragment,null,a().createElement("rect",{x:e,y:r,width:n,height:o,fill:i,stroke:"none"}),a().createElement("line",{x1:e+1,y1:c,x2:e+n-1,y2:c,fill:"none",stroke:"#fff"}),a().createElement("line",{x1:e+1,y1:c+2,x2:e+n-1,y2:c+2,fill:"none",stroke:"#fff"}))}},{key:"renderTraveller",value:function(t,e){var r;return a().isValidElement(t)?a().cloneElement(t,e):H()(t)?t(e):n.renderDefaultTraveller(e)}},{key:"getDerivedStateFromProps",value:function(t,e){var r=t.data,n=t.width,o=t.x,i=t.travellerWidth,a=t.updateId,c=t.startIndex,u=t.endIndex;if(r!==e.prevData||a!==e.prevUpdateId)return lB({prevData:r,prevTravellerWidth:i,prevUpdateId:a,prevX:o,prevWidth:n},r&&r.length?lq({data:r,width:n,x:o,travellerWidth:i,startIndex:c,endIndex:u}):{scale:null,scaleValues:null});if(e.scale&&(n!==e.prevWidth||o!==e.prevX||i!==e.prevTravellerWidth)){e.scale.range([o,o+n-i]);var l=e.scale.domain().map(function(t){return e.scale(t)});return{prevData:r,prevTravellerWidth:i,prevUpdateId:a,prevX:o,prevWidth:n,startX:e.scale(t.startIndex),endX:e.scale(t.endIndex),scaleValues:l}}return null}},{key:"getIndexInRange",value:function(t,e){for(var r=t.length,n=0,o=r-1;o-n>1;){var i=Math.floor((n+o)/2);t[i]>e?o=i:n=i}return e>=t[o]?o:n}}],e&&lR(n.prototype,e),r&&lR(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(i.PureComponent);function lG(t){return(lG="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function lV(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function lX(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?lV(Object(r),!0).forEach(function(e){lK(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):lV(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function lK(t,e,r){var n;return(n=function(t,e){if("object"!=lG(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=lG(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==lG(n)?n:n+"")in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function lY(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}lF(lH,"displayName","Brush"),lF(lH,"defaultProps",{height:40,travellerWidth:5,gap:1,fill:"#fff",stroke:"#666",padding:{top:1,right:1,bottom:1,left:1},leaveTimeOut:1e3,alwaysShowText:!1});var lZ=Math.PI/180,lJ=function(t,e,r,n){return{x:t+Math.cos(-lZ*n)*r,y:e+Math.sin(-lZ*n)*r}},lQ=function(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{top:0,right:0,bottom:0,left:0};return Math.min(Math.abs(t-(r.left||0)-(r.right||0)),Math.abs(e-(r.top||0)-(r.bottom||0)))/2},l0=function(t,e){var r=t.x,n=t.y;return Math.sqrt(Math.pow(r-e.x,2)+Math.pow(n-e.y,2))},l1=function(t,e){var r=t.x,n=t.y,o=e.cx,i=e.cy,a=l0({x:r,y:n},{x:o,y:i});if(a<=0)return{radius:a};var c=Math.acos((r-o)/a);return n>i&&(c=2*Math.PI-c),{radius:a,angle:180*c/Math.PI,angleInRadian:c}},l2=function(t){var e=t.startAngle,r=t.endAngle,n=Math.min(Math.floor(e/360),Math.floor(r/360));return{startAngle:e-360*n,endAngle:r-360*n}},l3=function(t,e){var r,n=l1({x:t.x,y:t.y},e),o=n.radius,i=n.angle,a=e.innerRadius,c=e.outerRadius;if(o<a||o>c)return!1;if(0===o)return!0;var u=l2(e),l=u.startAngle,s=u.endAngle,f=i;if(l<=s){for(;f>s;)f-=360;for(;f<l;)f+=360;r=f>=l&&f<=s}else{for(;f>l;)f-=360;for(;f<s;)f+=360;r=f>=s&&f<=l}return r?lX(lX({},e),{},{radius:o,angle:f+360*Math.min(Math.floor(e.startAngle/360),Math.floor(e.endAngle/360))}):null},l5=function(t){return(0,i.isValidElement)(t)||H()(t)||"boolean"==typeof t?"":t.className};function l4(t){return(l4="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var l6=["offset"];function l8(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function l7(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function l9(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?l7(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=l4(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=l4(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==l4(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):l7(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function st(){return(st=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var se=function(t){var e=t.value,r=t.formatter,n=q()(t.children)?e:t.children;return H()(r)?r(n):n},sr=function(t,e,r){var n,o,i=t.position,c=t.viewBox,u=t.offset,l=t.className,s=c.cx,f=c.cy,p=c.innerRadius,h=c.outerRadius,d=c.startAngle,y=c.endAngle,v=c.clockWise,m=(p+h)/2,g=M(y-d)*Math.min(Math.abs(y-d),360),x=g>=0?1:-1;"insideStart"===i?(n=d+x*u,o=v):"insideEnd"===i?(n=y-x*u,o=!v):"end"===i&&(n=y+x*u,o=v),o=g<=0?o:!o;var O=lJ(s,f,m,n),w=lJ(s,f,m,n+(o?1:-1)*359),j="M".concat(O.x,",").concat(O.y,"\n    A").concat(m,",").concat(m,",0,1,").concat(+!o,",\n    ").concat(w.x,",").concat(w.y),S=q()(t.id)?N("recharts-radial-line-"):t.id;return a().createElement("text",st({},r,{dominantBaseline:"central",className:(0,b.A)("recharts-radial-bar-label",l)}),a().createElement("defs",null,a().createElement("path",{id:S,d:j})),a().createElement("textPath",{xlinkHref:"#".concat(S)},e))},sn=function(t){var e=t.viewBox,r=t.offset,n=t.position,o=e.cx,i=e.cy,a=e.innerRadius,c=e.outerRadius,u=(e.startAngle+e.endAngle)/2;if("outside"===n){var l=lJ(o,i,c+r,u),s=l.x;return{x:s,y:l.y,textAnchor:s>=o?"start":"end",verticalAnchor:"middle"}}if("center"===n)return{x:o,y:i,textAnchor:"middle",verticalAnchor:"middle"};if("centerTop"===n)return{x:o,y:i,textAnchor:"middle",verticalAnchor:"start"};if("centerBottom"===n)return{x:o,y:i,textAnchor:"middle",verticalAnchor:"end"};var f=lJ(o,i,(a+c)/2,u);return{x:f.x,y:f.y,textAnchor:"middle",verticalAnchor:"middle"}},so=function(t){var e=t.viewBox,r=t.parentViewBox,n=t.offset,o=t.position,i=e.x,a=e.y,c=e.width,u=e.height,l=u>=0?1:-1,s=l*n,f=l>0?"end":"start",p=l>0?"start":"end",h=c>=0?1:-1,d=h*n,y=h>0?"end":"start",v=h>0?"start":"end";if("top"===o)return l9(l9({},{x:i+c/2,y:a-l*n,textAnchor:"middle",verticalAnchor:f}),r?{height:Math.max(a-r.y,0),width:c}:{});if("bottom"===o)return l9(l9({},{x:i+c/2,y:a+u+s,textAnchor:"middle",verticalAnchor:p}),r?{height:Math.max(r.y+r.height-(a+u),0),width:c}:{});if("left"===o){var m={x:i-d,y:a+u/2,textAnchor:y,verticalAnchor:"middle"};return l9(l9({},m),r?{width:Math.max(m.x-r.x,0),height:u}:{})}if("right"===o){var b={x:i+c+d,y:a+u/2,textAnchor:v,verticalAnchor:"middle"};return l9(l9({},b),r?{width:Math.max(r.x+r.width-b.x,0),height:u}:{})}var g=r?{width:c,height:u}:{};return"insideLeft"===o?l9({x:i+d,y:a+u/2,textAnchor:v,verticalAnchor:"middle"},g):"insideRight"===o?l9({x:i+c-d,y:a+u/2,textAnchor:y,verticalAnchor:"middle"},g):"insideTop"===o?l9({x:i+c/2,y:a+s,textAnchor:"middle",verticalAnchor:p},g):"insideBottom"===o?l9({x:i+c/2,y:a+u-s,textAnchor:"middle",verticalAnchor:f},g):"insideTopLeft"===o?l9({x:i+d,y:a+s,textAnchor:v,verticalAnchor:p},g):"insideTopRight"===o?l9({x:i+c-d,y:a+s,textAnchor:y,verticalAnchor:p},g):"insideBottomLeft"===o?l9({x:i+d,y:a+u-s,textAnchor:v,verticalAnchor:f},g):"insideBottomRight"===o?l9({x:i+c-d,y:a+u-s,textAnchor:y,verticalAnchor:f},g):V()(o)&&(_(o.x)||T(o.x))&&(_(o.y)||T(o.y))?l9({x:i+I(o.x,c),y:a+I(o.y,u),textAnchor:"end",verticalAnchor:"end"},g):l9({x:i+c/2,y:a+u/2,textAnchor:"middle",verticalAnchor:"middle"},g)};function si(t){var e,r=t.offset,n=l9({offset:void 0===r?5:r},function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,l6)),o=n.viewBox,c=n.position,u=n.value,l=n.children,s=n.content,f=n.className,p=n.textBreakAll;if(!o||q()(u)&&q()(l)&&!(0,i.isValidElement)(s)&&!H()(s))return null;if((0,i.isValidElement)(s))return(0,i.cloneElement)(s,n);if(H()(s)){if(e=(0,i.createElement)(s,n),(0,i.isValidElement)(e))return e}else e=se(n);var h="cx"in o&&_(o.cx),d=tm(n,!0);if(h&&("insideStart"===c||"insideEnd"===c||"end"===c))return sr(n,e,d);var y=h?sn(n):so(n);return a().createElement(n9,st({className:(0,b.A)("recharts-label",void 0===f?"":f)},d,y,{breakAll:p}),e)}si.displayName="Label";var sa=function(t){var e=t.cx,r=t.cy,n=t.angle,o=t.startAngle,i=t.endAngle,a=t.r,c=t.radius,u=t.innerRadius,l=t.outerRadius,s=t.x,f=t.y,p=t.top,h=t.left,d=t.width,y=t.height,v=t.clockWise,m=t.labelViewBox;if(m)return m;if(_(d)&&_(y)){if(_(s)&&_(f))return{x:s,y:f,width:d,height:y};if(_(p)&&_(h))return{x:p,y:h,width:d,height:y}}return _(s)&&_(f)?{x:s,y:f,width:0,height:0}:_(e)&&_(r)?{cx:e,cy:r,startAngle:o||n||0,endAngle:i||n||0,innerRadius:u||0,outerRadius:l||c||a||0,clockWise:v}:t.viewBox?t.viewBox:{}};si.parseViewBox=sa,si.renderCallByParent=function(t,e){var r,n,o=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!t||!t.children&&o&&!t.label)return null;var c=t.children,u=sa(t),l=tp(c,si).map(function(t,r){return(0,i.cloneElement)(t,{viewBox:e||u,key:"label-".concat(r)})});if(!o)return l;return[(r=t.label,n=e||u,!r?null:!0===r?a().createElement(si,{key:"label-implicit",viewBox:n}):C(r)?a().createElement(si,{key:"label-implicit",viewBox:n,value:r}):(0,i.isValidElement)(r)?r.type===si?(0,i.cloneElement)(r,{key:"label-implicit",viewBox:n}):a().createElement(si,{key:"label-implicit",content:r,viewBox:n}):H()(r)?a().createElement(si,{key:"label-implicit",content:r,viewBox:n}):V()(r)?a().createElement(si,st({viewBox:n},r,{key:"label-implicit"})):null)].concat(function(t){if(Array.isArray(t))return l8(t)}(l)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(l)||function(t,e){if(t){if("string"==typeof t)return l8(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return l8(t,e)}}(l)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}())};var sc=function(t,e){var r=t.alwaysShow,n=t.ifOverflow;return r&&(n="extendDomain"),n===e},su=r(69691),sl=r.n(su),ss=r(47212),sf=r.n(ss),sp=function(t){return null};sp.displayName="Cell";var sh=r(5359),sd=r.n(sh);function sy(t){return(sy="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var sv=["valueAccessor"],sm=["data","dataKey","clockWise","id","textBreakAll"];function sb(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function sg(){return(sg=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function sx(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function sO(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?sx(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=sy(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=sy(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==sy(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):sx(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function sw(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}var sj=function(t){return Array.isArray(t.value)?sd()(t.value):t.value};function sS(t){var e=t.valueAccessor,r=void 0===e?sj:e,n=sw(t,sv),o=n.data,i=n.dataKey,c=n.clockWise,u=n.id,l=n.textBreakAll,s=sw(n,sm);return o&&o.length?a().createElement(tR,{className:"recharts-label-list"},o.map(function(t,e){var n=q()(i)?r(t,e):u3(t&&t.payload,i),o=q()(u)?{}:{id:"".concat(u,"-").concat(e)};return a().createElement(si,sg({},tm(t,!0),s,o,{parentViewBox:t.parentViewBox,value:n,textBreakAll:l,viewBox:si.parseViewBox(q()(c)?t:sO(sO({},t),{},{clockWise:c})),key:"label-".concat(e),index:e}))})):null}sS.displayName="LabelList",sS.renderCallByParent=function(t,e){var r,n=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!t||!t.children&&n&&!t.label)return null;var o=tp(t.children,sS).map(function(t,r){return(0,i.cloneElement)(t,{data:e,key:"labelList-".concat(r)})});return n?[(r=t.label,!r?null:!0===r?a().createElement(sS,{key:"labelList-implicit",data:e}):a().isValidElement(r)||H()(r)?a().createElement(sS,{key:"labelList-implicit",data:e,content:r}):V()(r)?a().createElement(sS,sg({data:e},r,{key:"labelList-implicit"})):null)].concat(function(t){if(Array.isArray(t))return sb(t)}(o)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(o)||function(t,e){if(t){if("string"==typeof t)return sb(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return sb(t,e)}}(o)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()):o};var sA=r(38404),sP=r.n(sA),sk=r(98451),sE=r.n(sk);function sM(t){return(sM="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function sT(){return(sT=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function s_(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function sC(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function sD(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?sC(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=sM(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=sM(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==sM(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):sC(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var sN=function(t,e,r,n,o){var i,a=r-n;return"M ".concat(t,",").concat(e)+"L ".concat(t+r,",").concat(e)+"L ".concat(t+r-a/2,",").concat(e+o)+"L ".concat(t+r-a/2-n,",").concat(e+o)+"L ".concat(t,",").concat(e," Z")},sI={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},sB=function(t){var e,r=sD(sD({},sI),t),n=(0,i.useRef)(),o=function(t){if(Array.isArray(t))return t}(e=(0,i.useState)(-1))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{i=(r=r.call(t)).next,!1;for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(e,2)||function(t,e){if(t){if("string"==typeof t)return s_(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return s_(t,e)}}(e,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),c=o[0],u=o[1];(0,i.useEffect)(function(){if(n.current&&n.current.getTotalLength)try{var t=n.current.getTotalLength();t&&u(t)}catch(t){}},[]);var l=r.x,s=r.y,f=r.upperWidth,p=r.lowerWidth,h=r.height,d=r.className,y=r.animationEasing,v=r.animationDuration,m=r.animationBegin,g=r.isUpdateAnimationActive;if(l!==+l||s!==+s||f!==+f||p!==+p||h!==+h||0===f&&0===p||0===h)return null;var x=(0,b.A)("recharts-trapezoid",d);return g?a().createElement(ns,{canBegin:c>0,from:{upperWidth:0,lowerWidth:0,height:h,x:l,y:s},to:{upperWidth:f,lowerWidth:p,height:h,x:l,y:s},duration:v,animationEasing:y,isActive:g},function(t){var e=t.upperWidth,o=t.lowerWidth,i=t.height,u=t.x,l=t.y;return a().createElement(ns,{canBegin:c>0,from:"0px ".concat(-1===c?1:c,"px"),to:"".concat(c,"px 0px"),attributeName:"strokeDasharray",begin:m,duration:v,easing:y},a().createElement("path",sT({},tm(r,!0),{className:x,d:sN(u,l,e,o,i),ref:n})))}):a().createElement("g",null,a().createElement("path",sT({},tm(r,!0),{className:x,d:sN(l,s,f,p,h)})))};function sR(t){return(sR="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function sL(){return(sL=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function sz(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function sU(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?sz(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=sR(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=sR(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==sR(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):sz(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var sF=function(t){var e=t.cx,r=t.cy,n=t.radius,o=t.angle,i=t.sign,a=t.isExternal,c=t.cornerRadius,u=t.cornerIsExternal,l=c*(a?1:-1)+n,s=Math.asin(c/l)/lZ,f=u?o:o+i*s;return{center:lJ(e,r,l,f),circleTangency:lJ(e,r,n,f),lineTangency:lJ(e,r,l*Math.cos(s*lZ),u?o-i*s:o),theta:s}},s$=function(t){var e=t.cx,r=t.cy,n=t.innerRadius,o=t.outerRadius,i=t.startAngle,a=t.endAngle,c=M(a-i)*Math.min(Math.abs(a-i),359.999),u=i+c,l=lJ(e,r,o,i),s=lJ(e,r,o,u),f="M ".concat(l.x,",").concat(l.y,"\n    A ").concat(o,",").concat(o,",0,\n    ").concat(+(Math.abs(c)>180),",").concat(+(i>u),",\n    ").concat(s.x,",").concat(s.y,"\n  ");if(n>0){var p=lJ(e,r,n,i),h=lJ(e,r,n,u);f+="L ".concat(h.x,",").concat(h.y,"\n            A ").concat(n,",").concat(n,",0,\n            ").concat(+(Math.abs(c)>180),",").concat(+(i<=u),",\n            ").concat(p.x,",").concat(p.y," Z")}else f+="L ".concat(e,",").concat(r," Z");return f},sq=function(t){var e=t.cx,r=t.cy,n=t.innerRadius,o=t.outerRadius,i=t.cornerRadius,a=t.forceCornerRadius,c=t.cornerIsExternal,u=t.startAngle,l=t.endAngle,s=M(l-u),f=sF({cx:e,cy:r,radius:o,angle:u,sign:s,cornerRadius:i,cornerIsExternal:c}),p=f.circleTangency,h=f.lineTangency,d=f.theta,y=sF({cx:e,cy:r,radius:o,angle:l,sign:-s,cornerRadius:i,cornerIsExternal:c}),v=y.circleTangency,m=y.lineTangency,b=y.theta,g=c?Math.abs(u-l):Math.abs(u-l)-d-b;if(g<0)return a?"M ".concat(h.x,",").concat(h.y,"\n        a").concat(i,",").concat(i,",0,0,1,").concat(2*i,",0\n        a").concat(i,",").concat(i,",0,0,1,").concat(-(2*i),",0\n      "):s$({cx:e,cy:r,innerRadius:n,outerRadius:o,startAngle:u,endAngle:l});var x="M ".concat(h.x,",").concat(h.y,"\n    A").concat(i,",").concat(i,",0,0,").concat(+(s<0),",").concat(p.x,",").concat(p.y,"\n    A").concat(o,",").concat(o,",0,").concat(+(g>180),",").concat(+(s<0),",").concat(v.x,",").concat(v.y,"\n    A").concat(i,",").concat(i,",0,0,").concat(+(s<0),",").concat(m.x,",").concat(m.y,"\n  ");if(n>0){var O=sF({cx:e,cy:r,radius:n,angle:u,sign:s,isExternal:!0,cornerRadius:i,cornerIsExternal:c}),w=O.circleTangency,j=O.lineTangency,S=O.theta,A=sF({cx:e,cy:r,radius:n,angle:l,sign:-s,isExternal:!0,cornerRadius:i,cornerIsExternal:c}),P=A.circleTangency,k=A.lineTangency,E=A.theta,T=c?Math.abs(u-l):Math.abs(u-l)-S-E;if(T<0&&0===i)return"".concat(x,"L").concat(e,",").concat(r,"Z");x+="L".concat(k.x,",").concat(k.y,"\n      A").concat(i,",").concat(i,",0,0,").concat(+(s<0),",").concat(P.x,",").concat(P.y,"\n      A").concat(n,",").concat(n,",0,").concat(+(T>180),",").concat(+(s>0),",").concat(w.x,",").concat(w.y,"\n      A").concat(i,",").concat(i,",0,0,").concat(+(s<0),",").concat(j.x,",").concat(j.y,"Z")}else x+="L".concat(e,",").concat(r,"Z");return x},sW={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},sH=function(t){var e,r=sU(sU({},sW),t),n=r.cx,o=r.cy,i=r.innerRadius,c=r.outerRadius,u=r.cornerRadius,l=r.forceCornerRadius,s=r.cornerIsExternal,f=r.startAngle,p=r.endAngle,h=r.className;if(c<i||f===p)return null;var d=(0,b.A)("recharts-sector",h),y=c-i,v=I(u,y,0,!0);return e=v>0&&360>Math.abs(f-p)?sq({cx:n,cy:o,innerRadius:i,outerRadius:c,cornerRadius:Math.min(v,y/2),forceCornerRadius:l,cornerIsExternal:s,startAngle:f,endAngle:p}):s$({cx:n,cy:o,innerRadius:i,outerRadius:c,startAngle:f,endAngle:p}),a().createElement("path",sL({},tm(r,!0),{className:d,d:e,role:"img"}))},sG=["option","shapeType","propTransformer","activeClassName","isActive"];function sV(t){return(sV="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function sX(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function sK(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?sX(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=sV(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=sV(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==sV(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):sX(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function sY(t){var e=t.shapeType,r=t.elementProps;switch(e){case"rectangle":return a().createElement(ng,r);case"trapezoid":return a().createElement(sB,r);case"sector":return a().createElement(sH,r);case"symbols":if("symbols"===e)return a().createElement(eF,r);break;default:return null}}function sZ(t){var e,r=t.option,n=t.shapeType,o=t.propTransformer,c=t.activeClassName,u=t.isActive,l=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,sG);if((0,i.isValidElement)(r))e=(0,i.cloneElement)(r,sK(sK({},l),(0,i.isValidElement)(r)?r.props:r));else if(H()(r))e=r(l);else if(sP()(r)&&!sE()(r)){var s=(void 0===o?function(t,e){return sK(sK({},e),t)}:o)(r,l);e=a().createElement(sY,{shapeType:n,elementProps:s})}else e=a().createElement(sY,{shapeType:n,elementProps:l});return u?a().createElement(tR,{className:void 0===c?"recharts-active-shape":c},e):e}function sJ(t,e){return null!=e&&"trapezoids"in t.props}function sQ(t,e){return null!=e&&"sectors"in t.props}function s0(t,e){return null!=e&&"points"in t.props}function s1(t,e){var r,n,o=t.x===(null==e||null==(r=e.labelViewBox)?void 0:r.x)||t.x===e.x,i=t.y===(null==e||null==(n=e.labelViewBox)?void 0:n.y)||t.y===e.y;return o&&i}function s2(t,e){var r=t.endAngle===e.endAngle,n=t.startAngle===e.startAngle;return r&&n}function s3(t,e){var r=t.x===e.x,n=t.y===e.y,o=t.z===e.z;return r&&n&&o}var s5=["x","y"];function s4(t){return(s4="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function s6(){return(s6=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function s8(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function s7(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?s8(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=s4(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=s4(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==s4(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):s8(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function s9(t,e){var r=t.x,n=t.y,o=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,s5),i=parseInt("".concat(r),10),a=parseInt("".concat(n),10),c=parseInt("".concat(e.height||o.height),10),u=parseInt("".concat(e.width||o.width),10);return s7(s7(s7(s7(s7({},e),o),i?{x:i}:{}),a?{y:a}:{}),{},{height:c,width:u,name:e.name,radius:e.radius})}function ft(t){return a().createElement(sZ,s6({shapeType:"rectangle",propTransformer:s9,activeClassName:"recharts-active-bar"},t))}var fe=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return function(r,n){if("number"==typeof t)return t;var o="number"==typeof r;return o?t(r,n):(o||t_(!1),e)}},fr=["value","background"];function fn(t){return(fn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function fo(){return(fo=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function fi(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function fa(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?fi(Object(r),!0).forEach(function(e){ff(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):fi(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function fc(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,fp(n.key),n)}}function fu(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(fu=function(){return!!t})()}function fl(t){return(fl=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function fs(t,e){return(fs=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function ff(t,e,r){return(e=fp(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function fp(t){var e=function(t,e){if("object"!=fn(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=fn(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==fn(e)?e:e+""}var fh=function(t){var e,r;function n(){var t,e,r;if(!(this instanceof n))throw TypeError("Cannot call a class as a function");for(var o=arguments.length,i=Array(o),a=0;a<o;a++)i[a]=arguments[a];return e=n,r=[].concat(i),e=fl(e),ff(t=function(t,e){if(e&&("object"===fn(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,fu()?Reflect.construct(e,r||[],fl(this).constructor):e.apply(this,r)),"state",{isAnimationFinished:!1}),ff(t,"id",N("recharts-bar-")),ff(t,"handleAnimationEnd",function(){var e=t.props.onAnimationEnd;t.setState({isAnimationFinished:!0}),e&&e()}),ff(t,"handleAnimationStart",function(){var e=t.props.onAnimationStart;t.setState({isAnimationFinished:!1}),e&&e()}),t}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return n.prototype=Object.create(t&&t.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),t&&fs(n,t),e=[{key:"renderRectanglesStatically",value:function(t){var e=this,r=this.props,n=r.shape,o=r.dataKey,i=r.activeIndex,c=r.activeBar,u=tm(this.props,!1);return t&&t.map(function(t,r){var l=r===i,s=fa(fa(fa({},u),t),{},{isActive:l,option:l?c:n,index:r,dataKey:o,onAnimationStart:e.handleAnimationStart,onAnimationEnd:e.handleAnimationEnd});return a().createElement(tR,fo({className:"recharts-bar-rectangle"},tr(e.props,t,r),{key:"rectangle-".concat(null==t?void 0:t.x,"-").concat(null==t?void 0:t.y,"-").concat(null==t?void 0:t.value,"-").concat(r)}),a().createElement(ft,s))})}},{key:"renderRectanglesWithAnimation",value:function(){var t=this,e=this.props,r=e.data,n=e.layout,o=e.isAnimationActive,i=e.animationBegin,c=e.animationDuration,u=e.animationEasing,l=e.animationId,s=this.state.prevData;return a().createElement(ns,{begin:i,duration:c,isActive:o,easing:u,from:{t:0},to:{t:1},key:"bar-".concat(l),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(e){var o=e.t,i=r.map(function(t,e){var r=s&&s[e];if(r){var i=L(r.x,t.x),a=L(r.y,t.y),c=L(r.width,t.width),u=L(r.height,t.height);return fa(fa({},t),{},{x:i(o),y:a(o),width:c(o),height:u(o)})}if("horizontal"===n){var l=L(0,t.height)(o);return fa(fa({},t),{},{y:t.y+t.height-l,height:l})}var f=L(0,t.width)(o);return fa(fa({},t),{},{width:f})});return a().createElement(tR,null,t.renderRectanglesStatically(i))})}},{key:"renderRectangles",value:function(){var t=this.props,e=t.data,r=t.isAnimationActive,n=this.state.prevData;return r&&e&&e.length&&(!n||!c0()(n,e))?this.renderRectanglesWithAnimation():this.renderRectanglesStatically(e)}},{key:"renderBackground",value:function(){var t=this,e=this.props,r=e.data,n=e.dataKey,o=e.activeIndex,i=tm(this.props.background,!1);return r.map(function(e,r){e.value;var c=e.background,u=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(e,fr);if(!c)return null;var l=fa(fa(fa(fa(fa({},u),{},{fill:"#eee"},c),i),tr(t.props,e,r)),{},{onAnimationStart:t.handleAnimationStart,onAnimationEnd:t.handleAnimationEnd,dataKey:n,index:r,className:"recharts-bar-background-rectangle"});return a().createElement(ft,fo({key:"background-bar-".concat(r),option:t.props.background,isActive:r===o},l))})}},{key:"renderErrorBar",value:function(t,e){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var r=this.props,n=r.data,o=r.xAxis,i=r.yAxis,c=r.layout,u=tp(r.children,uG);if(!u)return null;var l="vertical"===c?n[0].height/2:n[0].width/2,s=function(t,e){var r=Array.isArray(t.value)?t.value[1]:t.value;return{x:t.x,y:t.y,value:r,errorVal:u3(t,e)}};return a().createElement(tR,{clipPath:t?"url(#clipPath-".concat(e,")"):null},u.map(function(t){return a().cloneElement(t,{key:"error-bar-".concat(e,"-").concat(t.props.dataKey),data:n,xAxis:o,yAxis:i,layout:c,offset:l,dataPointFormatter:s})}))}},{key:"render",value:function(){var t=this.props,e=t.hide,r=t.data,n=t.className,o=t.xAxis,i=t.yAxis,c=t.left,u=t.top,l=t.width,s=t.height,f=t.isAnimationActive,p=t.background,h=t.id;if(e||!r||!r.length)return null;var d=this.state.isAnimationFinished,y=(0,b.A)("recharts-bar",n),v=o&&o.allowDataOverflow,m=i&&i.allowDataOverflow,g=v||m,x=q()(h)?this.id:h;return a().createElement(tR,{className:y},v||m?a().createElement("defs",null,a().createElement("clipPath",{id:"clipPath-".concat(x)},a().createElement("rect",{x:v?c:c-l/2,y:m?u:u-s/2,width:v?l:2*l,height:m?s:2*s}))):null,a().createElement(tR,{className:"recharts-bar-rectangles",clipPath:g?"url(#clipPath-".concat(x,")"):null},p?this.renderBackground():null,this.renderRectangles()),this.renderErrorBar(g,x),(!f||d)&&sS.renderCallByParent(this.props,r))}}],r=[{key:"getDerivedStateFromProps",value:function(t,e){return t.animationId!==e.prevAnimationId?{prevAnimationId:t.animationId,curData:t.data,prevData:e.curData}:t.data!==e.curData?{curData:t.data}:null}}],e&&fc(n.prototype,e),r&&fc(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(i.PureComponent);function fd(t){return(fd="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function fy(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,fg(n.key),n)}}function fv(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function fm(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?fv(Object(r),!0).forEach(function(e){fb(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):fv(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function fb(t,e,r){return(e=fg(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function fg(t){var e=function(t,e){if("object"!=fd(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=fd(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==fd(e)?e:e+""}ff(fh,"displayName","Bar"),ff(fh,"defaultProps",{xAxisId:0,yAxisId:0,legendType:"rect",minPointSize:0,hide:!1,data:[],layout:"vertical",activeBar:!1,isAnimationActive:!t4.isSsr,animationBegin:0,animationDuration:400,animationEasing:"ease"}),ff(fh,"getComposedData",function(t){var e=t.props,r=t.item,n=t.barPosition,o=t.bandSize,i=t.xAxis,a=t.yAxis,c=t.xAxisTicks,u=t.yAxisTicks,l=t.stackedData,s=t.dataStartIndex,f=t.displayedData,p=t.offset,h=ls(n,r);if(!h)return null;var d=e.layout,y=r.type.defaultProps,v=void 0!==y?fa(fa({},y),r.props):r.props,m=v.dataKey,b=v.children,g=v.minPointSize,x="horizontal"===d?a:i,O=l?x.scale.domain():null,w=lb({numericAxis:x}),j=tp(b,sp),S=f.map(function(t,e){l?f=lf(l[s+e],O):Array.isArray(f=u3(t,m))||(f=[w,f]);var n=fe(g,fh.defaultProps.minPointSize)(f[1],e);if("horizontal"===d){var f,p,y,v,b,x,S,A=[a.scale(f[0]),a.scale(f[1])],P=A[0],k=A[1];p=lm({axis:i,ticks:c,bandSize:o,offset:h.offset,entry:t,index:e}),y=null!=(S=null!=k?k:P)?S:void 0,v=h.size;var E=P-k;if(b=Number.isNaN(E)?0:E,x={x:p,y:a.y,width:v,height:a.height},Math.abs(n)>0&&Math.abs(b)<Math.abs(n)){var T=M(b||n)*(Math.abs(n)-Math.abs(b));y-=T,b+=T}}else{var _=[i.scale(f[0]),i.scale(f[1])],C=_[0],D=_[1];if(p=C,y=lm({axis:a,ticks:u,bandSize:o,offset:h.offset,entry:t,index:e}),v=D-C,b=h.size,x={x:i.x,y:y,width:i.width,height:b},Math.abs(n)>0&&Math.abs(v)<Math.abs(n)){var N=M(v||n)*(Math.abs(n)-Math.abs(v));v+=N}}return fa(fa(fa({},t),{},{x:p,y:y,width:v,height:b,value:l?f:f[1],payload:t,background:x},j&&j[e]&&j[e].props),{},{tooltipPayload:[lP(r,t)],tooltipPosition:{x:p+v/2,y:y+b/2}})});return fa({data:S,layout:d},p)});var fx=function(t,e,r,n,o){var i=t.width,a=t.height,c=t.layout,u=t.children,l=Object.keys(e),s={left:r.left,leftMirror:r.left,right:i-r.right,rightMirror:i-r.right,top:r.top,topMirror:r.top,bottom:a-r.bottom,bottomMirror:a-r.bottom},f=!!th(u,fh);return l.reduce(function(i,a){var u,l,p,h,d,y=e[a],v=y.orientation,m=y.domain,b=y.padding,g=void 0===b?{}:b,x=y.mirror,O=y.reversed,w="".concat(v).concat(x?"Mirror":"");if("number"===y.type&&("gap"===y.padding||"no-gap"===y.padding)){var j=m[1]-m[0],S=1/0,A=y.categoricalDomain.sort(U);if(A.forEach(function(t,e){e>0&&(S=Math.min((t||0)-(A[e-1]||0),S))}),Number.isFinite(S)){var P=S/j,k="vertical"===y.layout?r.height:r.width;if("gap"===y.padding&&(u=P*k/2),"no-gap"===y.padding){var E=I(t.barCategoryGap,P*k),M=P*k/2;u=M-E-(M-E)/k*E}}}l="xAxis"===n?[r.left+(g.left||0)+(u||0),r.left+r.width-(g.right||0)-(u||0)]:"yAxis"===n?"horizontal"===c?[r.top+r.height-(g.bottom||0),r.top+(g.top||0)]:[r.top+(g.top||0)+(u||0),r.top+r.height-(g.bottom||0)-(u||0)]:y.range,O&&(l=[l[1],l[0]]);var T=lu(y,o,f),_=T.scale,C=T.realScaleType;_.domain(m).range(l),ll(_);var D=ly(_,fm(fm({},y),{},{realScaleType:C}));"xAxis"===n?(d="top"===v&&!x||"bottom"===v&&x,p=r.left,h=s[w]-d*y.height):"yAxis"===n&&(d="left"===v&&!x||"right"===v&&x,p=s[w]-d*y.width,h=r.top);var N=fm(fm(fm({},y),D),{},{realScaleType:C,x:p,y:h,scale:_,width:"xAxis"===n?r.width:y.width,height:"yAxis"===n?r.height:y.height});return N.bandSize=lS(N,D),y.hide||"xAxis"!==n?y.hide||(s[w]+=(d?-1:1)*N.width):s[w]+=(d?-1:1)*N.height,fm(fm({},i),{},fb({},a,N))},{})},fO=function(t,e){var r=t.x,n=t.y,o=e.x,i=e.y;return{x:Math.min(r,o),y:Math.min(n,i),width:Math.abs(o-r),height:Math.abs(i-n)}},fw=function(){var t,e;function r(t){if(!(this instanceof r))throw TypeError("Cannot call a class as a function");this.scale=t}return t=[{key:"domain",get:function(){return this.scale.domain}},{key:"range",get:function(){return this.scale.range}},{key:"rangeMin",get:function(){return this.range()[0]}},{key:"rangeMax",get:function(){return this.range()[1]}},{key:"bandwidth",get:function(){return this.scale.bandwidth}},{key:"apply",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=e.bandAware,n=e.position;if(void 0!==t){if(n)switch(n){case"start":default:return this.scale(t);case"middle":var o=this.bandwidth?this.bandwidth()/2:0;return this.scale(t)+o;case"end":var i=this.bandwidth?this.bandwidth():0;return this.scale(t)+i}if(r){var a=this.bandwidth?this.bandwidth()/2:0;return this.scale(t)+a}return this.scale(t)}}},{key:"isInRange",value:function(t){var e=this.range(),r=e[0],n=e[e.length-1];return r<=n?t>=r&&t<=n:t>=n&&t<=r}}],e=[{key:"create",value:function(t){return new r(t)}}],t&&fy(r.prototype,t),e&&fy(r,e),Object.defineProperty(r,"prototype",{writable:!1}),r}();fb(fw,"EPS",1e-4);var fj=function(t){var e=Object.keys(t).reduce(function(e,r){return fm(fm({},e),{},fb({},r,fw.create(t[r])))},{});return fm(fm({},e),{},{apply:function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=r.bandAware,o=r.position;return sl()(t,function(t,r){return e[r].apply(t,{bandAware:n,position:o})})},isInRange:function(t){return sf()(t,function(t,r){return e[r].isInRange(t)})}})},fS=function(t){var e=t.width,r=t.height,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,o=(n%180+180)%180*Math.PI/180,i=Math.atan(r/e);return Math.abs(o>i&&o<Math.PI-i?r/Math.sin(o):e/Math.cos(o))};function fA(){return(fA=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function fP(t){return(fP="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function fk(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function fE(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?fk(Object(r),!0).forEach(function(e){fC(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):fk(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function fM(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(fM=function(){return!!t})()}function fT(t){return(fT=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function f_(t,e){return(f_=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function fC(t,e,r){return(e=fD(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function fD(t){var e=function(t,e){if("object"!=fP(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=fP(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==fP(e)?e:e+""}var fN=function(t){var e=t.x,r=t.y,n=t.xAxis,o=t.yAxis,i=fj({x:n.scale,y:o.scale}),a=i.apply({x:e,y:r},{bandAware:!0});return sc(t,"discard")&&!i.isInRange(a)?null:a},fI=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=fT(t),function(t,e){if(e&&("object"===fP(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,fM()?Reflect.construct(t,e||[],fT(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&f_(r,t),e=[{key:"render",value:function(){var t=this.props,e=t.x,n=t.y,o=t.r,i=t.alwaysShow,c=t.clipPathId,u=C(e),l=C(n);if(F(void 0===i,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.'),!u||!l)return null;var s=fN(this.props);if(!s)return null;var f=s.x,p=s.y,h=this.props,d=h.shape,y=h.className,v=fE(fE({clipPath:sc(this.props,"hidden")?"url(#".concat(c,")"):void 0},tm(this.props,!0)),{},{cx:f,cy:p});return a().createElement(tR,{className:(0,b.A)("recharts-reference-dot",y)},r.renderDot(d,v),si.renderCallByParent(this.props,{x:f-o,y:p-o,width:2*o,height:2*o}))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,fD(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(a().Component);fC(fI,"displayName","ReferenceDot"),fC(fI,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#fff",stroke:"#ccc",fillOpacity:1,strokeWidth:1}),fC(fI,"renderDot",function(t,e){var r;return a().isValidElement(t)?a().cloneElement(t,e):H()(t)?t(e):a().createElement(rt,fA({},e,{cx:e.cx,cy:e.cy,className:"recharts-reference-dot-dot"}))});var fB=r(67367),fR=r.n(fB),fL=r(22964),fz=r.n(fL),fU=r(86451),fF=r.n(fU)()(function(t){return{x:t.left,y:t.top,width:t.width,height:t.height}},function(t){return["l",t.left,"t",t.top,"w",t.width,"h",t.height].join("")}),f$=(0,i.createContext)(void 0),fq=(0,i.createContext)(void 0),fW=(0,i.createContext)(void 0),fH=(0,i.createContext)({}),fG=(0,i.createContext)(void 0),fV=(0,i.createContext)(0),fX=(0,i.createContext)(0),fK=function(t){var e=t.state,r=e.xAxisMap,n=e.yAxisMap,o=e.offset,i=t.clipPathId,c=t.children,u=t.width,l=t.height,s=fF(o);return a().createElement(f$.Provider,{value:r},a().createElement(fq.Provider,{value:n},a().createElement(fH.Provider,{value:o},a().createElement(fW.Provider,{value:s},a().createElement(fG.Provider,{value:i},a().createElement(fV.Provider,{value:l},a().createElement(fX.Provider,{value:u},c)))))))},fY=function(t){var e=(0,i.useContext)(f$);null==e&&t_(!1);var r=e[t];return null==r&&t_(!1),r},fZ=function(){var t=(0,i.useContext)(fq);return fz()(t,function(t){return sf()(t.domain,Number.isFinite)})||B(t)},fJ=function(t){var e=(0,i.useContext)(fq);null==e&&t_(!1);var r=e[t];return null==r&&t_(!1),r},fQ=function(){return(0,i.useContext)(fX)},f0=function(){return(0,i.useContext)(fV)};function f1(t){return(f1="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function f2(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(f2=function(){return!!t})()}function f3(t){return(f3=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function f5(t,e){return(f5=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function f4(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function f6(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?f4(Object(r),!0).forEach(function(e){f8(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):f4(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function f8(t,e,r){return(e=f7(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function f7(t){var e=function(t,e){if("object"!=f1(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=f1(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==f1(e)?e:e+""}function f9(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function pt(){return(pt=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var pe=function(t,e){var r;return a().isValidElement(t)?a().cloneElement(t,e):H()(t)?t(e):a().createElement("line",pt({},e,{className:"recharts-reference-line-line"}))},pr=function(t,e,r,n,o,i,a,c,u){var l=o.x,s=o.y,f=o.width,p=o.height;if(r){var h=u.y,d=t.y.apply(h,{position:i});if(sc(u,"discard")&&!t.y.isInRange(d))return null;var y=[{x:l+f,y:d},{x:l,y:d}];return"left"===c?y.reverse():y}if(e){var v=u.x,m=t.x.apply(v,{position:i});if(sc(u,"discard")&&!t.x.isInRange(m))return null;var b=[{x:m,y:s+p},{x:m,y:s}];return"top"===a?b.reverse():b}if(n){var g=u.segment.map(function(e){return t.apply(e,{position:i})});return sc(u,"discard")&&fR()(g,function(e){return!t.isInRange(e)})?null:g}return null};function pn(t){var e,r=t.x,n=t.y,o=t.segment,c=t.xAxisId,u=t.yAxisId,l=t.shape,s=t.className,f=t.alwaysShow,p=(0,i.useContext)(fG),h=fY(c),d=fJ(u),y=(0,i.useContext)(fW);if(!p||!y)return null;F(void 0===f,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var v=pr(fj({x:h.scale,y:d.scale}),C(r),C(n),o&&2===o.length,y,t.position,h.orientation,d.orientation,t);if(!v)return null;var m=function(t){if(Array.isArray(t))return t}(v)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{i=(r=r.call(t)).next,!1;for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(v,2)||function(t,e){if(t){if("string"==typeof t)return f9(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return f9(t,e)}}(v,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),g=m[0],x=g.x,O=g.y,w=m[1],j=w.x,S=w.y,A=f6(f6({clipPath:sc(t,"hidden")?"url(#".concat(p,")"):void 0},tm(t,!0)),{},{x1:x,y1:O,x2:j,y2:S});return a().createElement(tR,{className:(0,b.A)("recharts-reference-line",s)},pe(l,A),si.renderCallByParent(t,fO({x:(e={x1:x,y1:O,x2:j,y2:S}).x1,y:e.y1},{x:e.x2,y:e.y2})))}var po=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=f3(t),function(t,e){if(e&&("object"===f1(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,f2()?Reflect.construct(t,e||[],f3(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&f5(r,t),e=[{key:"render",value:function(){return a().createElement(pn,this.props)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,f7(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(a().Component);function pi(){return(pi=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function pa(t){return(pa="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function pc(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function pu(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?pc(Object(r),!0).forEach(function(e){pp(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):pc(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}f8(po,"displayName","ReferenceLine"),f8(po,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,fill:"none",stroke:"#ccc",fillOpacity:1,strokeWidth:1,position:"middle"});function pl(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(pl=function(){return!!t})()}function ps(t){return(ps=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function pf(t,e){return(pf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function pp(t,e,r){return(e=ph(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function ph(t){var e=function(t,e){if("object"!=pa(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=pa(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==pa(e)?e:e+""}var pd=function(t,e,r,n,o){var i=o.x1,a=o.x2,c=o.y1,u=o.y2,l=o.xAxis,s=o.yAxis;if(!l||!s)return null;var f=fj({x:l.scale,y:s.scale}),p={x:t?f.x.apply(i,{position:"start"}):f.x.rangeMin,y:r?f.y.apply(c,{position:"start"}):f.y.rangeMin},h={x:e?f.x.apply(a,{position:"end"}):f.x.rangeMax,y:n?f.y.apply(u,{position:"end"}):f.y.rangeMax};return!sc(o,"discard")||f.isInRange(p)&&f.isInRange(h)?fO(p,h):null},py=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=ps(t),function(t,e){if(e&&("object"===pa(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,pl()?Reflect.construct(t,e||[],ps(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&pf(r,t),e=[{key:"render",value:function(){var t=this.props,e=t.x1,n=t.x2,o=t.y1,i=t.y2,c=t.className,u=t.alwaysShow,l=t.clipPathId;F(void 0===u,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var s=C(e),f=C(n),p=C(o),h=C(i),d=this.props.shape;if(!s&&!f&&!p&&!h&&!d)return null;var y=pd(s,f,p,h,this.props);if(!y&&!d)return null;var v=sc(this.props,"hidden")?"url(#".concat(l,")"):void 0;return a().createElement(tR,{className:(0,b.A)("recharts-reference-area",c)},r.renderRect(d,pu(pu({clipPath:v},tm(this.props,!0)),y)),si.renderCallByParent(this.props,y))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,ph(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(a().Component);function pv(t){return function(t){if(Array.isArray(t))return pm(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return pm(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return pm(t,e)}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function pm(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}pp(py,"displayName","ReferenceArea"),pp(py,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#ccc",fillOpacity:.5,stroke:"none",strokeWidth:1}),pp(py,"renderRect",function(t,e){var r;return a().isValidElement(t)?a().cloneElement(t,e):H()(t)?t(e):a().createElement(ng,pi({},e,{className:"recharts-reference-area-rect"}))});var pb=function(t,e,r,n,o){var i=tp(t,po),a=tp(t,fI),c=[].concat(pv(i),pv(a)),u=tp(t,py),l="".concat(n,"Id"),s=n[0],f=e;if(c.length&&(f=c.reduce(function(t,e){if(e.props[l]===r&&sc(e.props,"extendDomain")&&_(e.props[s])){var n=e.props[s];return[Math.min(t[0],n),Math.max(t[1],n)]}return t},f)),u.length){var p="".concat(s,"1"),h="".concat(s,"2");f=u.reduce(function(t,e){if(e.props[l]===r&&sc(e.props,"extendDomain")&&_(e.props[p])&&_(e.props[h])){var n=e.props[p],o=e.props[h];return[Math.min(t[0],n,o),Math.max(t[1],n,o)]}return t},f)}return o&&o.length&&(f=o.reduce(function(t,e){return _(e)?[Math.min(t[0],e),Math.max(t[1],e)]:t},f)),f},pg=r(11117),px=new(r.n(pg)()),pO="recharts.syncMouseEvents";function pw(t){return(pw="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function pj(t,e,r){return(e=pS(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function pS(t){var e=function(t,e){if("object"!=pw(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=pw(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==pw(e)?e:e+""}var pA=function(){var t,e;return t=function t(){if(!(this instanceof t))throw TypeError("Cannot call a class as a function");pj(this,"activeIndex",0),pj(this,"coordinateList",[]),pj(this,"layout","horizontal")},e=[{key:"setDetails",value:function(t){var e,r=t.coordinateList,n=void 0===r?null:r,o=t.container,i=void 0===o?null:o,a=t.layout,c=void 0===a?null:a,u=t.offset,l=void 0===u?null:u,s=t.mouseHandlerCallback,f=void 0===s?null:s;this.coordinateList=null!=(e=null!=n?n:this.coordinateList)?e:[],this.container=null!=i?i:this.container,this.layout=null!=c?c:this.layout,this.offset=null!=l?l:this.offset,this.mouseHandlerCallback=null!=f?f:this.mouseHandlerCallback,this.activeIndex=Math.min(Math.max(this.activeIndex,0),this.coordinateList.length-1)}},{key:"focus",value:function(){this.spoofMouse()}},{key:"keyboardEvent",value:function(t){if(0!==this.coordinateList.length)switch(t.key){case"ArrowRight":if("horizontal"!==this.layout)return;this.activeIndex=Math.min(this.activeIndex+1,this.coordinateList.length-1),this.spoofMouse();break;case"ArrowLeft":if("horizontal"!==this.layout)return;this.activeIndex=Math.max(this.activeIndex-1,0),this.spoofMouse()}}},{key:"setIndex",value:function(t){this.activeIndex=t}},{key:"spoofMouse",value:function(){if("horizontal"===this.layout&&0!==this.coordinateList.length){var t,e,r=this.container.getBoundingClientRect(),n=r.x,o=r.y,i=r.height,a=this.coordinateList[this.activeIndex].coordinate,c=(null==(t=window)?void 0:t.scrollX)||0,u=(null==(e=window)?void 0:e.scrollY)||0,l=o+this.offset.top+i/2+u;this.mouseHandlerCallback({pageX:n+a+c,pageY:l})}}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,pS(n.key),n)}}(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}();function pP(){}function pk(t,e,r){t._context.bezierCurveTo((2*t._x0+t._x1)/3,(2*t._y0+t._y1)/3,(t._x0+2*t._x1)/3,(t._y0+2*t._y1)/3,(t._x0+4*t._x1+e)/6,(t._y0+4*t._y1+r)/6)}function pE(t){this._context=t}function pM(t){this._context=t}function pT(t){this._context=t}pE.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:pk(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:pk(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}},pM.prototype={areaStart:pP,areaEnd:pP,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x2,this._y2),this._context.closePath();break;case 2:this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break;case 3:this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4)}},point:function(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1,this._x2=t,this._y2=e;break;case 1:this._point=2,this._x3=t,this._y3=e;break;case 2:this._point=3,this._x4=t,this._y4=e,this._context.moveTo((this._x0+4*this._x1+t)/6,(this._y0+4*this._y1+e)/6);break;default:pk(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}},pT.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var r=(this._x0+4*this._x1+t)/6,n=(this._y0+4*this._y1+e)/6;this._line?this._context.lineTo(r,n):this._context.moveTo(r,n);break;case 3:this._point=4;default:pk(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}};class p_{constructor(t,e){this._context=t,this._x=e}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line}point(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:this._x?this._context.bezierCurveTo(this._x0=(this._x0+t)/2,this._y0,this._x0,e,t,e):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+e)/2,t,this._y0,t,e)}this._x0=t,this._y0=e}}function pC(t){this._context=t}function pD(t){this._context=t}function pN(t){return new pD(t)}pC.prototype={areaStart:pP,areaEnd:pP,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(t,e){t*=1,e*=1,this._point?this._context.lineTo(t,e):(this._point=1,this._context.moveTo(t,e))}};function pI(t,e,r){var n=t._x1-t._x0,o=e-t._x1,i=(t._y1-t._y0)/(n||o<0&&-0),a=(r-t._y1)/(o||n<0&&-0);return((i<0?-1:1)+(a<0?-1:1))*Math.min(Math.abs(i),Math.abs(a),.5*Math.abs((i*o+a*n)/(n+o)))||0}function pB(t,e){var r=t._x1-t._x0;return r?(3*(t._y1-t._y0)/r-e)/2:e}function pR(t,e,r){var n=t._x0,o=t._y0,i=t._x1,a=t._y1,c=(i-n)/3;t._context.bezierCurveTo(n+c,o+c*e,i-c,a-c*r,i,a)}function pL(t){this._context=t}function pz(t){this._context=new pU(t)}function pU(t){this._context=t}function pF(t){this._context=t}function p$(t){var e,r,n=t.length-1,o=Array(n),i=Array(n),a=Array(n);for(o[0]=0,i[0]=2,a[0]=t[0]+2*t[1],e=1;e<n-1;++e)o[e]=1,i[e]=4,a[e]=4*t[e]+2*t[e+1];for(o[n-1]=2,i[n-1]=7,a[n-1]=8*t[n-1]+t[n],e=1;e<n;++e)r=o[e]/i[e-1],i[e]-=r,a[e]-=r*a[e-1];for(o[n-1]=a[n-1]/i[n-1],e=n-2;e>=0;--e)o[e]=(a[e]-o[e+1])/i[e];for(e=0,i[n-1]=(t[n]+o[n-1])/2;e<n-1;++e)i[e]=2*t[e+1]-o[e+1];return[o,i]}function pq(t,e){this._context=t,this._t=e}function pW(t){return t[0]}function pH(t){return t[1]}function pG(t,e){var r=eP(!0),n=null,o=pN,i=null,a=eC(c);function c(c){var u,l,s,f=(c=cN(c)).length,p=!1;for(null==n&&(i=o(s=a())),u=0;u<=f;++u)!(u<f&&r(l=c[u],u,c))===p&&((p=!p)?i.lineStart():i.lineEnd()),p&&i.point(+t(l,u,c),+e(l,u,c));if(s)return i=null,s+""||null}return t="function"==typeof t?t:void 0===t?pW:eP(t),e="function"==typeof e?e:void 0===e?pH:eP(e),c.x=function(e){return arguments.length?(t="function"==typeof e?e:eP(+e),c):t},c.y=function(t){return arguments.length?(e="function"==typeof t?t:eP(+t),c):e},c.defined=function(t){return arguments.length?(r="function"==typeof t?t:eP(!!t),c):r},c.curve=function(t){return arguments.length?(o=t,null!=n&&(i=o(n)),c):o},c.context=function(t){return arguments.length?(null==t?n=i=null:i=o(n=t),c):n},c}function pV(t,e,r){var n=null,o=eP(!0),i=null,a=pN,c=null,u=eC(l);function l(l){var s,f,p,h,d,y=(l=cN(l)).length,v=!1,m=Array(y),b=Array(y);for(null==i&&(c=a(d=u())),s=0;s<=y;++s){if(!(s<y&&o(h=l[s],s,l))===v)if(v=!v)f=s,c.areaStart(),c.lineStart();else{for(c.lineEnd(),c.lineStart(),p=s-1;p>=f;--p)c.point(m[p],b[p]);c.lineEnd(),c.areaEnd()}v&&(m[s]=+t(h,s,l),b[s]=+e(h,s,l),c.point(n?+n(h,s,l):m[s],r?+r(h,s,l):b[s]))}if(d)return c=null,d+""||null}function s(){return pG().defined(o).curve(a).context(i)}return t="function"==typeof t?t:void 0===t?pW:eP(+t),e="function"==typeof e?e:void 0===e?eP(0):eP(+e),r="function"==typeof r?r:void 0===r?pH:eP(+r),l.x=function(e){return arguments.length?(t="function"==typeof e?e:eP(+e),n=null,l):t},l.x0=function(e){return arguments.length?(t="function"==typeof e?e:eP(+e),l):t},l.x1=function(t){return arguments.length?(n=null==t?null:"function"==typeof t?t:eP(+t),l):n},l.y=function(t){return arguments.length?(e="function"==typeof t?t:eP(+t),r=null,l):e},l.y0=function(t){return arguments.length?(e="function"==typeof t?t:eP(+t),l):e},l.y1=function(t){return arguments.length?(r=null==t?null:"function"==typeof t?t:eP(+t),l):r},l.lineX0=l.lineY0=function(){return s().x(t).y(e)},l.lineY1=function(){return s().x(t).y(r)},l.lineX1=function(){return s().x(n).y(e)},l.defined=function(t){return arguments.length?(o="function"==typeof t?t:eP(!!t),l):o},l.curve=function(t){return arguments.length?(a=t,null!=i&&(c=a(i)),l):a},l.context=function(t){return arguments.length?(null==t?i=c=null:c=a(i=t),l):i},l}function pX(t){return(pX="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function pK(){return(pK=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function pY(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function pZ(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?pY(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=pX(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=pX(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==pX(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):pY(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}pD.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:this._context.lineTo(t,e)}}},pL.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:pR(this,this._t0,pB(this,this._t0))}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){var r=NaN;if(e*=1,(t*=1)!==this._x1||e!==this._y1){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;break;case 2:this._point=3,pR(this,pB(this,r=pI(this,t,e)),r);break;default:pR(this,this._t0,r=pI(this,t,e))}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e,this._t0=r}}},(pz.prototype=Object.create(pL.prototype)).point=function(t,e){pL.prototype.point.call(this,e,t)},pU.prototype={moveTo:function(t,e){this._context.moveTo(e,t)},closePath:function(){this._context.closePath()},lineTo:function(t,e){this._context.lineTo(e,t)},bezierCurveTo:function(t,e,r,n,o,i){this._context.bezierCurveTo(e,t,n,r,i,o)}},pF.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var t=this._x,e=this._y,r=t.length;if(r)if(this._line?this._context.lineTo(t[0],e[0]):this._context.moveTo(t[0],e[0]),2===r)this._context.lineTo(t[1],e[1]);else for(var n=p$(t),o=p$(e),i=0,a=1;a<r;++i,++a)this._context.bezierCurveTo(n[0][i],o[0][i],n[1][i],o[1][i],t[a],e[a]);(this._line||0!==this._line&&1===r)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(t,e){this._x.push(+t),this._y.push(+e)}},pq.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&2===this._point&&this._context.lineTo(this._x,this._y),(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:if(this._t<=0)this._context.lineTo(this._x,e),this._context.lineTo(t,e);else{var r=this._x*(1-this._t)+t*this._t;this._context.lineTo(r,this._y),this._context.lineTo(r,e)}}this._x=t,this._y=e}};var pJ={curveBasisClosed:function(t){return new pM(t)},curveBasisOpen:function(t){return new pT(t)},curveBasis:function(t){return new pE(t)},curveBumpX:function(t){return new p_(t,!0)},curveBumpY:function(t){return new p_(t,!1)},curveLinearClosed:function(t){return new pC(t)},curveLinear:pN,curveMonotoneX:function(t){return new pL(t)},curveMonotoneY:function(t){return new pz(t)},curveNatural:function(t){return new pF(t)},curveStep:function(t){return new pq(t,.5)},curveStepAfter:function(t){return new pq(t,1)},curveStepBefore:function(t){return new pq(t,0)}},pQ=function(t){return t.x===+t.x&&t.y===+t.y},p0=function(t){return t.x},p1=function(t){return t.y},p2=function(t,e){if(H()(t))return t;var r="curve".concat(es()(t));return("curveMonotone"===r||"curveBump"===r)&&e?pJ["".concat(r).concat("vertical"===e?"Y":"X")]:pJ[r]||pN},p3=function(t){var e,r=t.type,n=t.points,o=void 0===n?[]:n,i=t.baseLine,a=t.layout,c=t.connectNulls,u=void 0!==c&&c,l=p2(void 0===r?"linear":r,a),s=u?o.filter(function(t){return pQ(t)}):o;if(Array.isArray(i)){var f=u?i.filter(function(t){return pQ(t)}):i,p=s.map(function(t,e){return pZ(pZ({},t),{},{base:f[e]})});return(e="vertical"===a?pV().y(p1).x1(p0).x0(function(t){return t.base.x}):pV().x(p0).y1(p1).y0(function(t){return t.base.y})).defined(pQ).curve(l),e(p)}return(e="vertical"===a&&_(i)?pV().y(p1).x1(p0).x0(i):_(i)?pV().x(p0).y1(p1).y0(i):pG().x(p0).y(p1)).defined(pQ).curve(l),e(s)},p5=function(t){var e=t.className,r=t.points,n=t.path,o=t.pathRef;if((!r||!r.length)&&!n)return null;var i=r&&r.length?p3(t):n;return a().createElement("path",pK({},tm(t,!1),te(t),{className:(0,b.A)("recharts-curve",e),d:i,ref:o}))};function p4(t){return(p4="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var p6=["x","y","top","left","width","height","className"];function p8(){return(p8=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function p7(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}var p9=function(t){var e=t.x,r=void 0===e?0:e,n=t.y,o=void 0===n?0:n,i=t.top,c=void 0===i?0:i,u=t.left,l=void 0===u?0:u,s=t.width,f=void 0===s?0:s,p=t.height,h=void 0===p?0:p,d=t.className,y=function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?p7(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=p4(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=p4(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==p4(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):p7(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}({x:r,y:o,top:c,left:l,width:f,height:h},function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,p6));return _(r)&&_(o)&&_(f)&&_(h)&&_(c)&&_(l)?a().createElement("path",p8({},tm(y,!0),{className:(0,b.A)("recharts-cross",d),d:"M".concat(r,",").concat(c,"v").concat(h,"M").concat(l,",").concat(o,"h").concat(f)})):null};function ht(t){var e=t.cx,r=t.cy,n=t.radius,o=t.startAngle,i=t.endAngle;return{points:[lJ(e,r,n,o),lJ(e,r,n,i)],cx:e,cy:r,radius:n,startAngle:o,endAngle:i}}function he(t){return(he="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function hr(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function hn(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?hr(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=he(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=he(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==he(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):hr(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function ho(t){var e,r,n,o,a=t.element,c=t.tooltipEventType,u=t.isActive,l=t.activeCoordinate,s=t.activePayload,f=t.offset,p=t.activeTooltipIndex,h=t.tooltipAxisBandSize,d=t.layout,y=t.chartName,v=null!=(r=a.props.cursor)?r:null==(n=a.type.defaultProps)?void 0:n.cursor;if(!a||!v||!u||!l||"ScatterChart"!==y&&"axis"!==c)return null;var m=p5;if("ScatterChart"===y)o=l,m=p9;else if("BarChart"===y)e=h/2,o={stroke:"none",fill:"#ccc",x:"horizontal"===d?l.x-e:f.left+.5,y:"horizontal"===d?f.top+.5:l.y-e,width:"horizontal"===d?h:f.width-1,height:"horizontal"===d?f.height-1:h},m=ng;else if("radial"===d){var g=ht(l),x=g.cx,O=g.cy,w=g.radius;o={cx:x,cy:O,startAngle:g.startAngle,endAngle:g.endAngle,innerRadius:w,outerRadius:w},m=sH}else o={points:function(t,e,r){var n,o,i,a;if("horizontal"===t)i=n=e.x,o=r.top,a=r.top+r.height;else if("vertical"===t)a=o=e.y,n=r.left,i=r.left+r.width;else if(null!=e.cx&&null!=e.cy)if("centric"!==t)return ht(e);else{var c=e.cx,u=e.cy,l=e.innerRadius,s=e.outerRadius,f=e.angle,p=lJ(c,u,l,f),h=lJ(c,u,s,f);n=p.x,o=p.y,i=h.x,a=h.y}return[{x:n,y:o},{x:i,y:a}]}(d,l,f)},m=p5;var j=hn(hn(hn(hn({stroke:"#ccc",pointerEvents:"none"},f),o),tm(v,!1)),{},{payload:s,payloadIndex:p,className:(0,b.A)("recharts-tooltip-cursor",v.className)});return(0,i.isValidElement)(v)?(0,i.cloneElement)(v,j):(0,i.createElement)(m,j)}var hi=["item"],ha=["children","className","width","height","style","compact","title","desc"];function hc(t){return(hc="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function hu(){return(hu=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function hl(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,e)||hy(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function hs(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function hf(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(hf=function(){return!!t})()}function hp(t){return(hp=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function hh(t,e){return(hh=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function hd(t){return function(t){if(Array.isArray(t))return hv(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||hy(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function hy(t,e){if(t){if("string"==typeof t)return hv(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return hv(t,e)}}function hv(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function hm(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function hb(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?hm(Object(r),!0).forEach(function(e){hg(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):hm(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function hg(t,e,r){return(e=hx(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function hx(t){var e=function(t,e){if("object"!=hc(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=hc(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==hc(e)?e:e+""}var hO={xAxis:["bottom","top"],yAxis:["left","right"]},hw={width:"100%",height:"100%"},hj={x:0,y:0};function hS(t){return t}var hA=function(t,e,r,n){var o=e.find(function(t){return t&&t.index===r});if(o){if("horizontal"===t)return{x:o.coordinate,y:n.y};if("vertical"===t)return{x:n.x,y:o.coordinate};if("centric"===t){var i=o.coordinate,a=n.radius;return hb(hb(hb({},n),lJ(n.cx,n.cy,a,i)),{},{angle:i,radius:a})}var c=o.coordinate,u=n.angle;return hb(hb(hb({},n),lJ(n.cx,n.cy,c,u)),{},{angle:u,radius:c})}return hj},hP=function(t,e){var r=e.graphicalItems,n=e.dataStartIndex,o=e.dataEndIndex,i=(null!=r?r:[]).reduce(function(t,e){var r=e.props.data;return r&&r.length?[].concat(hd(t),hd(r)):t},[]);return i.length>0?i:t&&t.length&&_(n)&&_(o)?t.slice(n,o+1):[]};function hk(t){return"number"===t?[0,"auto"]:void 0}var hE=function(t,e,r,n){var o=t.graphicalItems,i=t.tooltipAxis,a=hP(e,t);return r<0||!o||!o.length||r>=a.length?null:o.reduce(function(o,c){var u,l,s=null!=(u=c.props.data)?u:e;return(s&&t.dataStartIndex+t.dataEndIndex!==0&&t.dataEndIndex-t.dataStartIndex>=r&&(s=s.slice(t.dataStartIndex,t.dataEndIndex+1)),l=i.dataKey&&!i.allowDuplicatedCategory?z(void 0===s?a:s,i.dataKey,n):s&&s[r]||a[r])?[].concat(hd(o),[lP(c,l)]):o},[])},hM=function(t,e,r,n){var o=n||{x:t.chartX,y:t.chartY},i="horizontal"===r?o.x:"vertical"===r?o.y:"centric"===r?o.angle:o.radius,a=t.orderedTooltipTicks,c=t.tooltipAxis,u=t.tooltipTicks,l=u4(i,a,u,c);if(l>=0&&u){var s=u[l]&&u[l].value,f=hE(t,e,l,s),p=hA(r,a,l,o);return{activeTooltipIndex:l,activeLabel:s,activePayload:f,activeCoordinate:p}}return null},hT=function(t,e){var r=e.axes,n=e.graphicalItems,o=e.axisType,i=e.axisIdKey,a=e.stackGroups,c=e.dataStartIndex,u=e.dataEndIndex,l=t.layout,s=t.children,f=t.stackOffset,p=ln(l,o);return r.reduce(function(e,r){var h=void 0!==r.type.defaultProps?hb(hb({},r.type.defaultProps),r.props):r.props,d=h.type,y=h.dataKey,v=h.allowDataOverflow,m=h.allowDuplicatedCategory,b=h.scale,g=h.ticks,x=h.includeHidden,O=h[i];if(e[O])return e;var w=hP(t.data,{graphicalItems:n.filter(function(t){var e;return(i in t.props?t.props[i]:null==(e=t.type.defaultProps)?void 0:e[i])===O}),dataStartIndex:c,dataEndIndex:u}),j=w.length;(function(t,e,r){if("number"===r&&!0===e&&Array.isArray(t)){var n=null==t?void 0:t[0],o=null==t?void 0:t[1];if(n&&o&&_(n)&&_(o))return!0}return!1})(h.domain,v,d)&&(P=lj(h.domain,null,v),p&&("number"===d||"auto"!==b)&&(E=u5(w,y,"category")));var S=hk(d);if(!P||0===P.length){var A,P,k,E,M,T=null!=(M=h.domain)?M:S;if(y){if(P=u5(w,y,d),"category"===d&&p){var C=R(P);m&&C?(k=P,P=tE()(0,j)):m||(P=lA(T,P,r).reduce(function(t,e){return t.indexOf(e)>=0?t:[].concat(hd(t),[e])},[]))}else if("category"===d)P=m?P.filter(function(t){return""!==t&&!q()(t)}):lA(T,P,r).reduce(function(t,e){return t.indexOf(e)>=0||""===e||q()(e)?t:[].concat(hd(t),[e])},[]);else if("number"===d){var D=le(w,n.filter(function(t){var e,r,n=i in t.props?t.props[i]:null==(e=t.type.defaultProps)?void 0:e[i],o="hide"in t.props?t.props.hide:null==(r=t.type.defaultProps)?void 0:r.hide;return n===O&&(x||!o)}),y,o,l);D&&(P=D)}p&&("number"===d||"auto"!==b)&&(E=u5(w,y,"category"))}else P=p?tE()(0,j):a&&a[O]&&a[O].hasStack&&"number"===d?"expand"===f?[0,1]:lx(a[O].stackGroups,c,u):lr(w,n.filter(function(t){var e=i in t.props?t.props[i]:t.type.defaultProps[i],r="hide"in t.props?t.props.hide:t.type.defaultProps.hide;return e===O&&(x||!r)}),d,l,!0);"number"===d?(P=pb(s,P,O,o,g),T&&(P=lj(T,P,v))):"category"===d&&T&&P.every(function(t){return T.indexOf(t)>=0})&&(P=T)}return hb(hb({},e),{},hg({},O,hb(hb({},h),{},{axisType:o,domain:P,categoricalDomain:E,duplicateDomain:k,originalDomain:null!=(A=h.domain)?A:S,isCategorical:p,layout:l})))},{})},h_=function(t,e){var r=e.graphicalItems,n=e.Axis,o=e.axisType,i=e.axisIdKey,a=e.stackGroups,c=e.dataStartIndex,u=e.dataEndIndex,l=t.layout,s=t.children,f=hP(t.data,{graphicalItems:r,dataStartIndex:c,dataEndIndex:u}),p=f.length,h=ln(l,o),d=-1;return r.reduce(function(t,e){var y,v=(void 0!==e.type.defaultProps?hb(hb({},e.type.defaultProps),e.props):e.props)[i],m=hk("number");return t[v]?t:(d++,y=h?tE()(0,p):a&&a[v]&&a[v].hasStack?pb(s,y=lx(a[v].stackGroups,c,u),v,o):pb(s,y=lj(m,lr(f,r.filter(function(t){var e,r,n=i in t.props?t.props[i]:null==(e=t.type.defaultProps)?void 0:e[i],o="hide"in t.props?t.props.hide:null==(r=t.type.defaultProps)?void 0:r.hide;return n===v&&!o}),"number",l),n.defaultProps.allowDataOverflow),v,o),hb(hb({},t),{},hg({},v,hb(hb({axisType:o},n.defaultProps),{},{hide:!0,orientation:P()(hO,"".concat(o,".").concat(d%2),null),domain:y,originalDomain:m,isCategorical:h,layout:l}))))},{})},hC=function(t,e){var r=e.axisType,n=void 0===r?"xAxis":r,o=e.AxisComp,i=e.graphicalItems,a=e.stackGroups,c=e.dataStartIndex,u=e.dataEndIndex,l=t.children,s="".concat(n,"Id"),f=tp(l,o),p={};return f&&f.length?p=hT(t,{axes:f,graphicalItems:i,axisType:n,axisIdKey:s,stackGroups:a,dataStartIndex:c,dataEndIndex:u}):i&&i.length&&(p=h_(t,{Axis:o,graphicalItems:i,axisType:n,axisIdKey:s,stackGroups:a,dataStartIndex:c,dataEndIndex:u})),p},hD=function(t){var e=B(t),r=li(e,!1,!0);return{tooltipTicks:r,orderedTooltipTicks:tT()(r,function(t){return t.coordinate}),tooltipAxis:e,tooltipAxisBandSize:lS(e,r)}},hN=function(t){var e=t.children,r=t.defaultShowTooltip,n=th(e,lH),o=0,i=0;return t.data&&0!==t.data.length&&(i=t.data.length-1),n&&n.props&&(n.props.startIndex>=0&&(o=n.props.startIndex),n.props.endIndex>=0&&(i=n.props.endIndex)),{chartX:0,chartY:0,dataStartIndex:o,dataEndIndex:i,activeTooltipIndex:-1,isTooltipActive:!!r}},hI=function(t){return"horizontal"===t?{numericAxisName:"yAxis",cateAxisName:"xAxis"}:"vertical"===t?{numericAxisName:"xAxis",cateAxisName:"yAxis"}:"centric"===t?{numericAxisName:"radiusAxis",cateAxisName:"angleAxis"}:{numericAxisName:"angleAxis",cateAxisName:"radiusAxis"}},hB=function(t,e){var r=t.props,n=t.graphicalItems,o=t.xAxisMap,i=void 0===o?{}:o,a=t.yAxisMap,c=void 0===a?{}:a,u=r.width,l=r.height,s=r.children,f=r.margin||{},p=th(s,lH),h=th(s,e7),d=Object.keys(c).reduce(function(t,e){var r=c[e],n=r.orientation;return r.mirror||r.hide?t:hb(hb({},t),{},hg({},n,t[n]+r.width))},{left:f.left||0,right:f.right||0}),y=Object.keys(i).reduce(function(t,e){var r=i[e],n=r.orientation;return r.mirror||r.hide?t:hb(hb({},t),{},hg({},n,P()(t,"".concat(n))+r.height))},{top:f.top||0,bottom:f.bottom||0}),v=hb(hb({},y),d),m=v.bottom;p&&(v.bottom+=p.props.height||lH.defaultProps.height),h&&e&&(v=u9(v,n,r,e));var b=u-v.left-v.right,g=l-v.top-v.bottom;return hb(hb({brushBottom:m},v),{},{width:Math.max(b,0),height:Math.max(g,0)})},hR=function(t){var e=t.chartName,r=t.GraphicalChild,n=t.defaultTooltipEventType,o=void 0===n?"axis":n,c=t.validateTooltipEventTypes,u=void 0===c?["axis"]:c,l=t.axisComponents,s=t.legendContent,f=t.formatAxisMap,p=t.defaultProps,h=function(t,e){var r=e.graphicalItems,n=e.stackGroups,o=e.offset,i=e.updateId,a=e.dataStartIndex,c=e.dataEndIndex,u=t.barSize,s=t.layout,f=t.barGap,p=t.barCategoryGap,h=t.maxBarSize,d=hI(s),y=d.numericAxisName,v=d.cateAxisName,m=!!r&&!!r.length&&r.some(function(t){var e=tu(t&&t.type);return e&&e.indexOf("Bar")>=0}),b=[];return r.forEach(function(r,d){var g=hP(t.data,{graphicalItems:[r],dataStartIndex:a,dataEndIndex:c}),x=void 0!==r.type.defaultProps?hb(hb({},r.type.defaultProps),r.props):r.props,O=x.dataKey,w=x.maxBarSize,j=x["".concat(y,"Id")],S=x["".concat(v,"Id")],A=l.reduce(function(t,r){var n=e["".concat(r.axisType,"Map")],o=x["".concat(r.axisType,"Id")];n&&n[o]||"zAxis"===r.axisType||t_(!1);var i=n[o];return hb(hb({},t),{},hg(hg({},r.axisType,i),"".concat(r.axisType,"Ticks"),li(i)))},{}),P=A[v],k=A["".concat(v,"Ticks")],E=n&&n[j]&&n[j].hasStack&&lg(r,n[j].stackGroups),M=tu(r.type).indexOf("Bar")>=0,T=lS(P,k),_=[],C=m&&u8({barSize:u,stackGroups:n,totalSize:"xAxis"===v?A[v].width:"yAxis"===v?A[v].height:void 0});if(M){var D,N,I=q()(w)?h:w,B=null!=(D=null!=(N=lS(P,k,!0))?N:I)?D:0;_=u7({barGap:f,barCategoryGap:p,bandSize:B!==T?B:T,sizeList:C[S],maxBarSize:I}),B!==T&&(_=_.map(function(t){return hb(hb({},t),{},{position:hb(hb({},t.position),{},{offset:t.position.offset-B/2})})}))}var R=r&&r.type&&r.type.getComposedData;R&&b.push({props:hb(hb({},R(hb(hb({},A),{},{displayedData:g,props:t,dataKey:O,item:r,bandSize:T,barPosition:_,offset:o,stackedData:E,layout:s,dataStartIndex:a,dataEndIndex:c}))),{},hg(hg(hg({key:r.key||"item-".concat(d)},y,A[y]),v,A[v]),"animationId",i)),childIndex:tf(t.children).indexOf(r),item:r})}),b},d=function(t,n){var o=t.props,i=t.dataStartIndex,a=t.dataEndIndex,c=t.updateId;if(!td({props:o}))return null;var u=o.children,s=o.layout,p=o.stackOffset,d=o.data,y=o.reverseStackOrder,v=hI(s),m=v.numericAxisName,b=v.cateAxisName,g=tp(u,r),x=ld(d,g,"".concat(m,"Id"),"".concat(b,"Id"),p,y),O=l.reduce(function(t,e){var r="".concat(e.axisType,"Map");return hb(hb({},t),{},hg({},r,hC(o,hb(hb({},e),{},{graphicalItems:g,stackGroups:e.axisType===m&&x,dataStartIndex:i,dataEndIndex:a}))))},{}),w=hB(hb(hb({},O),{},{props:o,graphicalItems:g}),null==n?void 0:n.legendBBox);Object.keys(O).forEach(function(t){O[t]=f(o,O[t],w,t.replace("Map",""),e)});var j=hD(O["".concat(b,"Map")]),S=h(o,hb(hb({},O),{},{dataStartIndex:i,dataEndIndex:a,updateId:c,graphicalItems:g,stackGroups:x,offset:w}));return hb(hb({formattedGraphicalItems:S,graphicalItems:g,offset:w,stackGroups:x},j),O)},y=function(t){var r;function n(t){var r,o,c,u,l;if(!(this instanceof n))throw TypeError("Cannot call a class as a function");return u=n,l=[t],u=hp(u),hg(c=function(t,e){if(e&&("object"===hc(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,hf()?Reflect.construct(u,l||[],hp(this).constructor):u.apply(this,l)),"eventEmitterSymbol",Symbol("rechartsEventEmitter")),hg(c,"accessibilityManager",new pA),hg(c,"handleLegendBBoxUpdate",function(t){if(t){var e=c.state,r=e.dataStartIndex,n=e.dataEndIndex,o=e.updateId;c.setState(hb({legendBBox:t},d({props:c.props,dataStartIndex:r,dataEndIndex:n,updateId:o},hb(hb({},c.state),{},{legendBBox:t}))))}}),hg(c,"handleReceiveSyncEvent",function(t,e,r){c.props.syncId===t&&(r!==c.eventEmitterSymbol||"function"==typeof c.props.syncMethod)&&c.applySyncEvent(e)}),hg(c,"handleBrushChange",function(t){var e=t.startIndex,r=t.endIndex;if(e!==c.state.dataStartIndex||r!==c.state.dataEndIndex){var n=c.state.updateId;c.setState(function(){return hb({dataStartIndex:e,dataEndIndex:r},d({props:c.props,dataStartIndex:e,dataEndIndex:r,updateId:n},c.state))}),c.triggerSyncEvent({dataStartIndex:e,dataEndIndex:r})}}),hg(c,"handleMouseEnter",function(t){var e=c.getMouseInfo(t);if(e){var r=hb(hb({},e),{},{isTooltipActive:!0});c.setState(r),c.triggerSyncEvent(r);var n=c.props.onMouseEnter;H()(n)&&n(r,t)}}),hg(c,"triggeredAfterMouseMove",function(t){var e=c.getMouseInfo(t),r=e?hb(hb({},e),{},{isTooltipActive:!0}):{isTooltipActive:!1};c.setState(r),c.triggerSyncEvent(r);var n=c.props.onMouseMove;H()(n)&&n(r,t)}),hg(c,"handleItemMouseEnter",function(t){c.setState(function(){return{isTooltipActive:!0,activeItem:t,activePayload:t.tooltipPayload,activeCoordinate:t.tooltipPosition||{x:t.cx,y:t.cy}}})}),hg(c,"handleItemMouseLeave",function(){c.setState(function(){return{isTooltipActive:!1}})}),hg(c,"handleMouseMove",function(t){t.persist(),c.throttleTriggeredAfterMouseMove(t)}),hg(c,"handleMouseLeave",function(t){c.throttleTriggeredAfterMouseMove.cancel();var e={isTooltipActive:!1};c.setState(e),c.triggerSyncEvent(e);var r=c.props.onMouseLeave;H()(r)&&r(e,t)}),hg(c,"handleOuterEvent",function(t){var e,r,n=tO(t),o=P()(c.props,"".concat(n));n&&H()(o)&&o(null!=(e=/.*touch.*/i.test(n)?c.getMouseInfo(t.changedTouches[0]):c.getMouseInfo(t))?e:{},t)}),hg(c,"handleClick",function(t){var e=c.getMouseInfo(t);if(e){var r=hb(hb({},e),{},{isTooltipActive:!0});c.setState(r),c.triggerSyncEvent(r);var n=c.props.onClick;H()(n)&&n(r,t)}}),hg(c,"handleMouseDown",function(t){var e=c.props.onMouseDown;H()(e)&&e(c.getMouseInfo(t),t)}),hg(c,"handleMouseUp",function(t){var e=c.props.onMouseUp;H()(e)&&e(c.getMouseInfo(t),t)}),hg(c,"handleTouchMove",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&c.throttleTriggeredAfterMouseMove(t.changedTouches[0])}),hg(c,"handleTouchStart",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&c.handleMouseDown(t.changedTouches[0])}),hg(c,"handleTouchEnd",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&c.handleMouseUp(t.changedTouches[0])}),hg(c,"handleDoubleClick",function(t){var e=c.props.onDoubleClick;H()(e)&&e(c.getMouseInfo(t),t)}),hg(c,"handleContextMenu",function(t){var e=c.props.onContextMenu;H()(e)&&e(c.getMouseInfo(t),t)}),hg(c,"triggerSyncEvent",function(t){void 0!==c.props.syncId&&px.emit(pO,c.props.syncId,t,c.eventEmitterSymbol)}),hg(c,"applySyncEvent",function(t){var e=c.props,r=e.layout,n=e.syncMethod,o=c.state.updateId,i=t.dataStartIndex,a=t.dataEndIndex;if(void 0!==t.dataStartIndex||void 0!==t.dataEndIndex)c.setState(hb({dataStartIndex:i,dataEndIndex:a},d({props:c.props,dataStartIndex:i,dataEndIndex:a,updateId:o},c.state)));else if(void 0!==t.activeTooltipIndex){var u=t.chartX,l=t.chartY,s=t.activeTooltipIndex,f=c.state,p=f.offset,h=f.tooltipTicks;if(!p)return;if("function"==typeof n)s=n(h,t);else if("value"===n){s=-1;for(var y=0;y<h.length;y++)if(h[y].value===t.activeLabel){s=y;break}}var v=hb(hb({},p),{},{x:p.left,y:p.top}),m=Math.min(u,v.x+v.width),b=Math.min(l,v.y+v.height),g=h[s]&&h[s].value,x=hE(c.state,c.props.data,s),O=h[s]?{x:"horizontal"===r?h[s].coordinate:m,y:"horizontal"===r?b:h[s].coordinate}:hj;c.setState(hb(hb({},t),{},{activeLabel:g,activeCoordinate:O,activePayload:x,activeTooltipIndex:s}))}else c.setState(t)}),hg(c,"renderCursor",function(t){var r,n=c.state,o=n.isTooltipActive,i=n.activeCoordinate,u=n.activePayload,l=n.offset,s=n.activeTooltipIndex,f=n.tooltipAxisBandSize,p=c.getTooltipEventType(),h=null!=(r=t.props.active)?r:o,d=c.props.layout,y=t.key||"_recharts-cursor";return a().createElement(ho,{key:y,activeCoordinate:i,activePayload:u,activeTooltipIndex:s,chartName:e,element:t,isActive:h,layout:d,offset:l,tooltipAxisBandSize:f,tooltipEventType:p})}),hg(c,"renderPolarAxis",function(t,e,r){var n=P()(t,"type.axisType"),o=P()(c.state,"".concat(n,"Map")),a=t.type.defaultProps,u=void 0!==a?hb(hb({},a),t.props):t.props,l=o&&o[u["".concat(n,"Id")]];return(0,i.cloneElement)(t,hb(hb({},l),{},{className:(0,b.A)(n,l.className),key:t.key||"".concat(e,"-").concat(r),ticks:li(l,!0)}))}),hg(c,"renderPolarGrid",function(t){var e=t.props,r=e.radialLines,n=e.polarAngles,o=e.polarRadius,a=c.state,u=a.radiusAxisMap,l=a.angleAxisMap,s=B(u),f=B(l),p=f.cx,h=f.cy,d=f.innerRadius,y=f.outerRadius;return(0,i.cloneElement)(t,{polarAngles:Array.isArray(n)?n:li(f,!0).map(function(t){return t.coordinate}),polarRadius:Array.isArray(o)?o:li(s,!0).map(function(t){return t.coordinate}),cx:p,cy:h,innerRadius:d,outerRadius:y,key:t.key||"polar-grid",radialLines:r})}),hg(c,"renderLegend",function(){var t=c.state.formattedGraphicalItems,e=c.props,r=e.children,n=e.width,o=e.height,a=c.props.margin||{},u=uY({children:r,formattedGraphicalItems:t,legendWidth:n-(a.left||0)-(a.right||0),legendContent:s});if(!u)return null;var l=u.item,f=hs(u,hi);return(0,i.cloneElement)(l,hb(hb({},f),{},{chartWidth:n,chartHeight:o,margin:a,onBBoxUpdate:c.handleLegendBBoxUpdate}))}),hg(c,"renderTooltip",function(){var t,e=c.props,r=e.children,n=e.accessibilityLayer,o=th(r,eu);if(!o)return null;var a=c.state,u=a.isTooltipActive,l=a.activeCoordinate,s=a.activePayload,f=a.activeLabel,p=a.offset,h=null!=(t=o.props.active)?t:u;return(0,i.cloneElement)(o,{viewBox:hb(hb({},p),{},{x:p.left,y:p.top}),active:h,label:f,payload:h?s:[],coordinate:l,accessibilityLayer:n})}),hg(c,"renderBrush",function(t){var e=c.props,r=e.margin,n=e.data,o=c.state,a=o.offset,u=o.dataStartIndex,l=o.dataEndIndex,s=o.updateId;return(0,i.cloneElement)(t,{key:t.key||"_recharts-brush",onChange:lc(c.handleBrushChange,t.props.onChange),data:n,x:_(t.props.x)?t.props.x:a.left,y:_(t.props.y)?t.props.y:a.top+a.height+a.brushBottom-(r.bottom||0),width:_(t.props.width)?t.props.width:a.width,startIndex:u,endIndex:l,updateId:"brush-".concat(s)})}),hg(c,"renderReferenceElement",function(t,e,r){if(!t)return null;var n=c.clipPathId,o=c.state,a=o.xAxisMap,u=o.yAxisMap,l=o.offset,s=t.type.defaultProps||{},f=t.props,p=f.xAxisId,h=void 0===p?s.xAxisId:p,d=f.yAxisId,y=void 0===d?s.yAxisId:d;return(0,i.cloneElement)(t,{key:t.key||"".concat(e,"-").concat(r),xAxis:a[h],yAxis:u[y],viewBox:{x:l.left,y:l.top,width:l.width,height:l.height},clipPathId:n})}),hg(c,"renderActivePoints",function(t){var e=t.item,r=t.activePoint,o=t.basePoint,i=t.childIndex,a=t.isRange,c=[],u=e.props.key,l=void 0!==e.item.type.defaultProps?hb(hb({},e.item.type.defaultProps),e.item.props):e.item.props,s=l.activeDot,f=hb(hb({index:i,dataKey:l.dataKey,cx:r.x,cy:r.y,r:4,fill:u6(e.item),strokeWidth:2,stroke:"#fff",payload:r.payload,value:r.value},tm(s,!1)),te(s));return c.push(n.renderActiveDot(s,f,"".concat(u,"-activePoint-").concat(i))),o?c.push(n.renderActiveDot(s,hb(hb({},f),{},{cx:o.x,cy:o.y}),"".concat(u,"-basePoint-").concat(i))):a&&c.push(null),c}),hg(c,"renderGraphicChild",function(t,e,r){var n=c.filterFormatItem(t,e,r);if(!n)return null;var o=c.getTooltipEventType(),a=c.state,u=a.isTooltipActive,l=a.tooltipAxis,s=a.activeTooltipIndex,f=a.activeLabel,p=th(c.props.children,eu),h=n.props,d=h.points,y=h.isRange,v=h.baseLine,m=void 0!==n.item.type.defaultProps?hb(hb({},n.item.type.defaultProps),n.item.props):n.item.props,b=m.activeDot,g=m.hide,x=m.activeBar,O=m.activeShape,w=!!(!g&&u&&p&&(b||x||O)),j={};"axis"!==o&&p&&"click"===p.props.trigger?j={onClick:lc(c.handleItemMouseEnter,t.props.onClick)}:"axis"!==o&&(j={onMouseLeave:lc(c.handleItemMouseLeave,t.props.onMouseLeave),onMouseEnter:lc(c.handleItemMouseEnter,t.props.onMouseEnter)});var S=(0,i.cloneElement)(t,hb(hb({},n.props),j));if(w)if(s>=0){if(l.dataKey&&!l.allowDuplicatedCategory){var A="function"==typeof l.dataKey?function(t){return"function"==typeof l.dataKey?l.dataKey(t.payload):null}:"payload.".concat(l.dataKey.toString());k=z(d,A,f),E=y&&v&&z(v,A,f)}else k=null==d?void 0:d[s],E=y&&v&&v[s];if(O||x){var P=void 0!==t.props.activeIndex?t.props.activeIndex:s;return[(0,i.cloneElement)(t,hb(hb(hb({},n.props),j),{},{activeIndex:P})),null,null]}if(!q()(k))return[S].concat(hd(c.renderActivePoints({item:n,activePoint:k,basePoint:E,childIndex:s,isRange:y})))}else{var k,E,M,T=(null!=(M=c.getItemByXY(c.state.activeCoordinate))?M:{graphicalItem:S}).graphicalItem,_=T.item,C=void 0===_?t:_,D=T.childIndex,N=hb(hb(hb({},n.props),j),{},{activeIndex:D});return[(0,i.cloneElement)(C,N),null,null]}return y?[S,null,null]:[S,null]}),hg(c,"renderCustomized",function(t,e,r){return(0,i.cloneElement)(t,hb(hb({key:"recharts-customized-".concat(r)},c.props),c.state))}),hg(c,"renderMap",{CartesianGrid:{handler:hS,once:!0},ReferenceArea:{handler:c.renderReferenceElement},ReferenceLine:{handler:hS},ReferenceDot:{handler:c.renderReferenceElement},XAxis:{handler:hS},YAxis:{handler:hS},Brush:{handler:c.renderBrush,once:!0},Bar:{handler:c.renderGraphicChild},Line:{handler:c.renderGraphicChild},Area:{handler:c.renderGraphicChild},Radar:{handler:c.renderGraphicChild},RadialBar:{handler:c.renderGraphicChild},Scatter:{handler:c.renderGraphicChild},Pie:{handler:c.renderGraphicChild},Funnel:{handler:c.renderGraphicChild},Tooltip:{handler:c.renderCursor,once:!0},PolarGrid:{handler:c.renderPolarGrid,once:!0},PolarAngleAxis:{handler:c.renderPolarAxis},PolarRadiusAxis:{handler:c.renderPolarAxis},Customized:{handler:c.renderCustomized}}),c.clipPathId="".concat(null!=(r=t.id)?r:N("recharts"),"-clip"),c.throttleTriggeredAfterMouseMove=x()(c.triggeredAfterMouseMove,null!=(o=t.throttleDelay)?o:1e3/60),c.state={},c}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return n.prototype=Object.create(t&&t.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),t&&hh(n,t),r=[{key:"componentDidMount",value:function(){var t,e;this.addListener(),this.accessibilityManager.setDetails({container:this.container,offset:{left:null!=(t=this.props.margin.left)?t:0,top:null!=(e=this.props.margin.top)?e:0},coordinateList:this.state.tooltipTicks,mouseHandlerCallback:this.triggeredAfterMouseMove,layout:this.props.layout}),this.displayDefaultTooltip()}},{key:"displayDefaultTooltip",value:function(){var t=this.props,e=t.children,r=t.data,n=t.height,o=t.layout,i=th(e,eu);if(i){var a=i.props.defaultIndex;if("number"==typeof a&&!(a<0)&&!(a>this.state.tooltipTicks.length-1)){var c=this.state.tooltipTicks[a]&&this.state.tooltipTicks[a].value,u=hE(this.state,r,a,c),l=this.state.tooltipTicks[a].coordinate,s=(this.state.offset.top+n)/2,f="horizontal"===o?{x:l,y:s}:{y:l,x:s},p=this.state.formattedGraphicalItems.find(function(t){return"Scatter"===t.item.type.name});p&&(f=hb(hb({},f),p.props.points[a].tooltipPosition),u=p.props.points[a].tooltipPayload);var h={activeTooltipIndex:a,isTooltipActive:!0,activeLabel:c,activePayload:u,activeCoordinate:f};this.setState(h),this.renderCursor(i),this.accessibilityManager.setIndex(a)}}}},{key:"getSnapshotBeforeUpdate",value:function(t,e){if(!this.props.accessibilityLayer)return null;if(this.state.tooltipTicks!==e.tooltipTicks&&this.accessibilityManager.setDetails({coordinateList:this.state.tooltipTicks}),this.props.layout!==t.layout&&this.accessibilityManager.setDetails({layout:this.props.layout}),this.props.margin!==t.margin){var r,n;this.accessibilityManager.setDetails({offset:{left:null!=(r=this.props.margin.left)?r:0,top:null!=(n=this.props.margin.top)?n:0}})}return null}},{key:"componentDidUpdate",value:function(t){tb([th(t.children,eu)],[th(this.props.children,eu)])||this.displayDefaultTooltip()}},{key:"componentWillUnmount",value:function(){this.removeListener(),this.throttleTriggeredAfterMouseMove.cancel()}},{key:"getTooltipEventType",value:function(){var t=th(this.props.children,eu);if(t&&"boolean"==typeof t.props.shared){var e=t.props.shared?"axis":"item";return u.indexOf(e)>=0?e:o}return o}},{key:"getMouseInfo",value:function(t){if(!this.container)return null;var e=this.container,r=e.getBoundingClientRect(),n={top:r.top+window.scrollY-document.documentElement.clientTop,left:r.left+window.scrollX-document.documentElement.clientLeft},o={chartX:Math.round(t.pageX-n.left),chartY:Math.round(t.pageY-n.top)},i=r.width/e.offsetWidth||1,a=this.inRange(o.chartX,o.chartY,i);if(!a)return null;var c=this.state,u=c.xAxisMap,l=c.yAxisMap,s=this.getTooltipEventType(),f=hM(this.state,this.props.data,this.props.layout,a);if("axis"!==s&&u&&l){var p=B(u).scale,h=B(l).scale,d=p&&p.invert?p.invert(o.chartX):null,y=h&&h.invert?h.invert(o.chartY):null;return hb(hb({},o),{},{xValue:d,yValue:y},f)}return f?hb(hb({},o),f):null}},{key:"inRange",value:function(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,n=this.props.layout,o=t/r,i=e/r;if("horizontal"===n||"vertical"===n){var a=this.state.offset;return o>=a.left&&o<=a.left+a.width&&i>=a.top&&i<=a.top+a.height?{x:o,y:i}:null}var c=this.state,u=c.angleAxisMap,l=c.radiusAxisMap;return u&&l?l3({x:o,y:i},B(u)):null}},{key:"parseEventsOfWrapper",value:function(){var t=this.props.children,e=this.getTooltipEventType(),r=th(t,eu),n={};return r&&"axis"===e&&(n="click"===r.props.trigger?{onClick:this.handleClick}:{onMouseEnter:this.handleMouseEnter,onDoubleClick:this.handleDoubleClick,onMouseMove:this.handleMouseMove,onMouseLeave:this.handleMouseLeave,onTouchMove:this.handleTouchMove,onTouchStart:this.handleTouchStart,onTouchEnd:this.handleTouchEnd,onContextMenu:this.handleContextMenu}),hb(hb({},te(this.props,this.handleOuterEvent)),n)}},{key:"addListener",value:function(){px.on(pO,this.handleReceiveSyncEvent)}},{key:"removeListener",value:function(){px.removeListener(pO,this.handleReceiveSyncEvent)}},{key:"filterFormatItem",value:function(t,e,r){for(var n=this.state.formattedGraphicalItems,o=0,i=n.length;o<i;o++){var a=n[o];if(a.item===t||a.props.key===t.key||e===tu(a.item.type)&&r===a.childIndex)return a}return null}},{key:"renderClipPath",value:function(){var t=this.clipPathId,e=this.state.offset,r=e.left,n=e.top,o=e.height,i=e.width;return a().createElement("defs",null,a().createElement("clipPath",{id:t},a().createElement("rect",{x:r,y:n,height:o,width:i})))}},{key:"getXScales",value:function(){var t=this.state.xAxisMap;return t?Object.entries(t).reduce(function(t,e){var r=hl(e,2),n=r[0],o=r[1];return hb(hb({},t),{},hg({},n,o.scale))},{}):null}},{key:"getYScales",value:function(){var t=this.state.yAxisMap;return t?Object.entries(t).reduce(function(t,e){var r=hl(e,2),n=r[0],o=r[1];return hb(hb({},t),{},hg({},n,o.scale))},{}):null}},{key:"getXScaleByAxisId",value:function(t){var e;return null==(e=this.state.xAxisMap)||null==(e=e[t])?void 0:e.scale}},{key:"getYScaleByAxisId",value:function(t){var e;return null==(e=this.state.yAxisMap)||null==(e=e[t])?void 0:e.scale}},{key:"getItemByXY",value:function(t){var e=this.state,r=e.formattedGraphicalItems,n=e.activeItem;if(r&&r.length)for(var o=0,i=r.length;o<i;o++){var a=r[o],c=a.props,u=a.item,l=void 0!==u.type.defaultProps?hb(hb({},u.type.defaultProps),u.props):u.props,s=tu(u.type);if("Bar"===s){var f=(c.data||[]).find(function(e){return nm(t,e)});if(f)return{graphicalItem:a,payload:f}}else if("RadialBar"===s){var p=(c.data||[]).find(function(e){return l3(t,e)});if(p)return{graphicalItem:a,payload:p}}else if(sJ(a,n)||sQ(a,n)||s0(a,n)){var h=function(t){var e,r,n,o=t.activeTooltipItem,i=t.graphicalItem,a=t.itemData,c=(sJ(i,o)?e="trapezoids":sQ(i,o)?e="sectors":s0(i,o)&&(e="points"),e),u=sJ(i,o)?null==(r=o.tooltipPayload)||null==(r=r[0])||null==(r=r.payload)?void 0:r.payload:sQ(i,o)?null==(n=o.tooltipPayload)||null==(n=n[0])||null==(n=n.payload)?void 0:n.payload:s0(i,o)?o.payload:{},l=a.filter(function(t,e){var r=c0()(u,t),n=i.props[c].filter(function(t){var e;return(sJ(i,o)?e=s1:sQ(i,o)?e=s2:s0(i,o)&&(e=s3),e)(t,o)}),a=i.props[c].indexOf(n[n.length-1]);return r&&e===a});return a.indexOf(l[l.length-1])}({graphicalItem:a,activeTooltipItem:n,itemData:l.data}),d=void 0===l.activeIndex?h:l.activeIndex;return{graphicalItem:hb(hb({},a),{},{childIndex:d}),payload:s0(a,n)?l.data[h]:a.props.data[h]}}}return null}},{key:"render",value:function(){var t,e,r=this;if(!td(this))return null;var n=this.props,o=n.children,i=n.className,c=n.width,u=n.height,l=n.style,s=n.compact,f=n.title,p=n.desc,h=tm(hs(n,ha),!1);if(s)return a().createElement(fK,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},a().createElement(tN,hu({},h,{width:c,height:u,title:f,desc:p}),this.renderClipPath(),tx(o,this.renderMap)));this.props.accessibilityLayer&&(h.tabIndex=null!=(t=this.props.tabIndex)?t:0,h.role=null!=(e=this.props.role)?e:"application",h.onKeyDown=function(t){r.accessibilityManager.keyboardEvent(t)},h.onFocus=function(){r.accessibilityManager.focus()});var d=this.parseEventsOfWrapper();return a().createElement(fK,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},a().createElement("div",hu({className:(0,b.A)("recharts-wrapper",i),style:hb({position:"relative",cursor:"default",width:c,height:u},l)},d,{ref:function(t){r.container=t}}),a().createElement(tN,hu({},h,{width:c,height:u,title:f,desc:p,style:hw}),this.renderClipPath(),tx(o,this.renderMap)),this.renderLegend(),this.renderTooltip()))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,hx(n.key),n)}}(n.prototype,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(i.Component);hg(y,"displayName",e),hg(y,"defaultProps",hb({layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},p)),hg(y,"getDerivedStateFromProps",function(t,e){var r=t.dataKey,n=t.data,o=t.children,i=t.width,a=t.height,c=t.layout,u=t.stackOffset,l=t.margin,s=e.dataStartIndex,f=e.dataEndIndex;if(void 0===e.updateId){var p=hN(t);return hb(hb(hb({},p),{},{updateId:0},d(hb(hb({props:t},p),{},{updateId:0}),e)),{},{prevDataKey:r,prevData:n,prevWidth:i,prevHeight:a,prevLayout:c,prevStackOffset:u,prevMargin:l,prevChildren:o})}if(r!==e.prevDataKey||n!==e.prevData||i!==e.prevWidth||a!==e.prevHeight||c!==e.prevLayout||u!==e.prevStackOffset||!K(l,e.prevMargin)){var h=hN(t),y={chartX:e.chartX,chartY:e.chartY,isTooltipActive:e.isTooltipActive},v=hb(hb({},hM(e,n,c)),{},{updateId:e.updateId+1}),m=hb(hb(hb({},h),y),v);return hb(hb(hb({},m),d(hb({props:t},m),e)),{},{prevDataKey:r,prevData:n,prevWidth:i,prevHeight:a,prevLayout:c,prevStackOffset:u,prevMargin:l,prevChildren:o})}if(!tb(o,e.prevChildren)){var b,g,x,O,w=th(o,lH),j=w&&null!=(b=null==(g=w.props)?void 0:g.startIndex)?b:s,S=w&&null!=(x=null==(O=w.props)?void 0:O.endIndex)?x:f,A=q()(n)||j!==s||S!==f?e.updateId+1:e.updateId;return hb(hb({updateId:A},d(hb(hb({props:t},e),{},{updateId:A,dataStartIndex:j,dataEndIndex:S}),e)),{},{prevChildren:o,dataStartIndex:j,dataEndIndex:S})}return null}),hg(y,"renderActiveDot",function(t,e,r){var n;return n=(0,i.isValidElement)(t)?(0,i.cloneElement)(t,e):H()(t)?t(e):a().createElement(rt,e),a().createElement(tR,{className:"recharts-active-dot",key:r},n)});var v=(0,i.forwardRef)(function(t,e){return a().createElement(y,hu({},t,{ref:e}))});return v.displayName=y.displayName,v},hL=["type","layout","connectNulls","ref"],hz=["key"];function hU(t){return(hU="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function hF(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function h$(){return(h$=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function hq(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function hW(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?hq(Object(r),!0).forEach(function(e){hZ(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):hq(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function hH(t){return function(t){if(Array.isArray(t))return hG(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return hG(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return hG(t,e)}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function hG(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function hV(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,hJ(n.key),n)}}function hX(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(hX=function(){return!!t})()}function hK(t){return(hK=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function hY(t,e){return(hY=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function hZ(t,e,r){return(e=hJ(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function hJ(t){var e=function(t,e){if("object"!=hU(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=hU(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==hU(e)?e:e+""}var hQ=function(t){var e,r;function n(){var t,e,r;if(!(this instanceof n))throw TypeError("Cannot call a class as a function");for(var o=arguments.length,i=Array(o),a=0;a<o;a++)i[a]=arguments[a];return e=n,r=[].concat(i),e=hK(e),hZ(t=function(t,e){if(e&&("object"===hU(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,hX()?Reflect.construct(e,r||[],hK(this).constructor):e.apply(this,r)),"state",{isAnimationFinished:!0,totalLength:0}),hZ(t,"generateSimpleStrokeDasharray",function(t,e){return"".concat(e,"px ").concat(t-e,"px")}),hZ(t,"getStrokeDasharray",function(e,r,o){var i=o.reduce(function(t,e){return t+e});if(!i)return t.generateSimpleStrokeDasharray(r,e);for(var a=Math.floor(e/i),c=e%i,u=r-e,l=[],s=0,f=0;s<o.length;f+=o[s],++s)if(f+o[s]>c){l=[].concat(hH(o.slice(0,s)),[c-f]);break}var p=l.length%2==0?[0,u]:[u];return[].concat(hH(n.repeat(o,a)),hH(l),p).map(function(t){return"".concat(t,"px")}).join(", ")}),hZ(t,"id",N("recharts-line-")),hZ(t,"pathRef",function(e){t.mainCurve=e}),hZ(t,"handleAnimationEnd",function(){t.setState({isAnimationFinished:!0}),t.props.onAnimationEnd&&t.props.onAnimationEnd()}),hZ(t,"handleAnimationStart",function(){t.setState({isAnimationFinished:!1}),t.props.onAnimationStart&&t.props.onAnimationStart()}),t}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return n.prototype=Object.create(t&&t.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),t&&hY(n,t),e=[{key:"componentDidMount",value:function(){if(this.props.isAnimationActive){var t=this.getTotalLength();this.setState({totalLength:t})}}},{key:"componentDidUpdate",value:function(){if(this.props.isAnimationActive){var t=this.getTotalLength();t!==this.state.totalLength&&this.setState({totalLength:t})}}},{key:"getTotalLength",value:function(){var t=this.mainCurve;try{return t&&t.getTotalLength&&t.getTotalLength()||0}catch(t){return 0}}},{key:"renderErrorBar",value:function(t,e){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var r=this.props,n=r.points,o=r.xAxis,i=r.yAxis,c=r.layout,u=tp(r.children,uG);if(!u)return null;var l=function(t,e){return{x:t.x,y:t.y,value:t.value,errorVal:u3(t.payload,e)}};return a().createElement(tR,{clipPath:t?"url(#clipPath-".concat(e,")"):null},u.map(function(t){return a().cloneElement(t,{key:"bar-".concat(t.props.dataKey),data:n,xAxis:o,yAxis:i,layout:c,dataPointFormatter:l})}))}},{key:"renderDots",value:function(t,e,r){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var o=this.props,i=o.dot,c=o.points,u=o.dataKey,l=tm(this.props,!1),s=tm(i,!0),f=c.map(function(t,e){var r=hW(hW(hW({key:"dot-".concat(e),r:3},l),s),{},{index:e,cx:t.x,cy:t.y,value:t.value,dataKey:u,payload:t.payload,points:c});return n.renderDotItem(i,r)}),p={clipPath:t?"url(#clipPath-".concat(e?"":"dots-").concat(r,")"):null};return a().createElement(tR,h$({className:"recharts-line-dots",key:"dots"},p),f)}},{key:"renderCurveStatically",value:function(t,e,r,n){var o=this.props,i=o.type,c=o.layout,u=o.connectNulls,l=hW(hW(hW({},tm((o.ref,hF(o,hL)),!0)),{},{fill:"none",className:"recharts-line-curve",clipPath:e?"url(#clipPath-".concat(r,")"):null,points:t},n),{},{type:i,layout:c,connectNulls:u});return a().createElement(p5,h$({},l,{pathRef:this.pathRef}))}},{key:"renderCurveWithAnimation",value:function(t,e){var r=this,n=this.props,o=n.points,i=n.strokeDasharray,c=n.isAnimationActive,u=n.animationBegin,l=n.animationDuration,s=n.animationEasing,f=n.animationId,p=n.animateNewValues,h=n.width,d=n.height,y=this.state,v=y.prevPoints,m=y.totalLength;return a().createElement(ns,{begin:u,duration:l,isActive:c,easing:s,from:{t:0},to:{t:1},key:"line-".concat(f),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(n){var a,c=n.t;if(v){var u=v.length/o.length,l=o.map(function(t,e){var r=Math.floor(e*u);if(v[r]){var n=v[r],o=L(n.x,t.x),i=L(n.y,t.y);return hW(hW({},t),{},{x:o(c),y:i(c)})}if(p){var a=L(2*h,t.x),l=L(d/2,t.y);return hW(hW({},t),{},{x:a(c),y:l(c)})}return hW(hW({},t),{},{x:t.x,y:t.y})});return r.renderCurveStatically(l,t,e)}var s=L(0,m)(c);if(i){var f="".concat(i).split(/[,\s]+/gim).map(function(t){return parseFloat(t)});a=r.getStrokeDasharray(s,m,f)}else a=r.generateSimpleStrokeDasharray(m,s);return r.renderCurveStatically(o,t,e,{strokeDasharray:a})})}},{key:"renderCurve",value:function(t,e){var r=this.props,n=r.points,o=r.isAnimationActive,i=this.state,a=i.prevPoints,c=i.totalLength;return o&&n&&n.length&&(!a&&c>0||!c0()(a,n))?this.renderCurveWithAnimation(t,e):this.renderCurveStatically(n,t,e)}},{key:"render",value:function(){var t,e=this.props,r=e.hide,n=e.dot,o=e.points,i=e.className,c=e.xAxis,u=e.yAxis,l=e.top,s=e.left,f=e.width,p=e.height,h=e.isAnimationActive,d=e.id;if(r||!o||!o.length)return null;var y=this.state.isAnimationFinished,v=1===o.length,m=(0,b.A)("recharts-line",i),g=c&&c.allowDataOverflow,x=u&&u.allowDataOverflow,O=g||x,w=q()(d)?this.id:d,j=null!=(t=tm(n,!1))?t:{r:3,strokeWidth:2},S=j.r,A=j.strokeWidth,P=(n&&"object"===ta(n)&&"clipDot"in n?n:{}).clipDot,k=void 0===P||P,E=2*(void 0===S?3:S)+(void 0===A?2:A);return a().createElement(tR,{className:m},g||x?a().createElement("defs",null,a().createElement("clipPath",{id:"clipPath-".concat(w)},a().createElement("rect",{x:g?s:s-f/2,y:x?l:l-p/2,width:g?f:2*f,height:x?p:2*p})),!k&&a().createElement("clipPath",{id:"clipPath-dots-".concat(w)},a().createElement("rect",{x:s-E/2,y:l-E/2,width:f+E,height:p+E}))):null,!v&&this.renderCurve(O,w),this.renderErrorBar(O,w),(v||n)&&this.renderDots(O,k,w),(!h||y)&&sS.renderCallByParent(this.props,o))}}],r=[{key:"getDerivedStateFromProps",value:function(t,e){return t.animationId!==e.prevAnimationId?{prevAnimationId:t.animationId,curPoints:t.points,prevPoints:e.curPoints}:t.points!==e.curPoints?{curPoints:t.points}:null}},{key:"repeat",value:function(t,e){for(var r=t.length%2!=0?[].concat(hH(t),[0]):t,n=[],o=0;o<e;++o)n=[].concat(hH(n),hH(r));return n}},{key:"renderDotItem",value:function(t,e){var r;if(a().isValidElement(t))r=a().cloneElement(t,e);else if(H()(t))r=t(e);else{var n=e.key,o=hF(e,hz),i=(0,b.A)("recharts-line-dot","boolean"!=typeof t?t.className:"");r=a().createElement(rt,h$({key:n},o,{className:i}))}return r}}],e&&hV(n.prototype,e),r&&hV(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(i.PureComponent);function h0(t,e,r){if(e<1)return[];if(1===e&&void 0===r)return t;for(var n=[],o=0;o<t.length;o+=e)if(void 0!==r&&!0!==r(t[o]))return;else n.push(t[o]);return n}function h1(t,e,r,n,o){if(t*e<t*n||t*e>t*o)return!1;var i=r();return t*(e-t*i/2-n)>=0&&t*(e+t*i/2-o)<=0}function h2(t){return(h2="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function h3(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function h5(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?h3(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=h2(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=h2(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==h2(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):h3(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function h4(t,e,r){var n,o,i,a,c,u=t.tick,l=t.ticks,s=t.viewBox,f=t.minTickGap,p=t.orientation,h=t.interval,d=t.tickFormatter,y=t.unit,v=t.angle;if(!l||!l.length||!u)return[];if(_(h)||t4.isSsr)return h0(l,("number"==typeof h&&_(h)?h:0)+1);var m=[],b="top"===p||"bottom"===p?"width":"height",g=y&&"width"===b?nB(y,{fontSize:e,letterSpacing:r}):{width:0,height:0},x=function(t,n){var o,i=H()(d)?d(t.value,n):t.value;return"width"===b?(o=nB(i,{fontSize:e,letterSpacing:r}),fS({width:o.width+g.width,height:o.height+g.height},v)):nB(i,{fontSize:e,letterSpacing:r})[b]},O=l.length>=2?M(l[1].coordinate-l[0].coordinate):1,w=(n="width"===b,o=s.x,i=s.y,a=s.width,c=s.height,1===O?{start:n?o:i,end:n?o+a:i+c}:{start:n?o+a:i+c,end:n?o:i});return"equidistantPreserveStart"===h?function(t,e,r,n,o){for(var i,a=(n||[]).slice(),c=e.start,u=e.end,l=0,s=1,f=c;s<=a.length;)if(i=function(){var e,i=null==n?void 0:n[l];if(void 0===i)return{v:h0(n,s)};var a=l,p=function(){return void 0===e&&(e=r(i,a)),e},h=i.coordinate,d=0===l||h1(t,h,p,f,u);d||(l=0,f=c,s+=1),d&&(f=h+t*(p()/2+o),l+=s)}())return i.v;return[]}(O,w,x,l,f):("preserveStart"===h||"preserveStartEnd"===h?function(t,e,r,n,o,i){var a=(n||[]).slice(),c=a.length,u=e.start,l=e.end;if(i){var s=n[c-1],f=r(s,c-1),p=t*(s.coordinate+t*f/2-l);a[c-1]=s=h5(h5({},s),{},{tickCoord:p>0?s.coordinate-p*t:s.coordinate}),h1(t,s.tickCoord,function(){return f},u,l)&&(l=s.tickCoord-t*(f/2+o),a[c-1]=h5(h5({},s),{},{isShow:!0}))}for(var h=i?c-1:c,d=function(e){var n,i=a[e],c=function(){return void 0===n&&(n=r(i,e)),n};if(0===e){var s=t*(i.coordinate-t*c()/2-u);a[e]=i=h5(h5({},i),{},{tickCoord:s<0?i.coordinate-s*t:i.coordinate})}else a[e]=i=h5(h5({},i),{},{tickCoord:i.coordinate});h1(t,i.tickCoord,c,u,l)&&(u=i.tickCoord+t*(c()/2+o),a[e]=h5(h5({},i),{},{isShow:!0}))},y=0;y<h;y++)d(y);return a}(O,w,x,l,f,"preserveStartEnd"===h):function(t,e,r,n,o){for(var i=(n||[]).slice(),a=i.length,c=e.start,u=e.end,l=function(e){var n,l=i[e],s=function(){return void 0===n&&(n=r(l,e)),n};if(e===a-1){var f=t*(l.coordinate+t*s()/2-u);i[e]=l=h5(h5({},l),{},{tickCoord:f>0?l.coordinate-f*t:l.coordinate})}else i[e]=l=h5(h5({},l),{},{tickCoord:l.coordinate});h1(t,l.tickCoord,s,c,u)&&(u=l.tickCoord-t*(s()/2+o),i[e]=h5(h5({},l),{},{isShow:!0}))},s=a-1;s>=0;s--)l(s);return i}(O,w,x,l,f)).filter(function(t){return t.isShow})}hZ(hQ,"displayName","Line"),hZ(hQ,"defaultProps",{xAxisId:0,yAxisId:0,connectNulls:!1,activeDot:!0,dot:!0,legendType:"line",stroke:"#3182bd",strokeWidth:1,fill:"#fff",points:[],isAnimationActive:!t4.isSsr,animateNewValues:!0,animationBegin:0,animationDuration:1500,animationEasing:"ease",hide:!1,label:!1}),hZ(hQ,"getComposedData",function(t){var e=t.props,r=t.xAxis,n=t.yAxis,o=t.xAxisTicks,i=t.yAxisTicks,a=t.dataKey,c=t.bandSize,u=t.displayedData,l=t.offset,s=e.layout;return hW({points:u.map(function(t,e){var u=u3(t,a);return"horizontal"===s?{x:lv({axis:r,ticks:o,bandSize:c,entry:t,index:e}),y:q()(u)?null:n.scale(u),value:u,payload:t}:{x:q()(u)?null:r.scale(u),y:lv({axis:n,ticks:i,bandSize:c,entry:t,index:e}),value:u,payload:t}}),layout:s},l)});var h6=["viewBox"],h8=["viewBox"],h7=["ticks"];function h9(t){return(h9="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function dt(){return(dt=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function de(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function dr(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?de(Object(r),!0).forEach(function(e){dl(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):de(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function dn(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function di(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,ds(n.key),n)}}function da(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(da=function(){return!!t})()}function dc(t){return(dc=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function du(t,e){return(du=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function dl(t,e,r){return(e=ds(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function ds(t){var e=function(t,e){if("object"!=h9(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=h9(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==h9(e)?e:e+""}var df=function(t){var e,r;function n(t){var e,r,o;if(!(this instanceof n))throw TypeError("Cannot call a class as a function");return r=n,o=[t],r=dc(r),(e=function(t,e){if(e&&("object"===h9(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,da()?Reflect.construct(r,o||[],dc(this).constructor):r.apply(this,o))).state={fontSize:"",letterSpacing:""},e}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return n.prototype=Object.create(t&&t.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),t&&du(n,t),e=[{key:"shouldComponentUpdate",value:function(t,e){var r=t.viewBox,n=dn(t,h6),o=this.props,i=o.viewBox,a=dn(o,h8);return!K(r,i)||!K(n,a)||!K(e,this.state)}},{key:"componentDidMount",value:function(){var t=this.layerReference;if(t){var e=t.getElementsByClassName("recharts-cartesian-axis-tick-value")[0];e&&this.setState({fontSize:window.getComputedStyle(e).fontSize,letterSpacing:window.getComputedStyle(e).letterSpacing})}}},{key:"getTickLineCoord",value:function(t){var e,r,n,o,i,a,c=this.props,u=c.x,l=c.y,s=c.width,f=c.height,p=c.orientation,h=c.tickSize,d=c.mirror,y=c.tickMargin,v=d?-1:1,m=t.tickSize||h,b=_(t.tickCoord)?t.tickCoord:t.coordinate;switch(p){case"top":e=r=t.coordinate,a=(n=(o=l+!d*f)-v*m)-v*y,i=b;break;case"left":n=o=t.coordinate,i=(e=(r=u+!d*s)-v*m)-v*y,a=b;break;case"right":n=o=t.coordinate,i=(e=(r=u+d*s)+v*m)+v*y,a=b;break;default:e=r=t.coordinate,a=(n=(o=l+d*f)+v*m)+v*y,i=b}return{line:{x1:e,y1:n,x2:r,y2:o},tick:{x:i,y:a}}}},{key:"getTickTextAnchor",value:function(){var t,e=this.props,r=e.orientation,n=e.mirror;switch(r){case"left":t=n?"start":"end";break;case"right":t=n?"end":"start";break;default:t="middle"}return t}},{key:"getTickVerticalAnchor",value:function(){var t=this.props,e=t.orientation,r=t.mirror,n="end";switch(e){case"left":case"right":n="middle";break;case"top":n=r?"start":"end";break;default:n=r?"end":"start"}return n}},{key:"renderAxisLine",value:function(){var t=this.props,e=t.x,r=t.y,n=t.width,o=t.height,i=t.orientation,c=t.mirror,u=t.axisLine,l=dr(dr(dr({},tm(this.props,!1)),tm(u,!1)),{},{fill:"none"});if("top"===i||"bottom"===i){var s=+("top"===i&&!c||"bottom"===i&&c);l=dr(dr({},l),{},{x1:e,y1:r+s*o,x2:e+n,y2:r+s*o})}else{var f=+("left"===i&&!c||"right"===i&&c);l=dr(dr({},l),{},{x1:e+f*n,y1:r,x2:e+f*n,y2:r+o})}return a().createElement("line",dt({},l,{className:(0,b.A)("recharts-cartesian-axis-line",P()(u,"className"))}))}},{key:"renderTicks",value:function(t,e,r){var o=this,i=this.props,c=i.tickLine,u=i.stroke,l=i.tick,s=i.tickFormatter,f=i.unit,p=h4(dr(dr({},this.props),{},{ticks:t}),e,r),h=this.getTickTextAnchor(),d=this.getTickVerticalAnchor(),y=tm(this.props,!1),v=tm(l,!1),m=dr(dr({},y),{},{fill:"none"},tm(c,!1)),g=p.map(function(t,e){var r=o.getTickLineCoord(t),i=r.line,g=r.tick,x=dr(dr(dr(dr({textAnchor:h,verticalAnchor:d},y),{},{stroke:"none",fill:u},v),g),{},{index:e,payload:t,visibleTicksCount:p.length,tickFormatter:s});return a().createElement(tR,dt({className:"recharts-cartesian-axis-tick",key:"tick-".concat(t.value,"-").concat(t.coordinate,"-").concat(t.tickCoord)},tr(o.props,t,e)),c&&a().createElement("line",dt({},m,i,{className:(0,b.A)("recharts-cartesian-axis-tick-line",P()(c,"className"))})),l&&n.renderTickItem(l,x,"".concat(H()(s)?s(t.value,e):t.value).concat(f||"")))});return a().createElement("g",{className:"recharts-cartesian-axis-ticks"},g)}},{key:"render",value:function(){var t=this,e=this.props,r=e.axisLine,n=e.width,o=e.height,i=e.ticksGenerator,c=e.className;if(e.hide)return null;var u=this.props,l=u.ticks,s=dn(u,h7),f=l;return(H()(i)&&(f=i(l&&l.length>0?this.props:s)),n<=0||o<=0||!f||!f.length)?null:a().createElement(tR,{className:(0,b.A)("recharts-cartesian-axis",c),ref:function(e){t.layerReference=e}},r&&this.renderAxisLine(),this.renderTicks(f,this.state.fontSize,this.state.letterSpacing),si.renderCallByParent(this.props))}}],r=[{key:"renderTickItem",value:function(t,e,r){var n;return a().isValidElement(t)?a().cloneElement(t,e):H()(t)?t(e):a().createElement(n9,dt({},e,{className:"recharts-cartesian-axis-tick-value"}),r)}}],e&&di(n.prototype,e),r&&di(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(i.Component);function dp(t){return(dp="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}dl(df,"displayName","CartesianAxis"),dl(df,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"});function dh(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(dh=function(){return!!t})()}function dd(t){return(dd=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function dy(t,e){return(dy=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function dv(t,e,r){return(e=dm(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function dm(t){var e=function(t,e){if("object"!=dp(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=dp(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==dp(e)?e:e+""}function db(){return(db=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function dg(t){var e=t.xAxisId,r=fQ(),n=f0(),o=fY(e);return null==o?null:a().createElement(df,db({},o,{className:(0,b.A)("recharts-".concat(o.axisType," ").concat(o.axisType),o.className),viewBox:{x:0,y:0,width:r,height:n},ticksGenerator:function(t){return li(t,!0)}}))}var dx=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=dd(t),function(t,e){if(e&&("object"===dp(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,dh()?Reflect.construct(t,e||[],dd(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&dy(r,t),e=[{key:"render",value:function(){return a().createElement(dg,this.props)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,dm(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(a().Component);function dO(t){return(dO="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}dv(dx,"displayName","XAxis"),dv(dx,"defaultProps",{allowDecimals:!0,hide:!1,orientation:"bottom",width:0,height:30,mirror:!1,xAxisId:0,tickCount:5,type:"category",padding:{left:0,right:0},allowDataOverflow:!1,scale:"auto",reversed:!1,allowDuplicatedCategory:!0});function dw(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(dw=function(){return!!t})()}function dj(t){return(dj=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function dS(t,e){return(dS=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function dA(t,e,r){return(e=dP(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function dP(t){var e=function(t,e){if("object"!=dO(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=dO(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==dO(e)?e:e+""}function dk(){return(dk=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var dE=function(t){var e=t.yAxisId,r=fQ(),n=f0(),o=fJ(e);return null==o?null:a().createElement(df,dk({},o,{className:(0,b.A)("recharts-".concat(o.axisType," ").concat(o.axisType),o.className),viewBox:{x:0,y:0,width:r,height:n},ticksGenerator:function(t){return li(t,!0)}}))},dM=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=dj(t),function(t,e){if(e&&("object"===dO(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,dw()?Reflect.construct(t,e||[],dj(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&dS(r,t),e=[{key:"render",value:function(){return a().createElement(dE,this.props)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,dP(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(a().Component);dA(dM,"displayName","YAxis"),dA(dM,"defaultProps",{allowDuplicatedCategory:!0,allowDecimals:!0,hide:!1,orientation:"left",width:60,height:0,mirror:!1,yAxisId:0,tickCount:5,type:"number",padding:{top:0,bottom:0},allowDataOverflow:!1,scale:"auto",reversed:!1});var dT=hR({chartName:"LineChart",GraphicalChild:hQ,axisComponents:[{axisType:"xAxis",AxisComp:dx},{axisType:"yAxis",AxisComp:dM}],formatAxisMap:fx}),d_=["x1","y1","x2","y2","key"],dC=["offset"];function dD(t){return(dD="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function dN(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function dI(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?dN(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=dD(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=dD(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==dD(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):dN(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function dB(){return(dB=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function dR(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}var dL=function(t){var e=t.fill;if(!e||"none"===e)return null;var r=t.fillOpacity,n=t.x,o=t.y,i=t.width,c=t.height,u=t.ry;return a().createElement("rect",{x:n,y:o,ry:u,width:i,height:c,stroke:"none",fill:e,fillOpacity:r,className:"recharts-cartesian-grid-bg"})};function dz(t,e){var r;if(a().isValidElement(t))r=a().cloneElement(t,e);else if(H()(t))r=t(e);else{var n=e.x1,o=e.y1,i=e.x2,c=e.y2,u=e.key,l=tm(dR(e,d_),!1),s=(l.offset,dR(l,dC));r=a().createElement("line",dB({},s,{x1:n,y1:o,x2:i,y2:c,fill:"none",key:u}))}return r}function dU(t){var e=t.x,r=t.width,n=t.horizontal,o=void 0===n||n,i=t.horizontalPoints;if(!o||!i||!i.length)return null;var c=i.map(function(n,i){return dz(o,dI(dI({},t),{},{x1:e,y1:n,x2:e+r,y2:n,key:"line-".concat(i),index:i}))});return a().createElement("g",{className:"recharts-cartesian-grid-horizontal"},c)}function dF(t){var e=t.y,r=t.height,n=t.vertical,o=void 0===n||n,i=t.verticalPoints;if(!o||!i||!i.length)return null;var c=i.map(function(n,i){return dz(o,dI(dI({},t),{},{x1:n,y1:e,x2:n,y2:e+r,key:"line-".concat(i),index:i}))});return a().createElement("g",{className:"recharts-cartesian-grid-vertical"},c)}function d$(t){var e=t.horizontalFill,r=t.fillOpacity,n=t.x,o=t.y,i=t.width,c=t.height,u=t.horizontalPoints,l=t.horizontal;if(!(void 0===l||l)||!e||!e.length)return null;var s=u.map(function(t){return Math.round(t+o-o)}).sort(function(t,e){return t-e});o!==s[0]&&s.unshift(0);var f=s.map(function(t,u){var l=s[u+1]?s[u+1]-t:o+c-t;if(l<=0)return null;var f=u%e.length;return a().createElement("rect",{key:"react-".concat(u),y:t,x:n,height:l,width:i,stroke:"none",fill:e[f],fillOpacity:r,className:"recharts-cartesian-grid-bg"})});return a().createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},f)}function dq(t){var e=t.vertical,r=t.verticalFill,n=t.fillOpacity,o=t.x,i=t.y,c=t.width,u=t.height,l=t.verticalPoints;if(!(void 0===e||e)||!r||!r.length)return null;var s=l.map(function(t){return Math.round(t+o-o)}).sort(function(t,e){return t-e});o!==s[0]&&s.unshift(0);var f=s.map(function(t,e){var l=s[e+1]?s[e+1]-t:o+c-t;if(l<=0)return null;var f=e%r.length;return a().createElement("rect",{key:"react-".concat(e),x:t,y:i,width:l,height:u,stroke:"none",fill:r[f],fillOpacity:n,className:"recharts-cartesian-grid-bg"})});return a().createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},f)}var dW=function(t,e){var r=t.xAxis,n=t.width,o=t.height,i=t.offset;return lo(h4(dI(dI(dI({},df.defaultProps),r),{},{ticks:li(r,!0),viewBox:{x:0,y:0,width:n,height:o}})),i.left,i.left+i.width,e)},dH=function(t,e){var r=t.yAxis,n=t.width,o=t.height,i=t.offset;return lo(h4(dI(dI(dI({},df.defaultProps),r),{},{ticks:li(r,!0),viewBox:{x:0,y:0,width:n,height:o}})),i.top,i.top+i.height,e)},dG={horizontal:!0,vertical:!0,stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[]};function dV(t){var e,r,n,o,c,u,l=fQ(),s=f0(),f=(0,i.useContext)(fH),p=dI(dI({},t),{},{stroke:null!=(e=t.stroke)?e:dG.stroke,fill:null!=(r=t.fill)?r:dG.fill,horizontal:null!=(n=t.horizontal)?n:dG.horizontal,horizontalFill:null!=(o=t.horizontalFill)?o:dG.horizontalFill,vertical:null!=(c=t.vertical)?c:dG.vertical,verticalFill:null!=(u=t.verticalFill)?u:dG.verticalFill,x:_(t.x)?t.x:f.left,y:_(t.y)?t.y:f.top,width:_(t.width)?t.width:f.width,height:_(t.height)?t.height:f.height}),h=p.x,d=p.y,y=p.width,v=p.height,m=p.syncWithTicks,b=p.horizontalValues,g=p.verticalValues,x=B((0,i.useContext)(f$)),O=fZ();if(!_(y)||y<=0||!_(v)||v<=0||!_(h)||h!==+h||!_(d)||d!==+d)return null;var w=p.verticalCoordinatesGenerator||dW,j=p.horizontalCoordinatesGenerator||dH,S=p.horizontalPoints,A=p.verticalPoints;if((!S||!S.length)&&H()(j)){var P=b&&b.length,k=j({yAxis:O?dI(dI({},O),{},{ticks:P?b:O.ticks}):void 0,width:l,height:s,offset:f},!!P||m);F(Array.isArray(k),"horizontalCoordinatesGenerator should return Array but instead it returned [".concat(dD(k),"]")),Array.isArray(k)&&(S=k)}if((!A||!A.length)&&H()(w)){var E=g&&g.length,M=w({xAxis:x?dI(dI({},x),{},{ticks:E?g:x.ticks}):void 0,width:l,height:s,offset:f},!!E||m);F(Array.isArray(M),"verticalCoordinatesGenerator should return Array but instead it returned [".concat(dD(M),"]")),Array.isArray(M)&&(A=M)}return a().createElement("g",{className:"recharts-cartesian-grid"},a().createElement(dL,{fill:p.fill,fillOpacity:p.fillOpacity,x:p.x,y:p.y,width:p.width,height:p.height,ry:p.ry}),a().createElement(dU,dB({},p,{offset:f,horizontalPoints:S,xAxis:x,yAxis:O})),a().createElement(dF,dB({},p,{offset:f,verticalPoints:A,xAxis:x,yAxis:O})),a().createElement(d$,dB({},p,{horizontalPoints:S})),a().createElement(dq,dB({},p,{verticalPoints:A})))}dV.displayName="CartesianGrid";var dX=hR({chartName:"BarChart",GraphicalChild:fh,defaultTooltipEventType:"axis",validateTooltipEventTypes:["axis","item"],axisComponents:[{axisType:"xAxis",AxisComp:dx},{axisType:"yAxis",AxisComp:dM}],formatAxisMap:fx}),dK=["points","className","baseLinePoints","connectNulls"];function dY(){return(dY=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function dZ(t){return function(t){if(Array.isArray(t))return dJ(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return dJ(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return dJ(t,e)}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function dJ(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var dQ=function(t){return t&&t.x===+t.x&&t.y===+t.y},d0=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],e=[[]];return t.forEach(function(t){dQ(t)?e[e.length-1].push(t):e[e.length-1].length>0&&e.push([])}),dQ(t[0])&&e[e.length-1].push(t[0]),e[e.length-1].length<=0&&(e=e.slice(0,-1)),e},d1=function(t,e){var r=d0(t);e&&(r=[r.reduce(function(t,e){return[].concat(dZ(t),dZ(e))},[])]);var n=r.map(function(t){return t.reduce(function(t,e,r){return"".concat(t).concat(0===r?"M":"L").concat(e.x,",").concat(e.y)},"")}).join("");return 1===r.length?"".concat(n,"Z"):n},d2=function(t,e,r){var n=d1(t,r);return"".concat("Z"===n.slice(-1)?n.slice(0,-1):n,"L").concat(d1(e.reverse(),r).slice(1))},d3=function(t){var e=t.points,r=t.className,n=t.baseLinePoints,o=t.connectNulls,i=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,dK);if(!e||!e.length)return null;var c=(0,b.A)("recharts-polygon",r);if(n&&n.length){var u=i.stroke&&"none"!==i.stroke,l=d2(e,n,o);return a().createElement("g",{className:c},a().createElement("path",dY({},tm(i,!0),{fill:"Z"===l.slice(-1)?i.fill:"none",stroke:"none",d:l})),u?a().createElement("path",dY({},tm(i,!0),{fill:"none",d:d1(e,o)})):null,u?a().createElement("path",dY({},tm(i,!0),{fill:"none",d:d1(n,o)})):null)}var s=d1(e,o);return a().createElement("path",dY({},tm(i,!0),{fill:"Z"===s.slice(-1)?i.fill:"none",className:c,d:s}))};function d5(t){return(d5="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function d4(){return(d4=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function d6(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function d8(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?d6(Object(r),!0).forEach(function(e){yr(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):d6(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function d7(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,yn(n.key),n)}}function d9(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(d9=function(){return!!t})()}function yt(t){return(yt=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function ye(t,e){return(ye=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function yr(t,e,r){return(e=yn(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function yn(t){var e=function(t,e){if("object"!=d5(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=d5(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==d5(e)?e:e+""}var yo=Math.PI/180,yi=function(t){var e,r;function n(){var t,e;if(!(this instanceof n))throw TypeError("Cannot call a class as a function");return t=n,e=arguments,t=yt(t),function(t,e){if(e&&("object"===d5(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,d9()?Reflect.construct(t,e||[],yt(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return n.prototype=Object.create(t&&t.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),t&&ye(n,t),e=[{key:"getTickLineCoord",value:function(t){var e=this.props,r=e.cx,n=e.cy,o=e.radius,i=e.orientation,a=e.tickSize,c=lJ(r,n,o,t.coordinate),u=lJ(r,n,o+("inner"===i?-1:1)*(a||8),t.coordinate);return{x1:c.x,y1:c.y,x2:u.x,y2:u.y}}},{key:"getTickTextAnchor",value:function(t){var e=this.props.orientation,r=Math.cos(-t.coordinate*yo);return r>1e-5?"outer"===e?"start":"end":r<-1e-5?"outer"===e?"end":"start":"middle"}},{key:"renderAxisLine",value:function(){var t=this.props,e=t.cx,r=t.cy,n=t.radius,o=t.axisLine,i=t.axisLineType,c=d8(d8({},tm(this.props,!1)),{},{fill:"none"},tm(o,!1));if("circle"===i)return a().createElement(rt,d4({className:"recharts-polar-angle-axis-line"},c,{cx:e,cy:r,r:n}));var u=this.props.ticks.map(function(t){return lJ(e,r,n,t.coordinate)});return a().createElement(d3,d4({className:"recharts-polar-angle-axis-line"},c,{points:u}))}},{key:"renderTicks",value:function(){var t=this,e=this.props,r=e.ticks,o=e.tick,i=e.tickLine,c=e.tickFormatter,u=e.stroke,l=tm(this.props,!1),s=tm(o,!1),f=d8(d8({},l),{},{fill:"none"},tm(i,!1)),p=r.map(function(e,r){var p=t.getTickLineCoord(e),h=d8(d8(d8({textAnchor:t.getTickTextAnchor(e)},l),{},{stroke:"none",fill:u},s),{},{index:r,payload:e,x:p.x2,y:p.y2});return a().createElement(tR,d4({className:(0,b.A)("recharts-polar-angle-axis-tick",l5(o)),key:"tick-".concat(e.coordinate)},tr(t.props,e,r)),i&&a().createElement("line",d4({className:"recharts-polar-angle-axis-tick-line"},f,p)),o&&n.renderTickItem(o,h,c?c(e.value,r):e.value))});return a().createElement(tR,{className:"recharts-polar-angle-axis-ticks"},p)}},{key:"render",value:function(){var t=this.props,e=t.ticks,r=t.radius,n=t.axisLine;return!(r<=0)&&e&&e.length?a().createElement(tR,{className:(0,b.A)("recharts-polar-angle-axis",this.props.className)},n&&this.renderAxisLine(),this.renderTicks()):null}}],r=[{key:"renderTickItem",value:function(t,e,r){var n;return a().isValidElement(t)?a().cloneElement(t,e):H()(t)?t(e):a().createElement(n9,d4({},e,{className:"recharts-polar-angle-axis-tick-value"}),r)}}],e&&d7(n.prototype,e),r&&d7(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(i.PureComponent);yr(yi,"displayName","PolarAngleAxis"),yr(yi,"axisType","angleAxis"),yr(yi,"defaultProps",{type:"category",angleAxisId:0,scale:"auto",cx:0,cy:0,orientation:"outer",axisLine:!0,tickLine:!0,tickSize:8,tick:!0,hide:!1,allowDuplicatedCategory:!0});var ya=r(57088),yc=r.n(ya),yu=r(10034),yl=r.n(yu),ys=["cx","cy","angle","ticks","axisLine"],yf=["ticks","tick","angle","tickFormatter","stroke"];function yp(t){return(yp="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function yh(){return(yh=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function yd(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function yy(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?yd(Object(r),!0).forEach(function(e){yO(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):yd(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function yv(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function ym(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,yw(n.key),n)}}function yb(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(yb=function(){return!!t})()}function yg(t){return(yg=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function yx(t,e){return(yx=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function yO(t,e,r){return(e=yw(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function yw(t){var e=function(t,e){if("object"!=yp(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=yp(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==yp(e)?e:e+""}var yj=function(t){var e,r;function n(){var t,e;if(!(this instanceof n))throw TypeError("Cannot call a class as a function");return t=n,e=arguments,t=yg(t),function(t,e){if(e&&("object"===yp(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,yb()?Reflect.construct(t,e||[],yg(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return n.prototype=Object.create(t&&t.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),t&&yx(n,t),e=[{key:"getTickValueCoord",value:function(t){var e=t.coordinate,r=this.props,n=r.angle;return lJ(r.cx,r.cy,e,n)}},{key:"getTickTextAnchor",value:function(){var t;switch(this.props.orientation){case"left":t="end";break;case"right":t="start";break;default:t="middle"}return t}},{key:"getViewBox",value:function(){var t=this.props,e=t.cx,r=t.cy,n=t.angle,o=t.ticks,i=yc()(o,function(t){return t.coordinate||0});return{cx:e,cy:r,startAngle:n,endAngle:n,innerRadius:yl()(o,function(t){return t.coordinate||0}).coordinate||0,outerRadius:i.coordinate||0}}},{key:"renderAxisLine",value:function(){var t=this.props,e=t.cx,r=t.cy,n=t.angle,o=t.ticks,i=t.axisLine,c=yv(t,ys),u=o.reduce(function(t,e){return[Math.min(t[0],e.coordinate),Math.max(t[1],e.coordinate)]},[1/0,-1/0]),l=lJ(e,r,u[0],n),s=lJ(e,r,u[1],n),f=yy(yy(yy({},tm(c,!1)),{},{fill:"none"},tm(i,!1)),{},{x1:l.x,y1:l.y,x2:s.x,y2:s.y});return a().createElement("line",yh({className:"recharts-polar-radius-axis-line"},f))}},{key:"renderTicks",value:function(){var t=this,e=this.props,r=e.ticks,o=e.tick,i=e.angle,c=e.tickFormatter,u=e.stroke,l=yv(e,yf),s=this.getTickTextAnchor(),f=tm(l,!1),p=tm(o,!1),h=r.map(function(e,r){var l=t.getTickValueCoord(e),h=yy(yy(yy(yy({textAnchor:s,transform:"rotate(".concat(90-i,", ").concat(l.x,", ").concat(l.y,")")},f),{},{stroke:"none",fill:u},p),{},{index:r},l),{},{payload:e});return a().createElement(tR,yh({className:(0,b.A)("recharts-polar-radius-axis-tick",l5(o)),key:"tick-".concat(e.coordinate)},tr(t.props,e,r)),n.renderTickItem(o,h,c?c(e.value,r):e.value))});return a().createElement(tR,{className:"recharts-polar-radius-axis-ticks"},h)}},{key:"render",value:function(){var t=this.props,e=t.ticks,r=t.axisLine,n=t.tick;return e&&e.length?a().createElement(tR,{className:(0,b.A)("recharts-polar-radius-axis",this.props.className)},r&&this.renderAxisLine(),n&&this.renderTicks(),si.renderCallByParent(this.props,this.getViewBox())):null}}],r=[{key:"renderTickItem",value:function(t,e,r){var n;return a().isValidElement(t)?a().cloneElement(t,e):H()(t)?t(e):a().createElement(n9,yh({},e,{className:"recharts-polar-radius-axis-tick-value"}),r)}}],e&&ym(n.prototype,e),r&&ym(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(i.PureComponent);function yS(t){return(yS="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function yA(){return(yA=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function yP(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function yk(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?yP(Object(r),!0).forEach(function(e){yC(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):yP(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function yE(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,yD(n.key),n)}}function yM(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(yM=function(){return!!t})()}function yT(t){return(yT=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function y_(t,e){return(y_=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function yC(t,e,r){return(e=yD(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function yD(t){var e=function(t,e){if("object"!=yS(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=yS(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==yS(e)?e:e+""}yO(yj,"displayName","PolarRadiusAxis"),yO(yj,"axisType","radiusAxis"),yO(yj,"defaultProps",{type:"number",radiusAxisId:0,cx:0,cy:0,angle:0,orientation:"right",stroke:"#ccc",axisLine:!0,tick:!0,tickCount:5,allowDataOverflow:!1,scale:"auto",allowDuplicatedCategory:!0});var yN=function(t){var e,r;function n(t){var e,r,o;if(!(this instanceof n))throw TypeError("Cannot call a class as a function");return r=n,o=[t],r=yT(r),yC(e=function(t,e){if(e&&("object"===yS(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,yM()?Reflect.construct(r,o||[],yT(this).constructor):r.apply(this,o)),"pieRef",null),yC(e,"sectorRefs",[]),yC(e,"id",N("recharts-pie-")),yC(e,"handleAnimationEnd",function(){var t=e.props.onAnimationEnd;e.setState({isAnimationFinished:!0}),H()(t)&&t()}),yC(e,"handleAnimationStart",function(){var t=e.props.onAnimationStart;e.setState({isAnimationFinished:!1}),H()(t)&&t()}),e.state={isAnimationFinished:!t.isAnimationActive,prevIsAnimationActive:t.isAnimationActive,prevAnimationId:t.animationId,sectorToFocus:0},e}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return n.prototype=Object.create(t&&t.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),t&&y_(n,t),e=[{key:"isActiveIndex",value:function(t){var e=this.props.activeIndex;return Array.isArray(e)?-1!==e.indexOf(t):t===e}},{key:"hasActiveIndex",value:function(){var t=this.props.activeIndex;return Array.isArray(t)?0!==t.length:t||0===t}},{key:"renderLabels",value:function(t){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var e=this.props,r=e.label,o=e.labelLine,i=e.dataKey,c=e.valueKey,u=tm(this.props,!1),l=tm(r,!1),s=tm(o,!1),f=r&&r.offsetRadius||20,p=t.map(function(t,e){var p=(t.startAngle+t.endAngle)/2,h=lJ(t.cx,t.cy,t.outerRadius+f,p),d=yk(yk(yk(yk({},u),t),{},{stroke:"none"},l),{},{index:e,textAnchor:n.getTextAnchor(h.x,t.cx)},h),y=yk(yk(yk(yk({},u),t),{},{fill:"none",stroke:t.fill},s),{},{index:e,points:[lJ(t.cx,t.cy,t.outerRadius,p),h]}),v=i;return q()(i)&&q()(c)?v="value":q()(i)&&(v=c),a().createElement(tR,{key:"label-".concat(t.startAngle,"-").concat(t.endAngle,"-").concat(t.midAngle,"-").concat(e)},o&&n.renderLabelLineItem(o,y,"line"),n.renderLabelItem(r,d,u3(t,v)))});return a().createElement(tR,{className:"recharts-pie-labels"},p)}},{key:"renderSectorsStatically",value:function(t){var e=this,r=this.props,n=r.activeShape,o=r.blendStroke,i=r.inactiveShape;return t.map(function(r,c){if((null==r?void 0:r.startAngle)===0&&(null==r?void 0:r.endAngle)===0&&1!==t.length)return null;var u=e.isActiveIndex(c),l=i&&e.hasActiveIndex()?i:null,s=yk(yk({},r),{},{stroke:o?r.fill:r.stroke,tabIndex:-1});return a().createElement(tR,yA({ref:function(t){t&&!e.sectorRefs.includes(t)&&e.sectorRefs.push(t)},tabIndex:-1,className:"recharts-pie-sector"},tr(e.props,r,c),{key:"sector-".concat(null==r?void 0:r.startAngle,"-").concat(null==r?void 0:r.endAngle,"-").concat(r.midAngle,"-").concat(c)}),a().createElement(sZ,yA({option:u?n:l,isActive:u,shapeType:"sector"},s)))})}},{key:"renderSectorsWithAnimation",value:function(){var t=this,e=this.props,r=e.sectors,n=e.isAnimationActive,o=e.animationBegin,i=e.animationDuration,c=e.animationEasing,u=e.animationId,l=this.state,s=l.prevSectors,f=l.prevIsAnimationActive;return a().createElement(ns,{begin:o,duration:i,isActive:n,easing:c,from:{t:0},to:{t:1},key:"pie-".concat(u,"-").concat(f),onAnimationStart:this.handleAnimationStart,onAnimationEnd:this.handleAnimationEnd},function(e){var n=e.t,o=[],i=(r&&r[0]).startAngle;return r.forEach(function(t,e){var r=s&&s[e],a=e>0?P()(t,"paddingAngle",0):0;if(r){var c=L(r.endAngle-r.startAngle,t.endAngle-t.startAngle),u=yk(yk({},t),{},{startAngle:i+a,endAngle:i+c(n)+a});o.push(u),i=u.endAngle}else{var l=L(0,t.endAngle-t.startAngle)(n),f=yk(yk({},t),{},{startAngle:i+a,endAngle:i+l+a});o.push(f),i=f.endAngle}}),a().createElement(tR,null,t.renderSectorsStatically(o))})}},{key:"attachKeyboardHandlers",value:function(t){var e=this;t.onkeydown=function(t){if(!t.altKey)switch(t.key){case"ArrowLeft":var r=++e.state.sectorToFocus%e.sectorRefs.length;e.sectorRefs[r].focus(),e.setState({sectorToFocus:r});break;case"ArrowRight":var n=--e.state.sectorToFocus<0?e.sectorRefs.length-1:e.state.sectorToFocus%e.sectorRefs.length;e.sectorRefs[n].focus(),e.setState({sectorToFocus:n});break;case"Escape":e.sectorRefs[e.state.sectorToFocus].blur(),e.setState({sectorToFocus:0})}}}},{key:"renderSectors",value:function(){var t=this.props,e=t.sectors,r=t.isAnimationActive,n=this.state.prevSectors;return r&&e&&e.length&&(!n||!c0()(n,e))?this.renderSectorsWithAnimation():this.renderSectorsStatically(e)}},{key:"componentDidMount",value:function(){this.pieRef&&this.attachKeyboardHandlers(this.pieRef)}},{key:"render",value:function(){var t=this,e=this.props,r=e.hide,n=e.sectors,o=e.className,i=e.label,c=e.cx,u=e.cy,l=e.innerRadius,s=e.outerRadius,f=e.isAnimationActive,p=this.state.isAnimationFinished;if(r||!n||!n.length||!_(c)||!_(u)||!_(l)||!_(s))return null;var h=(0,b.A)("recharts-pie",o);return a().createElement(tR,{tabIndex:this.props.rootTabIndex,className:h,ref:function(e){t.pieRef=e}},this.renderSectors(),i&&this.renderLabels(n),si.renderCallByParent(this.props,null,!1),(!f||p)&&sS.renderCallByParent(this.props,n,!1))}}],r=[{key:"getDerivedStateFromProps",value:function(t,e){return e.prevIsAnimationActive!==t.isAnimationActive?{prevIsAnimationActive:t.isAnimationActive,prevAnimationId:t.animationId,curSectors:t.sectors,prevSectors:[],isAnimationFinished:!0}:t.isAnimationActive&&t.animationId!==e.prevAnimationId?{prevAnimationId:t.animationId,curSectors:t.sectors,prevSectors:e.curSectors,isAnimationFinished:!0}:t.sectors!==e.curSectors?{curSectors:t.sectors,isAnimationFinished:!0}:null}},{key:"getTextAnchor",value:function(t,e){return t>e?"start":t<e?"end":"middle"}},{key:"renderLabelLineItem",value:function(t,e,r){if(a().isValidElement(t))return a().cloneElement(t,e);if(H()(t))return t(e);var n=(0,b.A)("recharts-pie-label-line","boolean"!=typeof t?t.className:"");return a().createElement(p5,yA({},e,{key:r,type:"linear",className:n}))}},{key:"renderLabelItem",value:function(t,e,r){if(a().isValidElement(t))return a().cloneElement(t,e);var n=r;if(H()(t)&&(n=t(e),a().isValidElement(n)))return n;var o=(0,b.A)("recharts-pie-label-text","boolean"==typeof t||H()(t)?"":t.className);return a().createElement(n9,yA({},e,{alignmentBaseline:"middle",className:o}),n)}}],e&&yE(n.prototype,e),r&&yE(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(i.PureComponent);yC(yN,"displayName","Pie"),yC(yN,"defaultProps",{stroke:"#fff",fill:"#808080",legendType:"rect",cx:"50%",cy:"50%",startAngle:0,endAngle:360,innerRadius:0,outerRadius:"80%",paddingAngle:0,labelLine:!0,hide:!1,minAngle:0,isAnimationActive:!t4.isSsr,animationBegin:400,animationDuration:1500,animationEasing:"ease",nameKey:"name",blendStroke:!1,rootTabIndex:0}),yC(yN,"parseDeltaAngle",function(t,e){return M(e-t)*Math.min(Math.abs(e-t),360)}),yC(yN,"getRealPieData",function(t){var e=t.data,r=t.children,n=tm(t,!1),o=tp(r,sp);return e&&e.length?e.map(function(t,e){return yk(yk(yk({payload:t},n),t),o&&o[e]&&o[e].props)}):o&&o.length?o.map(function(t){return yk(yk({},n),t.props)}):[]}),yC(yN,"parseCoordinateOfPie",function(t,e){var r=e.top,n=e.left,o=e.width,i=e.height,a=lQ(o,i);return{cx:n+I(t.cx,o,o/2),cy:r+I(t.cy,i,i/2),innerRadius:I(t.innerRadius,a,0),outerRadius:I(t.outerRadius,a,.8*a),maxRadius:t.maxRadius||Math.sqrt(o*o+i*i)/2}}),yC(yN,"getComposedData",function(t){var e,r,n=t.item,o=t.offset,i=void 0!==n.type.defaultProps?yk(yk({},n.type.defaultProps),n.props):n.props,a=yN.getRealPieData(i);if(!a||!a.length)return null;var c=i.cornerRadius,u=i.startAngle,l=i.endAngle,s=i.paddingAngle,f=i.dataKey,p=i.nameKey,h=i.valueKey,d=i.tooltipType,y=Math.abs(i.minAngle),v=yN.parseCoordinateOfPie(i,o),m=yN.parseDeltaAngle(u,l),b=Math.abs(m),g=f;q()(f)&&q()(h)?(F(!1,'Use "dataKey" to specify the value of pie,\n      the props "valueKey" will be deprecated in 1.1.0'),g="value"):q()(f)&&(F(!1,'Use "dataKey" to specify the value of pie,\n      the props "valueKey" will be deprecated in 1.1.0'),g=h);var x=a.filter(function(t){return 0!==u3(t,g,0)}).length,O=b-x*y-(b>=360?x:x-1)*s,w=a.reduce(function(t,e){var r=u3(e,g,0);return t+(_(r)?r:0)},0);return w>0&&(e=a.map(function(t,e){var n,o=u3(t,g,0),i=u3(t,p,e),a=(_(o)?o:0)/w,l=(n=e?r.endAngle+M(m)*s*(0!==o):u)+M(m)*((0!==o?y:0)+a*O),f=(n+l)/2,h=(v.innerRadius+v.outerRadius)/2,b=[{name:i,value:o,payload:t,dataKey:g,type:d}],x=lJ(v.cx,v.cy,h,f);return r=yk(yk(yk({percent:a,cornerRadius:c,name:i,tooltipPayload:b,midAngle:f,middleRadius:h,tooltipPosition:x},t),v),{},{value:u3(t,g),startAngle:n,endAngle:l,payload:t,paddingAngle:M(m)*s})})),yk(yk({},v),{},{sectors:e,data:a})});var yI=hR({chartName:"PieChart",GraphicalChild:yN,validateTooltipEventTypes:["item"],defaultTooltipEventType:"item",legendContent:"children",axisComponents:[{axisType:"angleAxis",AxisComp:yi},{axisType:"radiusAxis",AxisComp:yj}],formatAxisMap:function(t,e,r,n,o){var i=t.width,a=t.height,c=t.startAngle,u=t.endAngle,l=I(t.cx,i,i/2),s=I(t.cy,a,a/2),f=lQ(i,a,r),p=I(t.innerRadius,f,0),h=I(t.outerRadius,f,.8*f);return Object.keys(e).reduce(function(t,r){var i,a=e[r],f=a.domain,d=a.reversed;if(q()(a.range))"angleAxis"===n?i=[c,u]:"radiusAxis"===n&&(i=[p,h]),d&&(i=[i[1],i[0]]);else{var y,v=function(t){if(Array.isArray(t))return t}(y=i=a.range)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{i=(r=r.call(t)).next,!1;for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(y,2)||function(t,e){if(t){if("string"==typeof t)return lY(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return lY(t,e)}}(y,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();c=v[0],u=v[1]}var m=lu(a,o),b=m.realScaleType,g=m.scale;g.domain(f).range(i),ll(g);var x=ly(g,lX(lX({},a),{},{realScaleType:b})),O=lX(lX(lX({},a),x),{},{range:i,radius:h,realScaleType:b,scale:g,cx:l,cy:s,innerRadius:p,outerRadius:h,startAngle:c,endAngle:u});return lX(lX({},t),{},lK({},r,O))},{})},defaultProps:{layout:"centric",startAngle:0,endAngle:360,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"}}),yB=r(85814),yR=r.n(yB),yL=r(4780);let yz={primary:"#A0522D",hunting:"#8B4513",photo:"#228B22",pending:"#FFA500",confirmed:"#32CD32",completed:"#4169E1",cancelled:"#DC143C"};function yU(){let{user:t,loading:e}=(0,f.As)(),r=(0,c.useRouter)(),n=(0,c.useParams)().id,[a,b]=(0,i.useState)(null),[g,x]=(0,i.useState)(null),[O,w]=(0,i.useState)(!0),[j,S]=(0,i.useState)(null),[A,P]=(0,i.useState)("90d");return((0,i.useCallback)(async()=>{try{w(!0),S(null);let e=await s.eg.get(n);if(!e)throw Error("Farm not found");if(e.ownerId!==t.uid)throw Error("Access denied - you do not own this farm");b(e);let r=new Date,o=new Date;switch(A){case"30d":o.setDate(r.getDate()-30);break;case"90d":o.setDate(r.getDate()-90);break;case"1y":o.setFullYear(r.getFullYear()-1);break;case"all":o=new Date("2020-01-01")}let i=(await s.zi.getAll({farmId:n})).filter(t=>t.createdAt.toDate()>=o),a=await s.C2.getByFarm(n),c=i||[],u=a||[],l=c.length,f=c.filter(t=>"completed"===t.status).reduce((t,e)=>t+(e.totalPrice||0),0),p=u.length>0?u.reduce((t,e)=>t+e.rating,0)/u.length:0,h=new Map;c.forEach(t=>{let e=t.createdAt.toDate().toLocaleDateString("en-US",{year:"numeric",month:"short"}),r=h.get(e)||{bookings:0,revenue:0};h.set(e,{bookings:r.bookings+1,revenue:r.revenue+("completed"===t.status&&t.totalPrice||0)})});let d=Array.from(h.entries()).map(([t,e])=>({month:t,...e})).sort((t,e)=>new Date(t.month).getTime()-new Date(e.month).getTime()),y=new Map;c.forEach(t=>{y.set(t.status,(y.get(t.status)||0)+1)});let v=Array.from(y.entries()).map(([t,e])=>({status:t.charAt(0).toUpperCase()+t.slice(1),count:e,color:yz[t]||yz.primary})),m=new Map;c.forEach(t=>{let e="hunting"===t.activityType?"Hunting":"Photo Safari";m.set(e,(m.get(e)||0)+1)});let g=Array.from(m.entries()).map(([t,e])=>({activity:t,count:e,color:"Hunting"===t?yz.hunting:yz.photo}));x({totalBookings:l,totalRevenue:f,averageBookingValue:l>0?f/l:0,totalViews:0,conversionRate:0,averageRating:p,reviewCount:u.length,bookingsByMonth:d,bookingsByStatus:v,activityBreakdown:g})}catch(t){console.error("Error fetching analytics:",t),S("Failed to load analytics data")}finally{w(!1)}},[n,t,A]),e||O)?(0,o.jsx)("div",{className:"min-h-screen bg-earth-100 flex items-center justify-center",children:(0,o.jsxs)("div",{className:"text-center",children:[(0,o.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-accent-600 mx-auto"}),(0,o.jsx)("p",{className:"mt-4 text-earth-600",children:"Loading analytics..."})]})}):t?j&&!a?(0,o.jsx)("div",{className:"min-h-screen bg-earth-100 py-8",children:(0,o.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,o.jsxs)("div",{className:"text-center",children:[(0,o.jsx)("h1",{className:"text-2xl font-bold text-earth-900 mb-4",children:"Analytics Not Available"}),(0,o.jsx)("p",{className:"text-earth-600 mb-6",children:j}),(0,o.jsx)(yR(),{href:"/dashboard",children:(0,o.jsxs)(u.$,{variant:"primary",children:[(0,o.jsx)(p.A,{className:"w-4 h-4 mr-2"}),"Back to Dashboard"]})})]})})}):(0,o.jsx)("div",{className:"min-h-screen bg-earth-100 py-8",children:(0,o.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,o.jsxs)("div",{className:"mb-8",children:[(0,o.jsxs)("div",{className:"flex items-center gap-4 mb-4",children:[(0,o.jsx)(yR(),{href:"/dashboard",children:(0,o.jsxs)(u.$,{variant:"outline",size:"sm",children:[(0,o.jsx)(p.A,{className:"w-4 h-4 mr-2"}),"Back to Dashboard"]})}),(0,o.jsx)(yR(),{href:`/farms/${n}`,children:(0,o.jsxs)(u.$,{variant:"outline",size:"sm",children:[(0,o.jsx)(h.A,{className:"w-4 h-4 mr-2"}),"View Public Page"]})}),(0,o.jsx)(yR(),{href:`/farms/${n}/edit`,children:(0,o.jsx)(u.$,{variant:"outline",size:"sm",children:"Edit Farm"})})]}),(0,o.jsxs)("div",{className:"flex items-center justify-between",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("h1",{className:"text-3xl font-bold text-earth-900",children:"Farm Analytics"}),(0,o.jsxs)("p",{className:"text-earth-600 mt-2",children:[a?.name," - Performance insights and metrics"]})]}),(0,o.jsxs)("div",{className:"flex items-center gap-2",children:[(0,o.jsx)("span",{className:"text-sm text-earth-600",children:"Time Range:"}),(0,o.jsxs)("select",{value:A,onChange:t=>P(t.target.value),className:"px-3 py-2 border border-earth-300 rounded-md focus:outline-none focus:ring-2 focus:ring-accent-600 focus:border-transparent",children:[(0,o.jsx)("option",{value:"30d",children:"Last 30 Days"}),(0,o.jsx)("option",{value:"90d",children:"Last 90 Days"}),(0,o.jsx)("option",{value:"1y",children:"Last Year"}),(0,o.jsx)("option",{value:"all",children:"All Time"})]})]})]})]}),g&&(0,o.jsxs)("div",{className:"space-y-8",children:[(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,o.jsx)(l.Zp,{children:(0,o.jsx)(l.Wu,{className:"text-center py-6",children:(0,o.jsxs)("div",{className:"flex flex-col items-center",children:[(0,o.jsx)(d.A,{className:"text-accent-600 mb-2",size:24}),(0,o.jsx)("div",{className:"text-3xl font-bold text-accent-600 mb-1",children:g.totalBookings}),(0,o.jsx)("p",{className:"text-earth-600 text-sm",children:"Total Bookings"})]})})}),(0,o.jsx)(l.Zp,{children:(0,o.jsx)(l.Wu,{className:"text-center py-6",children:(0,o.jsxs)("div",{className:"flex flex-col items-center",children:[(0,o.jsx)(y.A,{className:"text-hunting-accent mb-2",size:24}),(0,o.jsxs)("div",{className:"text-3xl font-bold text-hunting-accent mb-1",children:["R",(0,yL.Z)(g.totalRevenue)]}),(0,o.jsx)("p",{className:"text-earth-600 text-sm",children:"Total Revenue"})]})})}),(0,o.jsx)(l.Zp,{children:(0,o.jsx)(l.Wu,{className:"text-center py-6",children:(0,o.jsxs)("div",{className:"flex flex-col items-center",children:[(0,o.jsx)(v,{className:"text-photo-accent mb-2",size:24}),(0,o.jsxs)("div",{className:"text-3xl font-bold text-photo-accent mb-1",children:["R",(0,yL.Z)(g.averageBookingValue)]}),(0,o.jsx)("p",{className:"text-earth-600 text-sm",children:"Avg. Booking Value"})]})})}),(0,o.jsx)(l.Zp,{children:(0,o.jsx)(l.Wu,{className:"text-center py-6",children:(0,o.jsxs)("div",{className:"flex flex-col items-center",children:[(0,o.jsx)(m.A,{className:"text-accent-600 mb-2",size:24}),(0,o.jsx)("div",{className:"text-3xl font-bold text-accent-600 mb-1",children:g.averageRating>0?g.averageRating.toFixed(1):"N/A"}),(0,o.jsxs)("p",{className:"text-earth-600 text-sm",children:["Avg. Rating (",g.reviewCount," reviews)"]})]})})})]}),(0,o.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,o.jsxs)(l.Zp,{children:[(0,o.jsx)(l.aR,{children:(0,o.jsx)(l.ZB,{children:"Bookings Over Time"})}),(0,o.jsx)(l.Wu,{children:(0,o.jsx)(tP,{width:"100%",height:300,children:(0,o.jsxs)(dT,{data:g.bookingsByMonth,children:[(0,o.jsx)(dV,{strokeDasharray:"3 3"}),(0,o.jsx)(dx,{dataKey:"month",tick:{fontSize:12}}),(0,o.jsx)(dM,{tick:{fontSize:12}}),(0,o.jsx)(eu,{}),(0,o.jsx)(hQ,{type:"monotone",dataKey:"bookings",stroke:yz.primary,strokeWidth:2,dot:{fill:yz.primary}})]})})})]}),(0,o.jsxs)(l.Zp,{children:[(0,o.jsx)(l.aR,{children:(0,o.jsx)(l.ZB,{children:"Revenue Over Time"})}),(0,o.jsx)(l.Wu,{children:(0,o.jsx)(tP,{width:"100%",height:300,children:(0,o.jsxs)(dX,{data:g.bookingsByMonth,children:[(0,o.jsx)(dV,{strokeDasharray:"3 3"}),(0,o.jsx)(dx,{dataKey:"month",tick:{fontSize:12}}),(0,o.jsx)(dM,{tick:{fontSize:12}}),(0,o.jsx)(eu,{formatter:t=>`R${Number(t).toLocaleString()}`}),(0,o.jsx)(fh,{dataKey:"revenue",fill:yz.hunting,radius:[4,4,0,0]})]})})})]})]}),(0,o.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,o.jsxs)(l.Zp,{children:[(0,o.jsx)(l.aR,{children:(0,o.jsx)(l.ZB,{children:"Booking Status Distribution"})}),(0,o.jsx)(l.Wu,{children:(0,o.jsx)(tP,{width:"100%",height:300,children:(0,o.jsxs)(yI,{children:[(0,o.jsx)(yN,{data:g.bookingsByStatus,cx:"50%",cy:"50%",labelLine:!1,label:({status:t,count:e})=>`${t}: ${e}`,outerRadius:80,fill:"#8884d8",dataKey:"count",children:g.bookingsByStatus.map((t,e)=>(0,o.jsx)(sp,{fill:t.color},`cell-${e}`))}),(0,o.jsx)(eu,{})]})})})]}),(0,o.jsxs)(l.Zp,{children:[(0,o.jsx)(l.aR,{children:(0,o.jsx)(l.ZB,{children:"Activity Type Breakdown"})}),(0,o.jsx)(l.Wu,{children:(0,o.jsx)(tP,{width:"100%",height:300,children:(0,o.jsxs)(yI,{children:[(0,o.jsx)(yN,{data:g.activityBreakdown,cx:"50%",cy:"50%",labelLine:!1,label:({activity:t,count:e})=>`${t}: ${e}`,outerRadius:80,fill:"#8884d8",dataKey:"count",children:g.activityBreakdown.map((t,e)=>(0,o.jsx)(sp,{fill:t.color},`cell-${e}`))}),(0,o.jsx)(eu,{})]})})})]})]})]})]})}):(r.push("/auth/login"),null)}},61548:(t,e,r)=>{var n=r(5231),o=r(52823),i=r(55048),a=r(12290),c=/^\[object .+?Constructor\]$/,u=Object.prototype,l=Function.prototype.toString,s=u.hasOwnProperty,f=RegExp("^"+l.call(s).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");t.exports=function(t){return!(!i(t)||o(t))&&(n(t)?f:c).test(a(t))}},61837:(t,e,r)=>{var n=r(21367),o=r(22),i=r(54765),a=r(40542);t.exports=function(t,e){return(a(t)?n:i)(t,o(e,3))}},62688:(t,e,r)=>{"use strict";r.d(e,{A:()=>f});var n=r(43210);let o=t=>t.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=t=>t.replace(/^([A-Z])|[\s-_]+(\w)/g,(t,e,r)=>r?r.toUpperCase():e.toLowerCase()),a=t=>{let e=i(t);return e.charAt(0).toUpperCase()+e.slice(1)},c=(...t)=>t.filter((t,e,r)=>!!t&&""!==t.trim()&&r.indexOf(t)===e).join(" ").trim(),u=t=>{for(let e in t)if(e.startsWith("aria-")||"role"===e||"title"===e)return!0};var l={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let s=(0,n.forwardRef)(({color:t="currentColor",size:e=24,strokeWidth:r=2,absoluteStrokeWidth:o,className:i="",children:a,iconNode:s,...f},p)=>(0,n.createElement)("svg",{ref:p,...l,width:e,height:e,stroke:t,strokeWidth:o?24*Number(r)/Number(e):r,className:c("lucide",i),...!a&&!u(f)&&{"aria-hidden":"true"},...f},[...s.map(([t,e])=>(0,n.createElement)(t,e)),...Array.isArray(a)?a:[a]])),f=(t,e)=>{let r=(0,n.forwardRef)(({className:r,...i},u)=>(0,n.createElement)(s,{ref:u,iconNode:e,className:c(`lucide-${o(a(t))}`,`lucide-${t}`,r),...i}));return r.displayName=a(t),r}},63033:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63866:(t,e,r)=>{var n=r(29395),o=r(40542),i=r(27467);t.exports=function(t){return"string"==typeof t||!o(t)&&i(t)&&"[object String]"==n(t)}},63979:(t,e,r)=>{var n=r(52599),o=r(6330),i=Object.prototype.propertyIsEnumerable,a=Object.getOwnPropertySymbols;t.exports=a?function(t){return null==t?[]:n(a(t=Object(t)),function(e){return i.call(t,e)})}:o},64398:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(62688).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},65662:t=>{t.exports=function(t,e){return function(r){return t(e(r))}}},65727:(t,e,r)=>{var n=r(81957);t.exports=function(t,e,r){for(var o=-1,i=t.criteria,a=e.criteria,c=i.length,u=r.length;++o<c;){var l=n(i[o],a[o]);if(l){if(o>=u)return l;return l*("desc"==r[o]?-1:1)}}return t.index-e.index}},65932:(t,e,r)=>{t.exports=r(65662)(Object.getPrototypeOf,Object)},65984:t=>{t.exports=function(t){return function(e,r,n){for(var o=-1,i=Object(e),a=n(e),c=a.length;c--;){var u=a[t?c:++o];if(!1===r(i[u],u,i))break}return e}}},66354:(t,e,r)=>{var n=r(85244),o=Math.max;t.exports=function(t,e,r){return e=o(void 0===e?t.length-1:e,0),function(){for(var i=arguments,a=-1,c=o(i.length-e,0),u=Array(c);++a<c;)u[a]=i[e+a];a=-1;for(var l=Array(e+1);++a<e;)l[a]=i[a];return l[e]=r(u),n(t,this,l)}}},66400:t=>{var e=Date.now;t.exports=function(t){var r=0,n=0;return function(){var o=e(),i=16-(o-n);if(n=o,i>0){if(++r>=800)return arguments[0]}else r=0;return t.apply(void 0,arguments)}}},66713:(t,e,r)=>{var n=r(3105),o=r(34117),i=r(48385);t.exports=function(t){return o(t)?i(t):n(t)}},66837:(t,e,r)=>{var n=r(58141);t.exports=function(){this.__data__=n?n(null):{},this.size=0}},66930:(t,e,r)=>{var n=r(27669),o=r(28837),i=r(94388),a=r(35800),c=r(58744);function u(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}u.prototype.clear=n,u.prototype.delete=o,u.prototype.get=i,u.prototype.has=a,u.prototype.set=c,t.exports=u},66992:(t,e)=>{"use strict";var r,n=Symbol.for("react.element"),o=Symbol.for("react.portal"),i=Symbol.for("react.fragment"),a=Symbol.for("react.strict_mode"),c=Symbol.for("react.profiler"),u=Symbol.for("react.provider"),l=Symbol.for("react.context"),s=Symbol.for("react.server_context"),f=Symbol.for("react.forward_ref"),p=Symbol.for("react.suspense"),h=Symbol.for("react.suspense_list"),d=Symbol.for("react.memo"),y=Symbol.for("react.lazy");Symbol.for("react.offscreen");Symbol.for("react.module.reference"),e.isFragment=function(t){return function(t){if("object"==typeof t&&null!==t){var e=t.$$typeof;switch(e){case n:switch(t=t.type){case i:case c:case a:case p:case h:return t;default:switch(t=t&&t.$$typeof){case s:case l:case f:case y:case d:case u:return t;default:return e}}case o:return e}}}(t)===i}},67009:t=>{t.exports=function(t,e){return t===e||t!=t&&e!=e}},67200:(t,e,r)=>{var n=r(66930),o=r(37575),i=r(75411),a=r(34746),c=r(25118),u=r(30854);function l(t){var e=this.__data__=new n(t);this.size=e.size}l.prototype.clear=o,l.prototype.delete=i,l.prototype.get=a,l.prototype.has=c,l.prototype.set=u,t.exports=l},67367:(t,e,r)=>{var n=r(99525),o=r(22),i=r(75847),a=r(40542),c=r(7383);t.exports=function(t,e,r){var u=a(t)?n:i;return r&&c(t,e,r)&&(e=void 0),u(t,o(e,3))}},67554:(t,e,r)=>{var n=r(99114);t.exports=r(56506)(n)},67619:(t,e,r)=>{var n=r(40542),o=r(49227),i=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,a=/^\w*$/;t.exports=function(t,e){if(n(t))return!1;var r=typeof t;return!!("number"==r||"symbol"==r||"boolean"==r||null==t||o(t))||a.test(t)||!i.test(t)||null!=e&&t in Object(e)}},69433:(t,e,r)=>{t.exports=r(5566)("toUpperCase")},69619:t=>{t.exports=function(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=0x1fffffffffffff}},69691:(t,e,r)=>{var n=r(41157),o=r(99114),i=r(22);t.exports=function(t,e){var r={};return e=i(e,3),o(t,function(t,o,i){n(r,o,e(t,o,i))}),r}},70151:(t,e,r)=>{var n=r(85718);t.exports=function(){return n.Date.now()}},70222:(t,e,r)=>{var n=r(79474),o=Object.prototype,i=o.hasOwnProperty,a=o.toString,c=n?n.toStringTag:void 0;t.exports=function(t){var e=i.call(t,c),r=t[c];try{t[c]=void 0;var n=!0}catch(t){}var o=a.call(t);return n&&(e?t[c]=r:delete t[c]),o}},70440:(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>o});var n=r(31658);let o=async t=>[{type:"image/x-icon",sizes:"16x16",url:(0,n.fillMetadataSegment)(".",await t.params,"favicon.ico")+""}]},71960:t=>{t.exports=function(t,e,r){for(var n=-1,o=null==t?0:t.length;++n<o;)if(r(e,t[n]))return!0;return!1}},71967:(t,e,r)=>{var n=r(15871);t.exports=function(t,e){return n(t,e)}},73496:t=>{"use strict";t.exports=require("http2")},74075:t=>{"use strict";t.exports=require("zlib")},74610:t=>{t.exports=function(t,e,r){for(var n=r-1,o=t.length;++n<o;)if(t[n]===e)return n;return -1}},75254:(t,e,r)=>{var n=r(78418),o=r(93311),i=r(41132);t.exports=function(t){var e=o(t);return 1==e.length&&e[0][2]?i(e[0][0],e[0][1]):function(r){return r===t||n(r,t,e)}}},75411:t=>{t.exports=function(t){var e=this.__data__,r=e.delete(t);return this.size=e.size,r}},75847:(t,e,r)=>{var n=r(67554);t.exports=function(t,e){var r;return n(t,function(t,n,o){return!(r=e(t,n,o))}),!!r}},77822:(t,e,r)=>{var n=r(93490);t.exports=function(t){return n(t)&&t!=+t}},77834:t=>{var e=Object.prototype;t.exports=function(t){var r=t&&t.constructor;return t===("function"==typeof r&&r.prototype||e)}},78418:(t,e,r)=>{var n=r(67200),o=r(15871);t.exports=function(t,e,r,i){var a=r.length,c=a,u=!i;if(null==t)return!c;for(t=Object(t);a--;){var l=r[a];if(u&&l[2]?l[1]!==t[l[0]]:!(l[0]in t))return!1}for(;++a<c;){var s=(l=r[a])[0],f=t[s],p=l[1];if(u&&l[2]){if(void 0===f&&!(s in t))return!1}else{var h=new n;if(i)var d=i(f,p,s,t,e,h);if(!(void 0===d?o(p,f,3,i,h):d))return!1}}return!0}},79474:(t,e,r)=>{t.exports=r(85718).Symbol},79551:t=>{"use strict";t.exports=require("url")},80195:(t,e,r)=>{var n=r(79474),o=r(21367),i=r(40542),a=r(49227),c=1/0,u=n?n.prototype:void 0,l=u?u.toString:void 0;t.exports=function t(e){if("string"==typeof e)return e;if(i(e))return o(e,t)+"";if(a(e))return l?l.call(e):"";var r=e+"";return"0"==r&&1/e==-c?"-0":r}},80329:(t,e,r)=>{t=r.nmd(t);var n=r(85718),o=r(1944),i=e&&!e.nodeType&&e,a=i&&t&&!t.nodeType&&t,c=a&&a.exports===i?n.Buffer:void 0,u=c?c.isBuffer:void 0;t.exports=u||o},80458:(t,e,r)=>{var n=r(29395),o=r(69619),i=r(27467),a={};a["[object Float32Array]"]=a["[object Float64Array]"]=a["[object Int8Array]"]=a["[object Int16Array]"]=a["[object Int32Array]"]=a["[object Uint8Array]"]=a["[object Uint8ClampedArray]"]=a["[object Uint16Array]"]=a["[object Uint32Array]"]=!0,a["[object Arguments]"]=a["[object Array]"]=a["[object ArrayBuffer]"]=a["[object Boolean]"]=a["[object DataView]"]=a["[object Date]"]=a["[object Error]"]=a["[object Function]"]=a["[object Map]"]=a["[object Number]"]=a["[object Object]"]=a["[object RegExp]"]=a["[object Set]"]=a["[object String]"]=a["[object WeakMap]"]=!1,t.exports=function(t){return i(t)&&o(t.length)&&!!a[n(t)]}},80704:(t,e,r)=>{var n=r(96678);t.exports=function(t,e){return!!(null==t?0:t.length)&&n(t,e,0)>-1}},81488:t=>{t.exports=function(t,e){return null!=t&&e in Object(t)}},81630:t=>{"use strict";t.exports=require("http")},81957:(t,e,r)=>{var n=r(49227);t.exports=function(t,e){if(t!==e){var r=void 0!==t,o=null===t,i=t==t,a=n(t),c=void 0!==e,u=null===e,l=e==e,s=n(e);if(!u&&!s&&!a&&t>e||a&&c&&l&&!u&&!s||o&&c&&l||!r&&l||!i)return 1;if(!o&&!a&&!s&&t<e||s&&r&&i&&!o&&!a||u&&r&&i||!c&&i||!l)return -1}return 0}},82038:(t,e,r)=>{var n=r(34821),o=r(35163),i=r(40542),a=r(80329),c=r(38428),u=r(10090),l=Object.prototype.hasOwnProperty;t.exports=function(t,e){var r=i(t),s=!r&&o(t),f=!r&&!s&&a(t),p=!r&&!s&&!f&&u(t),h=r||s||f||p,d=h?n(t.length,String):[],y=d.length;for(var v in t)(e||l.call(t,v))&&!(h&&("length"==v||f&&("offset"==v||"parent"==v)||p&&("buffer"==v||"byteLength"==v||"byteOffset"==v)||c(v,y)))&&d.push(v);return d}},84031:(t,e,r)=>{"use strict";var n=r(34452);function o(){}function i(){}i.resetWarningCache=o,t.exports=function(){function t(t,e,r,o,i,a){if(a!==n){var c=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw c.name="Invariant Violation",c}}function e(){return t}t.isRequired=t;var r={array:t,bigint:t,bool:t,func:t,number:t,object:t,string:t,symbol:t,any:t,arrayOf:e,element:t,elementType:t,instanceOf:e,node:t,objectOf:e,oneOf:e,oneOfType:e,shape:e,exact:e,checkPropTypes:i,resetWarningCache:o};return r.PropTypes=r,r}},84261:t=>{t.exports=function(t){var e=this.has(t)&&delete this.__data__[t];return this.size-=!!e,e}},84482:(t,e,r)=>{var n=r(28977);t.exports=function(t){var e=n(t),r=e%1;return e==e?r?e-r:e:0}},84713:t=>{var e=Object.prototype.toString;t.exports=function(t){return e.call(t)}},85244:t=>{t.exports=function(t,e,r){switch(r.length){case 0:return t.call(e);case 1:return t.call(e,r[0]);case 2:return t.call(e,r[0],r[1]);case 3:return t.call(e,r[0],r[1],r[2])}return t.apply(e,r)}},85406:(t,e,r)=>{t.exports=r(85718)["__core-js_shared__"]},85450:(t,e,r)=>{var n=r(79474),o=r(35163),i=r(40542),a=n?n.isConcatSpreadable:void 0;t.exports=function(t){return i(t)||o(t)||!!(a&&t&&t[a])}},85718:(t,e,r)=>{var n=r(10663),o="object"==typeof self&&self&&self.Object===Object&&self;t.exports=n||o||Function("return this")()},85745:(t,e,r)=>{var n=r(86451);t.exports=function(t){var e=n(t,function(t){return 500===r.size&&r.clear(),t}),r=e.cache;return e}},85938:(t,e,r)=>{var n=r(42205),o=r(17518),i=r(46229),a=r(7383);t.exports=i(function(t,e){if(null==t)return[];var r=e.length;return r>1&&a(t,e[0],e[1])?e=[]:r>2&&a(e[0],e[1],e[2])&&(e=[e[0]]),o(t,n(e,1),[])})},86451:(t,e,r)=>{var n=r(95746);function o(t,e){if("function"!=typeof t||null!=e&&"function"!=typeof e)throw TypeError("Expected a function");var r=function(){var n=arguments,o=e?e.apply(this,n):n[0],i=r.cache;if(i.has(o))return i.get(o);var a=t.apply(this,n);return r.cache=i.set(o,a)||i,a};return r.cache=new(o.Cache||n),r}o.Cache=n,t.exports=o},87270:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length;++r<n;)if(!e(t[r],r,t))return!1;return!0}},87321:(t,e,r)=>{var n=r(98798),o=r(7383),i=r(28977);t.exports=function(t){return function(e,r,a){return a&&"number"!=typeof a&&o(e,r,a)&&(r=a=void 0),e=i(e),void 0===r?(r=e,e=0):r=i(r),a=void 0===a?e<r?1:-1:i(a),n(e,r,a,t)}}},87506:(t,e,r)=>{var n=r(66837),o=r(84261),i=r(89492),a=r(90200),c=r(39672);function u(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}u.prototype.clear=n,u.prototype.delete=o,u.prototype.get=i,u.prototype.has=a,u.prototype.set=c,t.exports=u},87955:(t,e,r)=>{t.exports=r(84031)()},89167:(t,e,r)=>{t.exports=r(41547)(r(85718),"DataView")},89185:t=>{t.exports=function(t){return this.__data__.set(t,"__lodash_hash_undefined__"),this}},89492:(t,e,r)=>{var n=r(58141),o=Object.prototype.hasOwnProperty;t.exports=function(t){var e=this.__data__;if(n){var r=e[t];return"__lodash_hash_undefined__"===r?void 0:r}return o.call(e,t)?e[t]:void 0}},89605:(t,e,r)=>{t.exports=r(65662)(Object.keys,Object)},89624:t=>{t.exports=function(t){return function(e){return t(e)}}},90200:(t,e,r)=>{var n=r(58141),o=Object.prototype.hasOwnProperty;t.exports=function(t){var e=this.__data__;return n?void 0!==e[t]:o.call(e,t)}},90453:(t,e,r)=>{var n=r(2984),o=r(99180),i=r(48169);t.exports=function(t){return t&&t.length?n(t,i,o):void 0}},90851:t=>{t.exports=function(t,e){return null==t?void 0:t[e]}},91290:t=>{t.exports=function(t,e,r,n){for(var o=t.length,i=r+(n?1:-1);n?i--:++i<o;)if(e(t[i],i,t))return i;return -1}},91645:t=>{"use strict";t.exports=require("net")},91928:(t,e,r)=>{var n=r(41547);t.exports=function(){try{var t=n(Object,"defineProperty");return t({},"",{}),t}catch(t){}}()},92662:(t,e,r)=>{var n=r(46328),o=r(80704),i=r(71960),a=r(58276),c=r(95308),u=r(2408);t.exports=function(t,e,r){var l=-1,s=o,f=t.length,p=!0,h=[],d=h;if(r)p=!1,s=i;else if(f>=200){var y=e?null:c(t);if(y)return u(y);p=!1,s=a,d=new n}else d=e?[]:h;e:for(;++l<f;){var v=t[l],m=e?e(v):v;if(v=r||0!==v?v:0,p&&m==m){for(var b=d.length;b--;)if(d[b]===m)continue e;e&&d.push(m),h.push(v)}else s(d,m,r)||(d!==h&&d.push(m),h.push(v))}return h}},93311:(t,e,r)=>{var n=r(34883),o=r(7651);t.exports=function(t){for(var e=o(t),r=e.length;r--;){var i=e[r],a=t[i];e[r]=[i,a,n(a)]}return e}},93490:(t,e,r)=>{var n=r(29395),o=r(27467);t.exports=function(t){return"number"==typeof t||o(t)&&"[object Number]"==n(t)}},93780:(t,e,r)=>{"use strict";t.exports=r(66992)},94388:(t,e,r)=>{var n=r(57797);t.exports=function(t){var e=this.__data__,r=n(e,t);return r<0?void 0:e[r][1]}},94389:(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/src/personal/bvr-safaris/src/app/farms/[id]/analytics/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/src/personal/bvr-safaris/src/app/farms/[id]/analytics/page.tsx","default")},94735:t=>{"use strict";t.exports=require("events")},95308:(t,e,r)=>{var n=r(34772),o=r(36959),i=r(2408);t.exports=n&&1/i(new n([,-0]))[1]==1/0?function(t){return new n(t)}:o},95746:(t,e,r)=>{var n=r(15909),o=r(29205),i=r(29508),a=r(61320),c=r(19976);function u(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}u.prototype.clear=n,u.prototype.delete=o,u.prototype.get=i,u.prototype.has=a,u.prototype.set=c,t.exports=u},96678:(t,e,r)=>{var n=r(91290),o=r(39774),i=r(74610);t.exports=function(t,e,r){return e==e?i(t,e,r):n(t,o,r)}},98451:(t,e,r)=>{var n=r(29395),o=r(27467);t.exports=function(t){return!0===t||!1===t||o(t)&&"[object Boolean]"==n(t)}},98798:t=>{var e=Math.ceil,r=Math.max;t.exports=function(t,n,o,i){for(var a=-1,c=r(e((n-t)/(o||1)),0),u=Array(c);c--;)u[i?c:++a]=t,t+=o;return u}},99114:(t,e,r)=>{var n=r(12344),o=r(7651);t.exports=function(t,e){return t&&n(t,e,o)}},99180:t=>{t.exports=function(t,e){return t>e}},99525:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length;++r<n;)if(e(t[r],r,t))return!0;return!1}}};var e=require("../../../../webpack-runtime.js");e.C(t);var r=t=>e(e.s=t),n=e.X(0,[447,874,658,533,391],()=>r(39839));module.exports=n})();