(()=>{var e={};e.id=412,e.ids=[412],e.modules={273:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>d,routeModule:()=>p,tree:()=>o});var r=s(65239),i=s(48088),a=s(88170),n=s.n(a),l=s(30893),c={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);s.d(t,c);let o={children:["",{children:["farms",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,29422)),"/Users/<USER>/src/personal/bvr-safaris/src/app/farms/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,82893)),"/Users/<USER>/src/personal/bvr-safaris/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["/Users/<USER>/src/personal/bvr-safaris/src/app/farms/page.tsx"],x={require:s,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/farms/page",pathname:"/farms",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4997:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>v});var r=s(60687),i=s(43210),a=s(16189),n=s(47731),l=s(52504),c=s(2643),o=s(71170),d=s(28749),x=s(4780),p=s(66420);function h({filters:e,onFiltersChange:t,className:s}){let[a,n]=(0,i.useState)(!1),l=s=>{let r=e.activities.includes(s)?e.activities.filter(e=>e!==s):[...e.activities,s];t({...e,activities:r})},h=s=>{let r=e.provinces.includes(s)?e.provinces.filter(e=>e!==s):[...e.provinces,s];t({...e,provinces:r})},m=s=>{let r=e.amenities.includes(s)?e.amenities.filter(e=>e!==s):[...e.amenities,s];t({...e,amenities:r})},u=s=>{let r=e.species.includes(s)?e.species.filter(e=>e!==s):[...e.species,s];t({...e,species:r})},f=e.activities.length>0||e.provinces.length>0||e.amenities.length>0||e.species.length>0;return(0,r.jsxs)(d.Zp,{className:s,children:[(0,r.jsx)(d.aR,{children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)(d.ZB,{children:"Filters"}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[f&&(0,r.jsx)(c.$,{variant:"outline",size:"sm",onClick:()=>{t({activities:[],provinces:[],priceRange:[0,2e4],amenities:[],species:[]})},children:"Clear All"}),(0,r.jsxs)(c.$,{variant:"outline",size:"sm",onClick:()=>n(!a),children:[a?"Less":"More"," Filters"]})]})]})}),(0,r.jsxs)(d.Wu,{className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-semibold mb-3 text-earth-900",children:"Activity Type"}),(0,r.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,r.jsx)("button",{onClick:()=>l("hunting"),className:`px-3 py-2 rounded-md text-sm font-medium transition-colors ${e.activities.includes("hunting")?"bg-hunting-accent text-white":"bg-earth-200 text-earth-700 hover:bg-earth-300"}`,children:"Hunting"}),(0,r.jsx)("button",{onClick:()=>l("photo_safari"),className:`px-3 py-2 rounded-md text-sm font-medium transition-colors ${e.activities.includes("photo_safari")?"bg-photo-accent text-white":"bg-earth-200 text-earth-700 hover:bg-earth-300"}`,children:"Photo Safari"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-semibold mb-3 text-earth-900",children:"Price Range (per day)"}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("input",{type:"range",min:"0",max:"20000",step:"500",value:e.priceRange[1],onChange:s=>t({...e,priceRange:[e.priceRange[0],parseInt(s.target.value)]}),className:"w-full"}),(0,r.jsxs)("div",{className:"flex justify-between text-sm text-earth-600",children:[(0,r.jsx)("span",{children:"R0"}),(0,r.jsxs)("span",{children:["R",(0,x.Z)(e.priceRange[1])]})]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-semibold mb-3 text-earth-900",children:"Provinces"}),(0,r.jsx)("div",{className:"flex flex-wrap gap-2",children:p.L0.map(t=>(0,r.jsx)("button",{onClick:()=>h(t),className:`px-3 py-2 rounded-md text-sm font-medium transition-colors ${e.provinces.includes(t)?"bg-accent-600 text-white":"bg-earth-200 text-earth-700 hover:bg-earth-300"}`,children:t},t))})]}),a&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-semibold mb-3 text-earth-900",children:"Amenities"}),(0,r.jsx)("div",{className:"flex flex-wrap gap-2",children:p.lc.map(t=>(0,r.jsx)("button",{onClick:()=>m(t),className:`px-3 py-2 rounded-md text-sm font-medium transition-colors ${e.amenities.includes(t)?"bg-accent-600 text-white":"bg-earth-200 text-earth-700 hover:bg-earth-300"}`,children:t},t))})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-semibold mb-3 text-earth-900",children:"Available Species"}),(0,r.jsx)("div",{className:"flex flex-wrap gap-2",children:p.QC.map(t=>(0,r.jsx)("button",{onClick:()=>u(t),className:`px-3 py-2 rounded-md text-sm font-medium transition-colors ${e.species.includes(t)?"bg-accent-600 text-white":"bg-earth-200 text-earth-700 hover:bg-earth-300"}`,children:t},t))})]})]}),f&&(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-semibold mb-3 text-earth-900",children:"Active Filters"}),(0,r.jsxs)("div",{className:"flex flex-wrap gap-2",children:[e.activities.map(e=>(0,r.jsx)(o.E,{variant:"hunting"===e?"hunting":"photo",children:"hunting"===e?"Hunting":"Photo Safari"},e)),e.provinces.map(e=>(0,r.jsxs)(o.E,{variant:"location",children:["\uD83D\uDCCD ",e]},e)),e.amenities.slice(0,3).map(e=>(0,r.jsx)(o.E,{variant:"default",children:e},e)),e.amenities.length>3&&(0,r.jsxs)(o.E,{variant:"default",children:["+",e.amenities.length-3," more"]})]})]})]})]})}var m=s(89173),u=s(474);function f(){(0,a.useSearchParams)();let[e,t]=(0,i.useState)(""),[s,c]=(0,i.useState)(""),[o,d]=(0,i.useState)({activities:[],provinces:[],priceRange:[0,2e4],amenities:[],species:[]}),[x,p]=(0,i.useState)([]),[f,v]=(0,i.useState)(!0),[g,j]=(0,i.useState)(null),b=(0,i.useCallback)(async()=>{try{v(!0),j(null);let e=(await u.eg.getAll({isActive:!0})).map(e=>{let t=[];return"hunting"===e.activityTypes?t.push("hunting"):"photo_safari"===e.activityTypes?t.push("photo_safari"):"both"===e.activityTypes&&t.push("hunting","photo_safari"),{...e,activities:t,rating:void 0,reviewCount:0}});p(e)}catch(e){console.error("Error fetching farms:",e),j("Failed to load farms")}finally{v(!1)}},[]),N=x.filter(t=>{let r=!e||t.name.toLowerCase().includes(e.toLowerCase())||t.description&&t.description.toLowerCase().includes(e.toLowerCase()),i=!s||t.location.toLowerCase().includes(s.toLowerCase())||t.province.toLowerCase().includes(s.toLowerCase()),a=0===o.activities.length||o.activities.some(e=>t.activities.includes(e)),n=0===o.provinces.length||o.provinces.includes(t.province);return r&&i&&a&&n});return(0,r.jsxs)("div",{className:"min-h-screen bg-earth-100",children:[(0,r.jsx)("div",{className:"relative text-white h-96",children:(0,r.jsx)(m.t,{className:"absolute inset-0",children:(0,r.jsxs)("div",{className:"relative z-20 container mx-auto px-4 h-full flex items-center",children:[(0,r.jsxs)("div",{className:"text-center mb-8",children:[(0,r.jsx)("h1",{className:"text-4xl text-earth-200 md:text-5xl font-bold mb-4 font-display",children:"Find Your Perfect Safari Experience"}),(0,r.jsx)("p",{className:"text-xl text-earth-200 max-w-2xl mx-auto",children:"Discover exceptional hunting and photo safari opportunities across South Africa"})]}),(0,r.jsx)("div",{className:"max-w-4xl mx-auto",children:(0,r.jsx)(n.I,{onSearch:(e,s)=>{t(e),c(s)}})})]})})}),(0,r.jsx)("div",{className:"container mx-auto px-4 py-12",children:(0,r.jsxs)("div",{className:"flex flex-col lg:flex-row gap-8",children:[(0,r.jsx)("div",{className:"lg:w-1/4",children:(0,r.jsx)(h,{filters:o,onFiltersChange:d,className:"sticky top-24"})}),(0,r.jsxs)("div",{className:"lg:w-3/4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-8",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-earth-900",children:f?"Loading...":`${N.length} Farms Found`}),(e||s)&&(0,r.jsxs)("div",{className:"text-earth-600",children:[e&&(0,r.jsxs)("span",{children:["Search: “",e,"” "]}),s&&(0,r.jsxs)("span",{children:["Location: “",s,"”"]})]})]}),f?(0,r.jsx)("div",{className:"text-center py-16",children:(0,r.jsx)("p",{className:"text-earth-600 text-lg",children:"Loading farms..."})}):g?(0,r.jsxs)("div",{className:"text-center py-16",children:[(0,r.jsx)("p",{className:"text-red-600 text-lg",children:g}),(0,r.jsx)("button",{onClick:b,className:"mt-4 px-4 py-2 bg-accent-600 text-white rounded-lg hover:bg-accent-700",children:"Try Again"})]}):0===N.length?(0,r.jsx)("div",{className:"text-center py-16",children:(0,r.jsx)("p",{className:"text-earth-600 text-lg",children:"No farms found matching your criteria. Try adjusting your search or filters."})}):(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:N.map(e=>(0,r.jsx)(l.f,{id:e.id,name:e.name,location:e.location,province:e.province,description:e.description||"",activities:e.activities,priceRange:e.pricingInfo||"Contact for pricing",rating:e.rating,reviewCount:e.reviewCount},e.id))})]})]})})]})}function v(){return(0,r.jsx)(i.Suspense,{fallback:(0,r.jsx)("div",{className:"min-h-screen bg-earth-100 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-accent-600 mx-auto"}),(0,r.jsx)("p",{className:"mt-4 text-earth-600",children:"Loading farms..."})]})}),children:(0,r.jsx)(f,{})})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29422:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/src/personal/bvr-safaris/src/app/farms/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/src/personal/bvr-safaris/src/app/farms/page.tsx","default")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},37366:e=>{"use strict";e.exports=require("dns")},47731:(e,t,s)=>{"use strict";s.d(t,{I:()=>l});var r=s(60687),i=s(43210),a=s(51907),n=s(2643);function l({onSearch:e,placeholder:t="Search farms, activities, or species...",className:s}){let[l,c]=(0,i.useState)(""),[o,d]=(0,i.useState)("");return(0,r.jsx)("form",{onSubmit:t=>{t.preventDefault(),e?.(l,o)},className:s,children:(0,r.jsxs)("div",{className:"flex flex-col md:flex-row gap-4 p-6 bg-white rounded-lg shadow-lg",children:[(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)(a.p,{type:"text",placeholder:t,value:l,onChange:e=>c(e.target.value),className:"border-earth-300 focus:border-accent-600"})}),(0,r.jsx)("div",{className:"flex-1 md:max-w-xs",children:(0,r.jsx)(a.p,{type:"text",placeholder:"Location (province, city)",value:o,onChange:e=>d(e.target.value),className:"border-earth-300 focus:border-accent-600"})}),(0,r.jsx)(n.$,{type:"submit",variant:"primary",size:"lg",className:"md:px-8",children:"\uD83D\uDD0D Search"})]})})}},52504:(e,t,s)=>{"use strict";s.d(t,{f:()=>c});var r=s(60687),i=s(85814),a=s.n(i),n=s(28749),l=s(71170);function c({id:e,name:t,location:s,province:i,description:c,imageUrl:o,activities:d,priceRange:x,rating:p,reviewCount:h}){return(0,r.jsx)(a(),{href:`/farms/${e}`,children:(0,r.jsxs)(n.Zp,{className:"h-full cursor-pointer",children:[(0,r.jsx)(n.MH,{src:o,alt:`${t} farm`}),(0,r.jsxs)(n.Wu,{className:"flex flex-col h-full",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"flex items-start justify-between mb-3",children:[(0,r.jsx)(n.ZB,{className:"text-lg leading-tight",children:t}),p&&(0,r.jsxs)("div",{className:"flex items-center text-sm text-earth-600",children:[(0,r.jsx)("span",{className:"text-accent-600 mr-1",children:"★"}),(0,r.jsx)("span",{children:p}),h&&(0,r.jsxs)("span",{className:"ml-1",children:["(",h,")"]})]})]}),(0,r.jsxs)("div",{className:"text-earth-600 text-sm mb-3",children:[s,", ",i]}),(0,r.jsx)("p",{className:"text-earth-700 text-sm mb-4 line-clamp-3",children:c}),(0,r.jsx)("div",{className:"flex flex-wrap gap-2 mb-4",children:d.map(e=>(0,r.jsx)(l.E,{variant:"hunting"===e?"hunting":"photo",children:"hunting"===e?"Hunting":"Photo Safari"},e))})]}),x&&(0,r.jsx)("div",{className:"text-earth-900 font-semibold text-lg",children:x})]})]})})}},55511:e=>{"use strict";e.exports=require("crypto")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66420:(e,t,s)=>{"use strict";s.d(t,{L0:()=>r,QC:()=>a,lc:()=>i});let r=["Eastern Cape","Free State","Gauteng","KwaZulu-Natal","Limpopo","Mpumalanga","Northern Cape","North West","Western Cape"],i=["Luxury Lodge","Basic Accommodation","Restaurant","Bar","Swimming Pool","Spa","WiFi","Airport Transfer","Professional Guide","Trophy Preparation","Taxidermy"],a=["Lion","Leopard","Elephant","Buffalo","Rhino","Kudu","Impala","Springbok","Eland","Sable","Gemsbok","Waterbuck","Bushbuck","Warthog"]},69758:(e,t,s)=>{Promise.resolve().then(s.bind(s,4997))},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},74486:(e,t,s)=>{Promise.resolve().then(s.bind(s,29422))},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,874,658,533,391,546],()=>s(273));module.exports=r})();