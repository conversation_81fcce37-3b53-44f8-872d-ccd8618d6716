(()=>{var e={};e.id=1,e.ids=[1],e.modules={474:(e,r,t)=>{"use strict";t.d(r,{C2:()=>c,QQ:()=>o,eg:()=>n,zi:()=>l});var a=t(75535),s=t(50944);function i(e){if(!e.exists())return null;let r=e.data();return{id:e.id,...r,createdAt:r?.createdAt?.toDate?.()||r?.createdAt,updatedAt:r?.updatedAt?.toDate?.()||r?.updatedAt}}let o={async get(e){let r=(0,a.H9)(s.db,"users",e);return i(await (0,a.x7)(r))},async create(e,r){let t=(0,a.H9)(s.db,"users",e),i=new Date;await (0,a.BN)(t,{...r,createdAt:a.Dc.fromDate(i),updatedAt:a.Dc.fromDate(i)})},async update(e,r){let t=(0,a.H9)(s.db,"users",e);await (0,a.mZ)(t,{...r,updatedAt:a.Dc.now()})}},n={async getAll(e){let r=(0,a.rJ)(s.db,"farms");return e?.isActive!==void 0&&(r=(0,a.P)(r,(0,a._M)("isActive","==",e.isActive))),e?.featured!==void 0&&(r=(0,a.P)(r,(0,a._M)("featured","==",e.featured))),e?.province&&(r=(0,a.P)(r,(0,a._M)("province","==",e.province))),e?.activityType&&(r=(0,a.P)(r,(0,a._M)("activityTypes","==",e.activityType))),r=(0,a.P)(r,(0,a.My)("createdAt","desc")),e?.limit&&(r=(0,a.P)(r,(0,a.AB)(e.limit))),(await (0,a.GG)(r)).docs.map(e=>i(e)).filter(Boolean)},async get(e){let r=(0,a.H9)(s.db,"farms",e);return i(await (0,a.x7)(r))},async getByOwner(e){let r=(0,a.P)((0,a.rJ)(s.db,"farms"),(0,a._M)("ownerId","==",e),(0,a.My)("createdAt","desc"));return(await (0,a.GG)(r)).docs.map(e=>i(e)).filter(Boolean)},async getActive(e){let r=(0,a.P)((0,a.rJ)(s.db,"farms"),(0,a._M)("isActive","==",!0),(0,a.My)("createdAt","desc"));return e&&(r=(0,a.P)(r,(0,a.AB)(e))),(await (0,a.GG)(r)).docs.map(e=>i(e)).filter(Boolean)},async create(e){let r=new Date;return(await (0,a.gS)((0,a.rJ)(s.db,"farms"),{...e,createdAt:a.Dc.fromDate(r),updatedAt:a.Dc.fromDate(r)})).id},async update(e,r){let t=(0,a.H9)(s.db,"farms",e);await (0,a.mZ)(t,{...r,updatedAt:a.Dc.now()})},async delete(e){let r=(0,a.H9)(s.db,"farms",e);await (0,a.kd)(r)},async addImage(e,r){let t=new Date;return(await (0,a.gS)((0,a.rJ)(s.db,"farms",e,"images"),{...r,createdAt:a.Dc.fromDate(t),updatedAt:a.Dc.fromDate(t)})).id},async getImages(e){let r=(0,a.P)((0,a.rJ)(s.db,"farms",e,"images"),(0,a.My)("displayOrder","asc"),(0,a.My)("createdAt","asc"));return(await (0,a.GG)(r)).docs.map(e=>i(e)).filter(Boolean)},async deleteImage(e,r){let t=(0,a.H9)(s.db,"farms",e,"images",r);await (0,a.kd)(t)}},l={async getAll(e){let r=(0,a.rJ)(s.db,"bookings");return e?.hunterId&&(r=(0,a.P)(r,(0,a._M)("hunterId","==",e.hunterId))),e?.farmId&&(r=(0,a.P)(r,(0,a._M)("farmId","==",e.farmId))),e?.status&&(r=(0,a.P)(r,(0,a._M)("status","==",e.status))),r=(0,a.P)(r,(0,a.My)("createdAt","desc")),e?.limit&&(r=(0,a.P)(r,(0,a.AB)(e.limit))),(await (0,a.GG)(r)).docs.map(e=>i(e)).filter(Boolean)},async get(e){let r=(0,a.H9)(s.db,"bookings",e);return i(await (0,a.x7)(r))},async create(e){let r=new Date,t=`BVR-${r.getFullYear()}${(r.getMonth()+1).toString().padStart(2,"0")}${r.getDate().toString().padStart(2,"0")}-${Math.random().toString(36).substring(2,8).toUpperCase()}`;return(await (0,a.gS)((0,a.rJ)(s.db,"bookings"),{...e,bookingReference:t,createdAt:a.Dc.fromDate(r),updatedAt:a.Dc.fromDate(r)})).id},async update(e,r){let t=(0,a.H9)(s.db,"bookings",e);await (0,a.mZ)(t,{...r,updatedAt:a.Dc.now()})}},c={async getByFarm(e){let r=(0,a.P)((0,a.rJ)(s.db,"farms",e,"reviews"),(0,a._M)("isPublic","==",!0),(0,a.My)("createdAt","desc"));return(await (0,a.GG)(r)).docs.map(e=>i(e)).filter(Boolean)},async create(e,r){let t=new Date;return(await (0,a.gS)((0,a.rJ)(s.db,"farms",e,"reviews"),{...r,createdAt:a.Dc.fromDate(t),updatedAt:a.Dc.fromDate(t)})).id},async update(e,r,t){let i=(0,a.H9)(s.db,"farms",e,"reviews",r);await (0,a.mZ)(i,{...t,updatedAt:a.Dc.now()})}}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28749:(e,r,t)=>{"use strict";t.d(r,{MH:()=>u,Wu:()=>l,ZB:()=>d,Zp:()=>n,aR:()=>c});var a=t(60687),s=t(43210),i=t(30474),o=t(4780);let n=(0,s.forwardRef)(({className:e,hover:r=!0,children:t,...s},i)=>(0,a.jsx)("div",{ref:i,className:(0,o.cn)(`
          bg-white rounded-[var(--radius-lg)] shadow-[var(--shadow-md)] 
          overflow-hidden transition-all duration-300
          `,r&&"hover:transform hover:-translate-y-1 hover:shadow-[var(--shadow-xl)]",e),...s,children:t}));n.displayName="Card";let l=(0,s.forwardRef)(({className:e,...r},t)=>(0,a.jsx)("div",{ref:t,className:(0,o.cn)("p-[var(--space-lg)]",e),...r}));l.displayName="CardContent";let c=(0,s.forwardRef)(({className:e,...r},t)=>(0,a.jsx)("div",{ref:t,className:(0,o.cn)("px-[var(--space-lg)] pt-[var(--space-lg)]",e),...r}));c.displayName="CardHeader";let d=(0,s.forwardRef)(({className:e,...r},t)=>(0,a.jsx)("h3",{ref:t,className:(0,o.cn)("text-xl font-bold mb-2 text-[var(--primary-brown)] font-[var(--font-display)]",e),...r}));d.displayName="CardTitle";let u=(0,s.forwardRef)(({className:e,src:r,alt:t,children:s,...n},l)=>(0,a.jsx)("div",{ref:l,className:(0,o.cn)("relative w-full h-48 bg-gradient-to-br from-[var(--primary-green)] to-[var(--secondary-sunset)] flex items-center justify-center text-white text-5xl",e),...n,children:r?(0,a.jsx)(i.default,{src:r,alt:t||"",fill:!0,className:"object-cover"}):s}));u.displayName="CardImage"},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30474:(e,r,t)=>{"use strict";t.d(r,{default:()=>s.a});var a=t(31261),s=t.n(a)},31261:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(r,{default:function(){return l},getImageProps:function(){return n}});let a=t(14985),s=t(44953),i=t(46533),o=a._(t(1933));function n(e){let{props:r}=(0,s.getImgProps)(e,{defaultLoader:o.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,t]of Object.entries(r))void 0===t&&delete r[e];return{props:r}}let l=i.Image},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},37366:e=>{"use strict";e.exports=require("dns")},49636:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>j});var a=t(60687),s=t(43210),i=t(16189),o=t(30474),n=t(57445),l=t(2643),c=t(51907),d=t(28749),u=t(70146),m=t(50944),p=t(71557);async function h(e,r,t,a,s){try{let i=p.j2.currentUser;if(!i)throw Error("User must be authenticated to upload files");let o=await i.getIdToken(),n=new FormData;n.append("file",e),n.append("bucket",r),t&&n.append("farmId",t),a&&n.append("userId",a);let l=(()=>{if(!s?.onProgress)return;let r=0,t=setInterval(()=>{if((r+=20*Math.random())>=90)return void clearInterval(t);s.onProgress?.({bytesTransferred:Math.floor(r/100*e.size),totalBytes:e.size,progress:r})},200);return t})();try{let r=await fetch(function(){let e="rvbsafaris";if(!e)throw Error("NEXT_PUBLIC_FIREBASE_PROJECT_ID environment variable is required");return`https://us-central1-${e}.cloudfunctions.net/uploadImage`}(),{method:"POST",headers:{Authorization:`Bearer ${o}`},body:n});if(l&&clearInterval(l),!r.ok){let e=await r.json().catch(()=>({}));throw Error(e.error||`Upload failed with status ${r.status}`)}let t=await r.json();if(!t.success||!t.downloadURL)throw Error(t.error||"Upload failed");return s?.onProgress?.({bytesTransferred:e.size,totalBytes:e.size,progress:100}),s?.onComplete?.(t.downloadURL),t.downloadURL}catch(r){l&&clearInterval(l);let e=r instanceof Error?r:Error("Upload failed");throw s?.onError?.(e),e}}catch(r){console.error("Error uploading file via Cloud Function:",r);let e=r instanceof Error?r:Error("Upload failed");throw s?.onError?.(e),e}}let f={async deleteFile(e){try{let r=(0,u.KR)(m.IG,e);await (0,u.XR)(r)}catch(e){throw console.error("Error deleting file:",e),e}},async getDownloadURL(e){try{let r=(0,u.KR)(m.IG,e);return await (0,u.qk)(r)}catch(e){throw console.error("Error getting download URL:",e),e}},async listFiles(e){try{let r=(0,u.KR)(m.IG,e);return(await (0,u._w)(r)).items}catch(e){throw console.error("Error listing files:",e),e}},async getMetadata(e){try{let r=(0,u.KR)(m.IG,e);return await (0,u.yb)(r)}catch(e){throw console.error("Error getting metadata:",e),e}},async updateMetadata(e,r){try{let t=(0,u.KR)(m.IG,e);return await (0,u.D5)(t,r)}catch(e){throw console.error("Error updating metadata:",e),e}}},x={uploadFarmImage:async(e,r,t)=>(console.log("Using Cloud Function for farm image upload"),await h(r,"farm-images",e,void 0,t)),async deleteFarmImage(e,r){try{let t=v(r);t&&t.startsWith(`farm-images/${e}/`)&&await f.deleteFile(t)}catch(e){throw console.error("Error deleting farm image:",e),e}},async listFarmImages(e){try{let r=await f.listFiles(`farm-images/${e}`);return await Promise.all(r.map(e=>(0,u.qk)(e)))}catch(e){throw console.error("Error listing farm images:",e),e}}},g={uploadProfileImage:async(e,r,t)=>(console.log("Using Cloud Function for profile image upload"),await h(r,"profile-images",void 0,e,t)),async deleteProfileImage(e,r){try{let t=v(r);t&&t.startsWith(`profile-images/${e}/`)&&await f.deleteFile(t)}catch(e){throw console.error("Error deleting profile image:",e),e}}};function v(e){try{let r=new URL(e);if("firebasestorage.googleapis.com"===r.hostname){let e=r.pathname.match(/\/o\/(.+)\?/);if(e)return decodeURIComponent(e[1])}return null}catch(e){return console.error("Error extracting path from URL:",e),null}}function b({currentImage:e,alt:r="Upload preview",onUpload:t,bucket:i,farmId:n,userId:l,maxSize:c=5,className:d=""}){let[u,m]=(0,s.useState)(!1),[p,h]=(0,s.useState)(0),[f,v]=(0,s.useState)(null),[b,w]=(0,s.useState)(e||null),y=(0,s.useRef)(null),j=async r=>{let a=r.target.files?.[0];if(!a)return;if(v(null),h(0),a.size>1024*c*1024)return void v(`File size must be less than ${c}MB`);if(!a.type.startsWith("image/"))return void v("Please select an image file");if("farm-images"===i&&!n)return void v("Farm ID is required for farm image uploads");if("profile-images"===i&&!l)return void v("User ID is required for profile image uploads");let s=new FileReader;s.onload=e=>{w(e.target?.result)},s.readAsDataURL(a),m(!0);try{let e;if("farm-images"===i&&n)console.log("Starting farm image upload for farmId:",n),e=await x.uploadFarmImage(n,a,{onProgress:e=>{console.log("Upload progress:",e.progress),h(e.progress)},onError:e=>{console.error("Upload error:",e),v(e.message)}});else if("profile-images"===i&&l)console.log("Starting profile image upload for userId:",l),e=await g.uploadProfileImage(l,a,{onProgress:e=>{console.log("Upload progress:",e.progress),h(e.progress)},onError:e=>{console.error("Upload error:",e),v(e.message)}});else throw Error("Invalid bucket or missing required parameters");w(e),t(e),h(100)}catch(r){console.error("Upload error:",r),v(r instanceof Error?r.message:"Upload failed"),w(e||null)}finally{m(!1),setTimeout(()=>h(0),2e3)}};return(0,a.jsxs)("div",{className:d,children:[(0,a.jsx)("input",{ref:y,type:"file",accept:"image/*",onChange:j,className:"hidden"}),(0,a.jsx)("div",{onClick:()=>{y.current?.click()},className:"relative border-2 border-dashed border-earth-300 rounded-lg p-6 hover:border-accent-600 transition-colors cursor-pointer group",children:b?(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"relative w-full h-48 rounded-lg overflow-hidden",children:(0,a.jsx)(o.default,{src:b,alt:r,fill:!0,className:"object-cover",sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"})}),(0,a.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all rounded-lg flex items-center justify-center",children:(0,a.jsx)("div",{className:"opacity-0 group-hover:opacity-100 transition-opacity",children:(0,a.jsxs)("svg",{className:"w-8 h-8 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"}),(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 13a3 3 0 11-6 0 3 3 0 016 0z"})]})})}),u&&(0,a.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-50 rounded-lg flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center text-white",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-2"}),(0,a.jsxs)("p",{className:"text-sm",children:[Math.round(p),"%"]})]})})]}):(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("svg",{className:"mx-auto h-12 w-12 text-earth-400",stroke:"currentColor",fill:"none",viewBox:"0 0 48 48",children:(0,a.jsx)("path",{d:"M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"})}),(0,a.jsxs)("div",{className:"mt-4",children:[(0,a.jsx)("p",{className:"text-sm text-earth-600",children:u?`Uploading... ${Math.round(p)}%`:"Click to upload an image"}),(0,a.jsxs)("p",{className:"text-xs text-earth-500 mt-1",children:["PNG, JPG, GIF up to ",c,"MB"]})]})]})}),f&&(0,a.jsx)("p",{className:"text-red-600 text-sm mt-2",children:f})]})}var w=t(474),y=t(66420);function j(){let{user:e,loading:r}=(0,n.As)(),t=(0,i.useRouter)(),[u,m]=(0,s.useState)(!1),[p,h]=(0,s.useState)(null),[f,x]=(0,s.useState)([]),[g,v]=(0,s.useState)({name:"",description:"",descriptionAfrikaans:"",location:"",province:"",sizeHectares:"",activityTypes:"both",contactEmail:"",contactPhone:"",websiteUrl:"",rules:"",rulesAfrikaans:"",pricingInfo:""}),j=(e,r)=>{v(t=>({...t,[e]:r})),h(null)},N=e=>{x(r=>r.filter(r=>r!==e))},k=async r=>{r.preventDefault(),m(!0),h(null);try{if(!g.name||!g.location||!g.province||!g.contactEmail){h("Please fill in all required fields"),m(!1);return}if(g.websiteUrl&&g.websiteUrl.trim()&&!/^(https?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$/.test(g.websiteUrl.trim())){h("Please enter a valid website URL"),m(!1);return}let r={ownerId:e.uid,name:g.name,description:g.description||void 0,descriptionAfrikaans:g.descriptionAfrikaans||void 0,location:g.location,province:g.province,sizeHectares:g.sizeHectares?parseInt(g.sizeHectares):void 0,activityTypes:g.activityTypes,contactEmail:g.contactEmail,contactPhone:g.contactPhone||void 0,websiteUrl:g.websiteUrl||void 0,rules:g.rules||void 0,rulesAfrikaans:g.rulesAfrikaans||void 0,pricingInfo:g.pricingInfo||void 0,isActive:!0,featured:!1},a=await w.eg.create(r);f.length>0&&console.log(`Farm created with ${f.length} images uploaded to Firebase Storage`),t.push(`/farms/${a}`)}catch(e){h("An unexpected error occurred"),console.error("Farm creation error:",e)}finally{m(!1)}};return r?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-earth-100",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-accent-600"})}):e?(0,a.jsx)("div",{className:"min-h-screen bg-earth-100 py-8",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-earth-900",children:"Create Farm Listing"}),(0,a.jsx)("p",{className:"text-earth-600 mt-2",children:"Add your game farm to BvR Safaris and start receiving bookings"})]}),(0,a.jsxs)("form",{onSubmit:k,className:"space-y-8",children:[p&&(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-md p-3",children:(0,a.jsx)("p",{className:"text-red-600 text-sm",children:p})}),(0,a.jsxs)(d.Zp,{children:[(0,a.jsx)(d.aR,{children:(0,a.jsx)(d.ZB,{children:"Basic Information"})}),(0,a.jsxs)(d.Wu,{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-earth-700 mb-2",children:"Farm Name *"}),(0,a.jsx)(c.p,{id:"name",type:"text",value:g.name,onChange:e=>j("name",e.target.value),placeholder:"Enter your farm name",required:!0})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"location",className:"block text-sm font-medium text-earth-700 mb-2",children:"Location/City *"}),(0,a.jsx)(c.p,{id:"location",type:"text",value:g.location,onChange:e=>j("location",e.target.value),placeholder:"e.g., Lephalale",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"province",className:"block text-sm font-medium text-earth-700 mb-2",children:"Province *"}),(0,a.jsxs)("select",{id:"province",value:g.province,onChange:e=>j("province",e.target.value),className:"w-full px-3 py-2 border border-earth-300 rounded-md focus:outline-none focus:ring-2 focus:ring-accent-600 focus:border-transparent",required:!0,children:[(0,a.jsx)("option",{value:"",children:"Select Province"}),y.L0.map(e=>(0,a.jsx)("option",{value:e,children:e},e))]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"sizeHectares",className:"block text-sm font-medium text-earth-700 mb-2",children:"Size (Hectares)"}),(0,a.jsx)(c.p,{id:"sizeHectares",type:"number",value:g.sizeHectares,onChange:e=>j("sizeHectares",e.target.value),placeholder:"e.g., 5000"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"activityTypes",className:"block text-sm font-medium text-earth-700 mb-2",children:"Activity Types *"}),(0,a.jsxs)("select",{id:"activityTypes",value:g.activityTypes,onChange:e=>j("activityTypes",e.target.value),className:"w-full px-3 py-2 border border-earth-300 rounded-md focus:outline-none focus:ring-2 focus:ring-accent-600 focus:border-transparent",required:!0,children:[(0,a.jsx)("option",{value:"hunting",children:"Hunting Only"}),(0,a.jsx)("option",{value:"photo_safari",children:"Photo Safari Only"}),(0,a.jsx)("option",{value:"both",children:"Both Hunting & Photo Safari"})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"description",className:"block text-sm font-medium text-earth-700 mb-2",children:"Description (English)"}),(0,a.jsx)("textarea",{id:"description",value:g.description,onChange:e=>j("description",e.target.value),placeholder:"Describe your farm, facilities, and what makes it special...",rows:4,className:"w-full px-3 py-2 border border-earth-300 rounded-md focus:outline-none focus:ring-2 focus:ring-accent-600 focus:border-transparent"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"descriptionAfrikaans",className:"block text-sm font-medium text-earth-700 mb-2",children:"Description (Afrikaans)"}),(0,a.jsx)("textarea",{id:"descriptionAfrikaans",value:g.descriptionAfrikaans,onChange:e=>j("descriptionAfrikaans",e.target.value),placeholder:"Beskryf jou plaas, fasiliteite, en wat dit spesiaal maak...",rows:4,className:"w-full px-3 py-2 border border-earth-300 rounded-md focus:outline-none focus:ring-2 focus:ring-accent-600 focus:border-transparent"})]})]})]}),(0,a.jsxs)(d.Zp,{children:[(0,a.jsx)(d.aR,{children:(0,a.jsx)(d.ZB,{children:"Contact Information"})}),(0,a.jsxs)(d.Wu,{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"contactEmail",className:"block text-sm font-medium text-earth-700 mb-2",children:"Contact Email *"}),(0,a.jsx)(c.p,{id:"contactEmail",type:"email",value:g.contactEmail,onChange:e=>j("contactEmail",e.target.value),placeholder:"<EMAIL>",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"contactPhone",className:"block text-sm font-medium text-earth-700 mb-2",children:"Contact Phone"}),(0,a.jsx)(c.p,{id:"contactPhone",type:"tel",value:g.contactPhone,onChange:e=>j("contactPhone",e.target.value),placeholder:"+27 12 345 6789"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"websiteUrl",className:"block text-sm font-medium text-earth-700 mb-2",children:"Website URL"}),(0,a.jsx)(c.p,{id:"websiteUrl",type:"text",value:g.websiteUrl,onChange:e=>j("websiteUrl",e.target.value),placeholder:"www.yourfarm.com"})]})]})]}),(0,a.jsxs)(d.Zp,{children:[(0,a.jsx)(d.aR,{children:(0,a.jsx)(d.ZB,{children:"Additional Information"})}),(0,a.jsxs)(d.Wu,{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"pricingInfo",className:"block text-sm font-medium text-earth-700 mb-2",children:"Pricing Information"}),(0,a.jsx)("textarea",{id:"pricingInfo",value:g.pricingInfo,onChange:e=>j("pricingInfo",e.target.value),placeholder:"Provide general pricing information or mention 'Contact for pricing'...",rows:3,className:"w-full px-3 py-2 border border-earth-300 rounded-md focus:outline-none focus:ring-2 focus:ring-accent-600 focus:border-transparent"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"rules",className:"block text-sm font-medium text-earth-700 mb-2",children:"Rules & Regulations (English)"}),(0,a.jsx)("textarea",{id:"rules",value:g.rules,onChange:e=>j("rules",e.target.value),placeholder:"List important rules, safety requirements, what to bring, etc...",rows:4,className:"w-full px-3 py-2 border border-earth-300 rounded-md focus:outline-none focus:ring-2 focus:ring-accent-600 focus:border-transparent"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"rulesAfrikaans",className:"block text-sm font-medium text-earth-700 mb-2",children:"Rules & Regulations (Afrikaans)"}),(0,a.jsx)("textarea",{id:"rulesAfrikaans",value:g.rulesAfrikaans,onChange:e=>j("rulesAfrikaans",e.target.value),placeholder:"Lys belangrike re\xebls, veiligheidsvereistes, wat om te bring, ens...",rows:4,className:"w-full px-3 py-2 border border-earth-300 rounded-md focus:outline-none focus:ring-2 focus:ring-accent-600 focus:border-transparent"})]})]})]}),(0,a.jsxs)(d.Zp,{children:[(0,a.jsx)(d.aR,{children:(0,a.jsx)(d.ZB,{children:"Farm Images"})}),(0,a.jsx)(d.Wu,{className:"space-y-6",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-earth-700 mb-4",children:"Upload Farm Photos"}),(0,a.jsx)("p",{className:"text-sm text-earth-600 mb-4",children:"Add photos of your farm, facilities, wildlife, and accommodations. The first image will be used as the main photo."}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[f.map((e,r)=>(0,a.jsxs)("div",{className:"relative group",children:[(0,a.jsx)("div",{className:"relative w-full h-48 rounded-lg border overflow-hidden",children:(0,a.jsx)(o.default,{src:e,alt:`Farm image ${r+1}`,fill:!0,className:"object-cover",sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"})}),(0,a.jsx)("div",{className:"absolute top-2 left-2",children:0===r&&(0,a.jsx)("span",{className:"bg-accent-600 text-white text-xs px-2 py-1 rounded",children:"Main Photo"})}),(0,a.jsx)("button",{type:"button",onClick:()=>N(e),className:"absolute top-2 right-2 bg-red-600 text-white rounded-full w-6 h-6 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity",children:"\xd7"})]},e)),f.length<10&&e&&(0,a.jsx)(b,{bucket:"farm-images",farmId:e.uid,onUpload:e=>{x(r=>[...r,e])},maxSize:10,className:"h-48"})]}),f.length>=10&&(0,a.jsx)("p",{className:"text-sm text-earth-500 mt-2",children:"Maximum of 10 images allowed. Remove an image to add more."})]})})]}),(0,a.jsxs)("div",{className:"flex gap-4",children:[(0,a.jsx)(l.$,{type:"submit",variant:"primary",isLoading:u,className:"flex-1",children:u?"Creating Farm...":"Create Farm Listing"}),(0,a.jsx)(l.$,{type:"button",variant:"outline",onClick:()=>t.push("/dashboard"),className:"flex-1",children:"Cancel"})]})]})]})}):(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-earth-100",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-earth-600 mb-4",children:"Please log in to create a farm listing."}),(0,a.jsx)(l.$,{onClick:()=>t.push("/auth/login"),children:"Go to Login"})]})})}},51907:(e,r,t)=>{"use strict";t.d(r,{p:()=>o});var a=t(60687),s=t(43210),i=t(4780);let o=(0,s.forwardRef)(({className:e,type:r,label:t,error:s,...o},n)=>(0,a.jsxs)("div",{className:"w-full",children:[t&&(0,a.jsx)("label",{className:"block mb-2 font-semibold text-[var(--primary-brown)] font-[var(--font-ui)]",children:t}),(0,a.jsx)("input",{type:r,className:(0,i.cn)(`
            w-full px-4 py-2 border-2 border-[var(--medium-gray)] 
            rounded-[var(--radius-md)] font-[var(--font-ui)] 
            transition-colors duration-300
            focus:outline-none focus:border-[var(--primary-brown)] 
            focus:ring-0 focus:shadow-[0_0_0_3px_rgba(139,69,19,0.1)]
            placeholder:text-[var(--medium-gray)]
            `,s&&"border-red-500 focus:border-red-500",e),ref:n,...o}),s&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600",children:s})]}));o.displayName="Input"},55511:e=>{"use strict";e.exports=require("crypto")},58637:(e,r,t)=>{Promise.resolve().then(t.bind(t,49636))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66420:(e,r,t)=>{"use strict";t.d(r,{L0:()=>a,QC:()=>i,lc:()=>s});let a=["Eastern Cape","Free State","Gauteng","KwaZulu-Natal","Limpopo","Mpumalanga","Northern Cape","North West","Western Cape"],s=["Luxury Lodge","Basic Accommodation","Restaurant","Bar","Swimming Pool","Spa","WiFi","Airport Transfer","Professional Guide","Trophy Preparation","Taxidermy"],i=["Lion","Leopard","Elephant","Buffalo","Rhino","Kudu","Impala","Springbok","Eland","Sable","Gemsbok","Waterbuck","Bushbuck","Warthog"]},67869:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/src/personal/bvr-safaris/src/app/farms/create/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/src/personal/bvr-safaris/src/app/farms/create/page.tsx","default")},70440:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});var a=t(31658);let s=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83639:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c});var a=t(65239),s=t(48088),i=t(88170),o=t.n(i),n=t(30893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);t.d(r,l);let c={children:["",{children:["farms",{children:["create",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,67869)),"/Users/<USER>/src/personal/bvr-safaris/src/app/farms/create/page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,82893)),"/Users/<USER>/src/personal/bvr-safaris/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["/Users/<USER>/src/personal/bvr-safaris/src/app/farms/create/page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/farms/create/page",pathname:"/farms/create",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},95021:(e,r,t)=>{Promise.resolve().then(t.bind(t,67869))}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[447,874,658,533,391],()=>t(83639));module.exports=a})();