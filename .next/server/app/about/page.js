(()=>{var e={};e.id=220,e.ids=[220],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28770:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});var s=t(37413);function a(){return(0,s.jsx)("div",{className:"min-h-screen bg-earth-100 py-16",children:(0,s.jsxs)("div",{className:"container mx-auto px-4 max-w-3xl",children:[(0,s.jsx)("h1",{className:"text-4xl md:text-5xl font-bold mb-6 text-[var(--primary-brown)] font-[var(--font-display)]",children:"About BvR Safaris"}),(0,s.jsx)("p",{className:"text-xl text-earth-700 mb-8",children:"BvR Safaris is South Africa's premier platform for booking authentic hunting and photo safari experiences. We connect adventurers and nature lovers with certified game farms, luxury lodges, and unforgettable wilderness experiences."}),(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-[var(--shadow-md)] p-8 mb-8",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold mb-4 text-[var(--primary-green)]",children:"Our Mission"}),(0,s.jsx)("p",{className:"text-earth-700 mb-4",children:"To make Africa's wild beauty accessible to all, while supporting ethical tourism, conservation, and local communities. We believe in responsible travel that benefits both people and wildlife."}),(0,s.jsx)("h2",{className:"text-2xl font-bold mb-4 text-[var(--primary-green)]",children:"Our Vision"}),(0,s.jsx)("p",{className:"text-earth-700",children:"To be the trusted bridge between travelers and Africa's most remarkable safari destinations, fostering a love for nature and a commitment to sustainable adventure."})]}),(0,s.jsxs)("div",{className:"text-earth-600 text-sm",children:["\xa9 ",new Date().getFullYear()," BvR Safaris. All rights reserved."]})]})})}t(61120)},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},37366:e=>{"use strict";e.exports=require("dns")},47997:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>p,pages:()=>u,routeModule:()=>c,tree:()=>d});var s=t(65239),a=t(48088),i=t(88170),n=t.n(i),o=t(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);t.d(r,l);let d={children:["",{children:["about",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,28770)),"/Users/<USER>/src/personal/bvr-safaris/src/app/about/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,82893)),"/Users/<USER>/src/personal/bvr-safaris/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,u=["/Users/<USER>/src/personal/bvr-safaris/src/app/about/page.tsx"],p={require:t,loadChunk:()=>Promise.resolve()},c=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/about/page",pathname:"/about",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},55511:e=>{"use strict";e.exports=require("crypto")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});var s=t(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,874,658,391],()=>t(47997));module.exports=s})();