(()=>{var e={};e.id=274,e.ids=[274],e.modules={474:(e,t,r)=>{"use strict";r.d(t,{C2:()=>c,QQ:()=>n,eg:()=>l,zi:()=>d});var a=r(75535),s=r(50944);function i(e){if(!e.exists())return null;let t=e.data();return{id:e.id,...t,createdAt:t?.createdAt?.toDate?.()||t?.createdAt,updatedAt:t?.updatedAt?.toDate?.()||t?.updatedAt}}let n={async get(e){let t=(0,a.H9)(s.db,"users",e);return i(await (0,a.x7)(t))},async create(e,t){let r=(0,a.H9)(s.db,"users",e),i=new Date;await (0,a.BN)(r,{...t,createdAt:a.Dc.fromDate(i),updatedAt:a.Dc.fromDate(i)})},async update(e,t){let r=(0,a.H9)(s.db,"users",e);await (0,a.mZ)(r,{...t,updatedAt:a.Dc.now()})}},l={async getAll(e){let t=(0,a.rJ)(s.db,"farms");return e?.isActive!==void 0&&(t=(0,a.P)(t,(0,a._M)("isActive","==",e.isActive))),e?.featured!==void 0&&(t=(0,a.P)(t,(0,a._M)("featured","==",e.featured))),e?.province&&(t=(0,a.P)(t,(0,a._M)("province","==",e.province))),e?.activityType&&(t=(0,a.P)(t,(0,a._M)("activityTypes","==",e.activityType))),t=(0,a.P)(t,(0,a.My)("createdAt","desc")),e?.limit&&(t=(0,a.P)(t,(0,a.AB)(e.limit))),(await (0,a.GG)(t)).docs.map(e=>i(e)).filter(Boolean)},async get(e){let t=(0,a.H9)(s.db,"farms",e);return i(await (0,a.x7)(t))},async getByOwner(e){let t=(0,a.P)((0,a.rJ)(s.db,"farms"),(0,a._M)("ownerId","==",e),(0,a.My)("createdAt","desc"));return(await (0,a.GG)(t)).docs.map(e=>i(e)).filter(Boolean)},async getActive(e){let t=(0,a.P)((0,a.rJ)(s.db,"farms"),(0,a._M)("isActive","==",!0),(0,a.My)("createdAt","desc"));return e&&(t=(0,a.P)(t,(0,a.AB)(e))),(await (0,a.GG)(t)).docs.map(e=>i(e)).filter(Boolean)},async create(e){let t=new Date;return(await (0,a.gS)((0,a.rJ)(s.db,"farms"),{...e,createdAt:a.Dc.fromDate(t),updatedAt:a.Dc.fromDate(t)})).id},async update(e,t){let r=(0,a.H9)(s.db,"farms",e);await (0,a.mZ)(r,{...t,updatedAt:a.Dc.now()})},async delete(e){let t=(0,a.H9)(s.db,"farms",e);await (0,a.kd)(t)},async addImage(e,t){let r=new Date;return(await (0,a.gS)((0,a.rJ)(s.db,"farms",e,"images"),{...t,createdAt:a.Dc.fromDate(r),updatedAt:a.Dc.fromDate(r)})).id},async getImages(e){let t=(0,a.P)((0,a.rJ)(s.db,"farms",e,"images"),(0,a.My)("displayOrder","asc"),(0,a.My)("createdAt","asc"));return(await (0,a.GG)(t)).docs.map(e=>i(e)).filter(Boolean)},async deleteImage(e,t){let r=(0,a.H9)(s.db,"farms",e,"images",t);await (0,a.kd)(r)}},d={async getAll(e){let t=(0,a.rJ)(s.db,"bookings");return e?.hunterId&&(t=(0,a.P)(t,(0,a._M)("hunterId","==",e.hunterId))),e?.farmId&&(t=(0,a.P)(t,(0,a._M)("farmId","==",e.farmId))),e?.status&&(t=(0,a.P)(t,(0,a._M)("status","==",e.status))),t=(0,a.P)(t,(0,a.My)("createdAt","desc")),e?.limit&&(t=(0,a.P)(t,(0,a.AB)(e.limit))),(await (0,a.GG)(t)).docs.map(e=>i(e)).filter(Boolean)},async get(e){let t=(0,a.H9)(s.db,"bookings",e);return i(await (0,a.x7)(t))},async create(e){let t=new Date,r=`BVR-${t.getFullYear()}${(t.getMonth()+1).toString().padStart(2,"0")}${t.getDate().toString().padStart(2,"0")}-${Math.random().toString(36).substring(2,8).toUpperCase()}`;return(await (0,a.gS)((0,a.rJ)(s.db,"bookings"),{...e,bookingReference:r,createdAt:a.Dc.fromDate(t),updatedAt:a.Dc.fromDate(t)})).id},async update(e,t){let r=(0,a.H9)(s.db,"bookings",e);await (0,a.mZ)(r,{...t,updatedAt:a.Dc.now()})}},c={async getByFarm(e){let t=(0,a.P)((0,a.rJ)(s.db,"farms",e,"reviews"),(0,a._M)("isPublic","==",!0),(0,a.My)("createdAt","desc"));return(await (0,a.GG)(t)).docs.map(e=>i(e)).filter(Boolean)},async create(e,t){let r=new Date;return(await (0,a.gS)((0,a.rJ)(s.db,"farms",e,"reviews"),{...t,createdAt:a.Dc.fromDate(r),updatedAt:a.Dc.fromDate(r)})).id},async update(e,t,r){let i=(0,a.H9)(s.db,"farms",e,"reviews",t);await (0,a.mZ)(i,{...r,updatedAt:a.Dc.now()})}}},2830:(e,t,r)=>{Promise.resolve().then(r.bind(r,13875))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},9776:(e,t,r)=>{"use strict";r.d(t,{k:()=>i});var a=r(60687),s=r(4780);function i({size:e="md",className:t}){return(0,a.jsx)("div",{className:(0,s.cn)("flex items-center justify-center",t),children:(0,a.jsx)("div",{className:(0,s.cn)("animate-spin rounded-full border-2 border-white border-t-transparent",{sm:"w-4 h-4",md:"w-6 h-6",lg:"w-12 h-12"}[e])})})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13875:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>b});var a=r(60687),s=r(43210),i=r(16189),n=r(57445),l=r(2643),d=r(28749),c=r(9776);r(474);var o=r(5336),m=r(62688);let u=(0,m.A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]),p=(0,m.A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]);var x=r(48730),h=r(28559),f=r(40228),g=r(97992),y=r(85814),v=r.n(y);function b(){let{user:e,userProfile:t,loading:r}=(0,n.As)();(0,i.useRouter)();let[m,y]=(0,s.useState)([]),[b,w]=(0,s.useState)(!0),[j,A]=(0,s.useState)(null),N=e=>{switch(e){case"confirmed":return(0,a.jsx)(o.A,{className:"w-5 h-5 text-green-600"});case"pending":return(0,a.jsx)(u,{className:"w-5 h-5 text-yellow-600"});case"cancelled":return(0,a.jsx)(p,{className:"w-5 h-5 text-red-600"});case"completed":return(0,a.jsx)(o.A,{className:"w-5 h-5 text-blue-600"});default:return(0,a.jsx)(x.A,{className:"w-5 h-5 text-gray-600"})}},k=e=>{switch(e){case"confirmed":return"bg-green-100 text-green-800";case"pending":return"bg-yellow-100 text-yellow-800";case"cancelled":return"bg-red-100 text-red-800";case"completed":return"bg-blue-100 text-blue-800";default:return"bg-gray-100 text-gray-800"}};return r||b?(0,a.jsx)("div",{className:"min-h-screen bg-earth-100 flex items-center justify-center",children:(0,a.jsx)(c.k,{size:"lg"})}):e?(0,a.jsx)("div",{className:"min-h-screen bg-earth-100 py-8",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("div",{className:"flex items-center gap-4 mb-4",children:(0,a.jsx)(v(),{href:"/dashboard",children:(0,a.jsxs)(l.$,{variant:"outline",size:"sm",children:[(0,a.jsx)(h.A,{className:"w-4 h-4 mr-2"}),"Back to Dashboard"]})})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-earth-900",children:t?.role==="farm_owner"?"Booking Requests":"My Bookings"}),(0,a.jsx)("p",{className:"text-earth-600 mt-2",children:t?.role==="farm_owner"?"Manage booking requests for your farms":"View and manage your safari bookings"})]})]}),j&&(0,a.jsx)("div",{className:"mb-6 bg-red-50 border border-red-200 rounded-md p-4",children:(0,a.jsx)("p",{className:"text-red-600",children:j})}),0===m.length?(0,a.jsx)(d.Zp,{className:"p-8",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(f.A,{className:"w-16 h-16 text-earth-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-earth-900 mb-2",children:t?.role==="farm_owner"?"No booking requests yet":"No bookings yet"}),(0,a.jsx)("p",{className:"text-earth-600 mb-6",children:t?.role==="farm_owner"?"When guests book your farms, their requests will appear here.":"Start exploring our amazing farms and book your next safari adventure."}),t?.role!=="farm_owner"&&(0,a.jsx)(v(),{href:"/farms",children:(0,a.jsx)(l.$,{variant:"primary",children:"Browse Farms"})})]})}):(0,a.jsx)("div",{className:"space-y-6",children:m.map(e=>(0,a.jsx)(d.Zp,{children:(0,a.jsx)(d.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-3",children:[N(e.status),(0,a.jsx)("h3",{className:"text-lg font-semibold text-earth-900",children:e.farm?.name||"Unknown Farm"}),(0,a.jsx)("span",{className:`inline-flex px-2 py-1 text-xs font-medium rounded-full ${k(e.status)}`,children:e.status.charAt(0).toUpperCase()+e.status.slice(1)})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm text-earth-600",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(f.A,{className:"w-4 h-4"}),(0,a.jsxs)("span",{children:[new Date(e.startDate).toLocaleDateString()," - ",new Date(e.endDate).toLocaleDateString()]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(x.A,{className:"w-4 h-4"}),(0,a.jsxs)("span",{children:[e.durationDays," days"]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(g.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:e.farm?.location||"Unknown location"})]}),(0,a.jsx)("div",{children:(0,a.jsxs)("span",{className:"font-medium",children:[e.groupSize," ",1===e.groupSize?"guest":"guests"]})})]}),(0,a.jsxs)("div",{className:"mt-3 flex items-center gap-4",children:[(0,a.jsxs)("span",{className:"text-sm text-earth-600",children:["Activity: ",(0,a.jsx)("span",{className:"font-medium capitalize",children:e.activityType.replace("_"," ")})]}),(0,a.jsxs)("span",{className:"text-sm text-earth-600",children:["Booking ID: ",(0,a.jsx)("span",{className:"font-mono",children:e.bookingReference})]})]}),e.specialRequests&&(0,a.jsx)("div",{className:"mt-3",children:(0,a.jsxs)("p",{className:"text-sm text-earth-600",children:[(0,a.jsx)("span",{className:"font-medium",children:"Special Requests:"})," ",e.specialRequests]})})]}),(0,a.jsxs)("div",{className:"text-right ml-6",children:[e.totalPrice&&(0,a.jsxs)("p",{className:"text-xl font-bold text-earth-900 mb-2",children:["R",e.totalPrice.toLocaleString()]}),(0,a.jsxs)("p",{className:"text-sm text-earth-600 mb-3",children:["Booked on ",e.createdAt.toDate().toLocaleDateString()]}),t?.role==="farm_owner"&&"pending"===e.status&&(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(l.$,{variant:"primary",size:"sm",className:"w-full",children:"Accept"}),(0,a.jsx)(l.$,{variant:"outline",size:"sm",className:"w-full",children:"Decline"})]}),e.farm&&(0,a.jsx)(v(),{href:`/farms/${e.farm.id}`,children:(0,a.jsx)(l.$,{variant:"outline",size:"sm",className:"w-full mt-2",children:"View Farm"})})]})]})})},e.id))})]})}):null}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28559:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},28749:(e,t,r)=>{"use strict";r.d(t,{MH:()=>m,Wu:()=>d,ZB:()=>o,Zp:()=>l,aR:()=>c});var a=r(60687),s=r(43210),i=r(30474),n=r(4780);let l=(0,s.forwardRef)(({className:e,hover:t=!0,children:r,...s},i)=>(0,a.jsx)("div",{ref:i,className:(0,n.cn)(`
          bg-white rounded-[var(--radius-lg)] shadow-[var(--shadow-md)] 
          overflow-hidden transition-all duration-300
          `,t&&"hover:transform hover:-translate-y-1 hover:shadow-[var(--shadow-xl)]",e),...s,children:r}));l.displayName="Card";let d=(0,s.forwardRef)(({className:e,...t},r)=>(0,a.jsx)("div",{ref:r,className:(0,n.cn)("p-[var(--space-lg)]",e),...t}));d.displayName="CardContent";let c=(0,s.forwardRef)(({className:e,...t},r)=>(0,a.jsx)("div",{ref:r,className:(0,n.cn)("px-[var(--space-lg)] pt-[var(--space-lg)]",e),...t}));c.displayName="CardHeader";let o=(0,s.forwardRef)(({className:e,...t},r)=>(0,a.jsx)("h3",{ref:r,className:(0,n.cn)("text-xl font-bold mb-2 text-[var(--primary-brown)] font-[var(--font-display)]",e),...t}));o.displayName="CardTitle";let m=(0,s.forwardRef)(({className:e,src:t,alt:r,children:s,...l},d)=>(0,a.jsx)("div",{ref:d,className:(0,n.cn)("relative w-full h-48 bg-gradient-to-br from-[var(--primary-green)] to-[var(--secondary-sunset)] flex items-center justify-center text-white text-5xl",e),...l,children:t?(0,a.jsx)(i.default,{src:t,alt:r||"",fill:!0,className:"object-cover"}):s}));m.displayName="CardImage"},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30474:(e,t,r)=>{"use strict";r.d(t,{default:()=>s.a});var a=r(31261),s=r.n(a)},31261:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return d},getImageProps:function(){return l}});let a=r(14985),s=r(44953),i=r(46533),n=a._(r(1933));function l(e){let{props:t}=(0,s.getImgProps)(e,{defaultLoader:n.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,r]of Object.entries(t))void 0===r&&delete t[e];return{props:t}}let d=i.Image},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},37366:e=>{"use strict";e.exports=require("dns")},40228:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},41470:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/src/personal/bvr-safaris/src/app/dashboard/bookings/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/src/personal/bvr-safaris/src/app/dashboard/bookings/page.tsx","default")},42150:(e,t,r)=>{Promise.resolve().then(r.bind(r,41470))},48730:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},55511:e=>{"use strict";e.exports=require("crypto")},62688:(e,t,r)=>{"use strict";r.d(t,{A:()=>m});var a=r(43210);let s=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),n=e=>{let t=i(e);return t.charAt(0).toUpperCase()+t.slice(1)},l=(...e)=>e.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim(),d=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let o=(0,a.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:s,className:i="",children:n,iconNode:o,...m},u)=>(0,a.createElement)("svg",{ref:u,...c,width:t,height:t,stroke:e,strokeWidth:s?24*Number(r)/Number(t):r,className:l("lucide",i),...!n&&!d(m)&&{"aria-hidden":"true"},...m},[...o.map(([e,t])=>(0,a.createElement)(e,t)),...Array.isArray(n)?n:[n]])),m=(e,t)=>{let r=(0,a.forwardRef)(({className:r,...i},d)=>(0,a.createElement)(o,{ref:d,iconNode:t,className:l(`lucide-${s(n(e))}`,`lucide-${e}`,r),...i}));return r.displayName=n(e),r}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});var a=r(31658);let s=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},86761:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>o,routeModule:()=>u,tree:()=>c});var a=r(65239),s=r(48088),i=r(88170),n=r.n(i),l=r(30893),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);r.d(t,d);let c={children:["",{children:["dashboard",{children:["bookings",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,41470)),"/Users/<USER>/src/personal/bvr-safaris/src/app/dashboard/bookings/page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,82893)),"/Users/<USER>/src/personal/bvr-safaris/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["/Users/<USER>/src/personal/bvr-safaris/src/app/dashboard/bookings/page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/dashboard/bookings/page",pathname:"/dashboard/bookings",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},97992:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[447,874,658,533,391],()=>r(86761));module.exports=a})();