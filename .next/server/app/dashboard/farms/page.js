(()=>{var e={};e.id=23,e.ids=[23],e.modules={2295:(e,s,r)=>{Promise.resolve().then(r.bind(r,71409))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28559:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},37366:e=>{"use strict";e.exports=require("dns")},54823:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>n.a,__next_app__:()=>o,pages:()=>x,routeModule:()=>m,tree:()=>d});var t=r(65239),a=r(48088),i=r(88170),n=r.n(i),l=r(30893),c={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);r.d(s,c);let d={children:["",{children:["dashboard",{children:["farms",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,71409)),"/Users/<USER>/src/personal/bvr-safaris/src/app/dashboard/farms/page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,82893)),"/Users/<USER>/src/personal/bvr-safaris/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,x=["/Users/<USER>/src/personal/bvr-safaris/src/app/dashboard/farms/page.tsx"],o={require:r,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/farms/page",pathname:"/dashboard/farms",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},55439:(e,s,r)=>{Promise.resolve().then(r.bind(r,98911))},55511:e=>{"use strict";e.exports=require("crypto")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},71409:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/src/personal/bvr-safaris/src/app/dashboard/farms/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/src/personal/bvr-safaris/src/app/dashboard/farms/page.tsx","default")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},98911:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>b});var t=r(60687),a=r(43210),i=r(16189),n=r(57445),l=r(2643),c=r(28749),d=r(9776);r(474);var x=r(28559),o=r(96474),m=r(97992),h=r(13861),p=r(63143),u=r(53411),f=r(41312),j=r(64398),v=r(85814),N=r.n(v);function b(){let{user:e,userProfile:s,loading:r}=(0,n.As)(),v=(0,i.useRouter)(),[b,g]=(0,a.useState)([]),[y,w]=(0,a.useState)(!0),[A,q]=(0,a.useState)(null);return r||y?(0,t.jsx)("div",{className:"min-h-screen bg-earth-100 flex items-center justify-center",children:(0,t.jsx)(d.k,{size:"lg"})}):e?s?.role!=="farm_owner"?(v.push("/dashboard"),null):(0,t.jsx)("div",{className:"min-h-screen bg-earth-100 py-8",children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsx)("div",{className:"flex items-center gap-4 mb-4",children:(0,t.jsx)(N(),{href:"/dashboard",children:(0,t.jsxs)(l.$,{variant:"outline",size:"sm",children:[(0,t.jsx)(x.A,{className:"w-4 h-4 mr-2"}),"Back to Dashboard"]})})}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-earth-900",children:"My Farms"}),(0,t.jsx)("p",{className:"text-earth-600 mt-2",children:"Manage your farm listings and track their performance"})]}),(0,t.jsx)(N(),{href:"/farms/create",children:(0,t.jsxs)(l.$,{variant:"primary",className:"flex items-center gap-2",children:[(0,t.jsx)(o.A,{className:"w-4 h-4"}),"Add New Farm"]})})]})]}),A&&(0,t.jsx)("div",{className:"mb-6 bg-red-50 border border-red-200 rounded-md p-4",children:(0,t.jsx)("p",{className:"text-red-600",children:A})}),0===b.length?(0,t.jsx)(c.Zp,{className:"p-8",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)(m.A,{className:"w-16 h-16 text-earth-400 mx-auto mb-4"}),(0,t.jsx)("h3",{className:"text-xl font-semibold text-earth-900 mb-2",children:"No farms yet"}),(0,t.jsx)("p",{className:"text-earth-600 mb-6",children:"Create your first farm listing to start receiving bookings from safari enthusiasts."}),(0,t.jsx)(N(),{href:"/farms/create",children:(0,t.jsxs)(l.$,{variant:"primary",className:"flex items-center gap-2",children:[(0,t.jsx)(o.A,{className:"w-4 h-4"}),"Create Your First Farm"]})})]})}):(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:b.map(e=>(0,t.jsx)(c.Zp,{className:"overflow-hidden",children:(0,t.jsxs)(c.Wu,{className:"p-0",children:[(0,t.jsx)("div",{className:"h-48 bg-gradient-to-br from-earth-200 to-earth-300 flex items-center justify-center",children:(0,t.jsx)(m.A,{className:"w-12 h-12 text-earth-600"})}),(0,t.jsxs)("div",{className:"p-6",children:[(0,t.jsxs)("div",{className:"flex items-start justify-between mb-3",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-earth-900 mb-1",children:e.name}),(0,t.jsxs)("p",{className:"text-sm text-earth-600 flex items-center gap-1",children:[(0,t.jsx)(m.A,{className:"w-3 h-3"}),e.location,", ",e.province]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsx)("div",{className:`w-2 h-2 rounded-full ${e.isActive?"bg-green-500":"bg-red-500"}`}),(0,t.jsx)("span",{className:"text-xs text-earth-600",children:e.isActive?"Active":"Inactive"})]})]}),(0,t.jsxs)("div",{className:"space-y-2 mb-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,t.jsx)("span",{className:"text-earth-600",children:"Activity Type:"}),(0,t.jsx)("span",{className:"font-medium capitalize text-earth-900",children:e.activityTypes.replace("_"," ")})]}),e.sizeHectares&&(0,t.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,t.jsx)("span",{className:"text-earth-600",children:"Size:"}),(0,t.jsxs)("span",{className:"font-medium text-earth-900",children:[e.sizeHectares.toLocaleString()," hectares"]})]}),e.pricePerDay&&(0,t.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,t.jsx)("span",{className:"text-earth-600",children:"Price:"}),(0,t.jsxs)("span",{className:"font-medium text-earth-900",children:["R",e.pricePerDay.toLocaleString(),"/day"]})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[(0,t.jsx)(N(),{href:`/farms/${e.id}`,children:(0,t.jsxs)(l.$,{variant:"outline",size:"sm",className:"w-full flex items-center gap-1",children:[(0,t.jsx)(h.A,{className:"w-3 h-3"}),"View"]})}),(0,t.jsx)(N(),{href:`/farms/${e.id}/edit`,children:(0,t.jsxs)(l.$,{variant:"outline",size:"sm",className:"w-full flex items-center gap-1",children:[(0,t.jsx)(p.A,{className:"w-3 h-3"}),"Edit"]})})]}),(0,t.jsx)("div",{className:"mt-2",children:(0,t.jsx)(N(),{href:`/farms/${e.id}/analytics`,children:(0,t.jsxs)(l.$,{variant:"primary",size:"sm",className:"w-full flex items-center gap-1",children:[(0,t.jsx)(u.A,{className:"w-3 h-3"}),"Analytics"]})})}),(0,t.jsx)("div",{className:"mt-4 pt-4 border-t border-earth-200",children:(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-center",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"flex items-center justify-center gap-1 text-earth-600 mb-1",children:[(0,t.jsx)(f.A,{className:"w-3 h-3"}),(0,t.jsx)("span",{className:"text-xs",children:"Bookings"})]}),(0,t.jsx)("p",{className:"text-sm font-semibold text-earth-900",children:"0"})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"flex items-center justify-center gap-1 text-earth-600 mb-1",children:[(0,t.jsx)(j.A,{className:"w-3 h-3"}),(0,t.jsx)("span",{className:"text-xs",children:"Rating"})]}),(0,t.jsx)("p",{className:"text-sm font-semibold text-earth-900",children:"N/A"})]})]})})]})]})},e.id))}),b.length>0&&(0,t.jsxs)("div",{className:"mt-8 grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,t.jsx)(c.Zp,{className:"p-6",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("p",{className:"text-2xl font-bold text-earth-900",children:b.length}),(0,t.jsx)("p",{className:"text-earth-600 text-sm",children:"Total Farms"})]})}),(0,t.jsx)(c.Zp,{className:"p-6",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("p",{className:"text-2xl font-bold text-earth-900",children:b.filter(e=>e.isActive).length}),(0,t.jsx)("p",{className:"text-earth-600 text-sm",children:"Active Listings"})]})}),(0,t.jsx)(c.Zp,{className:"p-6",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("p",{className:"text-2xl font-bold text-earth-900",children:b.filter(e=>e.featured).length}),(0,t.jsx)("p",{className:"text-earth-600 text-sm",children:"Featured Farms"})]})})]})]})}):null}}};var s=require("../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[447,874,658,533,391,574],()=>r(54823));module.exports=t})();