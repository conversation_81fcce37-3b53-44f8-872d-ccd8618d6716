(()=>{var e={};e.id=105,e.ids=[105],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},6765:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>E});var r=t(60687),a=t(57445),i=t(43210),l=t(2643),n=t(28749),c=t(9776),d=t(474),o=t(96474),x=t(97992),m=t(40228),h=t(41312),u=t(23928),j=t(13861),p=t(63143),f=t(53411),g=t(62688);let N=(0,g.A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]);var v=t(64398),y=t(85814),b=t.n(y);function w(){let{userProfile:e}=(0,a.As)(),[s,t]=(0,i.useState)([]),[g,y]=(0,i.useState)([]),[w,k]=(0,i.useState)(!0),[A,D]=(0,i.useState)(null),[T,$]=(0,i.useState)(null),R=async(e,s)=>{if(confirm(`Are you sure you want to delete "${s}"? This action cannot be undone.`))try{$(e),await d.eg.delete(e),t(s=>s.filter(s=>s.id!==e)),y(s=>s.filter(s=>s.farmId!==e))}catch(e){console.error("Error deleting farm:",e),D("Failed to delete farm. Please try again.")}finally{$(null)}};if(w)return(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsx)(c.k,{size:"lg"})});if(A)return(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("p",{className:"text-red-600 mb-4",children:A}),(0,r.jsx)(l.$,{onClick:()=>window.location.reload(),children:"Try Again"})]})});let S=g.length,P=g.filter(e=>"pending"===e.status).length,q=g.filter(e=>"confirmed"===e.status).reduce((e,s)=>e+(s.totalPrice||0),0);return(0,r.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,r.jsx)("div",{className:"mb-8",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("h1",{className:"text-3xl font-bold text-earth-900",children:["Welcome back, ",e?.firstName||"Farm Owner","!"]}),(0,r.jsx)("p",{className:"text-earth-600 mt-2",children:"Manage your farms and bookings from your dashboard"})]}),(0,r.jsx)(b(),{href:"/farms/create",children:(0,r.jsxs)(l.$,{variant:"primary",className:"flex items-center gap-2",children:[(0,r.jsx)(o.A,{className:"w-4 h-4"}),"Add New Farm"]})})]})}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[(0,r.jsx)(n.Zp,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-earth-600 text-sm font-medium",children:"Total Farms"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-earth-900",children:s.length})]}),(0,r.jsx)(x.A,{className:"w-8 h-8 text-accent-600"})]})}),(0,r.jsx)(n.Zp,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-earth-600 text-sm font-medium",children:"Total Bookings"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-earth-900",children:S})]}),(0,r.jsx)(m.A,{className:"w-8 h-8 text-accent-600"})]})}),(0,r.jsx)(n.Zp,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-earth-600 text-sm font-medium",children:"Pending Requests"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-earth-900",children:P})]}),(0,r.jsx)(h.A,{className:"w-8 h-8 text-accent-600"})]})}),(0,r.jsx)(n.Zp,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-earth-600 text-sm font-medium",children:"Total Revenue"}),(0,r.jsxs)("p",{className:"text-2xl font-bold text-earth-900",children:["R",q.toLocaleString()]})]}),(0,r.jsx)(u.A,{className:"w-8 h-8 text-accent-600"})]})})]}),(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,r.jsx)("h2",{className:"text-2xl font-semibold text-earth-900",children:"My Farms"}),(0,r.jsx)(b(),{href:"/farms/create",children:(0,r.jsxs)(l.$,{variant:"primary",className:"flex items-center gap-2",children:[(0,r.jsx)(o.A,{className:"w-4 h-4"}),"Add New Farm"]})})]}),0===s.length?(0,r.jsx)(n.Zp,{className:"p-8",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(x.A,{className:"w-16 h-16 text-earth-400 mx-auto mb-4"}),(0,r.jsx)("h3",{className:"text-xl font-semibold text-earth-900 mb-2",children:"No farms yet"}),(0,r.jsx)("p",{className:"text-earth-600 mb-6",children:"Create your first farm listing to start receiving bookings from safari enthusiasts."}),(0,r.jsx)(b(),{href:"/farms/create",children:(0,r.jsxs)(l.$,{variant:"primary",className:"flex items-center gap-2",children:[(0,r.jsx)(o.A,{className:"w-4 h-4"}),"Create Your First Farm"]})})]})}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:s.slice(0,9).map(e=>(0,r.jsx)(n.Zp,{className:"overflow-hidden",children:(0,r.jsxs)("div",{className:"p-0",children:[(0,r.jsx)("div",{className:"h-48 bg-gradient-to-br from-earth-200 to-earth-300 flex items-center justify-center",children:(0,r.jsx)(x.A,{className:"w-12 h-12 text-earth-600"})}),(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("div",{className:"flex items-start justify-between mb-3",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-earth-900 mb-1",children:e.name}),(0,r.jsxs)("p",{className:"text-sm text-earth-600 flex items-center gap-1",children:[(0,r.jsx)(x.A,{className:"w-3 h-3"}),e.location,", ",e.province]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[(0,r.jsx)("div",{className:`w-2 h-2 rounded-full ${e.isActive?"bg-green-500":"bg-red-500"}`}),(0,r.jsx)("span",{className:"text-xs text-earth-600",children:e.isActive?"Active":"Inactive"})]})]}),(0,r.jsxs)("div",{className:"space-y-2 mb-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,r.jsx)("span",{className:"text-earth-600",children:"Activity Type:"}),(0,r.jsx)("span",{className:"font-medium capitalize text-earth-900",children:e.activityTypes.replace("_"," ")})]}),e.sizeHectares&&(0,r.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,r.jsx)("span",{className:"text-earth-600",children:"Size:"}),(0,r.jsxs)("span",{className:"font-medium text-earth-900",children:[e.sizeHectares.toLocaleString()," hectares"]})]}),e.pricePerDay&&(0,r.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,r.jsx)("span",{className:"text-earth-600",children:"Price:"}),(0,r.jsxs)("span",{className:"font-medium text-earth-900",children:["R",e.pricePerDay.toLocaleString(),"/day"]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-2 mb-2",children:[(0,r.jsx)(b(),{href:`/farms/${e.id}`,children:(0,r.jsxs)(l.$,{variant:"outline",size:"sm",className:"w-full flex items-center gap-1",children:[(0,r.jsx)(j.A,{className:"w-3 h-3"}),"View"]})}),(0,r.jsx)(b(),{href:`/farms/${e.id}/edit`,children:(0,r.jsxs)(l.$,{variant:"outline",size:"sm",className:"w-full flex items-center gap-1",children:[(0,r.jsx)(p.A,{className:"w-3 h-3"}),"Edit"]})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[(0,r.jsx)(b(),{href:`/farms/${e.id}/analytics`,children:(0,r.jsxs)(l.$,{variant:"primary",size:"sm",className:"w-full flex items-center gap-1",children:[(0,r.jsx)(f.A,{className:"w-3 h-3"}),"Analytics"]})}),(0,r.jsxs)(l.$,{variant:"outline",size:"sm",className:"w-full flex items-center gap-1 text-red-600 hover:text-red-700 hover:bg-red-50",onClick:()=>R(e.id,e.name),disabled:T===e.id,children:[(0,r.jsx)(N,{className:"w-3 h-3"}),T===e.id?"Deleting...":"Delete"]})]}),(0,r.jsx)("div",{className:"mt-4 pt-4 border-t border-earth-200",children:(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-center",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex items-center justify-center gap-1 text-earth-600 mb-1",children:[(0,r.jsx)(h.A,{className:"w-3 h-3"}),(0,r.jsx)("span",{className:"text-xs",children:"Bookings"})]}),(0,r.jsx)("p",{className:"text-sm font-semibold text-earth-900",children:g.filter(s=>s.farmId===e.id).length})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex items-center justify-center gap-1 text-earth-600 mb-1",children:[(0,r.jsx)(v.A,{className:"w-3 h-3"}),(0,r.jsx)("span",{className:"text-xs",children:"Rating"})]}),(0,r.jsx)("p",{className:"text-sm font-semibold text-earth-900",children:"N/A"})]})]})})]})]})},e.id))}),s.length>9&&(0,r.jsx)("div",{className:"text-center mt-6",children:(0,r.jsx)(b(),{href:"/dashboard/farms",children:(0,r.jsxs)(l.$,{variant:"outline",children:["View All ",s.length," Farms"]})})})]})]}),(0,r.jsx)("div",{children:(0,r.jsxs)(n.Zp,{className:"p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-earth-900",children:"Recent Booking Requests"}),(0,r.jsx)(b(),{href:"/dashboard/bookings",children:(0,r.jsx)(l.$,{variant:"outline",size:"sm",children:"View All"})})]}),0===g.length?(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)(m.A,{className:"w-12 h-12 text-earth-400 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-earth-600",children:"No booking requests yet"})]}):(0,r.jsx)("div",{className:"space-y-4",children:g.slice(0,3).map(e=>{let t=s.find(s=>s.id===e.farmId);return(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 bg-earth-50 rounded-lg",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-medium text-earth-900",children:t?.name||"Unknown Farm"}),(0,r.jsxs)("p",{className:"text-sm text-earth-600",children:[new Date(e.startDate).toLocaleDateString()," - ",new Date(e.endDate).toLocaleDateString()]})]}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsx)("span",{className:`inline-flex px-2 py-1 text-xs font-medium rounded-full ${"pending"===e.status?"bg-yellow-100 text-yellow-800":"confirmed"===e.status?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:e.status}),(0,r.jsxs)("p",{className:"text-sm font-medium text-earth-900 mt-1",children:["R",e.totalPrice?.toLocaleString()]})]})]},e.id)})})]})})]})}let k=(0,g.A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]]);var A=t(48730),D=t(5336);let T=(0,g.A)("heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]]);function $(){let{userProfile:e}=(0,a.As)(),[s,t]=(0,i.useState)([]),[d,o]=(0,i.useState)([]),[h,u]=(0,i.useState)(!0),[j,p]=(0,i.useState)(null);if(h)return(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsx)(c.k,{size:"lg"})});if(j)return(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("p",{className:"text-red-600 mb-4",children:j}),(0,r.jsx)(l.$,{onClick:()=>window.location.reload(),children:"Try Again"})]})});let f=s.length,g=s.filter(e=>new Date(e.startDate)>new Date&&"confirmed"===e.status).length,N=s.filter(e=>new Date(e.endDate)<new Date&&"confirmed"===e.status).length;return(0,r.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,r.jsx)("div",{className:"mb-8",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("h1",{className:"text-3xl font-bold text-earth-900",children:["Welcome back, ",e?.firstName||"Guest","!"]}),(0,r.jsx)("p",{className:"text-earth-600 mt-2",children:"Ready for your next safari adventure?"})]}),(0,r.jsx)(b(),{href:"/farms",children:(0,r.jsxs)(l.$,{variant:"primary",className:"flex items-center gap-2",children:[(0,r.jsx)(k,{className:"w-4 h-4"}),"Browse Farms"]})})]})}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[(0,r.jsx)(n.Zp,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-earth-600 text-sm font-medium",children:"Total Bookings"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-earth-900",children:f})]}),(0,r.jsx)(m.A,{className:"w-8 h-8 text-accent-600"})]})}),(0,r.jsx)(n.Zp,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-earth-600 text-sm font-medium",children:"Upcoming Trips"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-earth-900",children:g})]}),(0,r.jsx)(A.A,{className:"w-8 h-8 text-accent-600"})]})}),(0,r.jsx)(n.Zp,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-earth-600 text-sm font-medium",children:"Completed Trips"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-earth-900",children:N})]}),(0,r.jsx)(D.A,{className:"w-8 h-8 text-accent-600"})]})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,r.jsxs)(n.Zp,{className:"p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-earth-900",children:"My Bookings"}),(0,r.jsx)(b(),{href:"/dashboard/bookings",children:(0,r.jsx)(l.$,{variant:"outline",size:"sm",children:"View All"})})]}),0===s.length?(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)(m.A,{className:"w-12 h-12 text-earth-400 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-earth-600 mb-4",children:"You haven't made any bookings yet"}),(0,r.jsx)(b(),{href:"/farms",children:(0,r.jsx)(l.$,{variant:"primary",children:"Browse Farms"})})]}):(0,r.jsxs)("div",{className:"space-y-4",children:[s.slice(0,3).map(e=>(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 bg-earth-50 rounded-lg",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("h3",{className:"font-medium text-earth-900",children:["Booking #",e.id.slice(-6)]}),(0,r.jsxs)("p",{className:"text-sm text-earth-600",children:[new Date(e.startDate).toLocaleDateString()," - ",new Date(e.endDate).toLocaleDateString()]})]}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsx)("span",{className:`inline-flex px-2 py-1 text-xs font-medium rounded-full ${"pending"===e.status?"bg-yellow-100 text-yellow-800":"confirmed"===e.status?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:e.status}),(0,r.jsxs)("p",{className:"text-sm font-medium text-earth-900 mt-1",children:["R",e.totalPrice?.toLocaleString()]})]})]},e.id)),s.length>3&&(0,r.jsx)("div",{className:"text-center pt-4",children:(0,r.jsx)(b(),{href:"/dashboard/bookings",children:(0,r.jsx)(l.$,{variant:"outline",children:"View All Bookings"})})})]})]}),(0,r.jsxs)(n.Zp,{className:"p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-earth-900",children:"Recommended Farms"}),(0,r.jsx)(b(),{href:"/farms",children:(0,r.jsx)(l.$,{variant:"outline",size:"sm",children:"Browse All"})})]}),0===d.length?(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)(T,{className:"w-12 h-12 text-earth-400 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-earth-600 mb-4",children:"Discover amazing safari destinations"}),(0,r.jsx)(b(),{href:"/farms",children:(0,r.jsx)(l.$,{variant:"primary",children:"Explore Farms"})})]}):(0,r.jsx)("div",{className:"space-y-4",children:d.map(e=>(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 bg-earth-50 rounded-lg",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-medium text-earth-900",children:e.name}),(0,r.jsxs)("p",{className:"text-sm text-earth-600 flex items-center gap-1",children:[(0,r.jsx)(x.A,{className:"w-3 h-3"}),e.location]})]}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsxs)("p",{className:"text-sm font-medium text-earth-900",children:["R",e.pricePerDay?.toLocaleString(),"/day"]}),(0,r.jsx)(b(),{href:`/farms/${e.id}`,children:(0,r.jsx)(l.$,{variant:"outline",size:"sm",children:"View Details"})})]})]},e.id))})]})]}),(0,r.jsxs)(n.Zp,{className:"p-6 mt-8",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-earth-900 mb-6",children:"Quick Actions"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,r.jsx)(b(),{href:"/farms",children:(0,r.jsxs)(l.$,{variant:"outline",className:"w-full flex items-center gap-2",children:[(0,r.jsx)(k,{className:"w-4 h-4"}),"Browse Farms"]})}),(0,r.jsx)(b(),{href:"/profile",children:(0,r.jsxs)(l.$,{variant:"outline",className:"w-full flex items-center gap-2",children:[(0,r.jsx)(m.A,{className:"w-4 h-4"}),"Manage Profile"]})}),(0,r.jsx)(b(),{href:"/dashboard/bookings",children:(0,r.jsxs)(l.$,{variant:"outline",className:"w-full flex items-center gap-2",children:[(0,r.jsx)(A.A,{className:"w-4 h-4"}),"View Bookings"]})})]})]})]})}var R=t(50944);async function S(){let e=R.j2.currentUser;if(!e)return console.log("\uD83D\uDD0D Auth Debug: No user logged in"),null;try{let s=await e.getIdTokenResult(!0);return console.log("\uD83D\uDD0D Auth Debug Information:"),console.log("  User UID:",e.uid),console.log("  Email:",e.email),console.log("  Display Name:",e.displayName),console.log("  Email Verified:",e.emailVerified),console.log("  Custom Claims:",s.claims),console.log("  Role from Claims:",s.claims.role||"NO ROLE SET"),console.log("  Token Expiration:",new Date(s.expirationTime)),console.log("  Auth Time:",new Date(s.authTime)),console.log("  Issued At:",new Date(s.issuedAtTime)),s.claims.role||(console.warn("⚠️  WARNING: No role set in custom claims!"),console.log("   This will cause Firestore permission errors."),console.log("   The user may need to re-register or have their role set manually.")),{uid:e.uid,email:e.email,role:s.claims.role||"NO ROLE",claims:s.claims,hasRole:!!s.claims.role}}catch(e){return console.error("\uD83D\uDD0D Auth Debug Error:",e),null}}async function P(){let e=R.j2.currentUser;if(!e)return console.log("\uD83D\uDD04 Token Refresh: No user logged in"),!1;try{return console.log("\uD83D\uDD04 Refreshing user token..."),await e.getIdToken(!0),console.log("✅ Token refreshed successfully"),!0}catch(e){return console.error("❌ Token refresh failed:",e),!1}}function q(){let[e,s]=(0,i.useState)(null),[t,c]=(0,i.useState)(!1),[d,o]=(0,i.useState)(!1),{user:x}=(0,a.As)(),m=async()=>{c(!0);try{let e=await S();s(e)}catch(e){console.error("Debug failed:",e)}finally{c(!1)}},h=async()=>{o(!0);try{await P();let e=await S();s(e)}catch(e){console.error("Token refresh failed:",e)}finally{o(!1)}},u=async()=>{if(x)try{let e=await x.getIdToken(),s=await fetch("/api/auth/set-role",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${e}`},body:JSON.stringify({uid:x.uid,role:"guest"})});if(s.ok)console.log("Role set successfully"),await h();else{let e=await s.json();console.error("Failed to set role:",e.error)}}catch(e){console.error("Error setting role:",e)}};return x?(0,r.jsxs)(n.Zp,{className:"p-4 mb-4 bg-yellow-50 border-yellow-200",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-yellow-800 mb-3",children:"\uD83D\uDD0D Authentication Debug Panel"}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsx)(l.$,{onClick:m,variant:"outline",size:"sm",isLoading:t,children:"Check Auth State"}),(0,r.jsx)(l.$,{onClick:h,variant:"outline",size:"sm",isLoading:d,children:"Refresh Token"}),(0,r.jsx)(l.$,{onClick:u,variant:"outline",size:"sm",children:"Set Role (Guest)"})]}),e&&(0,r.jsxs)("div",{className:"bg-white p-3 rounded border text-sm",children:[(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("strong",{children:"UID:"})," ",e.uid]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("strong",{children:"Email:"})," ",e.email]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("strong",{children:"Role:"})," ",e.role||"NOT SET"]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("strong",{children:"Has Role:"})," ",e.hasRole?"✅":"❌"]})]}),!e.hasRole&&(0,r.jsx)("div",{className:"mt-2 p-2 bg-red-50 border border-red-200 rounded",children:(0,r.jsx)("p",{className:"text-red-700 text-xs",children:'⚠️ No role set in custom claims! This will cause permission errors. Click "Set Role (Guest)" to fix this issue.'})}),(0,r.jsxs)("details",{className:"mt-2",children:[(0,r.jsx)("summary",{className:"cursor-pointer text-xs text-gray-600",children:"View All Claims"}),(0,r.jsx)("pre",{className:"text-xs bg-gray-100 p-2 rounded mt-1 overflow-auto",children:JSON.stringify(e.claims,null,2)})]})]})]})]}):null}function z(){let{userProfile:e,loading:s}=(0,a.As)();return s||!e?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsx)(c.k,{size:"lg"})}):(0,r.jsxs)("div",{children:[(0,r.jsx)(q,{}),"farm_owner"===e.role?(0,r.jsx)(w,{}):(0,r.jsx)($,{})]})}var C=t(16189);function E(){let{loading:e,isAuthenticated:s}=(0,a.Nu)();return((0,C.useRouter)(),e)?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-earth-100",children:(0,r.jsx)(c.k,{size:"lg"})}):s?(0,r.jsx)("div",{className:"min-h-screen bg-earth-100",children:(0,r.jsx)(z,{})}):null}},9139:(e,s,t)=>{Promise.resolve().then(t.bind(t,80559))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},23928:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},37366:e=>{"use strict";e.exports=require("dns")},40228:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},46091:(e,s,t)=>{Promise.resolve().then(t.bind(t,6765))},48730:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},55511:e=>{"use strict";e.exports=require("crypto")},59223:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>m,tree:()=>d});var r=t(65239),a=t(48088),i=t(88170),l=t.n(i),n=t(30893),c={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);t.d(s,c);let d={children:["",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,80559)),"/Users/<USER>/src/personal/bvr-safaris/src/app/dashboard/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,82893)),"/Users/<USER>/src/personal/bvr-safaris/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["/Users/<USER>/src/personal/bvr-safaris/src/app/dashboard/page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},80559:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/src/personal/bvr-safaris/src/app/dashboard/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/src/personal/bvr-safaris/src/app/dashboard/page.tsx","default")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[447,874,658,533,391,574],()=>t(59223));module.exports=r})();