(()=>{var e={};e.id=810,e.ids=[810],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},23169:(e,r,s)=>{"use strict";s.r(r),s.d(r,{patchFetch:()=>x,routeModule:()=>l,serverHooks:()=>h,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>d});var t={};s.r(t),s.d(t,{OPTIONS:()=>c,POST:()=>u});var o=s(96559),n=s(48088),a=s(37719),i=s(32190);async function u(e){try{let r=await e.json();if(!r.uid||!r.role)return i.NextResponse.json({success:!1,error:"Missing uid or role"},{status:400});if("farm_owner"!==r.role&&"guest"!==r.role&&"admin"!==r.role)return i.NextResponse.json({success:!1,error:"Invalid role. Must be farm_owner, guest, or admin"},{status:400});let s=e.headers.get("authorization");if(!s)return i.NextResponse.json({success:!1,error:"Missing authorization header"},{status:401});let t=function(){let e="rvbsafaris";if(!e)throw Error("NEXT_PUBLIC_FIREBASE_PROJECT_ID environment variable is required");return`https://us-central1-${e}.cloudfunctions.net/setUserRole`}(),o=await fetch(t,{method:"POST",headers:{"Content-Type":"application/json",Authorization:s},body:JSON.stringify(r)}),n=await o.json();if(!o.ok)return i.NextResponse.json({success:!1,error:n.error||"Failed to set user role"},{status:o.status});return i.NextResponse.json({success:!0,message:n.message||"Role set successfully"})}catch(e){return console.error("Error in set-role API route:",e),i.NextResponse.json({success:!1,error:"Internal server error"},{status:500})}}async function c(){return new i.NextResponse(null,{status:200,headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"POST, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization"}})}let l=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/auth/set-role/route",pathname:"/api/auth/set-role",filename:"route",bundlePath:"app/api/auth/set-role/route"},resolvedPagePath:"/Users/<USER>/src/personal/bvr-safaris/src/app/api/auth/set-role/route.ts",nextConfigOutput:"",userland:t}),{workAsyncStorage:p,workUnitAsyncStorage:d,serverHooks:h}=l;function x(){return(0,a.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:d})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[447,580],()=>s(23169));module.exports=t})();