{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/lib/firebase/middleware.ts"], "sourcesContent": ["import { NextResponse, type NextRequest } from 'next/server'\n\nexport async function updateSession(request: NextRequest) {\n  const response = NextResponse.next({\n    request,\n  })\n\n  // Define public routes that don't require authentication\n  const publicRoutes = [\n    '/',\n    '/farms',\n    '/how-it-works',\n    '/about',\n    '/contact',\n    '/privacy',\n    '/terms',\n    '/help',\n    '/safety',\n    '/faq',\n    '/farm-owners'\n  ]\n\n  // Check if the current path is public or auth-related\n  const isPublicRoute = publicRoutes.some(route =>\n    request.nextUrl.pathname === route ||\n    request.nextUrl.pathname.startsWith('/auth/') ||\n    request.nextUrl.pathname.startsWith('/api/') ||\n    request.nextUrl.pathname.startsWith('/_next/') ||\n    request.nextUrl.pathname.includes('.')\n  )\n\n  // If it's a public route, allow access\n  if (isPublicRoute) {\n    return response\n  }\n\n  // For protected routes, check if user has a session token\n  const idToken = request.cookies.get('firebase-token')?.value\n\n  if (!idToken || idToken.trim() === '') {\n    // No valid token found, redirect to login\n    // But avoid redirect loops by checking if we're already going to login\n    if (request.nextUrl.pathname !== '/auth/login') {\n      return NextResponse.redirect(new URL('/auth/login', request.url))\n    }\n  }\n\n  // Token exists, allow access (verification happens server-side)\n  return response\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;;AAEO,eAAe,cAAc,OAAoB;IACtD,MAAM,WAAW,6LAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QACjC;IACF;IAEA,yDAAyD;IACzD,MAAM,eAAe;QACnB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,sDAAsD;IACtD,MAAM,gBAAgB,aAAa,IAAI,CAAC,CAAA,QACtC,QAAQ,OAAO,CAAC,QAAQ,KAAK,SAC7B,QAAQ,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,aACpC,QAAQ,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,YACpC,QAAQ,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,cACpC,QAAQ,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC;IAGpC,uCAAuC;IACvC,IAAI,eAAe;QACjB,OAAO;IACT;IAEA,0DAA0D;IAC1D,MAAM,UAAU,QAAQ,OAAO,CAAC,GAAG,CAAC,mBAAmB;IAEvD,IAAI,CAAC,WAAW,QAAQ,IAAI,OAAO,IAAI;QACrC,0CAA0C;QAC1C,uEAAuE;QACvE,IAAI,QAAQ,OAAO,CAAC,QAAQ,KAAK,eAAe;YAC9C,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,eAAe,QAAQ,GAAG;QACjE;IACF;IAEA,gEAAgE;IAChE,OAAO;AACT"}}, {"offset": {"line": 71, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import { type NextRequest } from 'next/server'\nimport { updateSession } from '@/lib/firebase/middleware'\n\nexport async function middleware(request: NextRequest) {\n  return await updateSession(request)\n}\n\nexport const config = {\n  matcher: [\n    /*\n     * Match all request paths except for the ones starting with:\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     * Feel free to modify this pattern to include more paths.\n     */\n    '/((?!_next/static|_next/image|favicon.ico|.*\\\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',\n  ],\n}"], "names": [], "mappings": ";;;;AACA;;AAEO,eAAe,WAAW,OAAoB;IACnD,OAAO,MAAM,CAAA,GAAA,4IAAA,CAAA,gBAAa,AAAD,EAAE;AAC7B;AAEO,MAAM,SAAS;IACpB,SAAS;QACP;;;;;;KAMC,GACD;KACD;AACH"}}]}