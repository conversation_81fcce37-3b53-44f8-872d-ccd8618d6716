[{"/Users/<USER>/src/personal/bvr-safaris/src/app/about/page.tsx": "1", "/Users/<USER>/src/personal/bvr-safaris/src/app/api/auth/set-role/route.ts": "2", "/Users/<USER>/src/personal/bvr-safaris/src/app/auth/forgot-password/page.tsx": "3", "/Users/<USER>/src/personal/bvr-safaris/src/app/auth/login/page.tsx": "4", "/Users/<USER>/src/personal/bvr-safaris/src/app/auth/register/page.tsx": "5", "/Users/<USER>/src/personal/bvr-safaris/src/app/auth/reset-password/page.tsx": "6", "/Users/<USER>/src/personal/bvr-safaris/src/app/auth/verify-email/page.tsx": "7", "/Users/<USER>/src/personal/bvr-safaris/src/app/contact/page.tsx": "8", "/Users/<USER>/src/personal/bvr-safaris/src/app/dashboard/bookings/page.tsx": "9", "/Users/<USER>/src/personal/bvr-safaris/src/app/dashboard/farms/page.tsx": "10", "/Users/<USER>/src/personal/bvr-safaris/src/app/dashboard/page.tsx": "11", "/Users/<USER>/src/personal/bvr-safaris/src/app/faq/page.tsx": "12", "/Users/<USER>/src/personal/bvr-safaris/src/app/farm-owners/page.tsx": "13", "/Users/<USER>/src/personal/bvr-safaris/src/app/farms/[id]/analytics/page.tsx": "14", "/Users/<USER>/src/personal/bvr-safaris/src/app/farms/[id]/edit/page.tsx": "15", "/Users/<USER>/src/personal/bvr-safaris/src/app/farms/[id]/page.tsx": "16", "/Users/<USER>/src/personal/bvr-safaris/src/app/farms/create/page.tsx": "17", "/Users/<USER>/src/personal/bvr-safaris/src/app/farms/page.tsx": "18", "/Users/<USER>/src/personal/bvr-safaris/src/app/help/page.tsx": "19", "/Users/<USER>/src/personal/bvr-safaris/src/app/how-it-works/page.tsx": "20", "/Users/<USER>/src/personal/bvr-safaris/src/app/layout.tsx": "21", "/Users/<USER>/src/personal/bvr-safaris/src/app/page.tsx": "22", "/Users/<USER>/src/personal/bvr-safaris/src/app/privacy/page.tsx": "23", "/Users/<USER>/src/personal/bvr-safaris/src/app/profile/page.tsx": "24", "/Users/<USER>/src/personal/bvr-safaris/src/app/safety/page.tsx": "25", "/Users/<USER>/src/personal/bvr-safaris/src/app/terms/page.tsx": "26", "/Users/<USER>/src/personal/bvr-safaris/src/components/debug/AuthDebugPanel.tsx": "27", "/Users/<USER>/src/personal/bvr-safaris/src/components/features/BookingModal.tsx": "28", "/Users/<USER>/src/personal/bvr-safaris/src/components/features/FarmCard.tsx": "29", "/Users/<USER>/src/personal/bvr-safaris/src/components/features/FarmOwnerDashboard.tsx": "30", "/Users/<USER>/src/personal/bvr-safaris/src/components/features/FilterPanel.tsx": "31", "/Users/<USER>/src/personal/bvr-safaris/src/components/features/GuestDashboard.tsx": "32", "/Users/<USER>/src/personal/bvr-safaris/src/components/features/PromoteFarmModal.tsx": "33", "/Users/<USER>/src/personal/bvr-safaris/src/components/features/ReviewCard.tsx": "34", "/Users/<USER>/src/personal/bvr-safaris/src/components/features/ReviewForm.tsx": "35", "/Users/<USER>/src/personal/bvr-safaris/src/components/features/SearchBar.tsx": "36", "/Users/<USER>/src/personal/bvr-safaris/src/components/features/ShareFarmModal.tsx": "37", "/Users/<USER>/src/personal/bvr-safaris/src/components/features/UnifiedDashboard.tsx": "38", "/Users/<USER>/src/personal/bvr-safaris/src/components/layout/Footer.tsx": "39", "/Users/<USER>/src/personal/bvr-safaris/src/components/layout/Navbar.tsx": "40", "/Users/<USER>/src/personal/bvr-safaris/src/components/test/FirebaseStorageTest.tsx": "41", "/Users/<USER>/src/personal/bvr-safaris/src/components/ui/Badge.tsx": "42", "/Users/<USER>/src/personal/bvr-safaris/src/components/ui/Button.tsx": "43", "/Users/<USER>/src/personal/bvr-safaris/src/components/ui/Card.tsx": "44", "/Users/<USER>/src/personal/bvr-safaris/src/components/ui/FileUpload.tsx": "45", "/Users/<USER>/src/personal/bvr-safaris/src/components/ui/HeroImageLoader.tsx": "46", "/Users/<USER>/src/personal/bvr-safaris/src/components/ui/Input.tsx": "47", "/Users/<USER>/src/personal/bvr-safaris/src/components/ui/LoadingSpinner.tsx": "48", "/Users/<USER>/src/personal/bvr-safaris/src/components/ui/Spinner.tsx": "49", "/Users/<USER>/src/personal/bvr-safaris/src/hooks/useAuth.tsx": "50", "/Users/<USER>/src/personal/bvr-safaris/src/lib/constants.ts": "51", "/Users/<USER>/src/personal/bvr-safaris/src/lib/firebase/client.ts": "52", "/Users/<USER>/src/personal/bvr-safaris/src/lib/firebase/config.ts": "53", "/Users/<USER>/src/personal/bvr-safaris/src/lib/firebase/firestore.ts": "54", "/Users/<USER>/src/personal/bvr-safaris/src/lib/firebase/heroImages.ts": "55", "/Users/<USER>/src/personal/bvr-safaris/src/lib/firebase/middleware.ts": "56", "/Users/<USER>/src/personal/bvr-safaris/src/lib/firebase/server.ts": "57", "/Users/<USER>/src/personal/bvr-safaris/src/lib/firebase/storage.ts": "58", "/Users/<USER>/src/personal/bvr-safaris/src/lib/types/database.ts": "59", "/Users/<USER>/src/personal/bvr-safaris/src/lib/types/firestore.ts": "60", "/Users/<USER>/src/personal/bvr-safaris/src/lib/utils/debugAuth.ts": "61", "/Users/<USER>/src/personal/bvr-safaris/src/lib/utils/roleManagement.ts": "62", "/Users/<USER>/src/personal/bvr-safaris/src/lib/utils.ts": "63", "/Users/<USER>/src/personal/bvr-safaris/src/middleware.ts": "64", "/Users/<USER>/src/personal/bvr-safaris/src/components/features/FeaturedFarmsCarousel.tsx": "65"}, {"size": 1603, "mtime": 1751710540001, "results": "66", "hashOfConfig": "67"}, {"size": 2916, "mtime": 1751710540001, "results": "68", "hashOfConfig": "67"}, {"size": 4215, "mtime": 1751710540001, "results": "69", "hashOfConfig": "67"}, {"size": 4156, "mtime": 1751710540001, "results": "70", "hashOfConfig": "67"}, {"size": 8139, "mtime": 1751710540002, "results": "71", "hashOfConfig": "67"}, {"size": 6331, "mtime": 1751710540003, "results": "72", "hashOfConfig": "67"}, {"size": 1743, "mtime": 1751710540003, "results": "73", "hashOfConfig": "67"}, {"size": 8095, "mtime": 1751710540004, "results": "74", "hashOfConfig": "67"}, {"size": 10751, "mtime": 1751710540004, "results": "75", "hashOfConfig": "67"}, {"size": 10405, "mtime": 1751710540005, "results": "76", "hashOfConfig": "67"}, {"size": 899, "mtime": 1751710540005, "results": "77", "hashOfConfig": "67"}, {"size": 8218, "mtime": 1751710540005, "results": "78", "hashOfConfig": "67"}, {"size": 11571, "mtime": 1751710540005, "results": "79", "hashOfConfig": "67"}, {"size": 16052, "mtime": 1751710540006, "results": "80", "hashOfConfig": "67"}, {"size": 17867, "mtime": 1751710540006, "results": "81", "hashOfConfig": "67"}, {"size": 15530, "mtime": 1751710540006, "results": "82", "hashOfConfig": "67"}, {"size": 19038, "mtime": 1751710540007, "results": "83", "hashOfConfig": "67"}, {"size": 7479, "mtime": 1751710540007, "results": "84", "hashOfConfig": "67"}, {"size": 9133, "mtime": 1751710540007, "results": "85", "hashOfConfig": "67"}, {"size": 7979, "mtime": 1751710540007, "results": "86", "hashOfConfig": "67"}, {"size": 1469, "mtime": 1751710540008, "results": "87", "hashOfConfig": "67"}, {"size": 8757, "mtime": 1751711312340, "results": "88", "hashOfConfig": "67"}, {"size": 8231, "mtime": 1751710540008, "results": "89", "hashOfConfig": "67"}, {"size": 8339, "mtime": 1751710540008, "results": "90", "hashOfConfig": "67"}, {"size": 11393, "mtime": 1751710540009, "results": "91", "hashOfConfig": "67"}, {"size": 6815, "mtime": 1751710540009, "results": "92", "hashOfConfig": "67"}, {"size": 4261, "mtime": 1751710540009, "results": "93", "hashOfConfig": "67"}, {"size": 11330, "mtime": 1751710540009, "results": "94", "hashOfConfig": "67"}, {"size": 2156, "mtime": 1751710540009, "results": "95", "hashOfConfig": "67"}, {"size": 15851, "mtime": 1751710540009, "results": "96", "hashOfConfig": "67"}, {"size": 8506, "mtime": 1751710540010, "results": "97", "hashOfConfig": "67"}, {"size": 9683, "mtime": 1751710540010, "results": "98", "hashOfConfig": "67"}, {"size": 6694, "mtime": 1751710540010, "results": "99", "hashOfConfig": "67"}, {"size": 7655, "mtime": 1751710540011, "results": "100", "hashOfConfig": "67"}, {"size": 7953, "mtime": 1751710540011, "results": "101", "hashOfConfig": "67"}, {"size": 1592, "mtime": 1751710540011, "results": "102", "hashOfConfig": "67"}, {"size": 5710, "mtime": 1751710540011, "results": "103", "hashOfConfig": "67"}, {"size": 1101, "mtime": 1751710540011, "results": "104", "hashOfConfig": "67"}, {"size": 5481, "mtime": 1751710540011, "results": "105", "hashOfConfig": "67"}, {"size": 10076, "mtime": 1751710540012, "results": "106", "hashOfConfig": "67"}, {"size": 4560, "mtime": 1751710540012, "results": "107", "hashOfConfig": "67"}, {"size": 886, "mtime": 1751710540012, "results": "108", "hashOfConfig": "67"}, {"size": 2756, "mtime": 1751710540012, "results": "109", "hashOfConfig": "67"}, {"size": 2370, "mtime": 1751710540012, "results": "110", "hashOfConfig": "67"}, {"size": 11621, "mtime": 1751710540012, "results": "111", "hashOfConfig": "67"}, {"size": 2460, "mtime": 1751710540012, "results": "112", "hashOfConfig": "67"}, {"size": 1299, "mtime": 1751710540013, "results": "113", "hashOfConfig": "67"}, {"size": 551, "mtime": 1751710540013, "results": "114", "hashOfConfig": "67"}, {"size": 539, "mtime": 1751710540013, "results": "115", "hashOfConfig": "67"}, {"size": 9444, "mtime": 1751710540013, "results": "116", "hashOfConfig": "67"}, {"size": 2596, "mtime": 1751710540013, "results": "117", "hashOfConfig": "67"}, {"size": 355, "mtime": 1751710540013, "results": "118", "hashOfConfig": "67"}, {"size": 1786, "mtime": 1751710540014, "results": "119", "hashOfConfig": "67"}, {"size": 8757, "mtime": 1751710540014, "results": "120", "hashOfConfig": "67"}, {"size": 3110, "mtime": 1751710540014, "results": "121", "hashOfConfig": "67"}, {"size": 1395, "mtime": 1751710540014, "results": "122", "hashOfConfig": "67"}, {"size": 2825, "mtime": 1751710540014, "results": "123", "hashOfConfig": "67"}, {"size": 7965, "mtime": 1751710540014, "results": "124", "hashOfConfig": "67"}, {"size": 4004, "mtime": 1751710540014, "results": "125", "hashOfConfig": "67"}, {"size": 5162, "mtime": 1751710540014, "results": "126", "hashOfConfig": "67"}, {"size": 2650, "mtime": 1751710540015, "results": "127", "hashOfConfig": "67"}, {"size": 3202, "mtime": 1751710540015, "results": "128", "hashOfConfig": "67"}, {"size": 917, "mtime": 1751710540014, "results": "129", "hashOfConfig": "67"}, {"size": 601, "mtime": 1751710540015, "results": "130", "hashOfConfig": "67"}, {"size": 5261, "mtime": 1751710540010, "results": "131", "hashOfConfig": "67"}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "tvc9qg", {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "255", "messages": "256", "suppressedMessages": "257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "267", "messages": "268", "suppressedMessages": "269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "270", "messages": "271", "suppressedMessages": "272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "273", "messages": "274", "suppressedMessages": "275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "282", "messages": "283", "suppressedMessages": "284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "291", "messages": "292", "suppressedMessages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "297", "messages": "298", "suppressedMessages": "299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "300", "messages": "301", "suppressedMessages": "302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "303", "messages": "304", "suppressedMessages": "305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "306", "messages": "307", "suppressedMessages": "308", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "309", "messages": "310", "suppressedMessages": "311", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "312", "messages": "313", "suppressedMessages": "314", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "315", "messages": "316", "suppressedMessages": "317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "318", "messages": "319", "suppressedMessages": "320", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "321", "messages": "322", "suppressedMessages": "323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "324", "messages": "325", "suppressedMessages": "326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/src/personal/bvr-safaris/src/app/about/page.tsx", [], [], "/Users/<USER>/src/personal/bvr-safaris/src/app/api/auth/set-role/route.ts", [], [], "/Users/<USER>/src/personal/bvr-safaris/src/app/auth/forgot-password/page.tsx", [], [], "/Users/<USER>/src/personal/bvr-safaris/src/app/auth/login/page.tsx", [], [], "/Users/<USER>/src/personal/bvr-safaris/src/app/auth/register/page.tsx", [], [], "/Users/<USER>/src/personal/bvr-safaris/src/app/auth/reset-password/page.tsx", [], [], "/Users/<USER>/src/personal/bvr-safaris/src/app/auth/verify-email/page.tsx", [], [], "/Users/<USER>/src/personal/bvr-safaris/src/app/contact/page.tsx", [], [], "/Users/<USER>/src/personal/bvr-safaris/src/app/dashboard/bookings/page.tsx", [], [], "/Users/<USER>/src/personal/bvr-safaris/src/app/dashboard/farms/page.tsx", [], [], "/Users/<USER>/src/personal/bvr-safaris/src/app/dashboard/page.tsx", [], [], "/Users/<USER>/src/personal/bvr-safaris/src/app/faq/page.tsx", [], [], "/Users/<USER>/src/personal/bvr-safaris/src/app/farm-owners/page.tsx", [], [], "/Users/<USER>/src/personal/bvr-safaris/src/app/farms/[id]/analytics/page.tsx", [], [], "/Users/<USER>/src/personal/bvr-safaris/src/app/farms/[id]/edit/page.tsx", [], [], "/Users/<USER>/src/personal/bvr-safaris/src/app/farms/[id]/page.tsx", [], [], "/Users/<USER>/src/personal/bvr-safaris/src/app/farms/create/page.tsx", [], [], "/Users/<USER>/src/personal/bvr-safaris/src/app/farms/page.tsx", [], [], "/Users/<USER>/src/personal/bvr-safaris/src/app/help/page.tsx", [], [], "/Users/<USER>/src/personal/bvr-safaris/src/app/how-it-works/page.tsx", [], [], "/Users/<USER>/src/personal/bvr-safaris/src/app/layout.tsx", [], [], "/Users/<USER>/src/personal/bvr-safaris/src/app/page.tsx", [], [], "/Users/<USER>/src/personal/bvr-safaris/src/app/privacy/page.tsx", [], [], "/Users/<USER>/src/personal/bvr-safaris/src/app/profile/page.tsx", [], [], "/Users/<USER>/src/personal/bvr-safaris/src/app/safety/page.tsx", [], [], "/Users/<USER>/src/personal/bvr-safaris/src/app/terms/page.tsx", [], [], "/Users/<USER>/src/personal/bvr-safaris/src/components/debug/AuthDebugPanel.tsx", [], [], "/Users/<USER>/src/personal/bvr-safaris/src/components/features/BookingModal.tsx", [], [], "/Users/<USER>/src/personal/bvr-safaris/src/components/features/FarmCard.tsx", [], [], "/Users/<USER>/src/personal/bvr-safaris/src/components/features/FarmOwnerDashboard.tsx", [], [], "/Users/<USER>/src/personal/bvr-safaris/src/components/features/FilterPanel.tsx", [], [], "/Users/<USER>/src/personal/bvr-safaris/src/components/features/GuestDashboard.tsx", [], [], "/Users/<USER>/src/personal/bvr-safaris/src/components/features/PromoteFarmModal.tsx", [], [], "/Users/<USER>/src/personal/bvr-safaris/src/components/features/ReviewCard.tsx", [], [], "/Users/<USER>/src/personal/bvr-safaris/src/components/features/ReviewForm.tsx", [], [], "/Users/<USER>/src/personal/bvr-safaris/src/components/features/SearchBar.tsx", [], [], "/Users/<USER>/src/personal/bvr-safaris/src/components/features/ShareFarmModal.tsx", [], [], "/Users/<USER>/src/personal/bvr-safaris/src/components/features/UnifiedDashboard.tsx", [], [], "/Users/<USER>/src/personal/bvr-safaris/src/components/layout/Footer.tsx", [], [], "/Users/<USER>/src/personal/bvr-safaris/src/components/layout/Navbar.tsx", [], [], "/Users/<USER>/src/personal/bvr-safaris/src/components/test/FirebaseStorageTest.tsx", [], [], "/Users/<USER>/src/personal/bvr-safaris/src/components/ui/Badge.tsx", [], [], "/Users/<USER>/src/personal/bvr-safaris/src/components/ui/Button.tsx", [], [], "/Users/<USER>/src/personal/bvr-safaris/src/components/ui/Card.tsx", [], [], "/Users/<USER>/src/personal/bvr-safaris/src/components/ui/FileUpload.tsx", [], [], "/Users/<USER>/src/personal/bvr-safaris/src/components/ui/HeroImageLoader.tsx", [], [], "/Users/<USER>/src/personal/bvr-safaris/src/components/ui/Input.tsx", [], [], "/Users/<USER>/src/personal/bvr-safaris/src/components/ui/LoadingSpinner.tsx", [], [], "/Users/<USER>/src/personal/bvr-safaris/src/components/ui/Spinner.tsx", [], [], "/Users/<USER>/src/personal/bvr-safaris/src/hooks/useAuth.tsx", [], [], "/Users/<USER>/src/personal/bvr-safaris/src/lib/constants.ts", [], [], "/Users/<USER>/src/personal/bvr-safaris/src/lib/firebase/client.ts", [], [], "/Users/<USER>/src/personal/bvr-safaris/src/lib/firebase/config.ts", [], [], "/Users/<USER>/src/personal/bvr-safaris/src/lib/firebase/firestore.ts", [], [], "/Users/<USER>/src/personal/bvr-safaris/src/lib/firebase/heroImages.ts", [], [], "/Users/<USER>/src/personal/bvr-safaris/src/lib/firebase/middleware.ts", [], [], "/Users/<USER>/src/personal/bvr-safaris/src/lib/firebase/server.ts", [], [], "/Users/<USER>/src/personal/bvr-safaris/src/lib/firebase/storage.ts", [], [], "/Users/<USER>/src/personal/bvr-safaris/src/lib/types/database.ts", [], [], "/Users/<USER>/src/personal/bvr-safaris/src/lib/types/firestore.ts", [], [], "/Users/<USER>/src/personal/bvr-safaris/src/lib/utils/debugAuth.ts", [], [], "/Users/<USER>/src/personal/bvr-safaris/src/lib/utils/roleManagement.ts", [], [], "/Users/<USER>/src/personal/bvr-safaris/src/lib/utils.ts", [], [], "/Users/<USER>/src/personal/bvr-safaris/src/middleware.ts", [], [], "/Users/<USER>/src/personal/bvr-safaris/src/components/features/FeaturedFarmsCarousel.tsx", [], []]