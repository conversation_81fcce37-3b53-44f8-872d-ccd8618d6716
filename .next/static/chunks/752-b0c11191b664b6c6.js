"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[752],{13741:(e,a,t)=>{t.d(a,{$:()=>i});var r=t(95155),n=t(12115),s=t(59434);let i=(0,n.forwardRef)((e,a)=>{let{className:t,variant:n="primary",size:i="md",isLoading:o,children:d,disabled:l,...c}=e;return(0,r.jsxs)("button",{className:(0,s.cn)("\n      inline-flex items-center justify-center rounded-[var(--radius-md)] \n      font-[var(--font-ui)] font-semibold transition-all duration-300 \n      focus:outline-none focus:ring-2 focus:ring-offset-2\n      disabled:opacity-50 disabled:cursor-not-allowed\n      hover:transform hover:-translate-y-0.5 hover:shadow-[var(--shadow-lg)]\n    ",{primary:"\n        bg-[var(--primary-brown)] text-white \n        hover:bg-[#A0522D] focus:ring-[var(--primary-brown)]\n      ",secondary:"\n        bg-[var(--secondary-sky)] text-white \n        hover:bg-[#357AE0] focus:ring-[var(--secondary-sky)]\n      ",outline:"\n        bg-transparent border-2 border-[var(--primary-brown)] text-[var(--primary-brown)]\n        hover:bg-[var(--primary-brown)] hover:text-white focus:ring-[var(--primary-brown)]\n      ",hunting:"\n        bg-[var(--hunting-accent)] text-white \n        hover:bg-[#A67C00] focus:ring-[var(--hunting-accent)]\n      ",photo:"\n        bg-[var(--photo-accent)] text-white \n        hover:bg-[#3557C7] focus:ring-[var(--photo-accent)]\n      "}[n],{sm:"px-3 py-1.5 text-sm",md:"px-6 py-2 text-base",lg:"px-8 py-3 text-lg"}[i],o&&"cursor-wait",t),disabled:l||o,ref:a,...c,children:[o&&(0,r.jsxs)("svg",{className:"mr-2 h-4 w-4 animate-spin",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,r.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,r.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),d]})});i.displayName="Button"},17154:(e,a,t)=>{t.d(a,{t:()=>u});var r=t(95155),n=t(12115),s=t(66766),i=t(74338);let o=["IMG_6015-min.JPG","IMG_6077-min.JPG","IMG_6207-min.JPG","IMG_6297-min.JPG","IMG_6333-min.JPG","IMG_6395-min.JPG","IMG_6498-min.JPG","IMG_6610-min.JPG","IMG_6632-min.JPG","IMG_6695-min.JPG","IMG_6738-min.JPG","IMG_6744-min.JPG","IMG_6784-min.JPG"],d="/banner_images/".concat(o[0]);async function l(){try{if(!o||0===o.length)return console.warn("No hero images found in banner_images directory, using default banner"),d;let e=Math.floor(Math.random()*o.length),a=o[e];return"/banner_images/".concat(a)}catch(e){return console.error("Error selecting random hero image from local directory, using default banner:",e),d}}var c=t(59434);function u(e){let{className:a,children:t,priority:o=!1,onImageLoad:u}=e,[m,f]=(0,n.useState)(d),[v,g]=(0,n.useState)(!0);return(0,n.useEffect)(()=>{(async()=>{try{g(!0);let e=await l();f(e),null==u||u(e)}catch(e){console.error("Error loading hero image:",e),f(d)}finally{g(!1)}})()},[u]),(0,r.jsxs)("div",{className:(0,c.cn)("relative w-full h-full overflow-hidden",a),children:[v&&(0,r.jsx)("div",{className:"absolute inset-0 z-10 bg-gradient-to-br from-[var(--primary-green)] to-[var(--secondary-sunset)] flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center text-white",children:[(0,r.jsx)(i.k,{size:"lg",className:"mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-sm font-medium",children:"Loading beautiful safari imagery..."})]})}),(0,r.jsx)(s.default,{src:m,alt:"Safari landscape",fill:!0,className:"object-cover",priority:o,onLoad:()=>{g(!1)},onError:()=>{console.error("Failed to load hero image, falling back to default"),f(d),g(!1)},sizes:"100vw"}),(0,r.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-40"}),t&&(0,r.jsx)("div",{className:"absolute inset-0 z-20",children:t})]})}},17703:(e,a,t)=>{t.d(a,{MH:()=>u,Wu:()=>d,ZB:()=>c,Zp:()=>o,aR:()=>l});var r=t(95155),n=t(12115),s=t(66766),i=t(59434);let o=(0,n.forwardRef)((e,a)=>{let{className:t,hover:n=!0,children:s,...o}=e;return(0,r.jsx)("div",{ref:a,className:(0,i.cn)("\n          bg-white rounded-[var(--radius-lg)] shadow-[var(--shadow-md)] \n          overflow-hidden transition-all duration-300\n          ",n&&"hover:transform hover:-translate-y-1 hover:shadow-[var(--shadow-xl)]",t),...o,children:s})});o.displayName="Card";let d=(0,n.forwardRef)((e,a)=>{let{className:t,...n}=e;return(0,r.jsx)("div",{ref:a,className:(0,i.cn)("p-[var(--space-lg)]",t),...n})});d.displayName="CardContent";let l=(0,n.forwardRef)((e,a)=>{let{className:t,...n}=e;return(0,r.jsx)("div",{ref:a,className:(0,i.cn)("px-[var(--space-lg)] pt-[var(--space-lg)]",t),...n})});l.displayName="CardHeader";let c=(0,n.forwardRef)((e,a)=>{let{className:t,...n}=e;return(0,r.jsx)("h3",{ref:a,className:(0,i.cn)("text-xl font-bold mb-2 text-[var(--primary-brown)] font-[var(--font-display)]",t),...n})});c.displayName="CardTitle";let u=(0,n.forwardRef)((e,a)=>{let{className:t,src:n,alt:o,children:d,...l}=e;return(0,r.jsx)("div",{ref:a,className:(0,i.cn)("relative w-full h-48 bg-gradient-to-br from-[var(--primary-green)] to-[var(--secondary-sunset)] flex items-center justify-center text-white text-5xl",t),...l,children:n?(0,r.jsx)(s.default,{src:n,alt:o||"",fill:!0,className:"object-cover"}):d})});u.displayName="CardImage"},28214:(e,a,t)=>{t.d(a,{IG:()=>r.IG,db:()=>r.db,j2:()=>r.j2});var r=t(67039)},28666:(e,a,t)=>{t.d(a,{C2:()=>l,QQ:()=>i,eg:()=>o,zi:()=>d});var r=t(35317),n=t(28214);function s(e){var a,t,r,n;if(!e.exists())return null;let s=e.data();return{id:e.id,...s,createdAt:(null==s||null==(t=s.createdAt)||null==(a=t.toDate)?void 0:a.call(t))||(null==s?void 0:s.createdAt),updatedAt:(null==s||null==(n=s.updatedAt)||null==(r=n.toDate)?void 0:r.call(n))||(null==s?void 0:s.updatedAt)}}let i={async get(e){let a=(0,r.H9)(n.db,"users",e);return s(await (0,r.x7)(a))},async create(e,a){let t=(0,r.H9)(n.db,"users",e),s=new Date;await (0,r.BN)(t,{...a,createdAt:r.Dc.fromDate(s),updatedAt:r.Dc.fromDate(s)})},async update(e,a){let t=(0,r.H9)(n.db,"users",e);await (0,r.mZ)(t,{...a,updatedAt:r.Dc.now()})}},o={async getAll(e){let a=(0,r.rJ)(n.db,"farms");return(null==e?void 0:e.isActive)!==void 0&&(a=(0,r.P)(a,(0,r._M)("isActive","==",e.isActive))),(null==e?void 0:e.featured)!==void 0&&(a=(0,r.P)(a,(0,r._M)("featured","==",e.featured))),(null==e?void 0:e.province)&&(a=(0,r.P)(a,(0,r._M)("province","==",e.province))),(null==e?void 0:e.activityType)&&(a=(0,r.P)(a,(0,r._M)("activityTypes","==",e.activityType))),a=(0,r.P)(a,(0,r.My)("createdAt","desc")),(null==e?void 0:e.limit)&&(a=(0,r.P)(a,(0,r.AB)(e.limit))),(await (0,r.GG)(a)).docs.map(e=>s(e)).filter(Boolean)},async get(e){let a=(0,r.H9)(n.db,"farms",e);return s(await (0,r.x7)(a))},async getByOwner(e){let a=(0,r.P)((0,r.rJ)(n.db,"farms"),(0,r._M)("ownerId","==",e),(0,r.My)("createdAt","desc"));return(await (0,r.GG)(a)).docs.map(e=>s(e)).filter(Boolean)},async getActive(e){let a=(0,r.P)((0,r.rJ)(n.db,"farms"),(0,r._M)("isActive","==",!0),(0,r.My)("createdAt","desc"));return e&&(a=(0,r.P)(a,(0,r.AB)(e))),(await (0,r.GG)(a)).docs.map(e=>s(e)).filter(Boolean)},async create(e){let a=new Date;return(await (0,r.gS)((0,r.rJ)(n.db,"farms"),{...e,createdAt:r.Dc.fromDate(a),updatedAt:r.Dc.fromDate(a)})).id},async update(e,a){let t=(0,r.H9)(n.db,"farms",e);await (0,r.mZ)(t,{...a,updatedAt:r.Dc.now()})},async delete(e){let a=(0,r.H9)(n.db,"farms",e);await (0,r.kd)(a)},async addImage(e,a){let t=new Date;return(await (0,r.gS)((0,r.rJ)(n.db,"farms",e,"images"),{...a,createdAt:r.Dc.fromDate(t),updatedAt:r.Dc.fromDate(t)})).id},async getImages(e){let a=(0,r.P)((0,r.rJ)(n.db,"farms",e,"images"),(0,r.My)("displayOrder","asc"),(0,r.My)("createdAt","asc"));return(await (0,r.GG)(a)).docs.map(e=>s(e)).filter(Boolean)},async deleteImage(e,a){let t=(0,r.H9)(n.db,"farms",e,"images",a);await (0,r.kd)(t)}},d={async getAll(e){let a=(0,r.rJ)(n.db,"bookings");return(null==e?void 0:e.hunterId)&&(a=(0,r.P)(a,(0,r._M)("hunterId","==",e.hunterId))),(null==e?void 0:e.farmId)&&(a=(0,r.P)(a,(0,r._M)("farmId","==",e.farmId))),(null==e?void 0:e.status)&&(a=(0,r.P)(a,(0,r._M)("status","==",e.status))),a=(0,r.P)(a,(0,r.My)("createdAt","desc")),(null==e?void 0:e.limit)&&(a=(0,r.P)(a,(0,r.AB)(e.limit))),(await (0,r.GG)(a)).docs.map(e=>s(e)).filter(Boolean)},async get(e){let a=(0,r.H9)(n.db,"bookings",e);return s(await (0,r.x7)(a))},async create(e){let a=new Date,t="BVR-".concat(a.getFullYear()).concat((a.getMonth()+1).toString().padStart(2,"0")).concat(a.getDate().toString().padStart(2,"0"),"-").concat(Math.random().toString(36).substring(2,8).toUpperCase());return(await (0,r.gS)((0,r.rJ)(n.db,"bookings"),{...e,bookingReference:t,createdAt:r.Dc.fromDate(a),updatedAt:r.Dc.fromDate(a)})).id},async update(e,a){let t=(0,r.H9)(n.db,"bookings",e);await (0,r.mZ)(t,{...a,updatedAt:r.Dc.now()})}},l={async getByFarm(e){let a=(0,r.P)((0,r.rJ)(n.db,"farms",e,"reviews"),(0,r._M)("isPublic","==",!0),(0,r.My)("createdAt","desc"));return(await (0,r.GG)(a)).docs.map(e=>s(e)).filter(Boolean)},async create(e,a){let t=new Date;return(await (0,r.gS)((0,r.rJ)(n.db,"farms",e,"reviews"),{...a,createdAt:r.Dc.fromDate(t),updatedAt:r.Dc.fromDate(t)})).id},async update(e,a,t){let s=(0,r.H9)(n.db,"farms",e,"reviews",a);await (0,r.mZ)(s,{...t,updatedAt:r.Dc.now()})}}},35695:(e,a,t)=>{var r=t(18999);t.o(r,"useParams")&&t.d(a,{useParams:function(){return r.useParams}}),t.o(r,"useRouter")&&t.d(a,{useRouter:function(){return r.useRouter}}),t.o(r,"useSearchParams")&&t.d(a,{useSearchParams:function(){return r.useSearchParams}})},52814:(e,a,t)=>{t.d(a,{E:()=>i});var r=t(95155),n=t(12115),s=t(59434);let i=(0,n.forwardRef)((e,a)=>{let{className:t,variant:n="default",...i}=e;return(0,r.jsx)("span",{ref:a,className:(0,s.cn)("inline-flex items-center px-2 py-1 rounded-[var(--radius-sm)] text-sm font-semibold",{hunting:"bg-[var(--hunting-accent)] text-white",photo:"bg-[var(--photo-accent)] text-white",location:"bg-[var(--secondary-stone)] text-white",default:"bg-[var(--medium-gray)] text-white"}[n],t),...i})});i.displayName="Badge"},59434:(e,a,t)=>{t.d(a,{Y:()=>o,Z:()=>i,cn:()=>s});var r=t(52596),n=t(39688);function s(){for(var e=arguments.length,a=Array(e),t=0;t<e;t++)a[t]=arguments[t];return(0,n.QP)((0,r.$)(a))}function i(e){return e.toString().replace(/\B(?=(\d{3})+(?!\d))/g," ")}function o(e){let a=new Date(e);return"".concat(a.getDate()," ").concat(["January","February","March","April","May","June","July","August","September","October","November","December"][a.getMonth()]," ").concat(a.getFullYear())}},66766:(e,a,t)=>{t.d(a,{default:()=>n.a});var r=t(71469),n=t.n(r)},67039:(e,a,t)=>{t.d(a,{IG:()=>c,db:()=>l,j2:()=>d});var r=t(23915),n=t(16203),s=t(35317),i=t(90858);let o=(0,r.Wp)({apiKey:"AIzaSyDdsTm8eifLrb1W0WLK0DwBk4p4XLFZKMw",authDomain:"rvbsafaris.firebaseapp.com",projectId:"rvbsafaris",storageBucket:"rvbsafaris.firebasestorage.app",messagingSenderId:"593781635754",appId:"1:593781635754:web:7a39a3b4451f34f85feed3"}),d=(0,n.xI)(o),l=(0,s.aU)(o),c=(0,i.c7)(o);console.log("Firebase Storage initialized with bucket:","rvbsafaris.firebasestorage.app")},71469:(e,a,t)=>{Object.defineProperty(a,"__esModule",{value:!0}),!function(e,a){for(var t in a)Object.defineProperty(e,t,{enumerable:!0,get:a[t]})}(a,{default:function(){return d},getImageProps:function(){return o}});let r=t(88229),n=t(38883),s=t(33063),i=r._(t(51193));function o(e){let{props:a}=(0,n.getImgProps)(e,{defaultLoader:i.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,t]of Object.entries(a))void 0===t&&delete a[e];return{props:a}}let d=s.Image},74338:(e,a,t)=>{t.d(a,{k:()=>s});var r=t(95155),n=t(59434);function s(e){let{size:a="md",className:t}=e;return(0,r.jsx)("div",{className:(0,n.cn)("flex items-center justify-center",t),children:(0,r.jsx)("div",{className:(0,n.cn)("animate-spin rounded-full border-2 border-white border-t-transparent",{sm:"w-4 h-4",md:"w-6 h-6",lg:"w-12 h-12"}[a])})})}},93915:(e,a,t)=>{t.d(a,{p:()=>i});var r=t(95155),n=t(12115),s=t(59434);let i=(0,n.forwardRef)((e,a)=>{let{className:t,type:n,label:i,error:o,...d}=e;return(0,r.jsxs)("div",{className:"w-full",children:[i&&(0,r.jsx)("label",{className:"block mb-2 font-semibold text-[var(--primary-brown)] font-[var(--font-ui)]",children:i}),(0,r.jsx)("input",{type:n,className:(0,s.cn)("\n            w-full px-4 py-2 border-2 border-[var(--medium-gray)] \n            rounded-[var(--radius-md)] font-[var(--font-ui)] \n            transition-colors duration-300\n            focus:outline-none focus:border-[var(--primary-brown)] \n            focus:ring-0 focus:shadow-[0_0_0_3px_rgba(139,69,19,0.1)]\n            placeholder:text-[var(--medium-gray)]\n            ",o&&"border-red-500 focus:border-red-500",t),ref:a,...d}),o&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-600",children:o})]})});i.displayName="Input"}}]);