"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[228],{13741:(e,t,a)=>{a.d(t,{$:()=>i});var r=a(95155),n=a(12115),o=a(59434);let i=(0,n.forwardRef)((e,t)=>{let{className:a,variant:n="primary",size:i="md",isLoading:s,children:l,disabled:d,...c}=e;return(0,r.jsxs)("button",{className:(0,o.cn)("\n      inline-flex items-center justify-center rounded-[var(--radius-md)] \n      font-[var(--font-ui)] font-semibold transition-all duration-300 \n      focus:outline-none focus:ring-2 focus:ring-offset-2\n      disabled:opacity-50 disabled:cursor-not-allowed\n      hover:transform hover:-translate-y-0.5 hover:shadow-[var(--shadow-lg)]\n    ",{primary:"\n        bg-[var(--primary-brown)] text-white \n        hover:bg-[#A0522D] focus:ring-[var(--primary-brown)]\n      ",secondary:"\n        bg-[var(--secondary-sky)] text-white \n        hover:bg-[#357AE0] focus:ring-[var(--secondary-sky)]\n      ",outline:"\n        bg-transparent border-2 border-[var(--primary-brown)] text-[var(--primary-brown)]\n        hover:bg-[var(--primary-brown)] hover:text-white focus:ring-[var(--primary-brown)]\n      ",hunting:"\n        bg-[var(--hunting-accent)] text-white \n        hover:bg-[#A67C00] focus:ring-[var(--hunting-accent)]\n      ",photo:"\n        bg-[var(--photo-accent)] text-white \n        hover:bg-[#3557C7] focus:ring-[var(--photo-accent)]\n      "}[n],{sm:"px-3 py-1.5 text-sm",md:"px-6 py-2 text-base",lg:"px-8 py-3 text-lg"}[i],s&&"cursor-wait",a),disabled:d||s,ref:t,...c,children:[s&&(0,r.jsxs)("svg",{className:"mr-2 h-4 w-4 animate-spin",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,r.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,r.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),l]})});i.displayName="Button"},17703:(e,t,a)=>{a.d(t,{MH:()=>u,Wu:()=>l,ZB:()=>c,Zp:()=>s,aR:()=>d});var r=a(95155),n=a(12115),o=a(66766),i=a(59434);let s=(0,n.forwardRef)((e,t)=>{let{className:a,hover:n=!0,children:o,...s}=e;return(0,r.jsx)("div",{ref:t,className:(0,i.cn)("\n          bg-white rounded-[var(--radius-lg)] shadow-[var(--shadow-md)] \n          overflow-hidden transition-all duration-300\n          ",n&&"hover:transform hover:-translate-y-1 hover:shadow-[var(--shadow-xl)]",a),...s,children:o})});s.displayName="Card";let l=(0,n.forwardRef)((e,t)=>{let{className:a,...n}=e;return(0,r.jsx)("div",{ref:t,className:(0,i.cn)("p-[var(--space-lg)]",a),...n})});l.displayName="CardContent";let d=(0,n.forwardRef)((e,t)=>{let{className:a,...n}=e;return(0,r.jsx)("div",{ref:t,className:(0,i.cn)("px-[var(--space-lg)] pt-[var(--space-lg)]",a),...n})});d.displayName="CardHeader";let c=(0,n.forwardRef)((e,t)=>{let{className:a,...n}=e;return(0,r.jsx)("h3",{ref:t,className:(0,i.cn)("text-xl font-bold mb-2 text-[var(--primary-brown)] font-[var(--font-display)]",a),...n})});c.displayName="CardTitle";let u=(0,n.forwardRef)((e,t)=>{let{className:a,src:n,alt:s,children:l,...d}=e;return(0,r.jsx)("div",{ref:t,className:(0,i.cn)("relative w-full h-48 bg-gradient-to-br from-[var(--primary-green)] to-[var(--secondary-sunset)] flex items-center justify-center text-white text-5xl",a),...d,children:n?(0,r.jsx)(o.default,{src:n,alt:s||"",fill:!0,className:"object-cover"}):l})});u.displayName="CardImage"},28214:(e,t,a)=>{a.d(t,{IG:()=>r.IG,db:()=>r.db,j2:()=>r.j2});var r=a(67039)},28666:(e,t,a)=>{a.d(t,{C2:()=>d,QQ:()=>i,eg:()=>s,zi:()=>l});var r=a(35317),n=a(28214);function o(e){var t,a,r,n;if(!e.exists())return null;let o=e.data();return{id:e.id,...o,createdAt:(null==o||null==(a=o.createdAt)||null==(t=a.toDate)?void 0:t.call(a))||(null==o?void 0:o.createdAt),updatedAt:(null==o||null==(n=o.updatedAt)||null==(r=n.toDate)?void 0:r.call(n))||(null==o?void 0:o.updatedAt)}}let i={async get(e){let t=(0,r.H9)(n.db,"users",e);return o(await (0,r.x7)(t))},async create(e,t){let a=(0,r.H9)(n.db,"users",e),o=new Date;await (0,r.BN)(a,{...t,createdAt:r.Dc.fromDate(o),updatedAt:r.Dc.fromDate(o)})},async update(e,t){let a=(0,r.H9)(n.db,"users",e);await (0,r.mZ)(a,{...t,updatedAt:r.Dc.now()})}},s={async getAll(e){let t=(0,r.rJ)(n.db,"farms");return(null==e?void 0:e.isActive)!==void 0&&(t=(0,r.P)(t,(0,r._M)("isActive","==",e.isActive))),(null==e?void 0:e.featured)!==void 0&&(t=(0,r.P)(t,(0,r._M)("featured","==",e.featured))),(null==e?void 0:e.province)&&(t=(0,r.P)(t,(0,r._M)("province","==",e.province))),(null==e?void 0:e.activityType)&&(t=(0,r.P)(t,(0,r._M)("activityTypes","==",e.activityType))),t=(0,r.P)(t,(0,r.My)("createdAt","desc")),(null==e?void 0:e.limit)&&(t=(0,r.P)(t,(0,r.AB)(e.limit))),(await (0,r.GG)(t)).docs.map(e=>o(e)).filter(Boolean)},async get(e){let t=(0,r.H9)(n.db,"farms",e);return o(await (0,r.x7)(t))},async getByOwner(e){let t=(0,r.P)((0,r.rJ)(n.db,"farms"),(0,r._M)("ownerId","==",e),(0,r.My)("createdAt","desc"));return(await (0,r.GG)(t)).docs.map(e=>o(e)).filter(Boolean)},async getActive(e){let t=(0,r.P)((0,r.rJ)(n.db,"farms"),(0,r._M)("isActive","==",!0),(0,r.My)("createdAt","desc"));return e&&(t=(0,r.P)(t,(0,r.AB)(e))),(await (0,r.GG)(t)).docs.map(e=>o(e)).filter(Boolean)},async create(e){let t=new Date;return(await (0,r.gS)((0,r.rJ)(n.db,"farms"),{...e,createdAt:r.Dc.fromDate(t),updatedAt:r.Dc.fromDate(t)})).id},async update(e,t){let a=(0,r.H9)(n.db,"farms",e);await (0,r.mZ)(a,{...t,updatedAt:r.Dc.now()})},async delete(e){let t=(0,r.H9)(n.db,"farms",e);await (0,r.kd)(t)},async addImage(e,t){let a=new Date;return(await (0,r.gS)((0,r.rJ)(n.db,"farms",e,"images"),{...t,createdAt:r.Dc.fromDate(a),updatedAt:r.Dc.fromDate(a)})).id},async getImages(e){let t=(0,r.P)((0,r.rJ)(n.db,"farms",e,"images"),(0,r.My)("displayOrder","asc"),(0,r.My)("createdAt","asc"));return(await (0,r.GG)(t)).docs.map(e=>o(e)).filter(Boolean)},async deleteImage(e,t){let a=(0,r.H9)(n.db,"farms",e,"images",t);await (0,r.kd)(a)}},l={async getAll(e){let t=(0,r.rJ)(n.db,"bookings");return(null==e?void 0:e.hunterId)&&(t=(0,r.P)(t,(0,r._M)("hunterId","==",e.hunterId))),(null==e?void 0:e.farmId)&&(t=(0,r.P)(t,(0,r._M)("farmId","==",e.farmId))),(null==e?void 0:e.status)&&(t=(0,r.P)(t,(0,r._M)("status","==",e.status))),t=(0,r.P)(t,(0,r.My)("createdAt","desc")),(null==e?void 0:e.limit)&&(t=(0,r.P)(t,(0,r.AB)(e.limit))),(await (0,r.GG)(t)).docs.map(e=>o(e)).filter(Boolean)},async get(e){let t=(0,r.H9)(n.db,"bookings",e);return o(await (0,r.x7)(t))},async create(e){let t=new Date,a="BVR-".concat(t.getFullYear()).concat((t.getMonth()+1).toString().padStart(2,"0")).concat(t.getDate().toString().padStart(2,"0"),"-").concat(Math.random().toString(36).substring(2,8).toUpperCase());return(await (0,r.gS)((0,r.rJ)(n.db,"bookings"),{...e,bookingReference:a,createdAt:r.Dc.fromDate(t),updatedAt:r.Dc.fromDate(t)})).id},async update(e,t){let a=(0,r.H9)(n.db,"bookings",e);await (0,r.mZ)(a,{...t,updatedAt:r.Dc.now()})}},d={async getByFarm(e){let t=(0,r.P)((0,r.rJ)(n.db,"farms",e,"reviews"),(0,r._M)("isPublic","==",!0),(0,r.My)("createdAt","desc"));return(await (0,r.GG)(t)).docs.map(e=>o(e)).filter(Boolean)},async create(e,t){let a=new Date;return(await (0,r.gS)((0,r.rJ)(n.db,"farms",e,"reviews"),{...t,createdAt:r.Dc.fromDate(a),updatedAt:r.Dc.fromDate(a)})).id},async update(e,t,a){let o=(0,r.H9)(n.db,"farms",e,"reviews",t);await (0,r.mZ)(o,{...a,updatedAt:r.Dc.now()})}}},59434:(e,t,a)=>{a.d(t,{Y:()=>s,Z:()=>i,cn:()=>o});var r=a(52596),n=a(39688);function o(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,n.QP)((0,r.$)(t))}function i(e){return e.toString().replace(/\B(?=(\d{3})+(?!\d))/g," ")}function s(e){let t=new Date(e);return"".concat(t.getDate()," ").concat(["January","February","March","April","May","June","July","August","September","October","November","December"][t.getMonth()]," ").concat(t.getFullYear())}},67039:(e,t,a)=>{a.d(t,{IG:()=>c,db:()=>d,j2:()=>l});var r=a(23915),n=a(16203),o=a(35317),i=a(90858);let s=(0,r.Wp)({apiKey:"AIzaSyDdsTm8eifLrb1W0WLK0DwBk4p4XLFZKMw",authDomain:"rvbsafaris.firebaseapp.com",projectId:"rvbsafaris",storageBucket:"rvbsafaris.firebasestorage.app",messagingSenderId:"593781635754",appId:"1:593781635754:web:7a39a3b4451f34f85feed3"}),l=(0,n.xI)(s),d=(0,o.aU)(s),c=(0,i.c7)(s);console.log("Firebase Storage initialized with bucket:","rvbsafaris.firebasestorage.app")},84105:(e,t,a)=>{a.d(t,{As:()=>c,AuthProvider:()=>d,Nu:()=>f});var r=a(95155),n=a(12115),o=a(16203),i=a(35317),s=a(28214);let l=(0,n.createContext)(void 0);function d(e){let{children:t}=e,[a,d]=(0,n.useState)(null),[c,f]=(0,n.useState)(null),[m,w]=(0,n.useState)(!0),[p,y]=(0,n.useState)(null),v=async e=>{try{let o=(0,i.H9)(s.db,"users",e),l=await (0,i.x7)(o);if(l.exists()){var t,a,r,n;let e=l.data();return{id:l.id,...e,createdAt:(null==(a=e.createdAt)||null==(t=a.toDate)?void 0:t.call(a))||e.createdAt,updatedAt:(null==(n=e.updatedAt)||null==(r=n.toDate)?void 0:r.call(n))||e.updatedAt}}return null}catch(e){return console.error("Error fetching user profile:",e),null}},h=async()=>{a&&f(await v(a.uid))},g=e=>new Promise(t=>{let a=(0,o.hg)(s.j2,r=>{e&&r&&r.uid===e.uid?(a(),t()):e||r||(a(),t())})});(0,n.useEffect)(()=>(0,o.hg)(s.j2,async e=>{if(d(e),e){try{let t=await e.getIdToken();document.cookie="firebase-token=".concat(t,"; path=/; max-age=3600; secure; samesite=strict")}catch(e){console.error("Error getting ID token:",e)}f(await v(e.uid))}else document.cookie="firebase-token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT",f(null);w(!1)}),[]);let b=async(e,t)=>{try{y(null),w(!0);let a=await (0,o.x9)(s.j2,e,t);await g(a.user),await new Promise(e=>setTimeout(e,100))}catch(e){throw y(u(e instanceof Error&&"code"in e?e.code:"unknown")),e}finally{w(!1)}},A=async(e,t,a)=>{try{y(null),w(!0);let r=(await (0,o.eJ)(s.j2,e,t)).user;await (0,o.r7)(r,{displayName:"".concat(a.firstName," ").concat(a.lastName)});let n=i.Dc.now(),l={email:e,fullName:"".concat(a.firstName," ").concat(a.lastName),firstName:a.firstName,lastName:a.lastName,phone:a.phone||null,role:a.role,languagePreference:"en",createdAt:n,updatedAt:n};await (0,i.BN)((0,i.H9)(s.db,"users",r.uid),l),f({id:r.uid,...l,phone:a.phone||void 0});try{let e=await r.getIdToken(),t=await fetch("/api/auth/set-role",{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(e)},body:JSON.stringify({uid:r.uid,role:a.role})});if(t.ok)console.log("User role set successfully");else{let e=await t.json();console.error("Failed to set user role:",e.error)}}catch(e){console.error("Error setting user role:",e)}await g(r),await new Promise(e=>setTimeout(e,100))}catch(e){throw y(u(e instanceof Error&&"code"in e?e.code:"unknown")),e}finally{w(!1)}},x=async()=>{try{y(null),await (0,o.CI)(s.j2),f(null)}catch(e){throw y(u(e instanceof Error&&"code"in e?e.code:"unknown")),e}},D=async e=>{try{y(null),await (0,o.J1)(s.j2,e)}catch(e){throw y(u(e instanceof Error&&"code"in e?e.code:"unknown")),e}};return(0,r.jsx)(l.Provider,{value:{user:a,userProfile:c,loading:m,error:p,signIn:b,signUp:A,signOut:x,resetPassword:D,clearError:()=>{y(null)},refreshProfile:h},children:t})}function c(){let e=(0,n.useContext)(l);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}function u(e){switch(e){case"auth/user-not-found":case"auth/wrong-password":case"auth/invalid-credential":return"Invalid email or password";case"auth/email-already-in-use":return"An account with this email already exists";case"auth/weak-password":return"Password should be at least 6 characters";case"auth/invalid-email":return"Invalid email address";case"auth/too-many-requests":return"Too many failed attempts. Please try again later";case"auth/network-request-failed":return"Network error. Please check your connection";default:return"An unexpected error occurred. Please try again"}}function f(){let{user:e,loading:t}=c();return{user:e,loading:t,isAuthenticated:!!e}}}}]);