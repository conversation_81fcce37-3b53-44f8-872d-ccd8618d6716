{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/src/personal/bvr-safaris/src/components/ui/Card.tsx"], "sourcesContent": ["import { HTMLAttributes, forwardRef } from 'react'\nimport Image from 'next/image'\nimport { cn } from '@/lib/utils'\n\nexport interface CardProps extends HTMLAttributes<HTMLDivElement> {\n  hover?: boolean\n}\n\nconst Card = forwardRef<HTMLDivElement, CardProps>(\n  ({ className, hover = true, children, ...props }, ref) => {\n    return (\n      <div\n        ref={ref}\n        className={cn(\n          `\n          bg-white rounded-[var(--radius-lg)] shadow-[var(--shadow-md)] \n          overflow-hidden transition-all duration-300\n          `,\n          hover && 'hover:transform hover:-translate-y-1 hover:shadow-[var(--shadow-xl)]',\n          className\n        )}\n        {...props}\n      >\n        {children}\n      </div>\n    )\n  }\n)\n\nCard.displayName = 'Card'\n\nconst CardContent = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn('p-[var(--space-lg)]', className)}\n      {...props}\n    />\n  )\n)\n\nCardContent.displayName = 'CardContent'\n\nconst CardHeader = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn('px-[var(--space-lg)] pt-[var(--space-lg)]', className)}\n      {...props}\n    />\n  )\n)\n\nCardHeader.displayName = 'CardHeader'\n\nconst CardTitle = forwardRef<HTMLHeadingElement, HTMLAttributes<HTMLHeadingElement>>(\n  ({ className, ...props }, ref) => (\n    <h3\n      ref={ref}\n      className={cn(\n        'text-xl font-bold mb-2 text-[var(--primary-brown)] font-[var(--font-display)]',\n        className\n      )}\n      {...props}\n    />\n  )\n)\n\nCardTitle.displayName = 'CardTitle'\n\nconst CardImage = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement> & {\n  src?: string\n  alt?: string\n  children?: React.ReactNode\n}>(\n  ({ className, src, alt, children, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(\n        'relative w-full h-48 bg-gradient-to-br from-[var(--primary-green)] to-[var(--secondary-sunset)] flex items-center justify-center text-white text-5xl',\n        className\n      )}\n      {...props}\n    >\n      {src ? (\n        <Image src={src} alt={alt || ''} fill className=\"object-cover\" />\n      ) : (\n        children\n      )}\n    </div>\n  )\n)\n\nCardImage.displayName = 'CardImage'\n\nexport { Card, CardContent, CardHeader, CardTitle, CardImage }"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;AACA;;;;;AAMA,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,OACpB,CAAC,EAAE,SAAS,EAAE,QAAQ,IAAI,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IAChD,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,CAAC;;;UAGD,CAAC,EACD,SAAS,wEACT;QAED,GAAG,KAAK;kBAER;;;;;;AAGP;;AAGF,KAAK,WAAW,GAAG;AAEnB,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,QAC3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;;AAKf,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,QAC1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;;AAKf,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,QACzB,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iFACA;QAED,GAAG,KAAK;;;;;;;AAKf,UAAU,WAAW,GAAG;AAExB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,QAKzB,CAAC,EAAE,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAC5C,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wJACA;QAED,GAAG,KAAK;kBAER,oBACC,6LAAC,gIAAA,CAAA,UAAK;YAAC,KAAK;YAAK,KAAK,OAAO;YAAI,IAAI;YAAC,WAAU;;;;;mBAEhD;;;;;;;AAMR,UAAU,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 114, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/src/personal/bvr-safaris/src/components/ui/LoadingSpinner.tsx"], "sourcesContent": ["import { cn } from '@/lib/utils'\n\ninterface LoadingSpinnerProps {\n  size?: 'sm' | 'md' | 'lg'\n  className?: string\n}\n\nexport function LoadingSpinner({ size = 'md', className }: LoadingSpinnerProps) {\n  const sizeClasses = {\n    sm: 'w-4 h-4',\n    md: 'w-6 h-6',\n    lg: 'w-12 h-12'\n  }\n\n  return (\n    <div className={cn('flex items-center justify-center', className)}>\n      <div\n        className={cn(\n          'animate-spin rounded-full border-2 border-white border-t-transparent',\n          sizeClasses[size]\n        )}\n      />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAOO,SAAS,eAAe,EAAE,OAAO,IAAI,EAAE,SAAS,EAAuB;IAC5E,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,oCAAoC;kBACrD,cAAA,6LAAC;YACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wEACA,WAAW,CAAC,KAAK;;;;;;;;;;;AAK3B;KAjBgB", "debugId": null}}, {"offset": {"line": 154, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/src/personal/bvr-safaris/src/lib/firebase/firestore.ts"], "sourcesContent": ["import {\n  collection,\n  doc,\n  getDoc,\n  getDocs,\n  addDoc,\n  setDoc,\n  updateDoc,\n  deleteDoc,\n  query,\n  where,\n  orderBy,\n  limit,\n  Timestamp,\n  DocumentSnapshot,\n  QueryDocumentSnapshot,\n  DocumentData,\n  Query\n} from 'firebase/firestore'\nimport { db } from './client'\nimport {\n  UserProfile,\n  GameFarm,\n  Booking,\n  Review,\n  FarmImage,\n  GameSpecies,\n  FarmAmenity,\n  FirestoreDocument\n} from '@/lib/types/firestore'\n\n// Helper function to convert Firestore document to typed object\nexport function docToData<T extends FirestoreDocument>(\n  doc: QueryDocumentSnapshot<DocumentData> | DocumentSnapshot<DocumentData>\n): T | null {\n  if (!doc.exists()) return null\n  \n  const data = doc.data()\n  return {\n    id: doc.id,\n    ...data,\n    // Convert Firestore Timestamps to Date objects for easier handling\n    createdAt: data?.createdAt?.toDate?.() || data?.createdAt,\n    updatedAt: data?.updatedAt?.toDate?.() || data?.updatedAt,\n  } as T\n}\n\n\n\n// User Profile operations\nexport const userProfileService = {\n  async get(userId: string): Promise<UserProfile | null> {\n    const docRef = doc(db, 'users', userId)\n    const docSnap = await getDoc(docRef)\n    return docToData<UserProfile>(docSnap)\n  },\n\n  async create(userId: string, data: Omit<UserProfile, 'id' | 'createdAt' | 'updatedAt'>): Promise<void> {\n    const docRef = doc(db, 'users', userId)\n    const now = new Date()\n    await setDoc(docRef, {\n      ...data,\n      createdAt: Timestamp.fromDate(now),\n      updatedAt: Timestamp.fromDate(now)\n    })\n  },\n\n  async update(userId: string, data: Partial<UserProfile>): Promise<void> {\n    const docRef = doc(db, 'users', userId)\n    await updateDoc(docRef, {\n      ...data,\n      updatedAt: Timestamp.now()\n    })\n  }\n}\n\n// Game Farm operations\nexport const farmService = {\n  async getAll(filters?: {\n    isActive?: boolean\n    featured?: boolean\n    province?: string\n    activityType?: string\n    limit?: number\n  }): Promise<GameFarm[]> {\n    let q: Query<DocumentData> = collection(db, 'farms')\n\n    if (filters?.isActive !== undefined) {\n      q = query(q, where('isActive', '==', filters.isActive))\n    }\n    if (filters?.featured !== undefined) {\n      q = query(q, where('featured', '==', filters.featured))\n    }\n    if (filters?.province) {\n      q = query(q, where('province', '==', filters.province))\n    }\n    if (filters?.activityType) {\n      q = query(q, where('activityTypes', '==', filters.activityType))\n    }\n\n    q = query(q, orderBy('createdAt', 'desc'))\n\n    if (filters?.limit) {\n      q = query(q, limit(filters.limit))\n    }\n\n    const querySnapshot = await getDocs(q)\n    return querySnapshot.docs.map(doc => docToData<GameFarm>(doc)!).filter(Boolean)\n  },\n\n  async get(farmId: string): Promise<GameFarm | null> {\n    const docRef = doc(db, 'farms', farmId)\n    const docSnap = await getDoc(docRef)\n    return docToData<GameFarm>(docSnap)\n  },\n\n  async getByOwner(ownerId: string): Promise<GameFarm[]> {\n    const q = query(\n      collection(db, 'farms'),\n      where('ownerId', '==', ownerId),\n      orderBy('createdAt', 'desc')\n    )\n    const querySnapshot = await getDocs(q)\n    return querySnapshot.docs.map(doc => docToData<GameFarm>(doc)!).filter(Boolean)\n  },\n\n  async getActive(limitCount?: number): Promise<GameFarm[]> {\n    let q = query(\n      collection(db, 'farms'),\n      where('isActive', '==', true),\n      orderBy('createdAt', 'desc')\n    )\n\n    if (limitCount) {\n      q = query(q, limit(limitCount))\n    }\n\n    const querySnapshot = await getDocs(q)\n    return querySnapshot.docs.map(doc => docToData<GameFarm>(doc)!).filter(Boolean)\n  },\n\n  async create(data: Omit<GameFarm, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {\n    const now = new Date()\n    const docRef = await addDoc(collection(db, 'farms'), {\n      ...data,\n      createdAt: Timestamp.fromDate(now),\n      updatedAt: Timestamp.fromDate(now)\n    })\n    return docRef.id\n  },\n\n  async update(farmId: string, data: Partial<GameFarm>): Promise<void> {\n    const docRef = doc(db, 'farms', farmId)\n    await updateDoc(docRef, {\n      ...data,\n      updatedAt: Timestamp.now()\n    })\n  },\n\n  async delete(farmId: string): Promise<void> {\n    const docRef = doc(db, 'farms', farmId)\n    await deleteDoc(docRef)\n  },\n\n  // Add farm images to subcollection\n  async addImage(farmId: string, imageData: Omit<FarmImage, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {\n    const now = new Date()\n    const docRef = await addDoc(collection(db, 'farms', farmId, 'images'), {\n      ...imageData,\n      createdAt: Timestamp.fromDate(now),\n      updatedAt: Timestamp.fromDate(now)\n    })\n    return docRef.id\n  },\n\n  // Get farm images\n  async getImages(farmId: string): Promise<FarmImage[]> {\n    const q = query(\n      collection(db, 'farms', farmId, 'images'),\n      orderBy('displayOrder', 'asc'),\n      orderBy('createdAt', 'asc')\n    )\n    const querySnapshot = await getDocs(q)\n    return querySnapshot.docs.map(doc => docToData<FarmImage>(doc)!).filter(Boolean)\n  },\n\n  // Delete farm image\n  async deleteImage(farmId: string, imageId: string): Promise<void> {\n    const docRef = doc(db, 'farms', farmId, 'images', imageId)\n    await deleteDoc(docRef)\n  }\n}\n\n// Booking operations\nexport const bookingService = {\n  async getAll(filters?: {\n    hunterId?: string\n    farmId?: string\n    status?: string\n    limit?: number\n  }): Promise<Booking[]> {\n    let q: Query<DocumentData> = collection(db, 'bookings')\n\n    if (filters?.hunterId) {\n      q = query(q, where('hunterId', '==', filters.hunterId))\n    }\n    if (filters?.farmId) {\n      q = query(q, where('farmId', '==', filters.farmId))\n    }\n    if (filters?.status) {\n      q = query(q, where('status', '==', filters.status))\n    }\n\n    q = query(q, orderBy('createdAt', 'desc'))\n\n    if (filters?.limit) {\n      q = query(q, limit(filters.limit))\n    }\n\n    const querySnapshot = await getDocs(q)\n    return querySnapshot.docs.map(doc => docToData<Booking>(doc)!).filter(Boolean)\n  },\n\n  async get(bookingId: string): Promise<Booking | null> {\n    const docRef = doc(db, 'bookings', bookingId)\n    const docSnap = await getDoc(docRef)\n    return docToData<Booking>(docSnap)\n  },\n\n  async create(data: Omit<Booking, 'id' | 'createdAt' | 'updatedAt' | 'bookingReference'>): Promise<string> {\n    const now = new Date()\n    const bookingReference = `BVR-${now.getFullYear()}${(now.getMonth() + 1).toString().padStart(2, '0')}${now.getDate().toString().padStart(2, '0')}-${Math.random().toString(36).substring(2, 8).toUpperCase()}`\n    \n    const docRef = await addDoc(collection(db, 'bookings'), {\n      ...data,\n      bookingReference,\n      createdAt: Timestamp.fromDate(now),\n      updatedAt: Timestamp.fromDate(now)\n    })\n    return docRef.id\n  },\n\n  async update(bookingId: string, data: Partial<Booking>): Promise<void> {\n    const docRef = doc(db, 'bookings', bookingId)\n    await updateDoc(docRef, {\n      ...data,\n      updatedAt: Timestamp.now()\n    })\n  }\n}\n\n// Review operations (subcollection under farms)\nexport const reviewService = {\n  async getByFarm(farmId: string): Promise<Review[]> {\n    const q = query(\n      collection(db, 'farms', farmId, 'reviews'),\n      where('isPublic', '==', true),\n      orderBy('createdAt', 'desc')\n    )\n    const querySnapshot = await getDocs(q)\n    return querySnapshot.docs.map(doc => docToData<Review>(doc)!).filter(Boolean)\n  },\n\n  async create(farmId: string, data: Omit<Review, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {\n    const now = new Date()\n    const docRef = await addDoc(collection(db, 'farms', farmId, 'reviews'), {\n      ...data,\n      createdAt: Timestamp.fromDate(now),\n      updatedAt: Timestamp.fromDate(now)\n    })\n    return docRef.id\n  },\n\n  async update(farmId: string, reviewId: string, data: Partial<Review>): Promise<void> {\n    const docRef = doc(db, 'farms', farmId, 'reviews', reviewId)\n    await updateDoc(docRef, {\n      ...data,\n      updatedAt: Timestamp.now()\n    })\n  }\n}\n\n// Species operations\nexport const speciesService = {\n  async getAll(): Promise<GameSpecies[]> {\n    const q = query(collection(db, 'species'), orderBy('name'))\n    const querySnapshot = await getDocs(q)\n    return querySnapshot.docs.map(doc => docToData<GameSpecies>(doc)!).filter(Boolean)\n  },\n\n  async get(speciesId: string): Promise<GameSpecies | null> {\n    const docRef = doc(db, 'species', speciesId)\n    const docSnap = await getDoc(docRef)\n    return docToData<GameSpecies>(docSnap)\n  }\n}\n\n// Amenities operations\nexport const amenityService = {\n  async getAll(): Promise<FarmAmenity[]> {\n    const q = query(collection(db, 'amenities'), orderBy('name'))\n    const querySnapshot = await getDocs(q)\n    return querySnapshot.docs.map(doc => docToData<FarmAmenity>(doc)!).filter(Boolean)\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAAA;AAmBA;AAAA;;;AAaO,SAAS,UACd,GAAyE;IAEzE,IAAI,CAAC,IAAI,MAAM,IAAI,OAAO;IAE1B,MAAM,OAAO,IAAI,IAAI;IACrB,OAAO;QACL,IAAI,IAAI,EAAE;QACV,GAAG,IAAI;QACP,mEAAmE;QACnE,WAAW,MAAM,WAAW,cAAc,MAAM;QAChD,WAAW,MAAM,WAAW,cAAc,MAAM;IAClD;AACF;AAKO,MAAM,qBAAqB;IAChC,MAAM,KAAI,MAAc;QACtB,MAAM,SAAS,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,mIAAA,CAAA,KAAE,EAAE,SAAS;QAChC,MAAM,UAAU,MAAM,CAAA,GAAA,sKAAA,CAAA,SAAM,AAAD,EAAE;QAC7B,OAAO,UAAuB;IAChC;IAEA,MAAM,QAAO,MAAc,EAAE,IAAyD;QACpF,MAAM,SAAS,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,mIAAA,CAAA,KAAE,EAAE,SAAS;QAChC,MAAM,MAAM,IAAI;QAChB,MAAM,CAAA,GAAA,sKAAA,CAAA,SAAM,AAAD,EAAE,QAAQ;YACnB,GAAG,IAAI;YACP,WAAW,sKAAA,CAAA,YAAS,CAAC,QAAQ,CAAC;YAC9B,WAAW,sKAAA,CAAA,YAAS,CAAC,QAAQ,CAAC;QAChC;IACF;IAEA,MAAM,QAAO,MAAc,EAAE,IAA0B;QACrD,MAAM,SAAS,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,mIAAA,CAAA,KAAE,EAAE,SAAS;QAChC,MAAM,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,QAAQ;YACtB,GAAG,IAAI;YACP,WAAW,sKAAA,CAAA,YAAS,CAAC,GAAG;QAC1B;IACF;AACF;AAGO,MAAM,cAAc;IACzB,MAAM,QAAO,OAMZ;QACC,IAAI,IAAyB,CAAA,GAAA,sKAAA,CAAA,aAAU,AAAD,EAAE,mIAAA,CAAA,KAAE,EAAE;QAE5C,IAAI,SAAS,aAAa,WAAW;YACnC,IAAI,CAAA,GAAA,sKAAA,CAAA,QAAK,AAAD,EAAE,GAAG,CAAA,GAAA,sKAAA,CAAA,QAAK,AAAD,EAAE,YAAY,MAAM,QAAQ,QAAQ;QACvD;QACA,IAAI,SAAS,aAAa,WAAW;YACnC,IAAI,CAAA,GAAA,sKAAA,CAAA,QAAK,AAAD,EAAE,GAAG,CAAA,GAAA,sKAAA,CAAA,QAAK,AAAD,EAAE,YAAY,MAAM,QAAQ,QAAQ;QACvD;QACA,IAAI,SAAS,UAAU;YACrB,IAAI,CAAA,GAAA,sKAAA,CAAA,QAAK,AAAD,EAAE,GAAG,CAAA,GAAA,sKAAA,CAAA,QAAK,AAAD,EAAE,YAAY,MAAM,QAAQ,QAAQ;QACvD;QACA,IAAI,SAAS,cAAc;YACzB,IAAI,CAAA,GAAA,sKAAA,CAAA,QAAK,AAAD,EAAE,GAAG,CAAA,GAAA,sKAAA,CAAA,QAAK,AAAD,EAAE,iBAAiB,MAAM,QAAQ,YAAY;QAChE;QAEA,IAAI,CAAA,GAAA,sKAAA,CAAA,QAAK,AAAD,EAAE,GAAG,CAAA,GAAA,sKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;QAElC,IAAI,SAAS,OAAO;YAClB,IAAI,CAAA,GAAA,sKAAA,CAAA,QAAK,AAAD,EAAE,GAAG,CAAA,GAAA,sKAAA,CAAA,QAAK,AAAD,EAAE,QAAQ,KAAK;QAClC;QAEA,MAAM,gBAAgB,MAAM,CAAA,GAAA,sKAAA,CAAA,UAAO,AAAD,EAAE;QACpC,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,UAAoB,MAAO,MAAM,CAAC;IACzE;IAEA,MAAM,KAAI,MAAc;QACtB,MAAM,SAAS,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,mIAAA,CAAA,KAAE,EAAE,SAAS;QAChC,MAAM,UAAU,MAAM,CAAA,GAAA,sKAAA,CAAA,SAAM,AAAD,EAAE;QAC7B,OAAO,UAAoB;IAC7B;IAEA,MAAM,YAAW,OAAe;QAC9B,MAAM,IAAI,CAAA,GAAA,sKAAA,CAAA,QAAK,AAAD,EACZ,CAAA,GAAA,sKAAA,CAAA,aAAU,AAAD,EAAE,mIAAA,CAAA,KAAE,EAAE,UACf,CAAA,GAAA,sKAAA,CAAA,QAAK,AAAD,EAAE,WAAW,MAAM,UACvB,CAAA,GAAA,sKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;QAEvB,MAAM,gBAAgB,MAAM,CAAA,GAAA,sKAAA,CAAA,UAAO,AAAD,EAAE;QACpC,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,UAAoB,MAAO,MAAM,CAAC;IACzE;IAEA,MAAM,WAAU,UAAmB;QACjC,IAAI,IAAI,CAAA,GAAA,sKAAA,CAAA,QAAK,AAAD,EACV,CAAA,GAAA,sKAAA,CAAA,aAAU,AAAD,EAAE,mIAAA,CAAA,KAAE,EAAE,UACf,CAAA,GAAA,sKAAA,CAAA,QAAK,AAAD,EAAE,YAAY,MAAM,OACxB,CAAA,GAAA,sKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;QAGvB,IAAI,YAAY;YACd,IAAI,CAAA,GAAA,sKAAA,CAAA,QAAK,AAAD,EAAE,GAAG,CAAA,GAAA,sKAAA,CAAA,QAAK,AAAD,EAAE;QACrB;QAEA,MAAM,gBAAgB,MAAM,CAAA,GAAA,sKAAA,CAAA,UAAO,AAAD,EAAE;QACpC,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,UAAoB,MAAO,MAAM,CAAC;IACzE;IAEA,MAAM,QAAO,IAAsD;QACjE,MAAM,MAAM,IAAI;QAChB,MAAM,SAAS,MAAM,CAAA,GAAA,sKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,sKAAA,CAAA,aAAU,AAAD,EAAE,mIAAA,CAAA,KAAE,EAAE,UAAU;YACnD,GAAG,IAAI;YACP,WAAW,sKAAA,CAAA,YAAS,CAAC,QAAQ,CAAC;YAC9B,WAAW,sKAAA,CAAA,YAAS,CAAC,QAAQ,CAAC;QAChC;QACA,OAAO,OAAO,EAAE;IAClB;IAEA,MAAM,QAAO,MAAc,EAAE,IAAuB;QAClD,MAAM,SAAS,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,mIAAA,CAAA,KAAE,EAAE,SAAS;QAChC,MAAM,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,QAAQ;YACtB,GAAG,IAAI;YACP,WAAW,sKAAA,CAAA,YAAS,CAAC,GAAG;QAC1B;IACF;IAEA,MAAM,QAAO,MAAc;QACzB,MAAM,SAAS,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,mIAAA,CAAA,KAAE,EAAE,SAAS;QAChC,MAAM,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE;IAClB;IAEA,mCAAmC;IACnC,MAAM,UAAS,MAAc,EAAE,SAA4D;QACzF,MAAM,MAAM,IAAI;QAChB,MAAM,SAAS,MAAM,CAAA,GAAA,sKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,sKAAA,CAAA,aAAU,AAAD,EAAE,mIAAA,CAAA,KAAE,EAAE,SAAS,QAAQ,WAAW;YACrE,GAAG,SAAS;YACZ,WAAW,sKAAA,CAAA,YAAS,CAAC,QAAQ,CAAC;YAC9B,WAAW,sKAAA,CAAA,YAAS,CAAC,QAAQ,CAAC;QAChC;QACA,OAAO,OAAO,EAAE;IAClB;IAEA,kBAAkB;IAClB,MAAM,WAAU,MAAc;QAC5B,MAAM,IAAI,CAAA,GAAA,sKAAA,CAAA,QAAK,AAAD,EACZ,CAAA,GAAA,sKAAA,CAAA,aAAU,AAAD,EAAE,mIAAA,CAAA,KAAE,EAAE,SAAS,QAAQ,WAChC,CAAA,GAAA,sKAAA,CAAA,UAAO,AAAD,EAAE,gBAAgB,QACxB,CAAA,GAAA,sKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;QAEvB,MAAM,gBAAgB,MAAM,CAAA,GAAA,sKAAA,CAAA,UAAO,AAAD,EAAE;QACpC,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,UAAqB,MAAO,MAAM,CAAC;IAC1E;IAEA,oBAAoB;IACpB,MAAM,aAAY,MAAc,EAAE,OAAe;QAC/C,MAAM,SAAS,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,mIAAA,CAAA,KAAE,EAAE,SAAS,QAAQ,UAAU;QAClD,MAAM,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE;IAClB;AACF;AAGO,MAAM,iBAAiB;IAC5B,MAAM,QAAO,OAKZ;QACC,IAAI,IAAyB,CAAA,GAAA,sKAAA,CAAA,aAAU,AAAD,EAAE,mIAAA,CAAA,KAAE,EAAE;QAE5C,IAAI,SAAS,UAAU;YACrB,IAAI,CAAA,GAAA,sKAAA,CAAA,QAAK,AAAD,EAAE,GAAG,CAAA,GAAA,sKAAA,CAAA,QAAK,AAAD,EAAE,YAAY,MAAM,QAAQ,QAAQ;QACvD;QACA,IAAI,SAAS,QAAQ;YACnB,IAAI,CAAA,GAAA,sKAAA,CAAA,QAAK,AAAD,EAAE,GAAG,CAAA,GAAA,sKAAA,CAAA,QAAK,AAAD,EAAE,UAAU,MAAM,QAAQ,MAAM;QACnD;QACA,IAAI,SAAS,QAAQ;YACnB,IAAI,CAAA,GAAA,sKAAA,CAAA,QAAK,AAAD,EAAE,GAAG,CAAA,GAAA,sKAAA,CAAA,QAAK,AAAD,EAAE,UAAU,MAAM,QAAQ,MAAM;QACnD;QAEA,IAAI,CAAA,GAAA,sKAAA,CAAA,QAAK,AAAD,EAAE,GAAG,CAAA,GAAA,sKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;QAElC,IAAI,SAAS,OAAO;YAClB,IAAI,CAAA,GAAA,sKAAA,CAAA,QAAK,AAAD,EAAE,GAAG,CAAA,GAAA,sKAAA,CAAA,QAAK,AAAD,EAAE,QAAQ,KAAK;QAClC;QAEA,MAAM,gBAAgB,MAAM,CAAA,GAAA,sKAAA,CAAA,UAAO,AAAD,EAAE;QACpC,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,UAAmB,MAAO,MAAM,CAAC;IACxE;IAEA,MAAM,KAAI,SAAiB;QACzB,MAAM,SAAS,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,mIAAA,CAAA,KAAE,EAAE,YAAY;QACnC,MAAM,UAAU,MAAM,CAAA,GAAA,sKAAA,CAAA,SAAM,AAAD,EAAE;QAC7B,OAAO,UAAmB;IAC5B;IAEA,MAAM,QAAO,IAA0E;QACrF,MAAM,MAAM,IAAI;QAChB,MAAM,mBAAmB,CAAC,IAAI,EAAE,IAAI,WAAW,KAAK,CAAC,IAAI,QAAQ,KAAK,CAAC,EAAE,QAAQ,GAAG,QAAQ,CAAC,GAAG,OAAO,IAAI,OAAO,GAAG,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG,GAAG,WAAW,IAAI;QAE9M,MAAM,SAAS,MAAM,CAAA,GAAA,sKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,sKAAA,CAAA,aAAU,AAAD,EAAE,mIAAA,CAAA,KAAE,EAAE,aAAa;YACtD,GAAG,IAAI;YACP;YACA,WAAW,sKAAA,CAAA,YAAS,CAAC,QAAQ,CAAC;YAC9B,WAAW,sKAAA,CAAA,YAAS,CAAC,QAAQ,CAAC;QAChC;QACA,OAAO,OAAO,EAAE;IAClB;IAEA,MAAM,QAAO,SAAiB,EAAE,IAAsB;QACpD,MAAM,SAAS,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,mIAAA,CAAA,KAAE,EAAE,YAAY;QACnC,MAAM,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,QAAQ;YACtB,GAAG,IAAI;YACP,WAAW,sKAAA,CAAA,YAAS,CAAC,GAAG;QAC1B;IACF;AACF;AAGO,MAAM,gBAAgB;IAC3B,MAAM,WAAU,MAAc;QAC5B,MAAM,IAAI,CAAA,GAAA,sKAAA,CAAA,QAAK,AAAD,EACZ,CAAA,GAAA,sKAAA,CAAA,aAAU,AAAD,EAAE,mIAAA,CAAA,KAAE,EAAE,SAAS,QAAQ,YAChC,CAAA,GAAA,sKAAA,CAAA,QAAK,AAAD,EAAE,YAAY,MAAM,OACxB,CAAA,GAAA,sKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;QAEvB,MAAM,gBAAgB,MAAM,CAAA,GAAA,sKAAA,CAAA,UAAO,AAAD,EAAE;QACpC,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,UAAkB,MAAO,MAAM,CAAC;IACvE;IAEA,MAAM,QAAO,MAAc,EAAE,IAAoD;QAC/E,MAAM,MAAM,IAAI;QAChB,MAAM,SAAS,MAAM,CAAA,GAAA,sKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,sKAAA,CAAA,aAAU,AAAD,EAAE,mIAAA,CAAA,KAAE,EAAE,SAAS,QAAQ,YAAY;YACtE,GAAG,IAAI;YACP,WAAW,sKAAA,CAAA,YAAS,CAAC,QAAQ,CAAC;YAC9B,WAAW,sKAAA,CAAA,YAAS,CAAC,QAAQ,CAAC;QAChC;QACA,OAAO,OAAO,EAAE;IAClB;IAEA,MAAM,QAAO,MAAc,EAAE,QAAgB,EAAE,IAAqB;QAClE,MAAM,SAAS,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,mIAAA,CAAA,KAAE,EAAE,SAAS,QAAQ,WAAW;QACnD,MAAM,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,QAAQ;YACtB,GAAG,IAAI;YACP,WAAW,sKAAA,CAAA,YAAS,CAAC,GAAG;QAC1B;IACF;AACF;AAGO,MAAM,iBAAiB;IAC5B,MAAM;QACJ,MAAM,IAAI,CAAA,GAAA,sKAAA,CAAA,QAAK,AAAD,EAAE,CAAA,GAAA,sKAAA,CAAA,aAAU,AAAD,EAAE,mIAAA,CAAA,KAAE,EAAE,YAAY,CAAA,GAAA,sKAAA,CAAA,UAAO,AAAD,EAAE;QACnD,MAAM,gBAAgB,MAAM,CAAA,GAAA,sKAAA,CAAA,UAAO,AAAD,EAAE;QACpC,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,UAAuB,MAAO,MAAM,CAAC;IAC5E;IAEA,MAAM,KAAI,SAAiB;QACzB,MAAM,SAAS,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,mIAAA,CAAA,KAAE,EAAE,WAAW;QAClC,MAAM,UAAU,MAAM,CAAA,GAAA,sKAAA,CAAA,SAAM,AAAD,EAAE;QAC7B,OAAO,UAAuB;IAChC;AACF;AAGO,MAAM,iBAAiB;IAC5B,MAAM;QACJ,MAAM,IAAI,CAAA,GAAA,sKAAA,CAAA,QAAK,AAAD,EAAE,CAAA,GAAA,sKAAA,CAAA,aAAU,AAAD,EAAE,mIAAA,CAAA,KAAE,EAAE,cAAc,CAAA,GAAA,sKAAA,CAAA,UAAO,AAAD,EAAE;QACrD,MAAM,gBAAgB,MAAM,CAAA,GAAA,sKAAA,CAAA,UAAO,AAAD,EAAE;QACpC,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,UAAuB,MAAO,MAAM,CAAC;IAC5E;AACF", "debugId": null}}, {"offset": {"line": 379, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/src/personal/bvr-safaris/src/components/features/FarmOwnerDashboard.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { useAuth } from '@/hooks/useAuth'\nimport { Button } from '@/components/ui/Button'\nimport { Card } from '@/components/ui/Card'\nimport { LoadingSpinner } from '@/components/ui/LoadingSpinner'\nimport { farmService, bookingService } from '@/lib/firebase/firestore'\nimport { GameFarm, Booking } from '@/lib/types/firestore'\nimport { Plus, MapPin, Calendar, DollarSign, Users, Eye, Edit, BarChart3, Star, Trash2 } from 'lucide-react'\nimport Link from 'next/link'\n\nexport function FarmOwnerDashboard() {\n  const { userProfile } = useAuth()\n  const [farms, setFarms] = useState<GameFarm[]>([])\n  const [bookings, setBookings] = useState<Booking[]>([])\n  const [loading, setLoading] = useState(true)\n  const [error, setError] = useState<string | null>(null)\n  const [deletingFarmId, setDeletingFarmId] = useState<string | null>(null)\n\n  useEffect(() => {\n    const fetchData = async () => {\n      if (!userProfile?.id) return\n\n      try {\n        setLoading(true)\n        setError(null)\n\n        // Fetch user's farms\n        const userFarms = await farmService.getByOwner(userProfile.id)\n        setFarms(userFarms)\n\n        // Fetch bookings for user's farms\n        const farmIds = userFarms.map(farm => farm.id)\n        if (farmIds.length > 0) {\n          // Fetch bookings for each farm separately to respect security rules\n          const allFarmBookings: Booking[] = []\n          for (const farmId of farmIds) {\n            try {\n              const farmBookings = await bookingService.getAll({ farmId, limit: 50 })\n              allFarmBookings.push(...farmBookings)\n            } catch (error) {\n              console.error(`Error fetching bookings for farm ${farmId}:`, error)\n            }\n          }\n          setBookings(allFarmBookings)\n        }\n      } catch (err) {\n        console.error('Error fetching dashboard data:', err)\n        setError('Failed to load dashboard data')\n      } finally {\n        setLoading(false)\n      }\n    }\n\n    fetchData()\n  }, [userProfile?.id])\n\n  const handleDeleteFarm = async (farmId: string, farmName: string) => {\n    if (!confirm(`Are you sure you want to delete \"${farmName}\"? This action cannot be undone.`)) {\n      return\n    }\n\n    try {\n      setDeletingFarmId(farmId)\n      await farmService.delete(farmId)\n\n      // Remove the farm from the local state\n      setFarms(prevFarms => prevFarms.filter(farm => farm.id !== farmId))\n\n      // Also remove any bookings for this farm\n      setBookings(prevBookings => prevBookings.filter(booking => booking.farmId !== farmId))\n    } catch (err) {\n      console.error('Error deleting farm:', err)\n      setError('Failed to delete farm. Please try again.')\n    } finally {\n      setDeletingFarmId(null)\n    }\n  }\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <LoadingSpinner size=\"lg\" />\n      </div>\n    )\n  }\n\n  if (error) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"text-center\">\n          <p className=\"text-red-600 mb-4\">{error}</p>\n          <Button onClick={() => window.location.reload()}>\n            Try Again\n          </Button>\n        </div>\n      </div>\n    )\n  }\n\n  // Calculate statistics\n  const totalBookings = bookings.length\n  const pendingBookings = bookings.filter(b => b.status === 'pending').length\n  const totalRevenue = bookings\n    .filter(b => b.status === 'confirmed')\n    .reduce((sum, b) => sum + (b.totalPrice || 0), 0)\n\n  return (\n    <div className=\"container mx-auto px-4 py-8\">\n      {/* Header */}\n      <div className=\"mb-8\">\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h1 className=\"text-3xl font-bold text-earth-900\">\n              Welcome back, {userProfile?.firstName || 'Farm Owner'}!\n            </h1>\n            <p className=\"text-earth-600 mt-2\">\n              Manage your farms and bookings from your dashboard\n            </p>\n          </div>\n          <Link href=\"/farms/create\">\n            <Button variant=\"primary\" className=\"flex items-center gap-2\">\n              <Plus className=\"w-4 h-4\" />\n              Add New Farm\n            </Button>\n          </Link>\n        </div>\n      </div>\n\n      {/* Statistics Cards */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n        <Card className=\"p-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-earth-600 text-sm font-medium\">Total Farms</p>\n              <p className=\"text-2xl font-bold text-earth-900\">{farms.length}</p>\n            </div>\n            <MapPin className=\"w-8 h-8 text-accent-600\" />\n          </div>\n        </Card>\n\n        <Card className=\"p-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-earth-600 text-sm font-medium\">Total Bookings</p>\n              <p className=\"text-2xl font-bold text-earth-900\">{totalBookings}</p>\n            </div>\n            <Calendar className=\"w-8 h-8 text-accent-600\" />\n          </div>\n        </Card>\n\n        <Card className=\"p-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-earth-600 text-sm font-medium\">Pending Requests</p>\n              <p className=\"text-2xl font-bold text-earth-900\">{pendingBookings}</p>\n            </div>\n            <Users className=\"w-8 h-8 text-accent-600\" />\n          </div>\n        </Card>\n\n        <Card className=\"p-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-earth-600 text-sm font-medium\">Total Revenue</p>\n              <p className=\"text-2xl font-bold text-earth-900\">\n                R{totalRevenue.toLocaleString()}\n              </p>\n            </div>\n            <DollarSign className=\"w-8 h-8 text-accent-600\" />\n          </div>\n        </Card>\n      </div>\n\n      {/* My Farms Section */}\n      <div className=\"mb-8\">\n        <div className=\"flex items-center justify-between mb-6\">\n          <h2 className=\"text-2xl font-semibold text-earth-900\">My Farms</h2>\n          <Link href=\"/farms/create\">\n            <Button variant=\"primary\" className=\"flex items-center gap-2\">\n              <Plus className=\"w-4 h-4\" />\n              Add New Farm\n            </Button>\n          </Link>\n        </div>\n\n        {farms.length === 0 ? (\n          <Card className=\"p-8\">\n            <div className=\"text-center\">\n              <MapPin className=\"w-16 h-16 text-earth-400 mx-auto mb-4\" />\n              <h3 className=\"text-xl font-semibold text-earth-900 mb-2\">\n                No farms yet\n              </h3>\n              <p className=\"text-earth-600 mb-6\">\n                Create your first farm listing to start receiving bookings from safari enthusiasts.\n              </p>\n              <Link href=\"/farms/create\">\n                <Button variant=\"primary\" className=\"flex items-center gap-2\">\n                  <Plus className=\"w-4 h-4\" />\n                  Create Your First Farm\n                </Button>\n              </Link>\n            </div>\n          </Card>\n        ) : (\n          <>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n              {farms.slice(0, 9).map((farm) => (\n                <Card key={farm.id} className=\"overflow-hidden\">\n                  <div className=\"p-0\">\n                    {/* Farm Image Placeholder */}\n                    <div className=\"h-48 bg-gradient-to-br from-earth-200 to-earth-300 flex items-center justify-center\">\n                      <MapPin className=\"w-12 h-12 text-earth-600\" />\n                    </div>\n\n                    <div className=\"p-6\">\n                      {/* Farm Header */}\n                      <div className=\"flex items-start justify-between mb-3\">\n                        <div>\n                          <h3 className=\"text-lg font-semibold text-earth-900 mb-1\">\n                            {farm.name}\n                          </h3>\n                          <p className=\"text-sm text-earth-600 flex items-center gap-1\">\n                            <MapPin className=\"w-3 h-3\" />\n                            {farm.location}, {farm.province}\n                          </p>\n                        </div>\n                        <div className=\"flex items-center gap-1\">\n                          <div className={`w-2 h-2 rounded-full ${farm.isActive ? 'bg-green-500' : 'bg-red-500'}`} />\n                          <span className=\"text-xs text-earth-600\">\n                            {farm.isActive ? 'Active' : 'Inactive'}\n                          </span>\n                        </div>\n                      </div>\n\n                      {/* Farm Details */}\n                      <div className=\"space-y-2 mb-4\">\n                        <div className=\"flex items-center justify-between text-sm\">\n                          <span className=\"text-earth-600\">Activity Type:</span>\n                          <span className=\"font-medium capitalize text-earth-900\">\n                            {farm.activityTypes.replace('_', ' ')}\n                          </span>\n                        </div>\n                        {farm.sizeHectares && (\n                          <div className=\"flex items-center justify-between text-sm\">\n                            <span className=\"text-earth-600\">Size:</span>\n                            <span className=\"font-medium text-earth-900\">\n                              {farm.sizeHectares.toLocaleString()} hectares\n                            </span>\n                          </div>\n                        )}\n                        {farm.pricePerDay && (\n                          <div className=\"flex items-center justify-between text-sm\">\n                            <span className=\"text-earth-600\">Price:</span>\n                            <span className=\"font-medium text-earth-900\">\n                              R{farm.pricePerDay.toLocaleString()}/day\n                            </span>\n                          </div>\n                        )}\n                      </div>\n\n                      {/* Action Buttons */}\n                      <div className=\"grid grid-cols-2 gap-2 mb-2\">\n                        <Link href={`/farms/${farm.id}`}>\n                          <Button variant=\"outline\" size=\"sm\" className=\"w-full flex items-center gap-1\">\n                            <Eye className=\"w-3 h-3\" />\n                            View\n                          </Button>\n                        </Link>\n                        <Link href={`/farms/${farm.id}/edit`}>\n                          <Button variant=\"outline\" size=\"sm\" className=\"w-full flex items-center gap-1\">\n                            <Edit className=\"w-3 h-3\" />\n                            Edit\n                          </Button>\n                        </Link>\n                      </div>\n\n                      <div className=\"grid grid-cols-2 gap-2\">\n                        <Link href={`/farms/${farm.id}/analytics`}>\n                          <Button variant=\"primary\" size=\"sm\" className=\"w-full flex items-center gap-1\">\n                            <BarChart3 className=\"w-3 h-3\" />\n                            Analytics\n                          </Button>\n                        </Link>\n                        <Button\n                          variant=\"outline\"\n                          size=\"sm\"\n                          className=\"w-full flex items-center gap-1 text-red-600 hover:text-red-700 hover:bg-red-50\"\n                          onClick={() => handleDeleteFarm(farm.id, farm.name)}\n                          disabled={deletingFarmId === farm.id}\n                        >\n                          <Trash2 className=\"w-3 h-3\" />\n                          {deletingFarmId === farm.id ? 'Deleting...' : 'Delete'}\n                        </Button>\n                      </div>\n\n                      {/* Quick Stats */}\n                      <div className=\"mt-4 pt-4 border-t border-earth-200\">\n                        <div className=\"grid grid-cols-2 gap-4 text-center\">\n                          <div>\n                            <div className=\"flex items-center justify-center gap-1 text-earth-600 mb-1\">\n                              <Users className=\"w-3 h-3\" />\n                              <span className=\"text-xs\">Bookings</span>\n                            </div>\n                            <p className=\"text-sm font-semibold text-earth-900\">\n                              {bookings.filter(b => b.farmId === farm.id).length}\n                            </p>\n                          </div>\n                          <div>\n                            <div className=\"flex items-center justify-center gap-1 text-earth-600 mb-1\">\n                              <Star className=\"w-3 h-3\" />\n                              <span className=\"text-xs\">Rating</span>\n                            </div>\n                            <p className=\"text-sm font-semibold text-earth-900\">N/A</p>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </Card>\n              ))}\n            </div>\n\n            {farms.length > 9 && (\n              <div className=\"text-center mt-6\">\n                <Link href=\"/dashboard/farms\">\n                  <Button variant=\"outline\">\n                    View All {farms.length} Farms\n                  </Button>\n                </Link>\n              </div>\n            )}\n          </>\n        )}\n      </div>\n\n      {/* Recent Booking Requests */}\n      <div>\n        <Card className=\"p-6\">\n          <div className=\"flex items-center justify-between mb-6\">\n            <h2 className=\"text-xl font-semibold text-earth-900\">Recent Booking Requests</h2>\n            <Link href=\"/dashboard/bookings\">\n              <Button variant=\"outline\" size=\"sm\">\n                View All\n              </Button>\n            </Link>\n          </div>\n\n          {bookings.length === 0 ? (\n            <div className=\"text-center py-8\">\n              <Calendar className=\"w-12 h-12 text-earth-400 mx-auto mb-4\" />\n              <p className=\"text-earth-600\">No booking requests yet</p>\n            </div>\n          ) : (\n            <div className=\"space-y-4\">\n              {bookings.slice(0, 3).map((booking) => {\n                const farm = farms.find(f => f.id === booking.farmId)\n                return (\n                  <div key={booking.id} className=\"flex items-center justify-between p-4 bg-earth-50 rounded-lg\">\n                    <div>\n                      <h3 className=\"font-medium text-earth-900\">{farm?.name || 'Unknown Farm'}</h3>\n                      <p className=\"text-sm text-earth-600\">\n                        {new Date(booking.startDate).toLocaleDateString()} - {new Date(booking.endDate).toLocaleDateString()}\n                      </p>\n                    </div>\n                    <div className=\"text-right\">\n                      <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${\n                        booking.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :\n                        booking.status === 'confirmed' ? 'bg-green-100 text-green-800' :\n                        'bg-red-100 text-red-800'\n                      }`}>\n                        {booking.status}\n                      </span>\n                      <p className=\"text-sm font-medium text-earth-900 mt-1\">\n                        R{booking.totalPrice?.toLocaleString()}\n                      </p>\n                    </div>\n                  </div>\n                )\n              })}\n            </div>\n          )}\n        </Card>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;;;AAVA;;;;;;;;;AAYO,SAAS;;IACd,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,UAAO,AAAD;IAC9B,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACjD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAEpE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,MAAM;0DAAY;oBAChB,IAAI,CAAC,aAAa,IAAI;oBAEtB,IAAI;wBACF,WAAW;wBACX,SAAS;wBAET,qBAAqB;wBACrB,MAAM,YAAY,MAAM,sIAAA,CAAA,cAAW,CAAC,UAAU,CAAC,YAAY,EAAE;wBAC7D,SAAS;wBAET,kCAAkC;wBAClC,MAAM,UAAU,UAAU,GAAG;8EAAC,CAAA,OAAQ,KAAK,EAAE;;wBAC7C,IAAI,QAAQ,MAAM,GAAG,GAAG;4BACtB,oEAAoE;4BACpE,MAAM,kBAA6B,EAAE;4BACrC,KAAK,MAAM,UAAU,QAAS;gCAC5B,IAAI;oCACF,MAAM,eAAe,MAAM,sIAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;wCAAE;wCAAQ,OAAO;oCAAG;oCACrE,gBAAgB,IAAI,IAAI;gCAC1B,EAAE,OAAO,OAAO;oCACd,QAAQ,KAAK,CAAC,CAAC,iCAAiC,EAAE,OAAO,CAAC,CAAC,EAAE;gCAC/D;4BACF;4BACA,YAAY;wBACd;oBACF,EAAE,OAAO,KAAK;wBACZ,QAAQ,KAAK,CAAC,kCAAkC;wBAChD,SAAS;oBACX,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA;QACF;uCAAG;QAAC,aAAa;KAAG;IAEpB,MAAM,mBAAmB,OAAO,QAAgB;QAC9C,IAAI,CAAC,QAAQ,CAAC,iCAAiC,EAAE,SAAS,gCAAgC,CAAC,GAAG;YAC5F;QACF;QAEA,IAAI;YACF,kBAAkB;YAClB,MAAM,sIAAA,CAAA,cAAW,CAAC,MAAM,CAAC;YAEzB,uCAAuC;YACvC,SAAS,CAAA,YAAa,UAAU,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;YAE3D,yCAAyC;YACzC,YAAY,CAAA,eAAgB,aAAa,MAAM,CAAC,CAAA,UAAW,QAAQ,MAAM,KAAK;QAChF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,wBAAwB;YACtC,SAAS;QACX,SAAU;YACR,kBAAkB;QACpB;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,6IAAA,CAAA,iBAAc;gBAAC,MAAK;;;;;;;;;;;IAG3B;IAEA,IAAI,OAAO;QACT,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAClC,6LAAC,qIAAA,CAAA,SAAM;wBAAC,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;kCAAI;;;;;;;;;;;;;;;;;IAMzD;IAEA,uBAAuB;IACvB,MAAM,gBAAgB,SAAS,MAAM;IACrC,MAAM,kBAAkB,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,WAAW,MAAM;IAC3E,MAAM,eAAe,SAClB,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aACzB,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,CAAC,EAAE,UAAU,IAAI,CAAC,GAAG;IAEjD,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;;wCAAoC;wCACjC,aAAa,aAAa;wCAAa;;;;;;;8CAExD,6LAAC;oCAAE,WAAU;8CAAsB;;;;;;;;;;;;sCAIrC,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;sCACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,WAAU;;kDAClC,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAY;;;;;;;;;;;;;;;;;;;;;;;0BAQpC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,mIAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAE,WAAU;sDAAqC;;;;;;sDAClD,6LAAC;4CAAE,WAAU;sDAAqC,MAAM,MAAM;;;;;;;;;;;;8CAEhE,6LAAC,6MAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAItB,6LAAC,mIAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAE,WAAU;sDAAqC;;;;;;sDAClD,6LAAC;4CAAE,WAAU;sDAAqC;;;;;;;;;;;;8CAEpD,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAIxB,6LAAC,mIAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAE,WAAU;sDAAqC;;;;;;sDAClD,6LAAC;4CAAE,WAAU;sDAAqC;;;;;;;;;;;;8CAEpD,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAIrB,6LAAC,mIAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAE,WAAU;sDAAqC;;;;;;sDAClD,6LAAC;4CAAE,WAAU;;gDAAoC;gDAC7C,aAAa,cAAc;;;;;;;;;;;;;8CAGjC,6LAAC,qNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0BAM5B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAwC;;;;;;0CACtD,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,WAAU;;sDAClC,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAY;;;;;;;;;;;;;;;;;;oBAMjC,MAAM,MAAM,KAAK,kBAChB,6LAAC,mIAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,6MAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,6LAAC;oCAAG,WAAU;8CAA4C;;;;;;8CAG1D,6LAAC;oCAAE,WAAU;8CAAsB;;;;;;8CAGnC,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;8CACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,WAAU;;0DAClC,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;;;;;;;;;;;6CAOpC;;0CACE,6LAAC;gCAAI,WAAU;0CACZ,MAAM,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,qBACtB,6LAAC,mIAAA,CAAA,OAAI;wCAAe,WAAU;kDAC5B,cAAA,6LAAC;4CAAI,WAAU;;8DAEb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,6MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;;;;;;8DAGpB,6LAAC;oDAAI,WAAU;;sEAEb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;;sFACC,6LAAC;4EAAG,WAAU;sFACX,KAAK,IAAI;;;;;;sFAEZ,6LAAC;4EAAE,WAAU;;8FACX,6LAAC,6MAAA,CAAA,SAAM;oFAAC,WAAU;;;;;;gFACjB,KAAK,QAAQ;gFAAC;gFAAG,KAAK,QAAQ;;;;;;;;;;;;;8EAGnC,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAW,CAAC,qBAAqB,EAAE,KAAK,QAAQ,GAAG,iBAAiB,cAAc;;;;;;sFACvF,6LAAC;4EAAK,WAAU;sFACb,KAAK,QAAQ,GAAG,WAAW;;;;;;;;;;;;;;;;;;sEAMlC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAK,WAAU;sFAAiB;;;;;;sFACjC,6LAAC;4EAAK,WAAU;sFACb,KAAK,aAAa,CAAC,OAAO,CAAC,KAAK;;;;;;;;;;;;gEAGpC,KAAK,YAAY,kBAChB,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAK,WAAU;sFAAiB;;;;;;sFACjC,6LAAC;4EAAK,WAAU;;gFACb,KAAK,YAAY,CAAC,cAAc;gFAAG;;;;;;;;;;;;;gEAIzC,KAAK,WAAW,kBACf,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAK,WAAU;sFAAiB;;;;;;sFACjC,6LAAC;4EAAK,WAAU;;gFAA6B;gFACzC,KAAK,WAAW,CAAC,cAAc;gFAAG;;;;;;;;;;;;;;;;;;;sEAO5C,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,+JAAA,CAAA,UAAI;oEAAC,MAAM,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE;8EAC7B,cAAA,6LAAC,qIAAA,CAAA,SAAM;wEAAC,SAAQ;wEAAU,MAAK;wEAAK,WAAU;;0FAC5C,6LAAC,mMAAA,CAAA,MAAG;gFAAC,WAAU;;;;;;4EAAY;;;;;;;;;;;;8EAI/B,6LAAC,+JAAA,CAAA,UAAI;oEAAC,MAAM,CAAC,OAAO,EAAE,KAAK,EAAE,CAAC,KAAK,CAAC;8EAClC,cAAA,6LAAC,qIAAA,CAAA,SAAM;wEAAC,SAAQ;wEAAU,MAAK;wEAAK,WAAU;;0FAC5C,6LAAC,8MAAA,CAAA,OAAI;gFAAC,WAAU;;;;;;4EAAY;;;;;;;;;;;;;;;;;;sEAMlC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,+JAAA,CAAA,UAAI;oEAAC,MAAM,CAAC,OAAO,EAAE,KAAK,EAAE,CAAC,UAAU,CAAC;8EACvC,cAAA,6LAAC,qIAAA,CAAA,SAAM;wEAAC,SAAQ;wEAAU,MAAK;wEAAK,WAAU;;0FAC5C,6LAAC,qNAAA,CAAA,YAAS;gFAAC,WAAU;;;;;;4EAAY;;;;;;;;;;;;8EAIrC,6LAAC,qIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,MAAK;oEACL,WAAU;oEACV,SAAS,IAAM,iBAAiB,KAAK,EAAE,EAAE,KAAK,IAAI;oEAClD,UAAU,mBAAmB,KAAK,EAAE;;sFAEpC,6LAAC,6MAAA,CAAA,SAAM;4EAAC,WAAU;;;;;;wEACjB,mBAAmB,KAAK,EAAE,GAAG,gBAAgB;;;;;;;;;;;;;sEAKlD,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;;0FACC,6LAAC;gFAAI,WAAU;;kGACb,6LAAC,uMAAA,CAAA,QAAK;wFAAC,WAAU;;;;;;kGACjB,6LAAC;wFAAK,WAAU;kGAAU;;;;;;;;;;;;0FAE5B,6LAAC;gFAAE,WAAU;0FACV,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,KAAK,EAAE,EAAE,MAAM;;;;;;;;;;;;kFAGtD,6LAAC;;0FACC,6LAAC;gFAAI,WAAU;;kGACb,6LAAC,qMAAA,CAAA,OAAI;wFAAC,WAAU;;;;;;kGAChB,6LAAC;wFAAK,WAAU;kGAAU;;;;;;;;;;;;0FAE5B,6LAAC;gFAAE,WAAU;0FAAuC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uCAzGrD,KAAK,EAAE;;;;;;;;;;4BAmHrB,MAAM,MAAM,GAAG,mBACd,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;8CACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;;4CAAU;4CACd,MAAM,MAAM;4CAAC;;;;;;;;;;;;;;;;;;;;;;;;;0BAUrC,6LAAC;0BACC,cAAA,6LAAC,mIAAA,CAAA,OAAI;oBAAC,WAAU;;sCACd,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAuC;;;;;;8CACrD,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;8CACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;kDAAK;;;;;;;;;;;;;;;;;wBAMvC,SAAS,MAAM,KAAK,kBACnB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,6LAAC;oCAAE,WAAU;8CAAiB;;;;;;;;;;;iDAGhC,6LAAC;4BAAI,WAAU;sCACZ,SAAS,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;gCACzB,MAAM,OAAO,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,QAAQ,MAAM;gCACpD,qBACE,6LAAC;oCAAqB,WAAU;;sDAC9B,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAA8B,MAAM,QAAQ;;;;;;8DAC1D,6LAAC;oDAAE,WAAU;;wDACV,IAAI,KAAK,QAAQ,SAAS,EAAE,kBAAkB;wDAAG;wDAAI,IAAI,KAAK,QAAQ,OAAO,EAAE,kBAAkB;;;;;;;;;;;;;sDAGtG,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAW,CAAC,uDAAuD,EACvE,QAAQ,MAAM,KAAK,YAAY,kCAC/B,QAAQ,MAAM,KAAK,cAAc,gCACjC,2BACA;8DACC,QAAQ,MAAM;;;;;;8DAEjB,6LAAC;oDAAE,WAAU;;wDAA0C;wDACnD,QAAQ,UAAU,EAAE;;;;;;;;;;;;;;mCAhBlB,QAAQ,EAAE;;;;;4BAqBxB;;;;;;;;;;;;;;;;;;;;;;;AAOd;GAvXgB;;QACU,2HAAA,CAAA,UAAO;;;KADjB", "debugId": null}}, {"offset": {"line": 1539, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/src/personal/bvr-safaris/src/components/features/GuestDashboard.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { useAuth } from '@/hooks/useAuth'\nimport { Button } from '@/components/ui/Button'\nimport { Card } from '@/components/ui/Card'\nimport { LoadingSpinner } from '@/components/ui/LoadingSpinner'\nimport { bookingService, farmService } from '@/lib/firebase/firestore'\nimport { Booking, GameFarm } from '@/lib/types/firestore'\nimport { Calendar, MapPin, Heart, Search, Clock, CheckCircle } from 'lucide-react'\nimport Link from 'next/link'\n\nexport function GuestDashboard() {\n  const { userProfile } = useAuth()\n  const [bookings, setBookings] = useState<Booking[]>([])\n  const [savedFarms, setSavedFarms] = useState<GameFarm[]>([])\n  const [loading, setLoading] = useState(true)\n  const [error, setError] = useState<string | null>(null)\n\n  useEffect(() => {\n    const fetchData = async () => {\n      if (!userProfile?.id) return\n\n      try {\n        setLoading(true)\n        setError(null)\n\n        // Fetch user's bookings using filters to respect security rules\n        const userBookings = await bookingService.getAll({\n          hunterId: userProfile.id,\n          limit: 10\n        })\n        setBookings(userBookings)\n\n        // Fetch active farms that guests can view (public farms)\n        const activeFarms = await farmService.getActive(3) // Get first 3 active farms\n        setSavedFarms(activeFarms)\n\n      } catch (err) {\n        console.error('Error fetching dashboard data:', err)\n        setError('Failed to load dashboard data')\n      } finally {\n        setLoading(false)\n      }\n    }\n\n    fetchData()\n  }, [userProfile?.id])\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <LoadingSpinner size=\"lg\" />\n      </div>\n    )\n  }\n\n  if (error) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"text-center\">\n          <p className=\"text-red-600 mb-4\">{error}</p>\n          <Button onClick={() => window.location.reload()}>\n            Try Again\n          </Button>\n        </div>\n      </div>\n    )\n  }\n\n  // Calculate statistics\n  const totalBookings = bookings.length\n  const upcomingBookings = bookings.filter(b => \n    new Date(b.startDate) > new Date() && b.status === 'confirmed'\n  ).length\n  const completedBookings = bookings.filter(b => \n    new Date(b.endDate) < new Date() && b.status === 'confirmed'\n  ).length\n\n  return (\n    <div className=\"container mx-auto px-4 py-8\">\n      {/* Header */}\n      <div className=\"mb-8\">\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h1 className=\"text-3xl font-bold text-earth-900\">\n              Welcome back, {userProfile?.firstName || 'Guest'}!\n            </h1>\n            <p className=\"text-earth-600 mt-2\">\n              Ready for your next safari adventure?\n            </p>\n          </div>\n          <Link href=\"/farms\">\n            <Button variant=\"primary\" className=\"flex items-center gap-2\">\n              <Search className=\"w-4 h-4\" />\n              Browse Farms\n            </Button>\n          </Link>\n        </div>\n      </div>\n\n      {/* Statistics Cards */}\n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\">\n        <Card className=\"p-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-earth-600 text-sm font-medium\">Total Bookings</p>\n              <p className=\"text-2xl font-bold text-earth-900\">{totalBookings}</p>\n            </div>\n            <Calendar className=\"w-8 h-8 text-accent-600\" />\n          </div>\n        </Card>\n\n        <Card className=\"p-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-earth-600 text-sm font-medium\">Upcoming Trips</p>\n              <p className=\"text-2xl font-bold text-earth-900\">{upcomingBookings}</p>\n            </div>\n            <Clock className=\"w-8 h-8 text-accent-600\" />\n          </div>\n        </Card>\n\n        <Card className=\"p-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-earth-600 text-sm font-medium\">Completed Trips</p>\n              <p className=\"text-2xl font-bold text-earth-900\">{completedBookings}</p>\n            </div>\n            <CheckCircle className=\"w-8 h-8 text-accent-600\" />\n          </div>\n        </Card>\n      </div>\n\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n        {/* My Bookings */}\n        <Card className=\"p-6\">\n          <div className=\"flex items-center justify-between mb-6\">\n            <h2 className=\"text-xl font-semibold text-earth-900\">My Bookings</h2>\n            <Link href=\"/dashboard/bookings\">\n              <Button variant=\"outline\" size=\"sm\">\n                View All\n              </Button>\n            </Link>\n          </div>\n\n          {bookings.length === 0 ? (\n            <div className=\"text-center py-8\">\n              <Calendar className=\"w-12 h-12 text-earth-400 mx-auto mb-4\" />\n              <p className=\"text-earth-600 mb-4\">You haven&apos;t made any bookings yet</p>\n              <Link href=\"/farms\">\n                <Button variant=\"primary\">Browse Farms</Button>\n              </Link>\n            </div>\n          ) : (\n            <div className=\"space-y-4\">\n              {bookings.slice(0, 3).map((booking) => (\n                <div key={booking.id} className=\"flex items-center justify-between p-4 bg-earth-50 rounded-lg\">\n                  <div>\n                    <h3 className=\"font-medium text-earth-900\">Booking #{booking.id.slice(-6)}</h3>\n                    <p className=\"text-sm text-earth-600\">\n                      {new Date(booking.startDate).toLocaleDateString()} - {new Date(booking.endDate).toLocaleDateString()}\n                    </p>\n                  </div>\n                  <div className=\"text-right\">\n                    <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${\n                      booking.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :\n                      booking.status === 'confirmed' ? 'bg-green-100 text-green-800' :\n                      'bg-red-100 text-red-800'\n                    }`}>\n                      {booking.status}\n                    </span>\n                    <p className=\"text-sm font-medium text-earth-900 mt-1\">\n                      R{booking.totalPrice?.toLocaleString()}\n                    </p>\n                  </div>\n                </div>\n              ))}\n              {bookings.length > 3 && (\n                <div className=\"text-center pt-4\">\n                  <Link href=\"/dashboard/bookings\">\n                    <Button variant=\"outline\">View All Bookings</Button>\n                  </Link>\n                </div>\n              )}\n            </div>\n          )}\n        </Card>\n\n        {/* Recommended Farms */}\n        <Card className=\"p-6\">\n          <div className=\"flex items-center justify-between mb-6\">\n            <h2 className=\"text-xl font-semibold text-earth-900\">Recommended Farms</h2>\n            <Link href=\"/farms\">\n              <Button variant=\"outline\" size=\"sm\">\n                Browse All\n              </Button>\n            </Link>\n          </div>\n\n          {savedFarms.length === 0 ? (\n            <div className=\"text-center py-8\">\n              <Heart className=\"w-12 h-12 text-earth-400 mx-auto mb-4\" />\n              <p className=\"text-earth-600 mb-4\">Discover amazing safari destinations</p>\n              <Link href=\"/farms\">\n                <Button variant=\"primary\">Explore Farms</Button>\n              </Link>\n            </div>\n          ) : (\n            <div className=\"space-y-4\">\n              {savedFarms.map((farm) => (\n                <div key={farm.id} className=\"flex items-center justify-between p-4 bg-earth-50 rounded-lg\">\n                  <div>\n                    <h3 className=\"font-medium text-earth-900\">{farm.name}</h3>\n                    <p className=\"text-sm text-earth-600 flex items-center gap-1\">\n                      <MapPin className=\"w-3 h-3\" />\n                      {farm.location}\n                    </p>\n                  </div>\n                  <div className=\"text-right\">\n                    <p className=\"text-sm font-medium text-earth-900\">\n                      R{farm.pricePerDay?.toLocaleString()}/day\n                    </p>\n                    <Link href={`/farms/${farm.id}`}>\n                      <Button variant=\"outline\" size=\"sm\">\n                        View Details\n                      </Button>\n                    </Link>\n                  </div>\n                </div>\n              ))}\n            </div>\n          )}\n        </Card>\n      </div>\n\n      {/* Quick Actions */}\n      <Card className=\"p-6 mt-8\">\n        <h2 className=\"text-xl font-semibold text-earth-900 mb-6\">Quick Actions</h2>\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n          <Link href=\"/farms\">\n            <Button variant=\"outline\" className=\"w-full flex items-center gap-2\">\n              <Search className=\"w-4 h-4\" />\n              Browse Farms\n            </Button>\n          </Link>\n          <Link href=\"/profile\">\n            <Button variant=\"outline\" className=\"w-full flex items-center gap-2\">\n              <Calendar className=\"w-4 h-4\" />\n              Manage Profile\n            </Button>\n          </Link>\n          <Link href=\"/dashboard/bookings\">\n            <Button variant=\"outline\" className=\"w-full flex items-center gap-2\">\n              <Clock className=\"w-4 h-4\" />\n              View Bookings\n            </Button>\n          </Link>\n        </div>\n      </Card>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;;;AAVA;;;;;;;;;AAYO,SAAS;;IACd,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,UAAO,AAAD;IAC9B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IAC3D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM;sDAAY;oBAChB,IAAI,CAAC,aAAa,IAAI;oBAEtB,IAAI;wBACF,WAAW;wBACX,SAAS;wBAET,gEAAgE;wBAChE,MAAM,eAAe,MAAM,sIAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;4BAC/C,UAAU,YAAY,EAAE;4BACxB,OAAO;wBACT;wBACA,YAAY;wBAEZ,yDAAyD;wBACzD,MAAM,cAAc,MAAM,sIAAA,CAAA,cAAW,CAAC,SAAS,CAAC,GAAG,2BAA2B;;wBAC9E,cAAc;oBAEhB,EAAE,OAAO,KAAK;wBACZ,QAAQ,KAAK,CAAC,kCAAkC;wBAChD,SAAS;oBACX,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA;QACF;mCAAG;QAAC,aAAa;KAAG;IAEpB,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,6IAAA,CAAA,iBAAc;gBAAC,MAAK;;;;;;;;;;;IAG3B;IAEA,IAAI,OAAO;QACT,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAClC,6LAAC,qIAAA,CAAA,SAAM;wBAAC,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;kCAAI;;;;;;;;;;;;;;;;;IAMzD;IAEA,uBAAuB;IACvB,MAAM,gBAAgB,SAAS,MAAM;IACrC,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAA,IACvC,IAAI,KAAK,EAAE,SAAS,IAAI,IAAI,UAAU,EAAE,MAAM,KAAK,aACnD,MAAM;IACR,MAAM,oBAAoB,SAAS,MAAM,CAAC,CAAA,IACxC,IAAI,KAAK,EAAE,OAAO,IAAI,IAAI,UAAU,EAAE,MAAM,KAAK,aACjD,MAAM;IAER,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;;wCAAoC;wCACjC,aAAa,aAAa;wCAAQ;;;;;;;8CAEnD,6LAAC;oCAAE,WAAU;8CAAsB;;;;;;;;;;;;sCAIrC,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;sCACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,WAAU;;kDAClC,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAY;;;;;;;;;;;;;;;;;;;;;;;0BAQtC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,mIAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAE,WAAU;sDAAqC;;;;;;sDAClD,6LAAC;4CAAE,WAAU;sDAAqC;;;;;;;;;;;;8CAEpD,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAIxB,6LAAC,mIAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAE,WAAU;sDAAqC;;;;;;sDAClD,6LAAC;4CAAE,WAAU;sDAAqC;;;;;;;;;;;;8CAEpD,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAIrB,6LAAC,mIAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAE,WAAU;sDAAqC;;;;;;sDAClD,6LAAC;4CAAE,WAAU;sDAAqC;;;;;;;;;;;;8CAEpD,6LAAC,8NAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0BAK7B,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,mIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAuC;;;;;;kDACrD,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;sDAAK;;;;;;;;;;;;;;;;;4BAMvC,SAAS,MAAM,KAAK,kBACnB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC;wCAAE,WAAU;kDAAsB;;;;;;kDACnC,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;sDAAU;;;;;;;;;;;;;;;;qDAI9B,6LAAC;gCAAI,WAAU;;oCACZ,SAAS,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,wBACzB,6LAAC;4CAAqB,WAAU;;8DAC9B,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;;gEAA6B;gEAAU,QAAQ,EAAE,CAAC,KAAK,CAAC,CAAC;;;;;;;sEACvE,6LAAC;4DAAE,WAAU;;gEACV,IAAI,KAAK,QAAQ,SAAS,EAAE,kBAAkB;gEAAG;gEAAI,IAAI,KAAK,QAAQ,OAAO,EAAE,kBAAkB;;;;;;;;;;;;;8DAGtG,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAW,CAAC,uDAAuD,EACvE,QAAQ,MAAM,KAAK,YAAY,kCAC/B,QAAQ,MAAM,KAAK,cAAc,gCACjC,2BACA;sEACC,QAAQ,MAAM;;;;;;sEAEjB,6LAAC;4DAAE,WAAU;;gEAA0C;gEACnD,QAAQ,UAAU,EAAE;;;;;;;;;;;;;;2CAhBlB,QAAQ,EAAE;;;;;oCAqBrB,SAAS,MAAM,GAAG,mBACjB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;sDACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;0DAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAStC,6LAAC,mIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAuC;;;;;;kDACrD,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;sDAAK;;;;;;;;;;;;;;;;;4BAMvC,WAAW,MAAM,KAAK,kBACrB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,6LAAC;wCAAE,WAAU;kDAAsB;;;;;;kDACnC,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;sDAAU;;;;;;;;;;;;;;;;qDAI9B,6LAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC;wCAAkB,WAAU;;0DAC3B,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAA8B,KAAK,IAAI;;;;;;kEACrD,6LAAC;wDAAE,WAAU;;0EACX,6LAAC,6MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;4DACjB,KAAK,QAAQ;;;;;;;;;;;;;0DAGlB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;;4DAAqC;4DAC9C,KAAK,WAAW,EAAE;4DAAiB;;;;;;;kEAEvC,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAM,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE;kEAC7B,cAAA,6LAAC,qIAAA,CAAA,SAAM;4DAAC,SAAQ;4DAAU,MAAK;sEAAK;;;;;;;;;;;;;;;;;;uCAbhC,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;0BA0B3B,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,6LAAC;wBAAG,WAAU;kCAA4C;;;;;;kCAC1D,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,WAAU;;sDAClC,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAY;;;;;;;;;;;;0CAIlC,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,WAAU;;sDAClC,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAY;;;;;;;;;;;;0CAIpC,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,WAAU;;sDAClC,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;wCAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ3C;GA1PgB;;QACU,2HAAA,CAAA,UAAO;;;KADjB", "debugId": null}}, {"offset": {"line": 2360, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/src/personal/bvr-safaris/src/lib/utils/debugAuth.ts"], "sourcesContent": ["import { auth } from '@/lib/firebase/client'\n\n/**\n * Debug utility to check authentication state and custom claims\n */\nexport async function debugAuthState() {\n  const user = auth.currentUser\n  \n  if (!user) {\n    console.log('🔍 Auth Debug: No user logged in')\n    return null\n  }\n\n  try {\n    // Get the ID token result to see custom claims\n    const idTokenResult = await user.getIdTokenResult(true) // Force refresh\n    \n    console.log('🔍 Auth Debug Information:')\n    console.log('  User UID:', user.uid)\n    console.log('  Email:', user.email)\n    console.log('  Display Name:', user.displayName)\n    console.log('  Email Verified:', user.emailVerified)\n    console.log('  Custom Claims:', idTokenResult.claims)\n    console.log('  Role from Claims:', idTokenResult.claims.role || 'NO ROLE SET')\n    console.log('  Token Expiration:', new Date(idTokenResult.expirationTime))\n    console.log('  Auth Time:', new Date(idTokenResult.authTime))\n    console.log('  Issued At:', new Date(idTokenResult.issuedAtTime))\n    \n    // Check if role is set\n    if (!idTokenResult.claims.role) {\n      console.warn('⚠️  WARNING: No role set in custom claims!')\n      console.log('   This will cause Firestore permission errors.')\n      console.log('   The user may need to re-register or have their role set manually.')\n    }\n    \n    return {\n      uid: user.uid,\n      email: user.email,\n      role: (idTokenResult.claims.role as string) || 'NO ROLE',\n      claims: idTokenResult.claims as Record<string, unknown>,\n      hasRole: !!idTokenResult.claims.role\n    }\n    \n  } catch (error) {\n    console.error('🔍 Auth Debug Error:', error)\n    return null\n  }\n}\n\n/**\n * Check if the current user has the required role\n */\nexport async function checkUserRole(requiredRole?: string) {\n  const debugInfo = await debugAuthState()\n  \n  if (!debugInfo) {\n    return false\n  }\n  \n  if (requiredRole) {\n    const hasRole = debugInfo.role === requiredRole\n    console.log(`🔍 Role Check: Required \"${requiredRole}\", User has \"${debugInfo.role}\" - ${hasRole ? 'PASS' : 'FAIL'}`)\n    return hasRole\n  }\n  \n  return !!debugInfo.hasRole\n}\n\n/**\n * Force refresh the user's ID token to get latest custom claims\n */\nexport async function refreshUserToken() {\n  const user = auth.currentUser\n  \n  if (!user) {\n    console.log('🔄 Token Refresh: No user logged in')\n    return false\n  }\n  \n  try {\n    console.log('🔄 Refreshing user token...')\n    await user.getIdToken(true) // Force refresh\n    console.log('✅ Token refreshed successfully')\n    return true\n  } catch (error) {\n    console.error('❌ Token refresh failed:', error)\n    return false\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;AAAA;;AAKO,eAAe;IACpB,MAAM,OAAO,mIAAA,CAAA,OAAI,CAAC,WAAW;IAE7B,IAAI,CAAC,MAAM;QACT,QAAQ,GAAG,CAAC;QACZ,OAAO;IACT;IAEA,IAAI;QACF,+CAA+C;QAC/C,MAAM,gBAAgB,MAAM,KAAK,gBAAgB,CAAC,MAAM,gBAAgB;;QAExE,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,eAAe,KAAK,GAAG;QACnC,QAAQ,GAAG,CAAC,YAAY,KAAK,KAAK;QAClC,QAAQ,GAAG,CAAC,mBAAmB,KAAK,WAAW;QAC/C,QAAQ,GAAG,CAAC,qBAAqB,KAAK,aAAa;QACnD,QAAQ,GAAG,CAAC,oBAAoB,cAAc,MAAM;QACpD,QAAQ,GAAG,CAAC,uBAAuB,cAAc,MAAM,CAAC,IAAI,IAAI;QAChE,QAAQ,GAAG,CAAC,uBAAuB,IAAI,KAAK,cAAc,cAAc;QACxE,QAAQ,GAAG,CAAC,gBAAgB,IAAI,KAAK,cAAc,QAAQ;QAC3D,QAAQ,GAAG,CAAC,gBAAgB,IAAI,KAAK,cAAc,YAAY;QAE/D,uBAAuB;QACvB,IAAI,CAAC,cAAc,MAAM,CAAC,IAAI,EAAE;YAC9B,QAAQ,IAAI,CAAC;YACb,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CAAC;QACd;QAEA,OAAO;YACL,KAAK,KAAK,GAAG;YACb,OAAO,KAAK,KAAK;YACjB,MAAM,AAAC,cAAc,MAAM,CAAC,IAAI,IAAe;YAC/C,QAAQ,cAAc,MAAM;YAC5B,SAAS,CAAC,CAAC,cAAc,MAAM,CAAC,IAAI;QACtC;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,OAAO;IACT;AACF;AAKO,eAAe,cAAc,YAAqB;IACvD,MAAM,YAAY,MAAM;IAExB,IAAI,CAAC,WAAW;QACd,OAAO;IACT;IAEA,IAAI,cAAc;QAChB,MAAM,UAAU,UAAU,IAAI,KAAK;QACnC,QAAQ,GAAG,CAAC,CAAC,yBAAyB,EAAE,aAAa,aAAa,EAAE,UAAU,IAAI,CAAC,IAAI,EAAE,UAAU,SAAS,QAAQ;QACpH,OAAO;IACT;IAEA,OAAO,CAAC,CAAC,UAAU,OAAO;AAC5B;AAKO,eAAe;IACpB,MAAM,OAAO,mIAAA,CAAA,OAAI,CAAC,WAAW;IAE7B,IAAI,CAAC,MAAM;QACT,QAAQ,GAAG,CAAC;QACZ,OAAO;IACT;IAEA,IAAI;QACF,QAAQ,GAAG,CAAC;QACZ,MAAM,KAAK,UAAU,CAAC,MAAM,gBAAgB;;QAC5C,QAAQ,GAAG,CAAC;QACZ,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 2444, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/src/personal/bvr-safaris/src/components/debug/AuthDebugPanel.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Button } from '@/components/ui/Button'\nimport { Card } from '@/components/ui/Card'\nimport { debugAuthState, refreshUserToken } from '@/lib/utils/debugAuth'\nimport { useAuth } from '@/hooks/useAuth'\n\ninterface AuthDebugInfo {\n  uid: string\n  email: string | null\n  role: string\n  hasRole: boolean\n  claims: Record<string, unknown>\n}\n\nexport function AuthDebugPanel() {\n  const [debugInfo, setDebugInfo] = useState<AuthDebugInfo | null>(null)\n  const [isDebugging, setIsDebugging] = useState(false)\n  const [isRefreshing, setIsRefreshing] = useState(false)\n  const { user } = useAuth()\n\n  const handleDebugAuth = async () => {\n    setIsDebugging(true)\n    try {\n      const info = await debugAuthState()\n      setDebugInfo(info)\n    } catch (error) {\n      console.error('Debug failed:', error)\n    } finally {\n      setIsDebugging(false)\n    }\n  }\n\n  const handleRefreshToken = async () => {\n    setIsRefreshing(true)\n    try {\n      await refreshUserToken()\n      // Re-run debug after refresh\n      const info = await debugAuthState()\n      setDebugInfo(info)\n    } catch (error) {\n      console.error('Token refresh failed:', error)\n    } finally {\n      setIsRefreshing(false)\n    }\n  }\n\n  const handleSetRole = async () => {\n    if (!user) return\n\n    try {\n      const idToken = await user.getIdToken()\n      const response = await fetch('/api/auth/set-role', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${idToken}`,\n        },\n        body: JSON.stringify({\n          uid: user.uid,\n          role: 'guest' // Default to guest role\n        }),\n      })\n\n      if (response.ok) {\n        console.log('Role set successfully')\n        // Refresh token to get new claims\n        await handleRefreshToken()\n      } else {\n        const errorData = await response.json()\n        console.error('Failed to set role:', errorData.error)\n      }\n    } catch (error) {\n      console.error('Error setting role:', error)\n    }\n  }\n\n  if (!user) {\n    return null\n  }\n\n  return (\n    <Card className=\"p-4 mb-4 bg-yellow-50 border-yellow-200\">\n      <h3 className=\"text-lg font-semibold text-yellow-800 mb-3\">\n        🔍 Authentication Debug Panel\n      </h3>\n      \n      <div className=\"space-y-3\">\n        <div className=\"flex gap-2\">\n          <Button\n            onClick={handleDebugAuth}\n            variant=\"outline\"\n            size=\"sm\"\n            isLoading={isDebugging}\n          >\n            Check Auth State\n          </Button>\n          \n          <Button\n            onClick={handleRefreshToken}\n            variant=\"outline\"\n            size=\"sm\"\n            isLoading={isRefreshing}\n          >\n            Refresh Token\n          </Button>\n\n          <Button\n            onClick={handleSetRole}\n            variant=\"outline\"\n            size=\"sm\"\n          >\n            Set Role (Guest)\n          </Button>\n        </div>\n\n        {debugInfo && (\n          <div className=\"bg-white p-3 rounded border text-sm\">\n            <div className=\"grid grid-cols-2 gap-2\">\n              <div><strong>UID:</strong> {debugInfo.uid}</div>\n              <div><strong>Email:</strong> {debugInfo.email}</div>\n              <div><strong>Role:</strong> {debugInfo.role || 'NOT SET'}</div>\n              <div><strong>Has Role:</strong> {debugInfo.hasRole ? '✅' : '❌'}</div>\n            </div>\n            \n            {!debugInfo.hasRole && (\n              <div className=\"mt-2 p-2 bg-red-50 border border-red-200 rounded\">\n                <p className=\"text-red-700 text-xs\">\n                  ⚠️ No role set in custom claims! This will cause permission errors.\n                  Click &quot;Set Role (Guest)&quot; to fix this issue.\n                </p>\n              </div>\n            )}\n            \n            <details className=\"mt-2\">\n              <summary className=\"cursor-pointer text-xs text-gray-600\">\n                View All Claims\n              </summary>\n              <pre className=\"text-xs bg-gray-100 p-2 rounded mt-1 overflow-auto\">\n                {JSON.stringify(debugInfo.claims, null, 2)}\n              </pre>\n            </details>\n          </div>\n        )}\n      </div>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAgBO,SAAS;;IACd,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwB;IACjE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,UAAO,AAAD;IAEvB,MAAM,kBAAkB;QACtB,eAAe;QACf,IAAI;YACF,MAAM,OAAO,MAAM,CAAA,GAAA,mIAAA,CAAA,iBAAc,AAAD;YAChC,aAAa;QACf,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;QACjC,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,qBAAqB;QACzB,gBAAgB;QAChB,IAAI;YACF,MAAM,CAAA,GAAA,mIAAA,CAAA,mBAAgB,AAAD;YACrB,6BAA6B;YAC7B,MAAM,OAAO,MAAM,CAAA,GAAA,mIAAA,CAAA,iBAAc,AAAD;YAChC,aAAa;QACf,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACzC,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,gBAAgB;QACpB,IAAI,CAAC,MAAM;QAEX,IAAI;YACF,MAAM,UAAU,MAAM,KAAK,UAAU;YACrC,MAAM,WAAW,MAAM,MAAM,sBAAsB;gBACjD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;oBAChB,iBAAiB,CAAC,OAAO,EAAE,SAAS;gBACtC;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,KAAK,KAAK,GAAG;oBACb,MAAM,QAAQ,wBAAwB;gBACxC;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,QAAQ,GAAG,CAAC;gBACZ,kCAAkC;gBAClC,MAAM;YACR,OAAO;gBACL,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,QAAQ,KAAK,CAAC,uBAAuB,UAAU,KAAK;YACtD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;QACvC;IACF;IAEA,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBACE,6LAAC,mIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,6LAAC;gBAAG,WAAU;0BAA6C;;;;;;0BAI3D,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAS;gCACT,SAAQ;gCACR,MAAK;gCACL,WAAW;0CACZ;;;;;;0CAID,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAS;gCACT,SAAQ;gCACR,MAAK;gCACL,WAAW;0CACZ;;;;;;0CAID,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAS;gCACT,SAAQ;gCACR,MAAK;0CACN;;;;;;;;;;;;oBAKF,2BACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DAAI,6LAAC;0DAAO;;;;;;4CAAa;4CAAE,UAAU,GAAG;;;;;;;kDACzC,6LAAC;;0DAAI,6LAAC;0DAAO;;;;;;4CAAe;4CAAE,UAAU,KAAK;;;;;;;kDAC7C,6LAAC;;0DAAI,6LAAC;0DAAO;;;;;;4CAAc;4CAAE,UAAU,IAAI,IAAI;;;;;;;kDAC/C,6LAAC;;0DAAI,6LAAC;0DAAO;;;;;;4CAAkB;4CAAE,UAAU,OAAO,GAAG,MAAM;;;;;;;;;;;;;4BAG5D,CAAC,UAAU,OAAO,kBACjB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAE,WAAU;8CAAuB;;;;;;;;;;;0CAOxC,6LAAC;gCAAQ,WAAU;;kDACjB,6LAAC;wCAAQ,WAAU;kDAAuC;;;;;;kDAG1D,6LAAC;wCAAI,WAAU;kDACZ,KAAK,SAAS,CAAC,UAAU,MAAM,EAAE,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQxD;GApIgB;;QAIG,2HAAA,CAAA,UAAO;;;KAJV", "debugId": null}}, {"offset": {"line": 2732, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/src/personal/bvr-safaris/src/components/features/UnifiedDashboard.tsx"], "sourcesContent": ["'use client'\n\nimport { useAuth } from '@/hooks/useAuth'\nimport { FarmOwnerDashboard } from './FarmOwnerDashboard'\nimport { GuestDashboard } from './GuestDashboard'\nimport { LoadingSpinner } from '@/components/ui/LoadingSpinner'\nimport { debugAuthState } from '@/lib/utils/debugAuth'\nimport { AuthDebugPanel } from '@/components/debug/AuthDebugPanel'\nimport { useEffect } from 'react'\n\nexport function UnifiedDashboard() {\n  const { userProfile, loading } = useAuth()\n\n  // Debug authentication state when component mounts\n  useEffect(() => {\n    if (!loading && userProfile) {\n      debugAuthState()\n    }\n  }, [loading, userProfile])\n\n  if (loading || !userProfile) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <LoadingSpinner size=\"lg\" />\n      </div>\n    )\n  }\n\n  // Render dashboard based on user role\n  return (\n    <div>\n      {/* Temporary debug panel - remove in production */}\n      <AuthDebugPanel />\n\n      {userProfile.role === 'farm_owner' ? (\n        <FarmOwnerDashboard />\n      ) : (\n        <GuestDashboard />\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AARA;;;;;;;;AAUO,SAAS;;IACd,MAAM,EAAE,WAAW,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,UAAO,AAAD;IAEvC,mDAAmD;IACnD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,CAAC,WAAW,aAAa;gBAC3B,CAAA,GAAA,mIAAA,CAAA,iBAAc,AAAD;YACf;QACF;qCAAG;QAAC;QAAS;KAAY;IAEzB,IAAI,WAAW,CAAC,aAAa;QAC3B,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,6IAAA,CAAA,iBAAc;gBAAC,MAAK;;;;;;;;;;;IAG3B;IAEA,sCAAsC;IACtC,qBACE,6LAAC;;0BAEC,6LAAC,gJAAA,CAAA,iBAAc;;;;;YAEd,YAAY,IAAI,KAAK,6BACpB,6LAAC,uJAAA,CAAA,qBAAkB;;;;qCAEnB,6LAAC,mJAAA,CAAA,iBAAc;;;;;;;;;;;AAIvB;GA/BgB;;QACmB,2HAAA,CAAA,UAAO;;;KAD1B", "debugId": null}}, {"offset": {"line": 2824, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/src/personal/bvr-safaris/src/app/dashboard/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useRequireAuth } from '@/hooks/useAuth'\nimport { UnifiedDashboard } from '@/components/features/UnifiedDashboard'\nimport { LoadingSpinner } from '@/components/ui/LoadingSpinner'\nimport { useRouter } from 'next/navigation'\nimport { useEffect } from 'react'\n\nexport default function DashboardPage() {\n  const { loading, isAuthenticated } = useRequireAuth()\n  const router = useRouter()\n\n  useEffect(() => {\n    if (!loading && !isAuthenticated) {\n      router.push('/auth/login')\n    }\n  }, [loading, isAuthenticated, router])\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center bg-earth-100\">\n        <LoadingSpinner size=\"lg\" />\n      </div>\n    )\n  }\n\n  if (!isAuthenticated) {\n    return null // Will redirect to login\n  }\n\n  return (\n    <div className=\"min-h-screen bg-earth-100\">\n      <UnifiedDashboard />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAQe,SAAS;;IACtB,MAAM,EAAE,OAAO,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,iBAAc,AAAD;IAClD,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,CAAC,WAAW,CAAC,iBAAiB;gBAChC,OAAO,IAAI,CAAC;YACd;QACF;kCAAG;QAAC;QAAS;QAAiB;KAAO;IAErC,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,6IAAA,CAAA,iBAAc;gBAAC,MAAK;;;;;;;;;;;IAG3B;IAEA,IAAI,CAAC,iBAAiB;QACpB,OAAO,KAAK,yBAAyB;;IACvC;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,qJAAA,CAAA,mBAAgB;;;;;;;;;;AAGvB;GA3BwB;;QACe,2HAAA,CAAA,iBAAc;QACpC,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}]}