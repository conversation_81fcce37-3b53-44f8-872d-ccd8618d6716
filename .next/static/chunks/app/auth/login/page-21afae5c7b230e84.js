(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[859],{4177:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>h});var a=t(95155),n=t(12115),s=t(6874),o=t.n(s),i=t(35695),l=t(84105),c=t(13741),u=t(93915),d=t(74338);function h(){let[e,r]=(0,n.useState)(""),[t,s]=(0,n.useState)(""),[h,m]=(0,n.useState)(!1),{signIn:f,error:p,clearError:b,user:w,loading:v}=(0,l.As)(),x=(0,i.useRouter)();if((0,n.useEffect)(()=>{!v&&w&&x.push("/dashboard")},[w,v,x]),v)return(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-earth-100 to-earth-200",children:(0,a.jsx)(d.k,{size:"lg"})});if(w)return null;let g=async r=>{r.preventDefault(),m(!0);try{await f(e,t),x.push("/dashboard")}catch(e){console.error("Login error:",e)}finally{m(!1)}},y=()=>{b()};return(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-earth-100 to-earth-200 px-4",children:(0,a.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-earth-900 mb-2",children:"Welcome Back"}),(0,a.jsx)("p",{className:"text-earth-600",children:"Sign in to your RvB Safaris account"})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-8",children:[(0,a.jsxs)("form",{onSubmit:g,className:"space-y-6",children:[p&&(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-md p-3",children:(0,a.jsx)("p",{className:"text-red-600 text-sm",children:p})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-earth-700 mb-2",children:"Email Address"}),(0,a.jsx)(u.p,{id:"email",type:"email",value:e,onChange:e=>{r(e.target.value),y()},placeholder:"Enter your email",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-earth-700 mb-2",children:"Password"}),(0,a.jsx)(u.p,{id:"password",type:"password",value:t,onChange:e=>{s(e.target.value),y()},placeholder:"Enter your password",required:!0})]}),(0,a.jsx)(c.$,{type:"submit",variant:"primary",size:"lg",className:"w-full",isLoading:h,disabled:!e||!t,children:h?"Signing In...":"Sign In"})]}),(0,a.jsx)("div",{className:"mt-6 text-center",children:(0,a.jsxs)("p",{className:"text-earth-600",children:["Don’t have an account?"," ",(0,a.jsx)(o(),{href:"/auth/register",className:"text-accent-600 hover:text-accent-700 font-medium",children:"Sign up here"})]})}),(0,a.jsx)("div",{className:"mt-4 text-center",children:(0,a.jsx)(o(),{href:"/auth/forgot-password",className:"text-sm text-earth-500 hover:text-earth-700",children:"Forgot your password?"})})]})]})})}},6654:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"useMergedRef",{enumerable:!0,get:function(){return n}});let a=t(12115);function n(e,r){let t=(0,a.useRef)(null),n=(0,a.useRef)(null);return(0,a.useCallback)(a=>{if(null===a){let e=t.current;e&&(t.current=null,e());let r=n.current;r&&(n.current=null,r())}else e&&(t.current=s(e,a)),r&&(n.current=s(r,a))},[e,r])}function s(e,r){if("function"!=typeof e)return e.current=r,()=>{e.current=null};{let t=e(r);return"function"==typeof t?t:()=>e(null)}}("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),e.exports=r.default)},13741:(e,r,t)=>{"use strict";t.d(r,{$:()=>o});var a=t(95155),n=t(12115),s=t(59434);let o=(0,n.forwardRef)((e,r)=>{let{className:t,variant:n="primary",size:o="md",isLoading:i,children:l,disabled:c,...u}=e;return(0,a.jsxs)("button",{className:(0,s.cn)("\n      inline-flex items-center justify-center rounded-[var(--radius-md)] \n      font-[var(--font-ui)] font-semibold transition-all duration-300 \n      focus:outline-none focus:ring-2 focus:ring-offset-2\n      disabled:opacity-50 disabled:cursor-not-allowed\n      hover:transform hover:-translate-y-0.5 hover:shadow-[var(--shadow-lg)]\n    ",{primary:"\n        bg-[var(--primary-brown)] text-white \n        hover:bg-[#A0522D] focus:ring-[var(--primary-brown)]\n      ",secondary:"\n        bg-[var(--secondary-sky)] text-white \n        hover:bg-[#357AE0] focus:ring-[var(--secondary-sky)]\n      ",outline:"\n        bg-transparent border-2 border-[var(--primary-brown)] text-[var(--primary-brown)]\n        hover:bg-[var(--primary-brown)] hover:text-white focus:ring-[var(--primary-brown)]\n      ",hunting:"\n        bg-[var(--hunting-accent)] text-white \n        hover:bg-[#A67C00] focus:ring-[var(--hunting-accent)]\n      ",photo:"\n        bg-[var(--photo-accent)] text-white \n        hover:bg-[#3557C7] focus:ring-[var(--photo-accent)]\n      "}[n],{sm:"px-3 py-1.5 text-sm",md:"px-6 py-2 text-base",lg:"px-8 py-3 text-lg"}[o],i&&"cursor-wait",t),disabled:c||i,ref:r,...u,children:[i&&(0,a.jsxs)("svg",{className:"mr-2 h-4 w-4 animate-spin",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),l]})});o.displayName="Button"},28214:(e,r,t)=>{"use strict";t.d(r,{IG:()=>a.IG,db:()=>a.db,j2:()=>a.j2});var a=t(67039)},35695:(e,r,t)=>{"use strict";var a=t(18999);t.o(a,"useParams")&&t.d(r,{useParams:function(){return a.useParams}}),t.o(a,"useRouter")&&t.d(r,{useRouter:function(){return a.useRouter}}),t.o(a,"useSearchParams")&&t.d(r,{useSearchParams:function(){return a.useSearchParams}})},36365:(e,r,t)=>{Promise.resolve().then(t.bind(t,4177))},59434:(e,r,t)=>{"use strict";t.d(r,{Y:()=>i,Z:()=>o,cn:()=>s});var a=t(52596),n=t(39688);function s(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,n.QP)((0,a.$)(r))}function o(e){return e.toString().replace(/\B(?=(\d{3})+(?!\d))/g," ")}function i(e){let r=new Date(e);return"".concat(r.getDate()," ").concat(["January","February","March","April","May","June","July","August","September","October","November","December"][r.getMonth()]," ").concat(r.getFullYear())}},67039:(e,r,t)=>{"use strict";t.d(r,{IG:()=>u,db:()=>c,j2:()=>l});var a=t(23915),n=t(16203),s=t(35317),o=t(90858);let i=(0,a.Wp)({apiKey:"AIzaSyDdsTm8eifLrb1W0WLK0DwBk4p4XLFZKMw",authDomain:"rvbsafaris.firebaseapp.com",projectId:"rvbsafaris",storageBucket:"rvbsafaris.firebasestorage.app",messagingSenderId:"593781635754",appId:"1:593781635754:web:7a39a3b4451f34f85feed3"}),l=(0,n.xI)(i),c=(0,s.aU)(i),u=(0,o.c7)(i);console.log("Firebase Storage initialized with bucket:","rvbsafaris.firebasestorage.app")},74338:(e,r,t)=>{"use strict";t.d(r,{k:()=>s});var a=t(95155),n=t(59434);function s(e){let{size:r="md",className:t}=e;return(0,a.jsx)("div",{className:(0,n.cn)("flex items-center justify-center",t),children:(0,a.jsx)("div",{className:(0,n.cn)("animate-spin rounded-full border-2 border-white border-t-transparent",{sm:"w-4 h-4",md:"w-6 h-6",lg:"w-12 h-12"}[r])})})}},84105:(e,r,t)=>{"use strict";t.d(r,{As:()=>u,AuthProvider:()=>c,Nu:()=>h});var a=t(95155),n=t(12115),s=t(16203),o=t(35317),i=t(28214);let l=(0,n.createContext)(void 0);function c(e){let{children:r}=e,[t,c]=(0,n.useState)(null),[u,h]=(0,n.useState)(null),[m,f]=(0,n.useState)(!0),[p,b]=(0,n.useState)(null),w=async e=>{try{let s=(0,o.H9)(i.db,"users",e),l=await (0,o.x7)(s);if(l.exists()){var r,t,a,n;let e=l.data();return{id:l.id,...e,createdAt:(null==(t=e.createdAt)||null==(r=t.toDate)?void 0:r.call(t))||e.createdAt,updatedAt:(null==(n=e.updatedAt)||null==(a=n.toDate)?void 0:a.call(n))||e.updatedAt}}return null}catch(e){return console.error("Error fetching user profile:",e),null}},v=async()=>{t&&h(await w(t.uid))},x=e=>new Promise(r=>{let t=(0,s.hg)(i.j2,a=>{e&&a&&a.uid===e.uid?(t(),r()):e||a||(t(),r())})});(0,n.useEffect)(()=>(0,s.hg)(i.j2,async e=>{if(c(e),e){try{let r=await e.getIdToken();document.cookie="firebase-token=".concat(r,"; path=/; max-age=3600; secure; samesite=strict")}catch(e){console.error("Error getting ID token:",e)}h(await w(e.uid))}else document.cookie="firebase-token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT",h(null);f(!1)}),[]);let g=async(e,r)=>{try{b(null),f(!0);let t=await (0,s.x9)(i.j2,e,r);await x(t.user),await new Promise(e=>setTimeout(e,100))}catch(e){throw b(d(e instanceof Error&&"code"in e?e.code:"unknown")),e}finally{f(!1)}},y=async(e,r,t)=>{try{b(null),f(!0);let a=(await (0,s.eJ)(i.j2,e,r)).user;await (0,s.r7)(a,{displayName:"".concat(t.firstName," ").concat(t.lastName)});let n=o.Dc.now(),l={email:e,fullName:"".concat(t.firstName," ").concat(t.lastName),firstName:t.firstName,lastName:t.lastName,phone:t.phone||null,role:t.role,languagePreference:"en",createdAt:n,updatedAt:n};await (0,o.BN)((0,o.H9)(i.db,"users",a.uid),l),h({id:a.uid,...l,phone:t.phone||void 0});try{let e=await a.getIdToken(),r=await fetch("/api/auth/set-role",{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(e)},body:JSON.stringify({uid:a.uid,role:t.role})});if(r.ok)console.log("User role set successfully");else{let e=await r.json();console.error("Failed to set user role:",e.error)}}catch(e){console.error("Error setting user role:",e)}await x(a),await new Promise(e=>setTimeout(e,100))}catch(e){throw b(d(e instanceof Error&&"code"in e?e.code:"unknown")),e}finally{f(!1)}},j=async()=>{try{b(null),await (0,s.CI)(i.j2),h(null)}catch(e){throw b(d(e instanceof Error&&"code"in e?e.code:"unknown")),e}},N=async e=>{try{b(null),await (0,s.J1)(i.j2,e)}catch(e){throw b(d(e instanceof Error&&"code"in e?e.code:"unknown")),e}};return(0,a.jsx)(l.Provider,{value:{user:t,userProfile:u,loading:m,error:p,signIn:g,signUp:y,signOut:j,resetPassword:N,clearError:()=>{b(null)},refreshProfile:v},children:r})}function u(){let e=(0,n.useContext)(l);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}function d(e){switch(e){case"auth/user-not-found":case"auth/wrong-password":case"auth/invalid-credential":return"Invalid email or password";case"auth/email-already-in-use":return"An account with this email already exists";case"auth/weak-password":return"Password should be at least 6 characters";case"auth/invalid-email":return"Invalid email address";case"auth/too-many-requests":return"Too many failed attempts. Please try again later";case"auth/network-request-failed":return"Network error. Please check your connection";default:return"An unexpected error occurred. Please try again"}}function h(){let{user:e,loading:r}=u();return{user:e,loading:r,isAuthenticated:!!e}}},93915:(e,r,t)=>{"use strict";t.d(r,{p:()=>o});var a=t(95155),n=t(12115),s=t(59434);let o=(0,n.forwardRef)((e,r)=>{let{className:t,type:n,label:o,error:i,...l}=e;return(0,a.jsxs)("div",{className:"w-full",children:[o&&(0,a.jsx)("label",{className:"block mb-2 font-semibold text-[var(--primary-brown)] font-[var(--font-ui)]",children:o}),(0,a.jsx)("input",{type:n,className:(0,s.cn)("\n            w-full px-4 py-2 border-2 border-[var(--medium-gray)] \n            rounded-[var(--radius-md)] font-[var(--font-ui)] \n            transition-colors duration-300\n            focus:outline-none focus:border-[var(--primary-brown)] \n            focus:ring-0 focus:shadow-[0_0_0_3px_rgba(139,69,19,0.1)]\n            placeholder:text-[var(--medium-gray)]\n            ",i&&"border-red-500 focus:border-red-500",t),ref:r,...l}),i&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600",children:i})]})});o.displayName="Input"}},e=>{var r=r=>e(e.s=r);e.O(0,[992,965,277,288,874,441,684,358],()=>r(36365)),_N_E=e.O()}]);