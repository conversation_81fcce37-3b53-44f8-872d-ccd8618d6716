(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[983],{6654:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"useMergedRef",{enumerable:!0,get:function(){return s}});let a=t(12115);function s(e,r){let t=(0,a.useRef)(null),s=(0,a.useRef)(null);return(0,a.useCallback)(a=>{if(null===a){let e=t.current;e&&(t.current=null,e());let r=s.current;r&&(s.current=null,r())}else e&&(t.current=n(e,a)),r&&(s.current=n(r,a))},[e,r])}function n(e,r){if("function"!=typeof e)return e.current=r,()=>{e.current=null};{let t=e(r);return"function"==typeof t?t:()=>e(null)}}("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),e.exports=r.default)},13741:(e,r,t)=>{"use strict";t.d(r,{$:()=>o});var a=t(95155),s=t(12115),n=t(59434);let o=(0,s.forwardRef)((e,r)=>{let{className:t,variant:s="primary",size:o="md",isLoading:l,children:i,disabled:c,...d}=e;return(0,a.jsxs)("button",{className:(0,n.cn)("\n      inline-flex items-center justify-center rounded-[var(--radius-md)] \n      font-[var(--font-ui)] font-semibold transition-all duration-300 \n      focus:outline-none focus:ring-2 focus:ring-offset-2\n      disabled:opacity-50 disabled:cursor-not-allowed\n      hover:transform hover:-translate-y-0.5 hover:shadow-[var(--shadow-lg)]\n    ",{primary:"\n        bg-[var(--primary-brown)] text-white \n        hover:bg-[#A0522D] focus:ring-[var(--primary-brown)]\n      ",secondary:"\n        bg-[var(--secondary-sky)] text-white \n        hover:bg-[#357AE0] focus:ring-[var(--secondary-sky)]\n      ",outline:"\n        bg-transparent border-2 border-[var(--primary-brown)] text-[var(--primary-brown)]\n        hover:bg-[var(--primary-brown)] hover:text-white focus:ring-[var(--primary-brown)]\n      ",hunting:"\n        bg-[var(--hunting-accent)] text-white \n        hover:bg-[#A67C00] focus:ring-[var(--hunting-accent)]\n      ",photo:"\n        bg-[var(--photo-accent)] text-white \n        hover:bg-[#3557C7] focus:ring-[var(--photo-accent)]\n      "}[s],{sm:"px-3 py-1.5 text-sm",md:"px-6 py-2 text-base",lg:"px-8 py-3 text-lg"}[o],l&&"cursor-wait",t),disabled:c||l,ref:r,...d,children:[l&&(0,a.jsxs)("svg",{className:"mr-2 h-4 w-4 animate-spin",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),i]})});o.displayName="Button"},28214:(e,r,t)=>{"use strict";t.d(r,{IG:()=>a.IG,db:()=>a.db,j2:()=>a.j2});var a=t(67039)},35695:(e,r,t)=>{"use strict";var a=t(18999);t.o(a,"useParams")&&t.d(r,{useParams:function(){return a.useParams}}),t.o(a,"useRouter")&&t.d(r,{useRouter:function(){return a.useRouter}}),t.o(a,"useSearchParams")&&t.d(r,{useSearchParams:function(){return a.useSearchParams}})},56117:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>m});var a=t(95155),s=t(12115),n=t(6874),o=t.n(n),l=t(35695),i=t(84105),c=t(13741),d=t(93915),u=t(74338);function m(){let[e,r]=(0,s.useState)({email:"",password:"",confirmPassword:"",firstName:"",lastName:"",role:"guest",phone:""}),[t,n]=(0,s.useState)(!1),{signUp:m,error:h,clearError:f,user:p,loading:b}=(0,i.As)(),x=(0,l.useRouter)();if((0,s.useEffect)(()=>{!b&&p&&x.push("/dashboard")},[p,b,x]),b)return(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-earth-100 to-earth-200",children:(0,a.jsx)(u.k,{size:"lg"})});if(p)return null;let w=e=>{r(r=>({...r,[e.target.name]:e.target.value})),f()},v=async r=>{if(r.preventDefault(),n(!0),e.password!==e.confirmPassword||e.password.length<6)return void n(!1);try{await m(e.email,e.password,{firstName:e.firstName,lastName:e.lastName,phone:e.phone||void 0,role:e.role}),x.push("/dashboard")}catch(e){console.error("Registration error:",e)}finally{n(!1)}};return(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-earth-100 to-earth-200 px-4 py-8",children:(0,a.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-earth-900 mb-2",children:"Join BvR Safaris"}),(0,a.jsx)("p",{className:"text-earth-600",children:"Create your account to start your safari adventure"})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-8",children:[(0,a.jsxs)("form",{onSubmit:v,className:"space-y-6",children:[h&&(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-md p-3",children:(0,a.jsx)("p",{className:"text-red-600 text-sm",children:h})}),e.password!==e.confirmPassword&&e.confirmPassword&&(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-md p-3",children:(0,a.jsx)("p",{className:"text-red-600 text-sm",children:"Passwords do not match"})}),e.password.length>0&&e.password.length<6&&(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-md p-3",children:(0,a.jsx)("p",{className:"text-red-600 text-sm",children:"Password must be at least 6 characters"})}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"firstName",className:"block text-sm font-medium text-earth-700 mb-2",children:"First Name"}),(0,a.jsx)(d.p,{id:"firstName",name:"firstName",type:"text",value:e.firstName,onChange:w,placeholder:"First name",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"lastName",className:"block text-sm font-medium text-earth-700 mb-2",children:"Last Name"}),(0,a.jsx)(d.p,{id:"lastName",name:"lastName",type:"text",value:e.lastName,onChange:w,placeholder:"Last name",required:!0})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-earth-700 mb-2",children:"Email Address"}),(0,a.jsx)(d.p,{id:"email",name:"email",type:"email",value:e.email,onChange:w,placeholder:"Enter your email",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"phone",className:"block text-sm font-medium text-earth-700 mb-2",children:"Phone Number"}),(0,a.jsx)(d.p,{id:"phone",name:"phone",type:"tel",value:e.phone,onChange:w,placeholder:"Enter your phone number"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"role",className:"block text-sm font-medium text-earth-700 mb-2",children:"I am a..."}),(0,a.jsxs)("select",{id:"role",name:"role",value:e.role,onChange:w,className:"w-full px-3 py-2 border border-earth-300 rounded-md focus:outline-none focus:ring-2 focus:ring-accent-500 focus:border-transparent",required:!0,children:[(0,a.jsx)("option",{value:"guest",children:"Guest (Hunter/Photo Safari Enthusiast)"}),(0,a.jsx)("option",{value:"farm_owner",children:"Farm Owner"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-earth-700 mb-2",children:"Password"}),(0,a.jsx)(d.p,{id:"password",name:"password",type:"password",value:e.password,onChange:w,placeholder:"Create a password (min. 6 characters)",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-earth-700 mb-2",children:"Confirm Password"}),(0,a.jsx)(d.p,{id:"confirmPassword",name:"confirmPassword",type:"password",value:e.confirmPassword,onChange:w,placeholder:"Confirm your password",required:!0})]}),(0,a.jsx)(c.$,{type:"submit",variant:"primary",size:"lg",className:"w-full",isLoading:t,disabled:!e.email||!e.password||!e.confirmPassword||!e.firstName||!e.lastName||e.password!==e.confirmPassword||e.password.length<6,children:t?"Creating Account...":"Create Account"})]}),(0,a.jsx)("div",{className:"mt-6 text-center",children:(0,a.jsxs)("p",{className:"text-earth-600",children:["Already have an account?"," ",(0,a.jsx)(o(),{href:"/auth/login",className:"text-accent-600 hover:text-accent-700 font-medium",children:"Sign in here"})]})})]})]})})}},59434:(e,r,t)=>{"use strict";t.d(r,{Y:()=>l,Z:()=>o,cn:()=>n});var a=t(52596),s=t(39688);function n(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,s.QP)((0,a.$)(r))}function o(e){return e.toString().replace(/\B(?=(\d{3})+(?!\d))/g," ")}function l(e){let r=new Date(e);return"".concat(r.getDate()," ").concat(["January","February","March","April","May","June","July","August","September","October","November","December"][r.getMonth()]," ").concat(r.getFullYear())}},67039:(e,r,t)=>{"use strict";t.d(r,{IG:()=>d,db:()=>c,j2:()=>i});var a=t(23915),s=t(16203),n=t(35317),o=t(90858);let l=(0,a.Wp)({apiKey:"AIzaSyDdsTm8eifLrb1W0WLK0DwBk4p4XLFZKMw",authDomain:"rvbsafaris.firebaseapp.com",projectId:"rvbsafaris",storageBucket:"rvbsafaris.firebasestorage.app",messagingSenderId:"************",appId:"1:************:web:7a39a3b4451f34f85feed3"}),i=(0,s.xI)(l),c=(0,n.aU)(l),d=(0,o.c7)(l);console.log("Firebase Storage initialized with bucket:","rvbsafaris.firebasestorage.app")},74338:(e,r,t)=>{"use strict";t.d(r,{k:()=>n});var a=t(95155),s=t(59434);function n(e){let{size:r="md",className:t}=e;return(0,a.jsx)("div",{className:(0,s.cn)("flex items-center justify-center",t),children:(0,a.jsx)("div",{className:(0,s.cn)("animate-spin rounded-full border-2 border-white border-t-transparent",{sm:"w-4 h-4",md:"w-6 h-6",lg:"w-12 h-12"}[r])})})}},84105:(e,r,t)=>{"use strict";t.d(r,{As:()=>d,AuthProvider:()=>c,Nu:()=>m});var a=t(95155),s=t(12115),n=t(16203),o=t(35317),l=t(28214);let i=(0,s.createContext)(void 0);function c(e){let{children:r}=e,[t,c]=(0,s.useState)(null),[d,m]=(0,s.useState)(null),[h,f]=(0,s.useState)(!0),[p,b]=(0,s.useState)(null),x=async e=>{try{let n=(0,o.H9)(l.db,"users",e),i=await (0,o.x7)(n);if(i.exists()){var r,t,a,s;let e=i.data();return{id:i.id,...e,createdAt:(null==(t=e.createdAt)||null==(r=t.toDate)?void 0:r.call(t))||e.createdAt,updatedAt:(null==(s=e.updatedAt)||null==(a=s.toDate)?void 0:a.call(s))||e.updatedAt}}return null}catch(e){return console.error("Error fetching user profile:",e),null}},w=async()=>{t&&m(await x(t.uid))},v=e=>new Promise(r=>{let t=(0,n.hg)(l.j2,a=>{e&&a&&a.uid===e.uid?(t(),r()):e||a||(t(),r())})});(0,s.useEffect)(()=>(0,n.hg)(l.j2,async e=>{if(c(e),e){try{let r=await e.getIdToken();document.cookie="firebase-token=".concat(r,"; path=/; max-age=3600; secure; samesite=strict")}catch(e){console.error("Error getting ID token:",e)}m(await x(e.uid))}else document.cookie="firebase-token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT",m(null);f(!1)}),[]);let g=async(e,r)=>{try{b(null),f(!0);let t=await (0,n.x9)(l.j2,e,r);await v(t.user),await new Promise(e=>setTimeout(e,100))}catch(e){throw b(u(e instanceof Error&&"code"in e?e.code:"unknown")),e}finally{f(!1)}},y=async(e,r,t)=>{try{b(null),f(!0);let a=(await (0,n.eJ)(l.j2,e,r)).user;await (0,n.r7)(a,{displayName:"".concat(t.firstName," ").concat(t.lastName)});let s=o.Dc.now(),i={email:e,fullName:"".concat(t.firstName," ").concat(t.lastName),firstName:t.firstName,lastName:t.lastName,phone:t.phone||null,role:t.role,languagePreference:"en",createdAt:s,updatedAt:s};await (0,o.BN)((0,o.H9)(l.db,"users",a.uid),i),m({id:a.uid,...i,phone:t.phone||void 0});try{let e=await a.getIdToken(),r=await fetch("/api/auth/set-role",{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(e)},body:JSON.stringify({uid:a.uid,role:t.role})});if(r.ok)console.log("User role set successfully");else{let e=await r.json();console.error("Failed to set user role:",e.error)}}catch(e){console.error("Error setting user role:",e)}await v(a),await new Promise(e=>setTimeout(e,100))}catch(e){throw b(u(e instanceof Error&&"code"in e?e.code:"unknown")),e}finally{f(!1)}},N=async()=>{try{b(null),await (0,n.CI)(l.j2),m(null)}catch(e){throw b(u(e instanceof Error&&"code"in e?e.code:"unknown")),e}},j=async e=>{try{b(null),await (0,n.J1)(l.j2,e)}catch(e){throw b(u(e instanceof Error&&"code"in e?e.code:"unknown")),e}};return(0,a.jsx)(i.Provider,{value:{user:t,userProfile:d,loading:h,error:p,signIn:g,signUp:y,signOut:N,resetPassword:j,clearError:()=>{b(null)},refreshProfile:w},children:r})}function d(){let e=(0,s.useContext)(i);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}function u(e){switch(e){case"auth/user-not-found":case"auth/wrong-password":case"auth/invalid-credential":return"Invalid email or password";case"auth/email-already-in-use":return"An account with this email already exists";case"auth/weak-password":return"Password should be at least 6 characters";case"auth/invalid-email":return"Invalid email address";case"auth/too-many-requests":return"Too many failed attempts. Please try again later";case"auth/network-request-failed":return"Network error. Please check your connection";default:return"An unexpected error occurred. Please try again"}}function m(){let{user:e,loading:r}=d();return{user:e,loading:r,isAuthenticated:!!e}}},93915:(e,r,t)=>{"use strict";t.d(r,{p:()=>o});var a=t(95155),s=t(12115),n=t(59434);let o=(0,s.forwardRef)((e,r)=>{let{className:t,type:s,label:o,error:l,...i}=e;return(0,a.jsxs)("div",{className:"w-full",children:[o&&(0,a.jsx)("label",{className:"block mb-2 font-semibold text-[var(--primary-brown)] font-[var(--font-ui)]",children:o}),(0,a.jsx)("input",{type:s,className:(0,n.cn)("\n            w-full px-4 py-2 border-2 border-[var(--medium-gray)] \n            rounded-[var(--radius-md)] font-[var(--font-ui)] \n            transition-colors duration-300\n            focus:outline-none focus:border-[var(--primary-brown)] \n            focus:ring-0 focus:shadow-[0_0_0_3px_rgba(139,69,19,0.1)]\n            placeholder:text-[var(--medium-gray)]\n            ",l&&"border-red-500 focus:border-red-500",t),ref:r,...i}),l&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600",children:l})]})});o.displayName="Input"},99609:(e,r,t)=>{Promise.resolve().then(t.bind(t,56117))}},e=>{var r=r=>e(e.s=r);e.O(0,[992,965,277,288,874,441,684,358],()=>r(99609)),_N_E=e.O()}]);