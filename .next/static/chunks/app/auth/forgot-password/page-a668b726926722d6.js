(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[413],{6654:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"useMergedRef",{enumerable:!0,get:function(){return n}});let a=t(12115);function n(e,r){let t=(0,a.useRef)(null),n=(0,a.useRef)(null);return(0,a.useCallback)(a=>{if(null===a){let e=t.current;e&&(t.current=null,e());let r=n.current;r&&(n.current=null,r())}else e&&(t.current=s(e,a)),r&&(n.current=s(r,a))},[e,r])}function s(e,r){if("function"!=typeof e)return e.current=r,()=>{e.current=null};{let t=e(r);return"function"==typeof t?t:()=>e(null)}}("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),e.exports=r.default)},13741:(e,r,t)=>{"use strict";t.d(r,{$:()=>o});var a=t(95155),n=t(12115),s=t(59434);let o=(0,n.forwardRef)((e,r)=>{let{className:t,variant:n="primary",size:o="md",isLoading:l,children:i,disabled:c,...d}=e;return(0,a.jsxs)("button",{className:(0,s.cn)("\n      inline-flex items-center justify-center rounded-[var(--radius-md)] \n      font-[var(--font-ui)] font-semibold transition-all duration-300 \n      focus:outline-none focus:ring-2 focus:ring-offset-2\n      disabled:opacity-50 disabled:cursor-not-allowed\n      hover:transform hover:-translate-y-0.5 hover:shadow-[var(--shadow-lg)]\n    ",{primary:"\n        bg-[var(--primary-brown)] text-white \n        hover:bg-[#A0522D] focus:ring-[var(--primary-brown)]\n      ",secondary:"\n        bg-[var(--secondary-sky)] text-white \n        hover:bg-[#357AE0] focus:ring-[var(--secondary-sky)]\n      ",outline:"\n        bg-transparent border-2 border-[var(--primary-brown)] text-[var(--primary-brown)]\n        hover:bg-[var(--primary-brown)] hover:text-white focus:ring-[var(--primary-brown)]\n      ",hunting:"\n        bg-[var(--hunting-accent)] text-white \n        hover:bg-[#A67C00] focus:ring-[var(--hunting-accent)]\n      ",photo:"\n        bg-[var(--photo-accent)] text-white \n        hover:bg-[#3557C7] focus:ring-[var(--photo-accent)]\n      "}[n],{sm:"px-3 py-1.5 text-sm",md:"px-6 py-2 text-base",lg:"px-8 py-3 text-lg"}[o],l&&"cursor-wait",t),disabled:c||l,ref:r,...d,children:[l&&(0,a.jsxs)("svg",{className:"mr-2 h-4 w-4 animate-spin",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),i]})});o.displayName="Button"},28214:(e,r,t)=>{"use strict";t.d(r,{IG:()=>a.IG,db:()=>a.db,j2:()=>a.j2});var a=t(67039)},59434:(e,r,t)=>{"use strict";t.d(r,{Y:()=>l,Z:()=>o,cn:()=>s});var a=t(52596),n=t(39688);function s(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,n.QP)((0,a.$)(r))}function o(e){return e.toString().replace(/\B(?=(\d{3})+(?!\d))/g," ")}function l(e){let r=new Date(e);return"".concat(r.getDate()," ").concat(["January","February","March","April","May","June","July","August","September","October","November","December"][r.getMonth()]," ").concat(r.getFullYear())}},62239:(e,r,t)=>{Promise.resolve().then(t.bind(t,96195))},67039:(e,r,t)=>{"use strict";t.d(r,{IG:()=>d,db:()=>c,j2:()=>i});var a=t(23915),n=t(16203),s=t(35317),o=t(90858);let l=(0,a.Wp)({apiKey:"AIzaSyDdsTm8eifLrb1W0WLK0DwBk4p4XLFZKMw",authDomain:"rvbsafaris.firebaseapp.com",projectId:"rvbsafaris",storageBucket:"rvbsafaris.firebasestorage.app",messagingSenderId:"593781635754",appId:"1:593781635754:web:7a39a3b4451f34f85feed3"}),i=(0,n.xI)(l),c=(0,s.aU)(l),d=(0,o.c7)(l);console.log("Firebase Storage initialized with bucket:","rvbsafaris.firebasestorage.app")},93915:(e,r,t)=>{"use strict";t.d(r,{p:()=>o});var a=t(95155),n=t(12115),s=t(59434);let o=(0,n.forwardRef)((e,r)=>{let{className:t,type:n,label:o,error:l,...i}=e;return(0,a.jsxs)("div",{className:"w-full",children:[o&&(0,a.jsx)("label",{className:"block mb-2 font-semibold text-[var(--primary-brown)] font-[var(--font-ui)]",children:o}),(0,a.jsx)("input",{type:n,className:(0,s.cn)("\n            w-full px-4 py-2 border-2 border-[var(--medium-gray)] \n            rounded-[var(--radius-md)] font-[var(--font-ui)] \n            transition-colors duration-300\n            focus:outline-none focus:border-[var(--primary-brown)] \n            focus:ring-0 focus:shadow-[0_0_0_3px_rgba(139,69,19,0.1)]\n            placeholder:text-[var(--medium-gray)]\n            ",l&&"border-red-500 focus:border-red-500",t),ref:r,...i}),l&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600",children:l})]})});o.displayName="Input"},96195:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>u});var a=t(95155),n=t(12115),s=t(6874),o=t.n(s),l=t(16203),i=t(28214),c=t(13741),d=t(93915);function u(){let[e,r]=(0,n.useState)(""),[t,s]=(0,n.useState)(!1),[u,m]=(0,n.useState)(!1),[f,h]=(0,n.useState)(""),p=async r=>{r.preventDefault(),s(!0),h("");try{await (0,l.J1)(i.j2,e,{url:"".concat(window.location.origin,"/auth/login")}),m(!0)}catch(e){console.error("Password reset error:",e),h(e instanceof Error?e.message:"An unexpected error occurred")}finally{s(!1)}};return u?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-earth-100 to-earth-200 px-4",children:(0,a.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"mx-auto w-16 h-16 bg-accent-100 rounded-full flex items-center justify-center mb-6",children:(0,a.jsx)("svg",{className:"w-8 h-8 text-accent-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})})}),(0,a.jsx)("h1",{className:"text-3xl font-bold text-earth-900 mb-2",children:"Check Your Email"}),(0,a.jsxs)("p",{className:"text-earth-600 mb-6",children:["We’ve sent a password reset link to ",e,". Please check your email and follow the instructions to reset your password."]})]}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-lg p-8 text-center",children:(0,a.jsx)(o(),{href:"/auth/login",children:(0,a.jsx)(c.$,{variant:"primary",size:"lg",className:"w-full",children:"Back to Login"})})})]})}):(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-earth-100 to-earth-200 px-4",children:(0,a.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-earth-900 mb-2",children:"Reset Password"}),(0,a.jsx)("p",{className:"text-earth-600",children:"Enter your email address and we’ll send you a link to reset your password"})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-8",children:[(0,a.jsxs)("form",{onSubmit:p,className:"space-y-6",children:[f&&(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-md p-3",children:(0,a.jsx)("p",{className:"text-red-600 text-sm",children:f})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-earth-700 mb-2",children:"Email Address"}),(0,a.jsx)(d.p,{id:"email",type:"email",value:e,onChange:e=>r(e.target.value),placeholder:"Enter your email",required:!0})]}),(0,a.jsx)(c.$,{type:"submit",variant:"primary",size:"lg",className:"w-full",isLoading:t,children:t?"Sending Reset Link...":"Send Reset Link"})]}),(0,a.jsx)("div",{className:"mt-6 text-center",children:(0,a.jsxs)("p",{className:"text-earth-600",children:["Remember your password?"," ",(0,a.jsx)(o(),{href:"/auth/login",className:"text-accent-600 hover:text-accent-700 font-medium",children:"Sign in here"})]})})]})]})})}}},e=>{var r=r=>e(e.s=r);e.O(0,[992,965,277,288,874,441,684,358],()=>r(62239)),_N_E=e.O()}]);