(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{6654:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"useMergedRef",{enumerable:!0,get:function(){return n}});let a=t(12115);function n(e,r){let t=(0,a.useRef)(null),n=(0,a.useRef)(null);return(0,a.useCallback)(a=>{if(null===a){let e=t.current;e&&(t.current=null,e());let r=n.current;r&&(n.current=null,r())}else e&&(t.current=s(e,a)),r&&(n.current=s(r,a))},[e,r])}function s(e,r){if("function"!=typeof e)return e.current=r,()=>{e.current=null};{let t=e(r);return"function"==typeof t?t:()=>e(null)}}("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),e.exports=r.default)},13741:(e,r,t)=>{"use strict";t.d(r,{$:()=>o});var a=t(95155),n=t(12115),s=t(59434);let o=(0,n.forwardRef)((e,r)=>{let{className:t,variant:n="primary",size:o="md",isLoading:i,children:l,disabled:c,...d}=e;return(0,a.jsxs)("button",{className:(0,s.cn)("\n      inline-flex items-center justify-center rounded-[var(--radius-md)] \n      font-[var(--font-ui)] font-semibold transition-all duration-300 \n      focus:outline-none focus:ring-2 focus:ring-offset-2\n      disabled:opacity-50 disabled:cursor-not-allowed\n      hover:transform hover:-translate-y-0.5 hover:shadow-[var(--shadow-lg)]\n    ",{primary:"\n        bg-[var(--primary-brown)] text-white \n        hover:bg-[#A0522D] focus:ring-[var(--primary-brown)]\n      ",secondary:"\n        bg-[var(--secondary-sky)] text-white \n        hover:bg-[#357AE0] focus:ring-[var(--secondary-sky)]\n      ",outline:"\n        bg-transparent border-2 border-[var(--primary-brown)] text-[var(--primary-brown)]\n        hover:bg-[var(--primary-brown)] hover:text-white focus:ring-[var(--primary-brown)]\n      ",hunting:"\n        bg-[var(--hunting-accent)] text-white \n        hover:bg-[#A67C00] focus:ring-[var(--hunting-accent)]\n      ",photo:"\n        bg-[var(--photo-accent)] text-white \n        hover:bg-[#3557C7] focus:ring-[var(--photo-accent)]\n      "}[n],{sm:"px-3 py-1.5 text-sm",md:"px-6 py-2 text-base",lg:"px-8 py-3 text-lg"}[o],i&&"cursor-wait",t),disabled:c||i,ref:r,...d,children:[i&&(0,a.jsxs)("svg",{className:"mr-2 h-4 w-4 animate-spin",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),l]})});o.displayName="Button"},18295:e=>{e.exports={style:{fontFamily:"'Montserrat', 'Montserrat Fallback'",fontStyle:"normal"},className:"__className_8a1034",variable:"__variable_8a1034"}},28214:(e,r,t)=>{"use strict";t.d(r,{IG:()=>a.IG,db:()=>a.db,j2:()=>a.j2});var a=t(67039)},30347:()=>{},35695:(e,r,t)=>{"use strict";var a=t(18999);t.o(a,"useParams")&&t.d(r,{useParams:function(){return a.useParams}}),t.o(a,"useRouter")&&t.d(r,{useRouter:function(){return a.useRouter}}),t.o(a,"useSearchParams")&&t.d(r,{useSearchParams:function(){return a.useSearchParams}})},43622:e=>{e.exports={style:{fontFamily:"'Source Sans 3', 'Source Sans 3 Fallback'",fontStyle:"normal"},className:"__className_dee724",variable:"__variable_dee724"}},53535:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,6874,23)),Promise.resolve().then(t.t.bind(t,18295,23)),Promise.resolve().then(t.t.bind(t,43622,23)),Promise.resolve().then(t.t.bind(t,30347,23)),Promise.resolve().then(t.bind(t,63955)),Promise.resolve().then(t.bind(t,84105))},59434:(e,r,t)=>{"use strict";t.d(r,{Y:()=>i,Z:()=>o,cn:()=>s});var a=t(52596),n=t(39688);function s(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,n.QP)((0,a.$)(r))}function o(e){return e.toString().replace(/\B(?=(\d{3})+(?!\d))/g," ")}function i(e){let r=new Date(e);return"".concat(r.getDate()," ").concat(["January","February","March","April","May","June","July","August","September","October","November","December"][r.getMonth()]," ").concat(r.getFullYear())}},63955:(e,r,t)=>{"use strict";t.d(r,{Navbar:()=>u});var a=t(95155),n=t(12115),s=t(6874),o=t.n(s),i=t(35695),l=t(13741),c=t(59434),d=t(84105);function u(){var e,r,t;let[s,u]=(0,n.useState)(!1),[h,m]=(0,n.useState)(!1),{user:f,loading:v,signOut:x}=(0,d.As)(),p=(0,i.useRouter)(),y=async()=>{await x(),p.push("/")};return(0,a.jsx)("nav",{className:"bg-white shadow-[var(--shadow-sm)] sticky top-0 z-50",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,a.jsx)(o(),{href:"/",className:"flex items-center font-bold",style:{fontFamily:"var(--font-display)",fontSize:"28px",fontWeight:"700",color:"var(--deep-brown)",textDecoration:"none"},children:"BvR Safaris"}),(0,a.jsxs)("div",{className:"hidden md:flex items-center space-x-8",children:[(0,a.jsx)(o(),{href:"/farms",className:"nav-text transition-colors hover:opacity-80",style:{fontFamily:"var(--font-body)",fontSize:"var(--text-secondary-body)",fontWeight:"400",color:"var(--dark-text)",textDecoration:"none"},children:"Farms"}),(0,a.jsx)(o(),{href:"/farms?activity=hunting",className:"nav-text transition-colors hover:opacity-80",style:{fontFamily:"var(--font-body)",fontSize:"var(--text-secondary-body)",fontWeight:"400",color:"var(--dark-text)",textDecoration:"none"},children:"Hunt"}),(0,a.jsx)(o(),{href:"/farms?activity=photo_safari",className:"nav-text transition-colors hover:opacity-80",style:{fontFamily:"var(--font-body)",fontSize:"var(--text-secondary-body)",fontWeight:"400",color:"var(--dark-text)",textDecoration:"none"},children:"Photo Safari"}),(0,a.jsx)(o(),{href:"/about",className:"nav-text transition-colors hover:opacity-80",style:{fontFamily:"var(--font-body)",fontSize:"var(--text-secondary-body)",fontWeight:"400",color:"var(--dark-text)",textDecoration:"none"},children:"About"})]}),(0,a.jsx)("div",{className:"hidden md:flex items-center space-x-4",children:v?(0,a.jsx)("div",{className:"animate-pulse bg-gray-200 h-8 w-20 rounded"}):f?(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)("button",{onClick:()=>m(!h),className:"flex items-center space-x-2 text-[var(--dark-gray)] hover:text-[var(--primary-brown)] font-medium transition-colors",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-[var(--primary-brown)] text-white rounded-full flex items-center justify-center text-sm font-semibold",children:(null==(e=f.displayName)?void 0:e[0])||(null==(t=f.email)||null==(r=t[0])?void 0:r.toUpperCase())||"U"}),(0,a.jsx)("span",{className:"hidden lg:block",children:f.displayName||"Account"}),(0,a.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})]}),h&&(0,a.jsxs)("div",{className:"absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 border",children:[(0,a.jsx)(o(),{href:"/dashboard",className:"block px-4 py-2 text-sm text-[var(--dark-gray)] hover:bg-[var(--earth-100)] hover:text-[var(--primary-brown)]",onClick:()=>m(!1),children:"Dashboard"}),(0,a.jsx)(o(),{href:"/profile",className:"block px-4 py-2 text-sm text-[var(--dark-gray)] hover:bg-[var(--earth-100)] hover:text-[var(--primary-brown)]",onClick:()=>m(!1),children:"Profile Settings"}),(0,a.jsx)("hr",{className:"my-1"}),(0,a.jsx)("button",{onClick:()=>{m(!1),y()},className:"block w-full text-left px-4 py-2 text-sm text-[var(--dark-gray)] hover:bg-[var(--earth-100)] hover:text-[var(--primary-brown)]",children:"Sign Out"})]})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(o(),{href:"/auth/login",children:(0,a.jsx)(l.$,{variant:"outline",size:"sm",style:{fontFamily:"var(--font-body)",fontSize:"var(--text-labels)",borderColor:"var(--deep-brown)",color:"var(--deep-brown)",borderRadius:"8px"},children:"Sign In"})}),(0,a.jsx)(o(),{href:"/auth/register",children:(0,a.jsx)(l.$,{variant:"primary",size:"sm",style:{fontFamily:"var(--font-body)",fontSize:"var(--text-labels)",backgroundColor:"var(--deep-brown)",color:"var(--cream)",borderRadius:"8px"},children:"Sign Up"})})]})}),(0,a.jsx)("div",{className:"md:hidden",children:(0,a.jsx)("button",{onClick:()=>u(!s),className:"text-[var(--dark-gray)] hover:text-[var(--primary-brown)] p-2",children:(0,a.jsx)("svg",{className:"h-6 w-6",fill:"none",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",viewBox:"0 0 24 24",stroke:"currentColor",children:s?(0,a.jsx)("path",{d:"M6 18L18 6M6 6l12 12"}):(0,a.jsx)("path",{d:"M4 6h16M4 12h16M4 18h16"})})})})]}),(0,a.jsx)("div",{className:(0,c.cn)("md:hidden transition-all duration-300 overflow-hidden",s?"max-h-96 pb-4":"max-h-0"),children:(0,a.jsxs)("div",{className:"px-2 pt-2 pb-3 space-y-1",children:[(0,a.jsx)(o(),{href:"/farms",className:"block px-3 py-2 text-[var(--dark-gray)] hover:text-[var(--primary-brown)] font-medium",children:"Farms"}),(0,a.jsx)(o(),{href:"/farms?activity=hunting",className:"block px-3 py-2 text-[var(--dark-gray)] hover:text-[var(--primary-brown)] font-medium",children:"Hunt"}),(0,a.jsx)(o(),{href:"/farms?activity=photo_safari",className:"block px-3 py-2 text-[var(--dark-gray)] hover:text-[var(--primary-brown)] font-medium",children:"Photo Safari"}),(0,a.jsx)(o(),{href:"/about",className:"block px-3 py-2 text-[var(--dark-gray)] hover:text-[var(--primary-brown)] font-medium",children:"About"}),(0,a.jsx)("div",{className:"pt-4 space-y-2",children:v?(0,a.jsx)("div",{className:"animate-pulse bg-gray-200 h-8 w-full rounded"}):f?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(o(),{href:"/dashboard",className:"block",children:(0,a.jsx)(l.$,{variant:"outline",size:"sm",className:"w-full",children:"Dashboard"})}),(0,a.jsx)(o(),{href:"/profile",className:"block",children:(0,a.jsx)(l.$,{variant:"outline",size:"sm",className:"w-full",children:"Profile"})}),(0,a.jsx)(l.$,{variant:"primary",size:"sm",className:"w-full",onClick:y,children:"Sign Out"})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(o(),{href:"/auth/login",className:"block",children:(0,a.jsx)(l.$,{variant:"outline",size:"sm",className:"w-full",children:"Sign In"})}),(0,a.jsx)(o(),{href:"/auth/register",className:"block",children:(0,a.jsx)(l.$,{variant:"primary",size:"sm",className:"w-full",children:"Sign Up"})})]})})]})})]})})}},67039:(e,r,t)=>{"use strict";t.d(r,{IG:()=>d,db:()=>c,j2:()=>l});var a=t(23915),n=t(16203),s=t(35317),o=t(90858);let i=(0,a.Wp)({apiKey:"AIzaSyDdsTm8eifLrb1W0WLK0DwBk4p4XLFZKMw",authDomain:"rvbsafaris.firebaseapp.com",projectId:"rvbsafaris",storageBucket:"rvbsafaris.firebasestorage.app",messagingSenderId:"593781635754",appId:"1:593781635754:web:7a39a3b4451f34f85feed3"}),l=(0,n.xI)(i),c=(0,s.aU)(i),d=(0,o.c7)(i);console.log("Firebase Storage initialized with bucket:","rvbsafaris.firebasestorage.app")},84105:(e,r,t)=>{"use strict";t.d(r,{As:()=>d,AuthProvider:()=>c,Nu:()=>h});var a=t(95155),n=t(12115),s=t(16203),o=t(35317),i=t(28214);let l=(0,n.createContext)(void 0);function c(e){let{children:r}=e,[t,c]=(0,n.useState)(null),[d,h]=(0,n.useState)(null),[m,f]=(0,n.useState)(!0),[v,x]=(0,n.useState)(null),p=async e=>{try{let s=(0,o.H9)(i.db,"users",e),l=await (0,o.x7)(s);if(l.exists()){var r,t,a,n;let e=l.data();return{id:l.id,...e,createdAt:(null==(t=e.createdAt)||null==(r=t.toDate)?void 0:r.call(t))||e.createdAt,updatedAt:(null==(n=e.updatedAt)||null==(a=n.toDate)?void 0:a.call(n))||e.updatedAt}}return null}catch(e){return console.error("Error fetching user profile:",e),null}},y=async()=>{t&&h(await p(t.uid))},b=e=>new Promise(r=>{let t=(0,s.hg)(i.j2,a=>{e&&a&&a.uid===e.uid?(t(),r()):e||a||(t(),r())})});(0,n.useEffect)(()=>(0,s.hg)(i.j2,async e=>{if(c(e),e){try{let r=await e.getIdToken();document.cookie="firebase-token=".concat(r,"; path=/; max-age=3600; secure; samesite=strict")}catch(e){console.error("Error getting ID token:",e)}h(await p(e.uid))}else document.cookie="firebase-token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT",h(null);f(!1)}),[]);let w=async(e,r)=>{try{x(null),f(!0);let t=await (0,s.x9)(i.j2,e,r);await b(t.user),await new Promise(e=>setTimeout(e,100))}catch(e){throw x(u(e instanceof Error&&"code"in e?e.code:"unknown")),e}finally{f(!1)}},g=async(e,r,t)=>{try{x(null),f(!0);let a=(await (0,s.eJ)(i.j2,e,r)).user;await (0,s.r7)(a,{displayName:"".concat(t.firstName," ").concat(t.lastName)});let n=o.Dc.now(),l={email:e,fullName:"".concat(t.firstName," ").concat(t.lastName),firstName:t.firstName,lastName:t.lastName,phone:t.phone||null,role:t.role,languagePreference:"en",createdAt:n,updatedAt:n};await (0,o.BN)((0,o.H9)(i.db,"users",a.uid),l),h({id:a.uid,...l,phone:t.phone||void 0});try{let e=await a.getIdToken(),r=await fetch("/api/auth/set-role",{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(e)},body:JSON.stringify({uid:a.uid,role:t.role})});if(r.ok)console.log("User role set successfully");else{let e=await r.json();console.error("Failed to set user role:",e.error)}}catch(e){console.error("Error setting user role:",e)}await b(a),await new Promise(e=>setTimeout(e,100))}catch(e){throw x(u(e instanceof Error&&"code"in e?e.code:"unknown")),e}finally{f(!1)}},j=async()=>{try{x(null),await (0,s.CI)(i.j2),h(null)}catch(e){throw x(u(e instanceof Error&&"code"in e?e.code:"unknown")),e}},k=async e=>{try{x(null),await (0,s.J1)(i.j2,e)}catch(e){throw x(u(e instanceof Error&&"code"in e?e.code:"unknown")),e}};return(0,a.jsx)(l.Provider,{value:{user:t,userProfile:d,loading:m,error:v,signIn:w,signUp:g,signOut:j,resetPassword:k,clearError:()=>{x(null)},refreshProfile:y},children:r})}function d(){let e=(0,n.useContext)(l);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}function u(e){switch(e){case"auth/user-not-found":case"auth/wrong-password":case"auth/invalid-credential":return"Invalid email or password";case"auth/email-already-in-use":return"An account with this email already exists";case"auth/weak-password":return"Password should be at least 6 characters";case"auth/invalid-email":return"Invalid email address";case"auth/too-many-requests":return"Too many failed attempts. Please try again later";case"auth/network-request-failed":return"Network error. Please check your connection";default:return"An unexpected error occurred. Please try again"}}function h(){let{user:e,loading:r}=d();return{user:e,loading:r,isAuthenticated:!!e}}}},e=>{var r=r=>e(e.s=r);e.O(0,[676,690,992,965,277,288,874,441,684,358],()=>r(53535)),_N_E=e.O()}]);