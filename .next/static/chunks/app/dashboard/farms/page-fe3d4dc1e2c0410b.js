(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[23],{4516:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},13717:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},17580:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},19946:(e,s,t)=>{"use strict";t.d(s,{A:()=>h});var a=t(12115);let r=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,s,t)=>t?t.toUpperCase():s.toLowerCase()),i=e=>{let s=l(e);return s.charAt(0).toUpperCase()+s.slice(1)},c=function(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return s.filter((e,s,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===s).join(" ").trim()},n=e=>{for(let s in e)if(s.startsWith("aria-")||"role"===s||"title"===s)return!0};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let m=(0,a.forwardRef)((e,s)=>{let{color:t="currentColor",size:r=24,strokeWidth:l=2,absoluteStrokeWidth:i,className:m="",children:h,iconNode:x,...o}=e;return(0,a.createElement)("svg",{ref:s,...d,width:r,height:r,stroke:t,strokeWidth:i?24*Number(l)/Number(r):l,className:c("lucide",m),...!h&&!n(o)&&{"aria-hidden":"true"},...o},[...x.map(e=>{let[s,t]=e;return(0,a.createElement)(s,t)}),...Array.isArray(h)?h:[h]])}),h=(e,s)=>{let t=(0,a.forwardRef)((t,l)=>{let{className:n,...d}=t;return(0,a.createElement)(m,{ref:l,iconNode:s,className:c("lucide-".concat(r(i(e))),"lucide-".concat(e),n),...d})});return t.displayName=i(e),t}},35169:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},35695:(e,s,t)=>{"use strict";var a=t(18999);t.o(a,"useParams")&&t.d(s,{useParams:function(){return a.useParams}}),t.o(a,"useRouter")&&t.d(s,{useRouter:function(){return a.useRouter}}),t.o(a,"useSearchParams")&&t.d(s,{useSearchParams:function(){return a.useSearchParams}})},38564:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},45013:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>y});var a=t(95155),r=t(12115),l=t(35695),i=t(84105),c=t(13741),n=t(17703),d=t(74338),m=t(28666),h=t(35169),x=t(84616),o=t(4516),u=t(92657),f=t(13717),p=t(72713),j=t(17580),N=t(38564),g=t(6874),v=t.n(g);function y(){let{user:e,userProfile:s,loading:t}=(0,i.As)(),g=(0,l.useRouter)(),[y,b]=(0,r.useState)([]),[w,A]=(0,r.useState)(!0),[k,z]=(0,r.useState)(null);return((0,r.useEffect)(()=>{let e=async()=>{if(null==s?void 0:s.id)try{if(A(!0),z(null),"farm_owner"!==s.role)return void g.push("/dashboard");let e=await m.eg.getByOwner(s.id);b(e)}catch(e){console.error("Error fetching farms:",e),z("Failed to load farms")}finally{A(!1)}};!t&&s&&e()},[t,s,g]),(0,r.useEffect)(()=>{t||e||g.push("/auth/login")},[t,e,g]),t||w)?(0,a.jsx)("div",{className:"min-h-screen bg-earth-100 flex items-center justify-center",children:(0,a.jsx)(d.k,{size:"lg"})}):e?(null==s?void 0:s.role)!=="farm_owner"?(g.push("/dashboard"),null):(0,a.jsx)("div",{className:"min-h-screen bg-earth-100 py-8",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("div",{className:"flex items-center gap-4 mb-4",children:(0,a.jsx)(v(),{href:"/dashboard",children:(0,a.jsxs)(c.$,{variant:"outline",size:"sm",children:[(0,a.jsx)(h.A,{className:"w-4 h-4 mr-2"}),"Back to Dashboard"]})})}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-earth-900",children:"My Farms"}),(0,a.jsx)("p",{className:"text-earth-600 mt-2",children:"Manage your farm listings and track their performance"})]}),(0,a.jsx)(v(),{href:"/farms/create",children:(0,a.jsxs)(c.$,{variant:"primary",className:"flex items-center gap-2",children:[(0,a.jsx)(x.A,{className:"w-4 h-4"}),"Add New Farm"]})})]})]}),k&&(0,a.jsx)("div",{className:"mb-6 bg-red-50 border border-red-200 rounded-md p-4",children:(0,a.jsx)("p",{className:"text-red-600",children:k})}),0===y.length?(0,a.jsx)(n.Zp,{className:"p-8",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(o.A,{className:"w-16 h-16 text-earth-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-earth-900 mb-2",children:"No farms yet"}),(0,a.jsx)("p",{className:"text-earth-600 mb-6",children:"Create your first farm listing to start receiving bookings from safari enthusiasts."}),(0,a.jsx)(v(),{href:"/farms/create",children:(0,a.jsxs)(c.$,{variant:"primary",className:"flex items-center gap-2",children:[(0,a.jsx)(x.A,{className:"w-4 h-4"}),"Create Your First Farm"]})})]})}):(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:y.map(e=>(0,a.jsx)(n.Zp,{className:"overflow-hidden",children:(0,a.jsxs)(n.Wu,{className:"p-0",children:[(0,a.jsx)("div",{className:"h-48 bg-gradient-to-br from-earth-200 to-earth-300 flex items-center justify-center",children:(0,a.jsx)(o.A,{className:"w-12 h-12 text-earth-600"})}),(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between mb-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-earth-900 mb-1",children:e.name}),(0,a.jsxs)("p",{className:"text-sm text-earth-600 flex items-center gap-1",children:[(0,a.jsx)(o.A,{className:"w-3 h-3"}),e.location,", ",e.province]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)("div",{className:"w-2 h-2 rounded-full ".concat(e.isActive?"bg-green-500":"bg-red-500")}),(0,a.jsx)("span",{className:"text-xs text-earth-600",children:e.isActive?"Active":"Inactive"})]})]}),(0,a.jsxs)("div",{className:"space-y-2 mb-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,a.jsx)("span",{className:"text-earth-600",children:"Activity Type:"}),(0,a.jsx)("span",{className:"font-medium capitalize text-earth-900",children:e.activityTypes.replace("_"," ")})]}),e.sizeHectares&&(0,a.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,a.jsx)("span",{className:"text-earth-600",children:"Size:"}),(0,a.jsxs)("span",{className:"font-medium text-earth-900",children:[e.sizeHectares.toLocaleString()," hectares"]})]}),e.pricePerDay&&(0,a.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,a.jsx)("span",{className:"text-earth-600",children:"Price:"}),(0,a.jsxs)("span",{className:"font-medium text-earth-900",children:["R",e.pricePerDay.toLocaleString(),"/day"]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[(0,a.jsx)(v(),{href:"/farms/".concat(e.id),children:(0,a.jsxs)(c.$,{variant:"outline",size:"sm",className:"w-full flex items-center gap-1",children:[(0,a.jsx)(u.A,{className:"w-3 h-3"}),"View"]})}),(0,a.jsx)(v(),{href:"/farms/".concat(e.id,"/edit"),children:(0,a.jsxs)(c.$,{variant:"outline",size:"sm",className:"w-full flex items-center gap-1",children:[(0,a.jsx)(f.A,{className:"w-3 h-3"}),"Edit"]})})]}),(0,a.jsx)("div",{className:"mt-2",children:(0,a.jsx)(v(),{href:"/farms/".concat(e.id,"/analytics"),children:(0,a.jsxs)(c.$,{variant:"primary",size:"sm",className:"w-full flex items-center gap-1",children:[(0,a.jsx)(p.A,{className:"w-3 h-3"}),"Analytics"]})})}),(0,a.jsx)("div",{className:"mt-4 pt-4 border-t border-earth-200",children:(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center justify-center gap-1 text-earth-600 mb-1",children:[(0,a.jsx)(j.A,{className:"w-3 h-3"}),(0,a.jsx)("span",{className:"text-xs",children:"Bookings"})]}),(0,a.jsx)("p",{className:"text-sm font-semibold text-earth-900",children:"0"})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center justify-center gap-1 text-earth-600 mb-1",children:[(0,a.jsx)(N.A,{className:"w-3 h-3"}),(0,a.jsx)("span",{className:"text-xs",children:"Rating"})]}),(0,a.jsx)("p",{className:"text-sm font-semibold text-earth-900",children:"N/A"})]})]})})]})]})},e.id))}),y.length>0&&(0,a.jsxs)("div",{className:"mt-8 grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,a.jsx)(n.Zp,{className:"p-6",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-2xl font-bold text-earth-900",children:y.length}),(0,a.jsx)("p",{className:"text-earth-600 text-sm",children:"Total Farms"})]})}),(0,a.jsx)(n.Zp,{className:"p-6",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-2xl font-bold text-earth-900",children:y.filter(e=>e.isActive).length}),(0,a.jsx)("p",{className:"text-earth-600 text-sm",children:"Active Listings"})]})}),(0,a.jsx)(n.Zp,{className:"p-6",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-2xl font-bold text-earth-900",children:y.filter(e=>e.featured).length}),(0,a.jsx)("p",{className:"text-earth-600 text-sm",children:"Featured Farms"})]})})]})]})}):null}},61897:(e,s,t)=>{Promise.resolve().then(t.bind(t,45013))},66766:(e,s,t)=>{"use strict";t.d(s,{default:()=>r.a});var a=t(71469),r=t.n(a)},71469:(e,s,t)=>{"use strict";Object.defineProperty(s,"__esModule",{value:!0}),!function(e,s){for(var t in s)Object.defineProperty(e,t,{enumerable:!0,get:s[t]})}(s,{default:function(){return n},getImageProps:function(){return c}});let a=t(88229),r=t(38883),l=t(33063),i=a._(t(51193));function c(e){let{props:s}=(0,r.getImgProps)(e,{defaultLoader:i.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,t]of Object.entries(s))void 0===t&&delete s[e];return{props:s}}let n=l.Image},72713:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},74338:(e,s,t)=>{"use strict";t.d(s,{k:()=>l});var a=t(95155),r=t(59434);function l(e){let{size:s="md",className:t}=e;return(0,a.jsx)("div",{className:(0,r.cn)("flex items-center justify-center",t),children:(0,a.jsx)("div",{className:(0,r.cn)("animate-spin rounded-full border-2 border-white border-t-transparent",{sm:"w-4 h-4",md:"w-6 h-6",lg:"w-12 h-12"}[s])})})}},84616:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},92657:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])}},e=>{var s=s=>e(e.s=s);e.O(0,[992,965,277,288,63,874,228,441,684,358],()=>s(61897)),_N_E=e.O()}]);