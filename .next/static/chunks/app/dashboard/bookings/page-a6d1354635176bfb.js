(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[274],{4516:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(19946).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},14186:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(19946).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},19946:(e,t,r)=>{"use strict";r.d(t,{A:()=>m});var s=r(12115);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),n=e=>{let t=l(e);return t.charAt(0).toUpperCase()+t.slice(1)},i=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()},c=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var o={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,s.forwardRef)((e,t)=>{let{color:r="currentColor",size:a=24,strokeWidth:l=2,absoluteStrokeWidth:n,className:d="",children:m,iconNode:u,...h}=e;return(0,s.createElement)("svg",{ref:t,...o,width:a,height:a,stroke:r,strokeWidth:n?24*Number(l)/Number(a):l,className:i("lucide",d),...!m&&!c(h)&&{"aria-hidden":"true"},...h},[...u.map(e=>{let[t,r]=e;return(0,s.createElement)(t,r)}),...Array.isArray(m)?m:[m]])}),m=(e,t)=>{let r=(0,s.forwardRef)((r,l)=>{let{className:c,...o}=r;return(0,s.createElement)(d,{ref:l,iconNode:t,className:i("lucide-".concat(a(n(e))),"lucide-".concat(e),c),...o})});return r.displayName=n(e),r}},33482:(e,t,r)=>{Promise.resolve().then(r.bind(r,50266))},35169:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(19946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},35695:(e,t,r)=>{"use strict";var s=r(18999);r.o(s,"useParams")&&r.d(t,{useParams:function(){return s.useParams}}),r.o(s,"useRouter")&&r.d(t,{useRouter:function(){return s.useRouter}}),r.o(s,"useSearchParams")&&r.d(t,{useSearchParams:function(){return s.useSearchParams}})},40646:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(19946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},50266:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>w});var s=r(95155),a=r(12115),l=r(35695),n=r(84105),i=r(13741),c=r(17703),o=r(74338),d=r(28666),m=r(40646),u=r(19946);let h=(0,u.A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]),x=(0,u.A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]);var f=r(14186),p=r(35169),g=r(69074),y=r(4516),j=r(6874),v=r.n(j);function w(){let{user:e,userProfile:t,loading:r}=(0,n.As)(),u=(0,l.useRouter)(),[j,w]=(0,a.useState)([]),[N,b]=(0,a.useState)(!0),[k,A]=(0,a.useState)(null);(0,a.useEffect)(()=>{let e=async()=>{if(null==t?void 0:t.id)try{b(!0),A(null);let e=[];if("farm_owner"===t.role){let r=(await d.eg.getByOwner(t.id)).map(e=>e.id);if(r.length>0)for(let t of r)try{let r=await d.zi.getAll({farmId:t});e.push(...r)}catch(e){console.error("Error fetching bookings for farm ".concat(t,":"),e)}}else e=await d.zi.getAll({hunterId:t.id});let r=await Promise.all(e.map(async e=>{try{let t=await d.eg.get(e.farmId);return{...e,farm:t||void 0}}catch(t){return console.error("Error fetching farm ".concat(e.farmId,":"),t),e}}));w(r)}catch(e){console.error("Error fetching bookings:",e),A("Failed to load bookings")}finally{b(!1)}};!r&&t&&e()},[r,t]),(0,a.useEffect)(()=>{r||e||u.push("/auth/login")},[r,e,u]);let z=e=>{switch(e){case"confirmed":return(0,s.jsx)(m.A,{className:"w-5 h-5 text-green-600"});case"pending":return(0,s.jsx)(h,{className:"w-5 h-5 text-yellow-600"});case"cancelled":return(0,s.jsx)(x,{className:"w-5 h-5 text-red-600"});case"completed":return(0,s.jsx)(m.A,{className:"w-5 h-5 text-blue-600"});default:return(0,s.jsx)(f.A,{className:"w-5 h-5 text-gray-600"})}},_=e=>{switch(e){case"confirmed":return"bg-green-100 text-green-800";case"pending":return"bg-yellow-100 text-yellow-800";case"cancelled":return"bg-red-100 text-red-800";case"completed":return"bg-blue-100 text-blue-800";default:return"bg-gray-100 text-gray-800"}};return r||N?(0,s.jsx)("div",{className:"min-h-screen bg-earth-100 flex items-center justify-center",children:(0,s.jsx)(o.k,{size:"lg"})}):e?(0,s.jsx)("div",{className:"min-h-screen bg-earth-100 py-8",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsx)("div",{className:"flex items-center gap-4 mb-4",children:(0,s.jsx)(v(),{href:"/dashboard",children:(0,s.jsxs)(i.$,{variant:"outline",size:"sm",children:[(0,s.jsx)(p.A,{className:"w-4 h-4 mr-2"}),"Back to Dashboard"]})})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-earth-900",children:(null==t?void 0:t.role)==="farm_owner"?"Booking Requests":"My Bookings"}),(0,s.jsx)("p",{className:"text-earth-600 mt-2",children:(null==t?void 0:t.role)==="farm_owner"?"Manage booking requests for your farms":"View and manage your safari bookings"})]})]}),k&&(0,s.jsx)("div",{className:"mb-6 bg-red-50 border border-red-200 rounded-md p-4",children:(0,s.jsx)("p",{className:"text-red-600",children:k})}),0===j.length?(0,s.jsx)(c.Zp,{className:"p-8",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)(g.A,{className:"w-16 h-16 text-earth-400 mx-auto mb-4"}),(0,s.jsx)("h3",{className:"text-xl font-semibold text-earth-900 mb-2",children:(null==t?void 0:t.role)==="farm_owner"?"No booking requests yet":"No bookings yet"}),(0,s.jsx)("p",{className:"text-earth-600 mb-6",children:(null==t?void 0:t.role)==="farm_owner"?"When guests book your farms, their requests will appear here.":"Start exploring our amazing farms and book your next safari adventure."}),(null==t?void 0:t.role)!=="farm_owner"&&(0,s.jsx)(v(),{href:"/farms",children:(0,s.jsx)(i.$,{variant:"primary",children:"Browse Farms"})})]})}):(0,s.jsx)("div",{className:"space-y-6",children:j.map(e=>{var r,a;return(0,s.jsx)(c.Zp,{children:(0,s.jsx)(c.Wu,{className:"p-6",children:(0,s.jsxs)("div",{className:"flex items-start justify-between",children:[(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3 mb-3",children:[z(e.status),(0,s.jsx)("h3",{className:"text-lg font-semibold text-earth-900",children:(null==(r=e.farm)?void 0:r.name)||"Unknown Farm"}),(0,s.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-medium rounded-full ".concat(_(e.status)),children:e.status.charAt(0).toUpperCase()+e.status.slice(1)})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm text-earth-600",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(g.A,{className:"w-4 h-4"}),(0,s.jsxs)("span",{children:[new Date(e.startDate).toLocaleDateString()," - ",new Date(e.endDate).toLocaleDateString()]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(f.A,{className:"w-4 h-4"}),(0,s.jsxs)("span",{children:[e.durationDays," days"]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(y.A,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:(null==(a=e.farm)?void 0:a.location)||"Unknown location"})]}),(0,s.jsx)("div",{children:(0,s.jsxs)("span",{className:"font-medium",children:[e.groupSize," ",1===e.groupSize?"guest":"guests"]})})]}),(0,s.jsxs)("div",{className:"mt-3 flex items-center gap-4",children:[(0,s.jsxs)("span",{className:"text-sm text-earth-600",children:["Activity: ",(0,s.jsx)("span",{className:"font-medium capitalize",children:e.activityType.replace("_"," ")})]}),(0,s.jsxs)("span",{className:"text-sm text-earth-600",children:["Booking ID: ",(0,s.jsx)("span",{className:"font-mono",children:e.bookingReference})]})]}),e.specialRequests&&(0,s.jsx)("div",{className:"mt-3",children:(0,s.jsxs)("p",{className:"text-sm text-earth-600",children:[(0,s.jsx)("span",{className:"font-medium",children:"Special Requests:"})," ",e.specialRequests]})})]}),(0,s.jsxs)("div",{className:"text-right ml-6",children:[e.totalPrice&&(0,s.jsxs)("p",{className:"text-xl font-bold text-earth-900 mb-2",children:["R",e.totalPrice.toLocaleString()]}),(0,s.jsxs)("p",{className:"text-sm text-earth-600 mb-3",children:["Booked on ",e.createdAt.toDate().toLocaleDateString()]}),(null==t?void 0:t.role)==="farm_owner"&&"pending"===e.status&&(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(i.$,{variant:"primary",size:"sm",className:"w-full",children:"Accept"}),(0,s.jsx)(i.$,{variant:"outline",size:"sm",className:"w-full",children:"Decline"})]}),e.farm&&(0,s.jsx)(v(),{href:"/farms/".concat(e.farm.id),children:(0,s.jsx)(i.$,{variant:"outline",size:"sm",className:"w-full mt-2",children:"View Farm"})})]})]})})},e.id)})})]})}):null}},66766:(e,t,r)=>{"use strict";r.d(t,{default:()=>a.a});var s=r(71469),a=r.n(s)},69074:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(19946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},71469:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return c},getImageProps:function(){return i}});let s=r(88229),a=r(38883),l=r(33063),n=s._(r(51193));function i(e){let{props:t}=(0,a.getImgProps)(e,{defaultLoader:n.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,r]of Object.entries(t))void 0===r&&delete t[e];return{props:t}}let c=l.Image},74338:(e,t,r)=>{"use strict";r.d(t,{k:()=>l});var s=r(95155),a=r(59434);function l(e){let{size:t="md",className:r}=e;return(0,s.jsx)("div",{className:(0,a.cn)("flex items-center justify-center",r),children:(0,s.jsx)("div",{className:(0,a.cn)("animate-spin rounded-full border-2 border-white border-t-transparent",{sm:"w-4 h-4",md:"w-6 h-6",lg:"w-12 h-12"}[t])})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[992,965,277,288,63,874,228,441,684,358],()=>t(33482)),_N_E=e.O()}]);