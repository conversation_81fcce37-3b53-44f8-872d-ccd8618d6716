(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[105,228],{4516:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},13717:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},13741:(e,t,s)=>{"use strict";s.d(t,{$:()=>i});var a=s(95155),r=s(12115),l=s(59434);let i=(0,r.forwardRef)((e,t)=>{let{className:s,variant:r="primary",size:i="md",isLoading:n,children:c,disabled:d,...o}=e;return(0,a.jsxs)("button",{className:(0,l.cn)("\n      inline-flex items-center justify-center rounded-[var(--radius-md)] \n      font-[var(--font-ui)] font-semibold transition-all duration-300 \n      focus:outline-none focus:ring-2 focus:ring-offset-2\n      disabled:opacity-50 disabled:cursor-not-allowed\n      hover:transform hover:-translate-y-0.5 hover:shadow-[var(--shadow-lg)]\n    ",{primary:"\n        bg-[var(--primary-brown)] text-white \n        hover:bg-[#A0522D] focus:ring-[var(--primary-brown)]\n      ",secondary:"\n        bg-[var(--secondary-sky)] text-white \n        hover:bg-[#357AE0] focus:ring-[var(--secondary-sky)]\n      ",outline:"\n        bg-transparent border-2 border-[var(--primary-brown)] text-[var(--primary-brown)]\n        hover:bg-[var(--primary-brown)] hover:text-white focus:ring-[var(--primary-brown)]\n      ",hunting:"\n        bg-[var(--hunting-accent)] text-white \n        hover:bg-[#A67C00] focus:ring-[var(--hunting-accent)]\n      ",photo:"\n        bg-[var(--photo-accent)] text-white \n        hover:bg-[#3557C7] focus:ring-[var(--photo-accent)]\n      "}[r],{sm:"px-3 py-1.5 text-sm",md:"px-6 py-2 text-base",lg:"px-8 py-3 text-lg"}[i],n&&"cursor-wait",s),disabled:d||n,ref:t,...o,children:[n&&(0,a.jsxs)("svg",{className:"mr-2 h-4 w-4 animate-spin",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),c]})});i.displayName="Button"},14186:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},17580:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},17703:(e,t,s)=>{"use strict";s.d(t,{MH:()=>m,Wu:()=>c,ZB:()=>o,Zp:()=>n,aR:()=>d});var a=s(95155),r=s(12115),l=s(66766),i=s(59434);let n=(0,r.forwardRef)((e,t)=>{let{className:s,hover:r=!0,children:l,...n}=e;return(0,a.jsx)("div",{ref:t,className:(0,i.cn)("\n          bg-white rounded-[var(--radius-lg)] shadow-[var(--shadow-md)] \n          overflow-hidden transition-all duration-300\n          ",r&&"hover:transform hover:-translate-y-1 hover:shadow-[var(--shadow-xl)]",s),...n,children:l})});n.displayName="Card";let c=(0,r.forwardRef)((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,i.cn)("p-[var(--space-lg)]",s),...r})});c.displayName="CardContent";let d=(0,r.forwardRef)((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,i.cn)("px-[var(--space-lg)] pt-[var(--space-lg)]",s),...r})});d.displayName="CardHeader";let o=(0,r.forwardRef)((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("h3",{ref:t,className:(0,i.cn)("text-xl font-bold mb-2 text-[var(--primary-brown)] font-[var(--font-display)]",s),...r})});o.displayName="CardTitle";let m=(0,r.forwardRef)((e,t)=>{let{className:s,src:r,alt:n,children:c,...d}=e;return(0,a.jsx)("div",{ref:t,className:(0,i.cn)("relative w-full h-48 bg-gradient-to-br from-[var(--primary-green)] to-[var(--secondary-sunset)] flex items-center justify-center text-white text-5xl",s),...d,children:r?(0,a.jsx)(l.default,{src:r,alt:n||"",fill:!0,className:"object-cover"}):c})});m.displayName="CardImage"},19946:(e,t,s)=>{"use strict";s.d(t,{A:()=>m});var a=s(12115);let r=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,s)=>s?s.toUpperCase():t.toLowerCase()),i=e=>{let t=l(e);return t.charAt(0).toUpperCase()+t.slice(1)},n=function(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return t.filter((e,t,s)=>!!e&&""!==e.trim()&&s.indexOf(e)===t).join(" ").trim()},c=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let o=(0,a.forwardRef)((e,t)=>{let{color:s="currentColor",size:r=24,strokeWidth:l=2,absoluteStrokeWidth:i,className:o="",children:m,iconNode:h,...u}=e;return(0,a.createElement)("svg",{ref:t,...d,width:r,height:r,stroke:s,strokeWidth:i?24*Number(l)/Number(r):l,className:n("lucide",o),...!m&&!c(u)&&{"aria-hidden":"true"},...u},[...h.map(e=>{let[t,s]=e;return(0,a.createElement)(t,s)}),...Array.isArray(m)?m:[m]])}),m=(e,t)=>{let s=(0,a.forwardRef)((s,l)=>{let{className:c,...d}=s;return(0,a.createElement)(o,{ref:l,iconNode:t,className:n("lucide-".concat(r(i(e))),"lucide-".concat(e),c),...d})});return s.displayName=i(e),s}},22658:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>E});var a=s(95155),r=s(84105),l=s(12115),i=s(13741),n=s(17703),c=s(74338),d=s(28666),o=s(84616),m=s(4516),h=s(69074),u=s(17580),x=s(55868),f=s(92657),p=s(13717),g=s(72713),j=s(19946);let y=(0,j.A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]);var v=s(38564),w=s(6874),N=s.n(w);function b(){let{userProfile:e}=(0,r.As)(),[t,s]=(0,l.useState)([]),[j,w]=(0,l.useState)([]),[b,A]=(0,l.useState)(!0),[k,D]=(0,l.useState)(null),[S,M]=(0,l.useState)(null);(0,l.useEffect)(()=>{(async()=>{if(null==e?void 0:e.id)try{A(!0),D(null);let t=await d.eg.getByOwner(e.id);s(t);let a=t.map(e=>e.id);if(a.length>0){let e=[];for(let t of a)try{let s=await d.zi.getAll({farmId:t,limit:50});e.push(...s)}catch(e){console.error("Error fetching bookings for farm ".concat(t,":"),e)}w(e)}}catch(e){console.error("Error fetching dashboard data:",e),D("Failed to load dashboard data")}finally{A(!1)}})()},[null==e?void 0:e.id]);let P=async(e,t)=>{if(confirm('Are you sure you want to delete "'.concat(t,'"? This action cannot be undone.')))try{M(e),await d.eg.delete(e),s(t=>t.filter(t=>t.id!==e)),w(t=>t.filter(t=>t.farmId!==e))}catch(e){console.error("Error deleting farm:",e),D("Failed to delete farm. Please try again.")}finally{M(null)}};if(b)return(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsx)(c.k,{size:"lg"})});if(k)return(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-red-600 mb-4",children:k}),(0,a.jsx)(i.$,{onClick:()=>window.location.reload(),children:"Try Again"})]})});let T=j.length,C=j.filter(e=>"pending"===e.status).length,R=j.filter(e=>"confirmed"===e.status).reduce((e,t)=>e+(t.totalPrice||0),0);return(0,a.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,a.jsx)("div",{className:"mb-8",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h1",{className:"text-3xl font-bold text-earth-900",children:["Welcome back, ",(null==e?void 0:e.firstName)||"Farm Owner","!"]}),(0,a.jsx)("p",{className:"text-earth-600 mt-2",children:"Manage your farms and bookings from your dashboard"})]}),(0,a.jsx)(N(),{href:"/farms/create",children:(0,a.jsxs)(i.$,{variant:"primary",className:"flex items-center gap-2",children:[(0,a.jsx)(o.A,{className:"w-4 h-4"}),"Add New Farm"]})})]})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[(0,a.jsx)(n.Zp,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-earth-600 text-sm font-medium",children:"Total Farms"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-earth-900",children:t.length})]}),(0,a.jsx)(m.A,{className:"w-8 h-8 text-accent-600"})]})}),(0,a.jsx)(n.Zp,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-earth-600 text-sm font-medium",children:"Total Bookings"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-earth-900",children:T})]}),(0,a.jsx)(h.A,{className:"w-8 h-8 text-accent-600"})]})}),(0,a.jsx)(n.Zp,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-earth-600 text-sm font-medium",children:"Pending Requests"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-earth-900",children:C})]}),(0,a.jsx)(u.A,{className:"w-8 h-8 text-accent-600"})]})}),(0,a.jsx)(n.Zp,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-earth-600 text-sm font-medium",children:"Total Revenue"}),(0,a.jsxs)("p",{className:"text-2xl font-bold text-earth-900",children:["R",R.toLocaleString()]})]}),(0,a.jsx)(x.A,{className:"w-8 h-8 text-accent-600"})]})})]}),(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsx)("h2",{className:"text-2xl font-semibold text-earth-900",children:"My Farms"}),(0,a.jsx)(N(),{href:"/farms/create",children:(0,a.jsxs)(i.$,{variant:"primary",className:"flex items-center gap-2",children:[(0,a.jsx)(o.A,{className:"w-4 h-4"}),"Add New Farm"]})})]}),0===t.length?(0,a.jsx)(n.Zp,{className:"p-8",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(m.A,{className:"w-16 h-16 text-earth-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-earth-900 mb-2",children:"No farms yet"}),(0,a.jsx)("p",{className:"text-earth-600 mb-6",children:"Create your first farm listing to start receiving bookings from safari enthusiasts."}),(0,a.jsx)(N(),{href:"/farms/create",children:(0,a.jsxs)(i.$,{variant:"primary",className:"flex items-center gap-2",children:[(0,a.jsx)(o.A,{className:"w-4 h-4"}),"Create Your First Farm"]})})]})}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:t.slice(0,9).map(e=>(0,a.jsx)(n.Zp,{className:"overflow-hidden",children:(0,a.jsxs)("div",{className:"p-0",children:[(0,a.jsx)("div",{className:"h-48 bg-gradient-to-br from-earth-200 to-earth-300 flex items-center justify-center",children:(0,a.jsx)(m.A,{className:"w-12 h-12 text-earth-600"})}),(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between mb-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-earth-900 mb-1",children:e.name}),(0,a.jsxs)("p",{className:"text-sm text-earth-600 flex items-center gap-1",children:[(0,a.jsx)(m.A,{className:"w-3 h-3"}),e.location,", ",e.province]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)("div",{className:"w-2 h-2 rounded-full ".concat(e.isActive?"bg-green-500":"bg-red-500")}),(0,a.jsx)("span",{className:"text-xs text-earth-600",children:e.isActive?"Active":"Inactive"})]})]}),(0,a.jsxs)("div",{className:"space-y-2 mb-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,a.jsx)("span",{className:"text-earth-600",children:"Activity Type:"}),(0,a.jsx)("span",{className:"font-medium capitalize text-earth-900",children:e.activityTypes.replace("_"," ")})]}),e.sizeHectares&&(0,a.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,a.jsx)("span",{className:"text-earth-600",children:"Size:"}),(0,a.jsxs)("span",{className:"font-medium text-earth-900",children:[e.sizeHectares.toLocaleString()," hectares"]})]}),e.pricePerDay&&(0,a.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,a.jsx)("span",{className:"text-earth-600",children:"Price:"}),(0,a.jsxs)("span",{className:"font-medium text-earth-900",children:["R",e.pricePerDay.toLocaleString(),"/day"]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-2 mb-2",children:[(0,a.jsx)(N(),{href:"/farms/".concat(e.id),children:(0,a.jsxs)(i.$,{variant:"outline",size:"sm",className:"w-full flex items-center gap-1",children:[(0,a.jsx)(f.A,{className:"w-3 h-3"}),"View"]})}),(0,a.jsx)(N(),{href:"/farms/".concat(e.id,"/edit"),children:(0,a.jsxs)(i.$,{variant:"outline",size:"sm",className:"w-full flex items-center gap-1",children:[(0,a.jsx)(p.A,{className:"w-3 h-3"}),"Edit"]})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[(0,a.jsx)(N(),{href:"/farms/".concat(e.id,"/analytics"),children:(0,a.jsxs)(i.$,{variant:"primary",size:"sm",className:"w-full flex items-center gap-1",children:[(0,a.jsx)(g.A,{className:"w-3 h-3"}),"Analytics"]})}),(0,a.jsxs)(i.$,{variant:"outline",size:"sm",className:"w-full flex items-center gap-1 text-red-600 hover:text-red-700 hover:bg-red-50",onClick:()=>P(e.id,e.name),disabled:S===e.id,children:[(0,a.jsx)(y,{className:"w-3 h-3"}),S===e.id?"Deleting...":"Delete"]})]}),(0,a.jsx)("div",{className:"mt-4 pt-4 border-t border-earth-200",children:(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center justify-center gap-1 text-earth-600 mb-1",children:[(0,a.jsx)(u.A,{className:"w-3 h-3"}),(0,a.jsx)("span",{className:"text-xs",children:"Bookings"})]}),(0,a.jsx)("p",{className:"text-sm font-semibold text-earth-900",children:j.filter(t=>t.farmId===e.id).length})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center justify-center gap-1 text-earth-600 mb-1",children:[(0,a.jsx)(v.A,{className:"w-3 h-3"}),(0,a.jsx)("span",{className:"text-xs",children:"Rating"})]}),(0,a.jsx)("p",{className:"text-sm font-semibold text-earth-900",children:"N/A"})]})]})})]})]})},e.id))}),t.length>9&&(0,a.jsx)("div",{className:"text-center mt-6",children:(0,a.jsx)(N(),{href:"/dashboard/farms",children:(0,a.jsxs)(i.$,{variant:"outline",children:["View All ",t.length," Farms"]})})})]})]}),(0,a.jsx)("div",{children:(0,a.jsxs)(n.Zp,{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-earth-900",children:"Recent Booking Requests"}),(0,a.jsx)(N(),{href:"/dashboard/bookings",children:(0,a.jsx)(i.$,{variant:"outline",size:"sm",children:"View All"})})]}),0===j.length?(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)(h.A,{className:"w-12 h-12 text-earth-400 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-earth-600",children:"No booking requests yet"})]}):(0,a.jsx)("div",{className:"space-y-4",children:j.slice(0,3).map(e=>{var s;let r=t.find(t=>t.id===e.farmId);return(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 bg-earth-50 rounded-lg",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium text-earth-900",children:(null==r?void 0:r.name)||"Unknown Farm"}),(0,a.jsxs)("p",{className:"text-sm text-earth-600",children:[new Date(e.startDate).toLocaleDateString()," - ",new Date(e.endDate).toLocaleDateString()]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-medium rounded-full ".concat("pending"===e.status?"bg-yellow-100 text-yellow-800":"confirmed"===e.status?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:e.status}),(0,a.jsxs)("p",{className:"text-sm font-medium text-earth-900 mt-1",children:["R",null==(s=e.totalPrice)?void 0:s.toLocaleString()]})]})]},e.id)})})]})})]})}let A=(0,j.A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]]);var k=s(14186),D=s(40646);let S=(0,j.A)("heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]]);function M(){let{userProfile:e}=(0,r.As)(),[t,s]=(0,l.useState)([]),[o,u]=(0,l.useState)([]),[x,f]=(0,l.useState)(!0),[p,g]=(0,l.useState)(null);if((0,l.useEffect)(()=>{(async()=>{if(null==e?void 0:e.id)try{f(!0),g(null);let t=await d.zi.getAll({hunterId:e.id,limit:10});s(t);let a=await d.eg.getActive(3);u(a)}catch(e){console.error("Error fetching dashboard data:",e),g("Failed to load dashboard data")}finally{f(!1)}})()},[null==e?void 0:e.id]),x)return(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsx)(c.k,{size:"lg"})});if(p)return(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-red-600 mb-4",children:p}),(0,a.jsx)(i.$,{onClick:()=>window.location.reload(),children:"Try Again"})]})});let j=t.length,y=t.filter(e=>new Date(e.startDate)>new Date&&"confirmed"===e.status).length,v=t.filter(e=>new Date(e.endDate)<new Date&&"confirmed"===e.status).length;return(0,a.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,a.jsx)("div",{className:"mb-8",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h1",{className:"text-3xl font-bold text-earth-900",children:["Welcome back, ",(null==e?void 0:e.firstName)||"Guest","!"]}),(0,a.jsx)("p",{className:"text-earth-600 mt-2",children:"Ready for your next safari adventure?"})]}),(0,a.jsx)(N(),{href:"/farms",children:(0,a.jsxs)(i.$,{variant:"primary",className:"flex items-center gap-2",children:[(0,a.jsx)(A,{className:"w-4 h-4"}),"Browse Farms"]})})]})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[(0,a.jsx)(n.Zp,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-earth-600 text-sm font-medium",children:"Total Bookings"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-earth-900",children:j})]}),(0,a.jsx)(h.A,{className:"w-8 h-8 text-accent-600"})]})}),(0,a.jsx)(n.Zp,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-earth-600 text-sm font-medium",children:"Upcoming Trips"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-earth-900",children:y})]}),(0,a.jsx)(k.A,{className:"w-8 h-8 text-accent-600"})]})}),(0,a.jsx)(n.Zp,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-earth-600 text-sm font-medium",children:"Completed Trips"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-earth-900",children:v})]}),(0,a.jsx)(D.A,{className:"w-8 h-8 text-accent-600"})]})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,a.jsxs)(n.Zp,{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-earth-900",children:"My Bookings"}),(0,a.jsx)(N(),{href:"/dashboard/bookings",children:(0,a.jsx)(i.$,{variant:"outline",size:"sm",children:"View All"})})]}),0===t.length?(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)(h.A,{className:"w-12 h-12 text-earth-400 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-earth-600 mb-4",children:"You haven't made any bookings yet"}),(0,a.jsx)(N(),{href:"/farms",children:(0,a.jsx)(i.$,{variant:"primary",children:"Browse Farms"})})]}):(0,a.jsxs)("div",{className:"space-y-4",children:[t.slice(0,3).map(e=>{var t;return(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 bg-earth-50 rounded-lg",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"font-medium text-earth-900",children:["Booking #",e.id.slice(-6)]}),(0,a.jsxs)("p",{className:"text-sm text-earth-600",children:[new Date(e.startDate).toLocaleDateString()," - ",new Date(e.endDate).toLocaleDateString()]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-medium rounded-full ".concat("pending"===e.status?"bg-yellow-100 text-yellow-800":"confirmed"===e.status?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:e.status}),(0,a.jsxs)("p",{className:"text-sm font-medium text-earth-900 mt-1",children:["R",null==(t=e.totalPrice)?void 0:t.toLocaleString()]})]})]},e.id)}),t.length>3&&(0,a.jsx)("div",{className:"text-center pt-4",children:(0,a.jsx)(N(),{href:"/dashboard/bookings",children:(0,a.jsx)(i.$,{variant:"outline",children:"View All Bookings"})})})]})]}),(0,a.jsxs)(n.Zp,{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-earth-900",children:"Recommended Farms"}),(0,a.jsx)(N(),{href:"/farms",children:(0,a.jsx)(i.$,{variant:"outline",size:"sm",children:"Browse All"})})]}),0===o.length?(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)(S,{className:"w-12 h-12 text-earth-400 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-earth-600 mb-4",children:"Discover amazing safari destinations"}),(0,a.jsx)(N(),{href:"/farms",children:(0,a.jsx)(i.$,{variant:"primary",children:"Explore Farms"})})]}):(0,a.jsx)("div",{className:"space-y-4",children:o.map(e=>{var t;return(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 bg-earth-50 rounded-lg",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium text-earth-900",children:e.name}),(0,a.jsxs)("p",{className:"text-sm text-earth-600 flex items-center gap-1",children:[(0,a.jsx)(m.A,{className:"w-3 h-3"}),e.location]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsxs)("p",{className:"text-sm font-medium text-earth-900",children:["R",null==(t=e.pricePerDay)?void 0:t.toLocaleString(),"/day"]}),(0,a.jsx)(N(),{href:"/farms/".concat(e.id),children:(0,a.jsx)(i.$,{variant:"outline",size:"sm",children:"View Details"})})]})]},e.id)})})]})]}),(0,a.jsxs)(n.Zp,{className:"p-6 mt-8",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-earth-900 mb-6",children:"Quick Actions"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsx)(N(),{href:"/farms",children:(0,a.jsxs)(i.$,{variant:"outline",className:"w-full flex items-center gap-2",children:[(0,a.jsx)(A,{className:"w-4 h-4"}),"Browse Farms"]})}),(0,a.jsx)(N(),{href:"/profile",children:(0,a.jsxs)(i.$,{variant:"outline",className:"w-full flex items-center gap-2",children:[(0,a.jsx)(h.A,{className:"w-4 h-4"}),"Manage Profile"]})}),(0,a.jsx)(N(),{href:"/dashboard/bookings",children:(0,a.jsxs)(i.$,{variant:"outline",className:"w-full flex items-center gap-2",children:[(0,a.jsx)(k.A,{className:"w-4 h-4"}),"View Bookings"]})})]})]})]})}var P=s(28214);async function T(){let e=P.j2.currentUser;if(!e)return console.log("\uD83D\uDD0D Auth Debug: No user logged in"),null;try{let t=await e.getIdTokenResult(!0);return console.log("\uD83D\uDD0D Auth Debug Information:"),console.log("  User UID:",e.uid),console.log("  Email:",e.email),console.log("  Display Name:",e.displayName),console.log("  Email Verified:",e.emailVerified),console.log("  Custom Claims:",t.claims),console.log("  Role from Claims:",t.claims.role||"NO ROLE SET"),console.log("  Token Expiration:",new Date(t.expirationTime)),console.log("  Auth Time:",new Date(t.authTime)),console.log("  Issued At:",new Date(t.issuedAtTime)),t.claims.role||(console.warn("⚠️  WARNING: No role set in custom claims!"),console.log("   This will cause Firestore permission errors."),console.log("   The user may need to re-register or have their role set manually.")),{uid:e.uid,email:e.email,role:t.claims.role||"NO ROLE",claims:t.claims,hasRole:!!t.claims.role}}catch(e){return console.error("\uD83D\uDD0D Auth Debug Error:",e),null}}async function C(){let e=P.j2.currentUser;if(!e)return console.log("\uD83D\uDD04 Token Refresh: No user logged in"),!1;try{return console.log("\uD83D\uDD04 Refreshing user token..."),await e.getIdToken(!0),console.log("✅ Token refreshed successfully"),!0}catch(e){return console.error("❌ Token refresh failed:",e),!1}}function R(){let[e,t]=(0,l.useState)(null),[s,c]=(0,l.useState)(!1),[d,o]=(0,l.useState)(!1),{user:m}=(0,r.As)(),h=async()=>{c(!0);try{let e=await T();t(e)}catch(e){console.error("Debug failed:",e)}finally{c(!1)}},u=async()=>{o(!0);try{await C();let e=await T();t(e)}catch(e){console.error("Token refresh failed:",e)}finally{o(!1)}},x=async()=>{if(m)try{let e=await m.getIdToken(),t=await fetch("/api/auth/set-role",{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(e)},body:JSON.stringify({uid:m.uid,role:"guest"})});if(t.ok)console.log("Role set successfully"),await u();else{let e=await t.json();console.error("Failed to set role:",e.error)}}catch(e){console.error("Error setting role:",e)}};return m?(0,a.jsxs)(n.Zp,{className:"p-4 mb-4 bg-yellow-50 border-yellow-200",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-yellow-800 mb-3",children:"\uD83D\uDD0D Authentication Debug Panel"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(i.$,{onClick:h,variant:"outline",size:"sm",isLoading:s,children:"Check Auth State"}),(0,a.jsx)(i.$,{onClick:u,variant:"outline",size:"sm",isLoading:d,children:"Refresh Token"}),(0,a.jsx)(i.$,{onClick:x,variant:"outline",size:"sm",children:"Set Role (Guest)"})]}),e&&(0,a.jsxs)("div",{className:"bg-white p-3 rounded border text-sm",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{children:"UID:"})," ",e.uid]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{children:"Email:"})," ",e.email]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{children:"Role:"})," ",e.role||"NOT SET"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{children:"Has Role:"})," ",e.hasRole?"✅":"❌"]})]}),!e.hasRole&&(0,a.jsx)("div",{className:"mt-2 p-2 bg-red-50 border border-red-200 rounded",children:(0,a.jsx)("p",{className:"text-red-700 text-xs",children:'⚠️ No role set in custom claims! This will cause permission errors. Click "Set Role (Guest)" to fix this issue.'})}),(0,a.jsxs)("details",{className:"mt-2",children:[(0,a.jsx)("summary",{className:"cursor-pointer text-xs text-gray-600",children:"View All Claims"}),(0,a.jsx)("pre",{className:"text-xs bg-gray-100 p-2 rounded mt-1 overflow-auto",children:JSON.stringify(e.claims,null,2)})]})]})]})]}):null}function I(){let{userProfile:e,loading:t}=(0,r.As)();return((0,l.useEffect)(()=>{!t&&e&&T()},[t,e]),t||!e)?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsx)(c.k,{size:"lg"})}):(0,a.jsxs)("div",{children:[(0,a.jsx)(R,{}),"farm_owner"===e.role?(0,a.jsx)(b,{}):(0,a.jsx)(M,{})]})}var z=s(35695);function E(){let{loading:e,isAuthenticated:t}=(0,r.Nu)(),s=(0,z.useRouter)();return((0,l.useEffect)(()=>{e||t||s.push("/auth/login")},[e,t,s]),e)?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-earth-100",children:(0,a.jsx)(c.k,{size:"lg"})}):t?(0,a.jsx)("div",{className:"min-h-screen bg-earth-100",children:(0,a.jsx)(I,{})}):null}},26261:(e,t,s)=>{Promise.resolve().then(s.bind(s,22658))},28214:(e,t,s)=>{"use strict";s.d(t,{IG:()=>a.IG,db:()=>a.db,j2:()=>a.j2});var a=s(67039)},28666:(e,t,s)=>{"use strict";s.d(t,{C2:()=>d,QQ:()=>i,eg:()=>n,zi:()=>c});var a=s(35317),r=s(28214);function l(e){var t,s,a,r;if(!e.exists())return null;let l=e.data();return{id:e.id,...l,createdAt:(null==l||null==(s=l.createdAt)||null==(t=s.toDate)?void 0:t.call(s))||(null==l?void 0:l.createdAt),updatedAt:(null==l||null==(r=l.updatedAt)||null==(a=r.toDate)?void 0:a.call(r))||(null==l?void 0:l.updatedAt)}}let i={async get(e){let t=(0,a.H9)(r.db,"users",e);return l(await (0,a.x7)(t))},async create(e,t){let s=(0,a.H9)(r.db,"users",e),l=new Date;await (0,a.BN)(s,{...t,createdAt:a.Dc.fromDate(l),updatedAt:a.Dc.fromDate(l)})},async update(e,t){let s=(0,a.H9)(r.db,"users",e);await (0,a.mZ)(s,{...t,updatedAt:a.Dc.now()})}},n={async getAll(e){let t=(0,a.rJ)(r.db,"farms");return(null==e?void 0:e.isActive)!==void 0&&(t=(0,a.P)(t,(0,a._M)("isActive","==",e.isActive))),(null==e?void 0:e.featured)!==void 0&&(t=(0,a.P)(t,(0,a._M)("featured","==",e.featured))),(null==e?void 0:e.province)&&(t=(0,a.P)(t,(0,a._M)("province","==",e.province))),(null==e?void 0:e.activityType)&&(t=(0,a.P)(t,(0,a._M)("activityTypes","==",e.activityType))),t=(0,a.P)(t,(0,a.My)("createdAt","desc")),(null==e?void 0:e.limit)&&(t=(0,a.P)(t,(0,a.AB)(e.limit))),(await (0,a.GG)(t)).docs.map(e=>l(e)).filter(Boolean)},async get(e){let t=(0,a.H9)(r.db,"farms",e);return l(await (0,a.x7)(t))},async getByOwner(e){let t=(0,a.P)((0,a.rJ)(r.db,"farms"),(0,a._M)("ownerId","==",e),(0,a.My)("createdAt","desc"));return(await (0,a.GG)(t)).docs.map(e=>l(e)).filter(Boolean)},async getActive(e){let t=(0,a.P)((0,a.rJ)(r.db,"farms"),(0,a._M)("isActive","==",!0),(0,a.My)("createdAt","desc"));return e&&(t=(0,a.P)(t,(0,a.AB)(e))),(await (0,a.GG)(t)).docs.map(e=>l(e)).filter(Boolean)},async create(e){let t=new Date;return(await (0,a.gS)((0,a.rJ)(r.db,"farms"),{...e,createdAt:a.Dc.fromDate(t),updatedAt:a.Dc.fromDate(t)})).id},async update(e,t){let s=(0,a.H9)(r.db,"farms",e);await (0,a.mZ)(s,{...t,updatedAt:a.Dc.now()})},async delete(e){let t=(0,a.H9)(r.db,"farms",e);await (0,a.kd)(t)},async addImage(e,t){let s=new Date;return(await (0,a.gS)((0,a.rJ)(r.db,"farms",e,"images"),{...t,createdAt:a.Dc.fromDate(s),updatedAt:a.Dc.fromDate(s)})).id},async getImages(e){let t=(0,a.P)((0,a.rJ)(r.db,"farms",e,"images"),(0,a.My)("displayOrder","asc"),(0,a.My)("createdAt","asc"));return(await (0,a.GG)(t)).docs.map(e=>l(e)).filter(Boolean)},async deleteImage(e,t){let s=(0,a.H9)(r.db,"farms",e,"images",t);await (0,a.kd)(s)}},c={async getAll(e){let t=(0,a.rJ)(r.db,"bookings");return(null==e?void 0:e.hunterId)&&(t=(0,a.P)(t,(0,a._M)("hunterId","==",e.hunterId))),(null==e?void 0:e.farmId)&&(t=(0,a.P)(t,(0,a._M)("farmId","==",e.farmId))),(null==e?void 0:e.status)&&(t=(0,a.P)(t,(0,a._M)("status","==",e.status))),t=(0,a.P)(t,(0,a.My)("createdAt","desc")),(null==e?void 0:e.limit)&&(t=(0,a.P)(t,(0,a.AB)(e.limit))),(await (0,a.GG)(t)).docs.map(e=>l(e)).filter(Boolean)},async get(e){let t=(0,a.H9)(r.db,"bookings",e);return l(await (0,a.x7)(t))},async create(e){let t=new Date,s="BVR-".concat(t.getFullYear()).concat((t.getMonth()+1).toString().padStart(2,"0")).concat(t.getDate().toString().padStart(2,"0"),"-").concat(Math.random().toString(36).substring(2,8).toUpperCase());return(await (0,a.gS)((0,a.rJ)(r.db,"bookings"),{...e,bookingReference:s,createdAt:a.Dc.fromDate(t),updatedAt:a.Dc.fromDate(t)})).id},async update(e,t){let s=(0,a.H9)(r.db,"bookings",e);await (0,a.mZ)(s,{...t,updatedAt:a.Dc.now()})}},d={async getByFarm(e){let t=(0,a.P)((0,a.rJ)(r.db,"farms",e,"reviews"),(0,a._M)("isPublic","==",!0),(0,a.My)("createdAt","desc"));return(await (0,a.GG)(t)).docs.map(e=>l(e)).filter(Boolean)},async create(e,t){let s=new Date;return(await (0,a.gS)((0,a.rJ)(r.db,"farms",e,"reviews"),{...t,createdAt:a.Dc.fromDate(s),updatedAt:a.Dc.fromDate(s)})).id},async update(e,t,s){let l=(0,a.H9)(r.db,"farms",e,"reviews",t);await (0,a.mZ)(l,{...s,updatedAt:a.Dc.now()})}}},35695:(e,t,s)=>{"use strict";var a=s(18999);s.o(a,"useParams")&&s.d(t,{useParams:function(){return a.useParams}}),s.o(a,"useRouter")&&s.d(t,{useRouter:function(){return a.useRouter}}),s.o(a,"useSearchParams")&&s.d(t,{useSearchParams:function(){return a.useSearchParams}})},38564:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},40646:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},55868:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},59434:(e,t,s)=>{"use strict";s.d(t,{Y:()=>n,Z:()=>i,cn:()=>l});var a=s(52596),r=s(39688);function l(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,r.QP)((0,a.$)(t))}function i(e){return e.toString().replace(/\B(?=(\d{3})+(?!\d))/g," ")}function n(e){let t=new Date(e);return"".concat(t.getDate()," ").concat(["January","February","March","April","May","June","July","August","September","October","November","December"][t.getMonth()]," ").concat(t.getFullYear())}},66766:(e,t,s)=>{"use strict";s.d(t,{default:()=>r.a});var a=s(71469),r=s.n(a)},67039:(e,t,s)=>{"use strict";s.d(t,{IG:()=>o,db:()=>d,j2:()=>c});var a=s(23915),r=s(16203),l=s(35317),i=s(90858);let n=(0,a.Wp)({apiKey:"AIzaSyDdsTm8eifLrb1W0WLK0DwBk4p4XLFZKMw",authDomain:"rvbsafaris.firebaseapp.com",projectId:"rvbsafaris",storageBucket:"rvbsafaris.firebasestorage.app",messagingSenderId:"593781635754",appId:"1:593781635754:web:7a39a3b4451f34f85feed3"}),c=(0,r.xI)(n),d=(0,l.aU)(n),o=(0,i.c7)(n);console.log("Firebase Storage initialized with bucket:","rvbsafaris.firebasestorage.app")},69074:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},71469:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var s in t)Object.defineProperty(e,s,{enumerable:!0,get:t[s]})}(t,{default:function(){return c},getImageProps:function(){return n}});let a=s(88229),r=s(38883),l=s(33063),i=a._(s(51193));function n(e){let{props:t}=(0,r.getImgProps)(e,{defaultLoader:i.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,s]of Object.entries(t))void 0===s&&delete t[e];return{props:t}}let c=l.Image},72713:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},74338:(e,t,s)=>{"use strict";s.d(t,{k:()=>l});var a=s(95155),r=s(59434);function l(e){let{size:t="md",className:s}=e;return(0,a.jsx)("div",{className:(0,r.cn)("flex items-center justify-center",s),children:(0,a.jsx)("div",{className:(0,r.cn)("animate-spin rounded-full border-2 border-white border-t-transparent",{sm:"w-4 h-4",md:"w-6 h-6",lg:"w-12 h-12"}[t])})})}},84105:(e,t,s)=>{"use strict";s.d(t,{As:()=>o,AuthProvider:()=>d,Nu:()=>h});var a=s(95155),r=s(12115),l=s(16203),i=s(35317),n=s(28214);let c=(0,r.createContext)(void 0);function d(e){let{children:t}=e,[s,d]=(0,r.useState)(null),[o,h]=(0,r.useState)(null),[u,x]=(0,r.useState)(!0),[f,p]=(0,r.useState)(null),g=async e=>{try{let l=(0,i.H9)(n.db,"users",e),c=await (0,i.x7)(l);if(c.exists()){var t,s,a,r;let e=c.data();return{id:c.id,...e,createdAt:(null==(s=e.createdAt)||null==(t=s.toDate)?void 0:t.call(s))||e.createdAt,updatedAt:(null==(r=e.updatedAt)||null==(a=r.toDate)?void 0:a.call(r))||e.updatedAt}}return null}catch(e){return console.error("Error fetching user profile:",e),null}},j=async()=>{s&&h(await g(s.uid))},y=e=>new Promise(t=>{let s=(0,l.hg)(n.j2,a=>{e&&a&&a.uid===e.uid?(s(),t()):e||a||(s(),t())})});(0,r.useEffect)(()=>(0,l.hg)(n.j2,async e=>{if(d(e),e){try{let t=await e.getIdToken();document.cookie="firebase-token=".concat(t,"; path=/; max-age=3600; secure; samesite=strict")}catch(e){console.error("Error getting ID token:",e)}h(await g(e.uid))}else document.cookie="firebase-token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT",h(null);x(!1)}),[]);let v=async(e,t)=>{try{p(null),x(!0);let s=await (0,l.x9)(n.j2,e,t);await y(s.user),await new Promise(e=>setTimeout(e,100))}catch(e){throw p(m(e instanceof Error&&"code"in e?e.code:"unknown")),e}finally{x(!1)}},w=async(e,t,s)=>{try{p(null),x(!0);let a=(await (0,l.eJ)(n.j2,e,t)).user;await (0,l.r7)(a,{displayName:"".concat(s.firstName," ").concat(s.lastName)});let r=i.Dc.now(),c={email:e,fullName:"".concat(s.firstName," ").concat(s.lastName),firstName:s.firstName,lastName:s.lastName,phone:s.phone||null,role:s.role,languagePreference:"en",createdAt:r,updatedAt:r};await (0,i.BN)((0,i.H9)(n.db,"users",a.uid),c),h({id:a.uid,...c,phone:s.phone||void 0});try{let e=await a.getIdToken(),t=await fetch("/api/auth/set-role",{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(e)},body:JSON.stringify({uid:a.uid,role:s.role})});if(t.ok)console.log("User role set successfully");else{let e=await t.json();console.error("Failed to set user role:",e.error)}}catch(e){console.error("Error setting user role:",e)}await y(a),await new Promise(e=>setTimeout(e,100))}catch(e){throw p(m(e instanceof Error&&"code"in e?e.code:"unknown")),e}finally{x(!1)}},N=async()=>{try{p(null),await (0,l.CI)(n.j2),h(null)}catch(e){throw p(m(e instanceof Error&&"code"in e?e.code:"unknown")),e}},b=async e=>{try{p(null),await (0,l.J1)(n.j2,e)}catch(e){throw p(m(e instanceof Error&&"code"in e?e.code:"unknown")),e}};return(0,a.jsx)(c.Provider,{value:{user:s,userProfile:o,loading:u,error:f,signIn:v,signUp:w,signOut:N,resetPassword:b,clearError:()=>{p(null)},refreshProfile:j},children:t})}function o(){let e=(0,r.useContext)(c);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}function m(e){switch(e){case"auth/user-not-found":case"auth/wrong-password":case"auth/invalid-credential":return"Invalid email or password";case"auth/email-already-in-use":return"An account with this email already exists";case"auth/weak-password":return"Password should be at least 6 characters";case"auth/invalid-email":return"Invalid email address";case"auth/too-many-requests":return"Too many failed attempts. Please try again later";case"auth/network-request-failed":return"Network error. Please check your connection";default:return"An unexpected error occurred. Please try again"}}function h(){let{user:e,loading:t}=o();return{user:e,loading:t,isAuthenticated:!!e}}},84616:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},92657:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])}},e=>{var t=t=>e(e.s=t);e.O(0,[992,965,277,288,63,874,441,684,358],()=>t(26261)),_N_E=e.O()}]);