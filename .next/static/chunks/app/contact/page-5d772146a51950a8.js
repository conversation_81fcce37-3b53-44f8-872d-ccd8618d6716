(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[977],{1912:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>h});var t=a(95155),s=a(12115),n=a(13741),l=a(93915),i=a(17703),c=a(19946);let o=(0,c.A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]]),d=(0,c.A)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]]);var m=a(4516),u=a(14186);function h(){let[e,r]=(0,s.useState)({name:"",email:"",subject:"",message:""}),[a,c]=(0,s.useState)(!1),[h,x]=(0,s.useState)(!1),f=(e,a)=>{r(r=>({...r,[e]:a}))},p=async e=>{e.preventDefault(),c(!0),await new Promise(e=>setTimeout(e,1e3)),x(!0),c(!1),r({name:"",email:"",subject:"",message:""})};return(0,t.jsx)("div",{className:"min-h-screen bg-earth-100 py-16",children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,t.jsxs)("div",{className:"text-center mb-12",children:[(0,t.jsx)("h1",{className:"text-4xl md:text-5xl font-bold mb-6 text-earth-900",children:"Contact Us"}),(0,t.jsx)("p",{className:"text-xl text-earth-700 max-w-3xl mx-auto",children:"Get in touch with our team. We're here to help you plan your perfect safari experience."})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12",children:[(0,t.jsxs)("div",{className:"space-y-8",children:[(0,t.jsxs)(i.Zp,{children:[(0,t.jsx)(i.aR,{children:(0,t.jsxs)(i.ZB,{className:"flex items-center",children:[(0,t.jsx)(o,{className:"w-5 h-5 mr-2 text-accent-600"}),"Email Us"]})}),(0,t.jsxs)(i.Wu,{children:[(0,t.jsx)("p",{className:"text-earth-700 mb-2",children:"General Inquiries"}),(0,t.jsx)("p",{className:"font-medium text-earth-900",children:"<EMAIL>"}),(0,t.jsx)("p",{className:"text-earth-700 mb-2 mt-4",children:"Booking Support"}),(0,t.jsx)("p",{className:"font-medium text-earth-900",children:"<EMAIL>"})]})]}),(0,t.jsxs)(i.Zp,{children:[(0,t.jsx)(i.aR,{children:(0,t.jsxs)(i.ZB,{className:"flex items-center",children:[(0,t.jsx)(d,{className:"w-5 h-5 mr-2 text-accent-600"}),"Call Us"]})}),(0,t.jsxs)(i.Wu,{children:[(0,t.jsx)("p",{className:"text-earth-700 mb-2",children:"Main Office"}),(0,t.jsx)("p",{className:"font-medium text-earth-900",children:"+27 (0)11 123 4567"}),(0,t.jsx)("p",{className:"text-earth-700 mb-2 mt-4",children:"WhatsApp"}),(0,t.jsx)("p",{className:"font-medium text-earth-900",children:"+27 (0)82 123 4567"})]})]}),(0,t.jsxs)(i.Zp,{children:[(0,t.jsx)(i.aR,{children:(0,t.jsxs)(i.ZB,{className:"flex items-center",children:[(0,t.jsx)(m.A,{className:"w-5 h-5 mr-2 text-accent-600"}),"Visit Us"]})}),(0,t.jsxs)(i.Wu,{children:[(0,t.jsx)("p",{className:"text-earth-700 mb-2",children:"Head Office"}),(0,t.jsxs)("p",{className:"font-medium text-earth-900",children:["123 Safari Street",(0,t.jsx)("br",{}),"Johannesburg, 2000",(0,t.jsx)("br",{}),"South Africa"]})]})]}),(0,t.jsxs)(i.Zp,{children:[(0,t.jsx)(i.aR,{children:(0,t.jsxs)(i.ZB,{className:"flex items-center",children:[(0,t.jsx)(u.A,{className:"w-5 h-5 mr-2 text-accent-600"}),"Business Hours"]})}),(0,t.jsx)(i.Wu,{children:(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-earth-700",children:"Monday - Friday"}),(0,t.jsx)("span",{className:"font-medium text-earth-900",children:"8:00 AM - 6:00 PM"})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-earth-700",children:"Saturday"}),(0,t.jsx)("span",{className:"font-medium text-earth-900",children:"9:00 AM - 4:00 PM"})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-earth-700",children:"Sunday"}),(0,t.jsx)("span",{className:"font-medium text-earth-900",children:"Closed"})]})]})})]})]}),(0,t.jsx)("div",{children:(0,t.jsxs)(i.Zp,{children:[(0,t.jsx)(i.aR,{children:(0,t.jsx)(i.ZB,{children:"Send us a Message"})}),(0,t.jsx)(i.Wu,{children:h?(0,t.jsxs)("div",{className:"text-center py-8",children:[(0,t.jsx)("div",{className:"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,t.jsx)("svg",{className:"w-8 h-8 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})}),(0,t.jsx)("h3",{className:"text-xl font-semibold text-earth-900 mb-2",children:"Message Sent!"}),(0,t.jsx)("p",{className:"text-earth-700",children:"Thank you for contacting us. We'll get back to you within 24 hours."}),(0,t.jsx)(n.$,{onClick:()=>x(!1),variant:"outline",className:"mt-4",children:"Send Another Message"})]}):(0,t.jsxs)("form",{onSubmit:p,className:"space-y-6",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsx)(l.p,{label:"Full Name",value:e.name,onChange:e=>f("name",e.target.value),required:!0}),(0,t.jsx)(l.p,{label:"Email Address",type:"email",value:e.email,onChange:e=>f("email",e.target.value),required:!0})]}),(0,t.jsx)(l.p,{label:"Subject",value:e.subject,onChange:e=>f("subject",e.target.value),required:!0}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-earth-900 mb-2",children:"Message"}),(0,t.jsx)("textarea",{className:"w-full px-3 py-2 border border-earth-300 rounded-md focus:outline-none focus:ring-2 focus:ring-accent-600 focus:border-transparent",rows:6,value:e.message,onChange:e=>f("message",e.target.value),required:!0,placeholder:"Tell us about your safari requirements..."})]}),(0,t.jsx)(n.$,{type:"submit",variant:"primary",size:"lg",className:"w-full",isLoading:a,children:a?"Sending...":"Send Message"})]})})]})})]})]})})}},4516:(e,r,a)=>{"use strict";a.d(r,{A:()=>t});let t=(0,a(19946).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},13741:(e,r,a)=>{"use strict";a.d(r,{$:()=>l});var t=a(95155),s=a(12115),n=a(59434);let l=(0,s.forwardRef)((e,r)=>{let{className:a,variant:s="primary",size:l="md",isLoading:i,children:c,disabled:o,...d}=e;return(0,t.jsxs)("button",{className:(0,n.cn)("\n      inline-flex items-center justify-center rounded-[var(--radius-md)] \n      font-[var(--font-ui)] font-semibold transition-all duration-300 \n      focus:outline-none focus:ring-2 focus:ring-offset-2\n      disabled:opacity-50 disabled:cursor-not-allowed\n      hover:transform hover:-translate-y-0.5 hover:shadow-[var(--shadow-lg)]\n    ",{primary:"\n        bg-[var(--primary-brown)] text-white \n        hover:bg-[#A0522D] focus:ring-[var(--primary-brown)]\n      ",secondary:"\n        bg-[var(--secondary-sky)] text-white \n        hover:bg-[#357AE0] focus:ring-[var(--secondary-sky)]\n      ",outline:"\n        bg-transparent border-2 border-[var(--primary-brown)] text-[var(--primary-brown)]\n        hover:bg-[var(--primary-brown)] hover:text-white focus:ring-[var(--primary-brown)]\n      ",hunting:"\n        bg-[var(--hunting-accent)] text-white \n        hover:bg-[#A67C00] focus:ring-[var(--hunting-accent)]\n      ",photo:"\n        bg-[var(--photo-accent)] text-white \n        hover:bg-[#3557C7] focus:ring-[var(--photo-accent)]\n      "}[s],{sm:"px-3 py-1.5 text-sm",md:"px-6 py-2 text-base",lg:"px-8 py-3 text-lg"}[l],i&&"cursor-wait",a),disabled:o||i,ref:r,...d,children:[i&&(0,t.jsxs)("svg",{className:"mr-2 h-4 w-4 animate-spin",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,t.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,t.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),c]})});l.displayName="Button"},14186:(e,r,a)=>{"use strict";a.d(r,{A:()=>t});let t=(0,a(19946).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},17703:(e,r,a)=>{"use strict";a.d(r,{MH:()=>m,Wu:()=>c,ZB:()=>d,Zp:()=>i,aR:()=>o});var t=a(95155),s=a(12115),n=a(66766),l=a(59434);let i=(0,s.forwardRef)((e,r)=>{let{className:a,hover:s=!0,children:n,...i}=e;return(0,t.jsx)("div",{ref:r,className:(0,l.cn)("\n          bg-white rounded-[var(--radius-lg)] shadow-[var(--shadow-md)] \n          overflow-hidden transition-all duration-300\n          ",s&&"hover:transform hover:-translate-y-1 hover:shadow-[var(--shadow-xl)]",a),...i,children:n})});i.displayName="Card";let c=(0,s.forwardRef)((e,r)=>{let{className:a,...s}=e;return(0,t.jsx)("div",{ref:r,className:(0,l.cn)("p-[var(--space-lg)]",a),...s})});c.displayName="CardContent";let o=(0,s.forwardRef)((e,r)=>{let{className:a,...s}=e;return(0,t.jsx)("div",{ref:r,className:(0,l.cn)("px-[var(--space-lg)] pt-[var(--space-lg)]",a),...s})});o.displayName="CardHeader";let d=(0,s.forwardRef)((e,r)=>{let{className:a,...s}=e;return(0,t.jsx)("h3",{ref:r,className:(0,l.cn)("text-xl font-bold mb-2 text-[var(--primary-brown)] font-[var(--font-display)]",a),...s})});d.displayName="CardTitle";let m=(0,s.forwardRef)((e,r)=>{let{className:a,src:s,alt:i,children:c,...o}=e;return(0,t.jsx)("div",{ref:r,className:(0,l.cn)("relative w-full h-48 bg-gradient-to-br from-[var(--primary-green)] to-[var(--secondary-sunset)] flex items-center justify-center text-white text-5xl",a),...o,children:s?(0,t.jsx)(n.default,{src:s,alt:i||"",fill:!0,className:"object-cover"}):c})});m.displayName="CardImage"},19946:(e,r,a)=>{"use strict";a.d(r,{A:()=>m});var t=a(12115);let s=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),n=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,r,a)=>a?a.toUpperCase():r.toLowerCase()),l=e=>{let r=n(e);return r.charAt(0).toUpperCase()+r.slice(1)},i=function(){for(var e=arguments.length,r=Array(e),a=0;a<e;a++)r[a]=arguments[a];return r.filter((e,r,a)=>!!e&&""!==e.trim()&&a.indexOf(e)===r).join(" ").trim()},c=e=>{for(let r in e)if(r.startsWith("aria-")||"role"===r||"title"===r)return!0};var o={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,t.forwardRef)((e,r)=>{let{color:a="currentColor",size:s=24,strokeWidth:n=2,absoluteStrokeWidth:l,className:d="",children:m,iconNode:u,...h}=e;return(0,t.createElement)("svg",{ref:r,...o,width:s,height:s,stroke:a,strokeWidth:l?24*Number(n)/Number(s):n,className:i("lucide",d),...!m&&!c(h)&&{"aria-hidden":"true"},...h},[...u.map(e=>{let[r,a]=e;return(0,t.createElement)(r,a)}),...Array.isArray(m)?m:[m]])}),m=(e,r)=>{let a=(0,t.forwardRef)((a,n)=>{let{className:c,...o}=a;return(0,t.createElement)(d,{ref:n,iconNode:r,className:i("lucide-".concat(s(l(e))),"lucide-".concat(e),c),...o})});return a.displayName=l(e),a}},59434:(e,r,a)=>{"use strict";a.d(r,{Y:()=>i,Z:()=>l,cn:()=>n});var t=a(52596),s=a(39688);function n(){for(var e=arguments.length,r=Array(e),a=0;a<e;a++)r[a]=arguments[a];return(0,s.QP)((0,t.$)(r))}function l(e){return e.toString().replace(/\B(?=(\d{3})+(?!\d))/g," ")}function i(e){let r=new Date(e);return"".concat(r.getDate()," ").concat(["January","February","March","April","May","June","July","August","September","October","November","December"][r.getMonth()]," ").concat(r.getFullYear())}},66766:(e,r,a)=>{"use strict";a.d(r,{default:()=>s.a});var t=a(71469),s=a.n(t)},71469:(e,r,a)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,r){for(var a in r)Object.defineProperty(e,a,{enumerable:!0,get:r[a]})}(r,{default:function(){return c},getImageProps:function(){return i}});let t=a(88229),s=a(38883),n=a(33063),l=t._(a(51193));function i(e){let{props:r}=(0,s.getImgProps)(e,{defaultLoader:l.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,a]of Object.entries(r))void 0===a&&delete r[e];return{props:r}}let c=n.Image},93915:(e,r,a)=>{"use strict";a.d(r,{p:()=>l});var t=a(95155),s=a(12115),n=a(59434);let l=(0,s.forwardRef)((e,r)=>{let{className:a,type:s,label:l,error:i,...c}=e;return(0,t.jsxs)("div",{className:"w-full",children:[l&&(0,t.jsx)("label",{className:"block mb-2 font-semibold text-[var(--primary-brown)] font-[var(--font-ui)]",children:l}),(0,t.jsx)("input",{type:s,className:(0,n.cn)("\n            w-full px-4 py-2 border-2 border-[var(--medium-gray)] \n            rounded-[var(--radius-md)] font-[var(--font-ui)] \n            transition-colors duration-300\n            focus:outline-none focus:border-[var(--primary-brown)] \n            focus:ring-0 focus:shadow-[0_0_0_3px_rgba(139,69,19,0.1)]\n            placeholder:text-[var(--medium-gray)]\n            ",i&&"border-red-500 focus:border-red-500",a),ref:r,...c}),i&&(0,t.jsx)("p",{className:"mt-1 text-sm text-red-600",children:i})]})});l.displayName="Input"},97493:(e,r,a)=>{Promise.resolve().then(a.bind(a,1912))}},e=>{var r=r=>e(e.s=r);e.O(0,[277,63,441,684,358],()=>r(97493)),_N_E=e.O()}]);