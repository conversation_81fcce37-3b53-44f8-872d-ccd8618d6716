(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[494],{23472:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>f});var a=s(95155),i=s(12115),r=s(35695),n=s(66766),l=s(13741),c=s(17703),d=s(52814),o=s(93915),h=s(84105),m=s(28666);function x(e){let{farm:t,activity:s,onClose:n}=e,{user:x}=(0,h.As)(),u=(0,r.useRouter)(),[p,j]=(0,i.useState)(1),[g,f]=(0,i.useState)(!1),[v,N]=(0,i.useState)(null),[b,w]=(0,i.useState)({checkIn:"",checkOut:"",guests:2,accommodation:"standard",specialRequests:"",contactPhone:""}),y=(e,t)=>{w(s=>({...s,[e]:t}))},k=()=>{if(!b.checkIn||!b.checkOut)return 0;let e=new Date(b.checkIn);return Math.ceil(Math.abs(new Date(b.checkOut).getTime()-e.getTime())/864e5)},S=async()=>{if(!x)return void u.push("/auth/login");f(!0),N(null);try{let e=k();await m.zi.create({farmId:t.id,hunterId:x.uid,startDate:b.checkIn,endDate:b.checkOut,durationDays:e,activityType:s,groupSize:b.guests,specialRequests:b.specialRequests||void 0,status:"pending"}),b.contactPhone&&await m.QQ.update(x.uid,{phone:b.contactPhone}),n(),u.push("/dashboard")}catch(e){console.error("Booking submission failed:",e),N(e instanceof Error?e.message:"Failed to submit booking request")}finally{f(!1)}};return x?(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,a.jsxs)(c.Zp,{className:"w-full max-w-2xl max-h-[90vh] overflow-y-auto",children:[(0,a.jsx)(c.aR,{children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(c.ZB,{children:"Book Your Safari"}),(0,a.jsx)("button",{onClick:n,className:"text-earth-500 hover:text-earth-700",children:"✕"})]})}),(0,a.jsxs)(c.Wu,{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-earth-50 p-4 rounded-lg",children:[(0,a.jsx)("h3",{className:"font-semibold text-earth-900 mb-2",children:t.name}),(0,a.jsxs)("p",{className:"text-earth-600 mb-3",children:[" ",t.location,", ",t.province]}),(0,a.jsx)(d.E,{variant:"hunting"===s?"hunting":"photo",children:"hunting"===s?"Hunting Safari":"photo_safari"===s?"Photo Safari":"Combined Safari"})]}),1===p&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-earth-900",children:"Step 1: Trip Details"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-earth-700 mb-2",children:"Check-in Date"}),(0,a.jsx)(o.p,{type:"date",value:b.checkIn,onChange:e=>y("checkIn",e.target.value),min:new Date().toISOString().split("T")[0],required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-earth-700 mb-2",children:"Check-out Date"}),(0,a.jsx)(o.p,{type:"date",value:b.checkOut,onChange:e=>y("checkOut",e.target.value),min:b.checkIn||new Date().toISOString().split("T")[0],required:!0})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-earth-700 mb-2",children:"Number of Guests"}),(0,a.jsx)(o.p,{type:"number",min:"1",max:"12",value:b.guests,onChange:e=>y("guests",parseInt(e.target.value)),required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-earth-700 mb-2",children:"Accommodation Preference"}),(0,a.jsxs)("select",{value:b.accommodation,onChange:e=>y("accommodation",e.target.value),className:"w-full px-3 py-2 border border-earth-300 rounded-md focus:outline-none focus:ring-2 focus:ring-accent-500 focus:border-transparent",children:[(0,a.jsx)("option",{value:"standard",children:"Standard Room"}),(0,a.jsx)("option",{value:"luxury",children:"Luxury Suite"}),(0,a.jsx)("option",{value:"self-catering",children:"Self-Catering Chalet"})]})]}),b.checkIn&&b.checkOut&&(0,a.jsxs)("div",{className:"bg-accent-50 p-4 rounded-lg",children:[(0,a.jsx)("h4",{className:"font-semibold text-earth-900 mb-2",children:"Trip Summary"}),(0,a.jsxs)("div",{className:"space-y-1 text-sm text-earth-700",children:[(0,a.jsxs)("p",{children:["Duration: ",k()," days"]}),(0,a.jsxs)("p",{children:["Guests: ",b.guests]}),(0,a.jsxs)("p",{children:["Activity: ","hunting"===s?"Hunting Safari":"photo_safari"===s?"Photo Safari":"Combined Safari"]}),(0,a.jsxs)("p",{children:["Accommodation: ",b.accommodation.replace("-"," ")]})]})]}),(0,a.jsx)("div",{className:"flex justify-end",children:(0,a.jsx)(l.$,{variant:"primary",onClick:()=>j(2),disabled:!b.checkIn||!b.checkOut||1>k(),children:"Continue"})})]}),2===p&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-earth-900",children:"Step 2: Contact & Requests"}),v&&(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,a.jsx)("p",{className:"text-red-600 text-sm",children:v})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-earth-700 mb-2",children:"Contact Phone Number"}),(0,a.jsx)(o.p,{type:"tel",placeholder:"e.g., +27 12 345 6789",value:b.contactPhone,onChange:e=>y("contactPhone",e.target.value),required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-earth-700 mb-2",children:"Special Requests or Requirements"}),(0,a.jsx)("textarea",{rows:4,placeholder:"Any dietary requirements, special occasions, equipment needs, or other requests...",value:b.specialRequests,onChange:e=>y("specialRequests",e.target.value),className:"w-full px-3 py-2 border border-earth-300 rounded-md focus:outline-none focus:ring-2 focus:ring-accent-500 focus:border-transparent resize-none"})]}),(0,a.jsxs)("div",{className:"bg-earth-50 p-4 rounded-lg",children:[(0,a.jsx)("h4",{className:"font-semibold text-earth-900 mb-2",children:"Important Notes"}),(0,a.jsxs)("ul",{className:"space-y-1 text-sm text-earth-700",children:[(0,a.jsx)("li",{children:"• This is a booking request, not a confirmed reservation"}),(0,a.jsx)("li",{children:"• The farm owner will review and respond within 24-48 hours"}),(0,a.jsx)("li",{children:"• Final pricing may vary based on specific requirements"}),(0,a.jsx)("li",{children:"• Cancellation policies apply as per farm terms"})]})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(l.$,{variant:"outline",onClick:()=>j(1),className:"flex-1",children:"Back"}),(0,a.jsx)(l.$,{variant:"primary",onClick:S,disabled:g||!b.contactPhone,className:"flex-1",children:g?"Submitting...":"Submit Request"})]})]})]})]})}):(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,a.jsxs)(c.Zp,{className:"w-full max-w-md",children:[(0,a.jsx)(c.aR,{children:(0,a.jsx)(c.ZB,{children:"Sign In Required"})}),(0,a.jsxs)(c.Wu,{className:"space-y-4",children:[(0,a.jsx)("p",{className:"text-earth-700",children:"Please sign in to submit a booking request."}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(l.$,{variant:"primary",className:"flex-1",onClick:()=>u.push("/auth/login"),children:"Sign In"}),(0,a.jsx)(l.$,{variant:"outline",className:"flex-1",onClick:n,children:"Cancel"})]})]})]})})}var u=s(59434);function p(e){let t,{review:s}=e;return(0,a.jsx)(c.Zp,{children:(0,a.jsxs)(c.Wu,{className:"p-6",children:[(0,a.jsx)("div",{className:"flex items-start justify-between mb-4",children:(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-2",children:[(0,a.jsx)("div",{className:"flex items-center gap-1",children:(t=s.rating,Array.from({length:5},(e,s)=>(0,a.jsx)("svg",{className:"w-4 h-4 ".concat(s<t?"text-yellow-400 fill-current":"text-earth-300"),viewBox:"0 0 20 20",children:(0,a.jsx)("path",{d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"})},s)))}),(0,a.jsx)("span",{className:"text-sm text-earth-600",children:(0,u.Y)(s.createdAt.toDate().toISOString())})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,a.jsx)("h4",{className:"font-semibold text-earth-900",children:"Anonymous Reviewer"}),s.wouldRecommend&&(0,a.jsx)(d.E,{variant:"hunting",children:"Recommends"})]})]})}),s.title&&(0,a.jsx)("h5",{className:"font-medium text-earth-900 mb-2",children:s.title}),s.comment&&(0,a.jsx)("p",{className:"text-earth-700 mb-4 leading-relaxed",children:s.comment}),s.responseFromOwner&&(0,a.jsxs)("div",{className:"bg-earth-50 rounded-lg p-4 mt-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,a.jsx)("svg",{className:"w-4 h-4 text-accent-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.955 8.955 0 01-2.126-.275c-1.15-.29-2.074-1.214-2.364-2.364A8.955 8.955 0 018 15c0-4.418 3.582-8 8-8s8 3.582 8 8z"})}),(0,a.jsx)("span",{className:"text-sm font-medium text-earth-900",children:"Response from farm owner"}),s.respondedAt&&(0,a.jsx)("span",{className:"text-xs text-earth-500",children:(0,u.Y)(s.respondedAt.toDate().toISOString())})]}),(0,a.jsx)("p",{className:"text-earth-700 text-sm",children:s.responseFromOwner})]})]})})}function j(e){let{reviews:t,className:s=""}=e;if(0===t.length)return(0,a.jsxs)("div",{className:"text-center py-8 ".concat(s),children:[(0,a.jsx)("p",{className:"text-earth-600",children:"No reviews yet"}),(0,a.jsx)("p",{className:"text-earth-500 text-sm mt-1",children:"Be the first to leave a review after your visit!"})]});let i=t.reduce((e,t)=>e+t.rating,0)/t.length,r=Math.round(10*i)/10,n=Array.from({length:5},(e,s)=>{let a=5-s,i=t.filter(e=>e.rating===a).length,r=t.length>0?i/t.length*100:0;return{stars:a,count:i,percentage:r}});return(0,a.jsx)("div",{className:s,children:(0,a.jsxs)("div",{className:"bg-white rounded-lg p-6 border",children:[(0,a.jsxs)("div",{className:"flex items-center gap-6 mb-6",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-4xl font-bold text-earth-900 mb-1",children:r}),(0,a.jsx)("div",{className:"flex items-center gap-1 mb-1",children:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"sm",s="lg"===t?"w-5 h-5":"w-4 h-4";return Array.from({length:5},(t,i)=>(0,a.jsx)("svg",{className:"".concat(s," ").concat(i<Math.floor(e)?"text-yellow-400 fill-current":"text-earth-300"),viewBox:"0 0 20 20",children:(0,a.jsx)("path",{d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"})},i))}(i,"lg")}),(0,a.jsxs)("p",{className:"text-sm text-earth-600",children:[t.length," review",1!==t.length?"s":""]})]}),(0,a.jsx)("div",{className:"flex-1",children:n.map(e=>{let{stars:t,count:s,percentage:i}=e;return(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,a.jsx)("span",{className:"text-sm text-earth-600 w-6",children:t}),(0,a.jsx)("svg",{className:"w-4 h-4 text-yellow-400 fill-current",children:(0,a.jsx)("path",{d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"})}),(0,a.jsx)("div",{className:"flex-1 bg-earth-200 rounded-full h-2",children:(0,a.jsx)("div",{className:"bg-yellow-400 h-2 rounded-full transition-all duration-300",style:{width:"".concat(i,"%")}})}),(0,a.jsx)("span",{className:"text-sm text-earth-600 w-8",children:s})]},t)})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 text-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-hunting-accent",children:t.filter(e=>e.wouldRecommend).length}),(0,a.jsx)("p",{className:"text-sm text-earth-600",children:"Would Recommend"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-photo-accent",children:t.filter(e=>e.rating>=4).length}),(0,a.jsx)("p",{className:"text-sm text-earth-600",children:"4+ Star Reviews"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-accent-600",children:t.filter(e=>e.responseFromOwner).length}),(0,a.jsx)("p",{className:"text-sm text-earth-600",children:"Owner Responses"})]})]})]})})}var g=s(17154);function f(){let e=(0,r.useParams)(),[t,s]=(0,i.useState)(null),[o,h]=(0,i.useState)("hunting"),[u,f]=(0,i.useState)(!1),[v,N]=(0,i.useState)(!0),[b,w]=(0,i.useState)(null),y=(0,i.useCallback)(async e=>{try{N(!0),w(null);let t=await m.eg.get(e);if(!t)throw Error("Farm not found");let a=await m.C2.getByFarm(e),i=[];"hunting"===t.activityTypes?i.push("hunting"):"photo_safari"===t.activityTypes?i.push("photo_safari"):"both"===t.activityTypes&&i.push("hunting","photo_safari");let r=a.length>0?a.reduce((e,t)=>e+t.rating,0)/a.length:0;s({...t,activities:i,amenities:[],species:[],reviews:a,images:[],rating:Math.round(10*r)/10}),i.length>0&&h(i[0])}catch(e){console.error("Error fetching farm details:",e),w(e instanceof Error?e.message:"Failed to load farm details")}finally{N(!1)}},[]);return((0,i.useEffect)(()=>{e.id&&y(e.id)},[e.id,y]),v)?(0,a.jsx)("div",{className:"min-h-screen bg-earth-100 flex items-center justify-center",children:(0,a.jsx)("p",{className:"text-earth-600 text-lg",children:"Loading farm details..."})}):b||!t?(0,a.jsx)("div",{className:"min-h-screen bg-earth-100 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-red-600 text-lg mb-4",children:b||"Farm not found"}),(0,a.jsx)(l.$,{onClick:()=>window.history.back(),variant:"outline",children:"Go Back"})]})}):(0,a.jsxs)("div",{className:"min-h-screen bg-earth-100",children:[(0,a.jsx)("div",{className:"relative h-96",children:(0,a.jsx)(g.t,{className:"absolute inset-0",children:(0,a.jsx)("div",{className:"relative z-20 h-96 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center text-white",children:[(0,a.jsx)("h1",{className:"text-4xl md:text-5xl font-bold mb-4",children:t.name}),(0,a.jsxs)("p",{className:"text-xl",children:["\uD83D\uDCCD ",t.location,", ",t.province]}),(0,a.jsxs)("div",{className:"flex items-center justify-center mt-4",children:[(0,a.jsx)("span",{className:"text-accent-600 mr-2 text-2xl",children:"★"}),(0,a.jsx)("span",{className:"text-xl",children:t.rating||"New"}),(0,a.jsxs)("span",{className:"ml-2",children:["(",t.reviews.length," reviews)"]})]})]})})})}),(0,a.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,a.jsxs)("div",{className:"lg:col-span-2 space-y-8",children:[(0,a.jsx)(c.Zp,{children:(0,a.jsx)(c.Wu,{className:"p-0",children:t.images.length>0?(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"relative h-64 md:h-80",children:[(0,a.jsx)(n.default,{src:t.images[0].imageUrl,alt:t.images[0].altText||"".concat(t.name," main image"),fill:!0,className:"object-cover",sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 66vw, 50vw"}),t.images.length>1&&(0,a.jsxs)("div",{className:"absolute bottom-4 right-4 bg-black bg-opacity-50 text-white px-3 py-1 rounded-full text-sm",children:["1 / ",t.images.length]})]}),t.images.length>1&&(0,a.jsx)("div",{className:"p-4",children:(0,a.jsxs)("div",{className:"grid grid-cols-4 md:grid-cols-6 gap-2",children:[t.images.slice(1,7).map((e,s)=>(0,a.jsx)("div",{className:"relative aspect-square",children:(0,a.jsx)(n.default,{src:e.imageUrl,alt:e.altText||"".concat(t.name," image ").concat(s+2),fill:!0,className:"object-cover rounded cursor-pointer hover:opacity-80 transition-opacity",sizes:"(max-width: 768px) 25vw, (max-width: 1200px) 16vw, 12vw"})},e.id)),t.images.length>7&&(0,a.jsx)("div",{className:"relative aspect-square bg-earth-200 rounded flex items-center justify-center cursor-pointer hover:bg-earth-300 transition-colors",children:(0,a.jsxs)("span",{className:"text-earth-600 text-sm font-medium",children:["+",t.images.length-7]})})]})})]}):(0,a.jsx)("div",{className:"h-64 bg-gradient-to-br from-earth-600 to-earth-900 flex items-center justify-center text-white",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-6xl mb-4",children:"\uD83C\uDFDE️"}),(0,a.jsx)("p",{className:"text-lg",children:"No images available"}),(0,a.jsx)("p",{className:"text-sm opacity-75",children:"Contact farm for photos"})]})})})}),(0,a.jsxs)(c.Zp,{children:[(0,a.jsx)(c.aR,{children:(0,a.jsx)(c.ZB,{children:"Available Activities"})}),(0,a.jsxs)(c.Wu,{children:[(0,a.jsx)("div",{className:"flex gap-4 mb-6",children:t.activities.map(e=>(0,a.jsx)("button",{onClick:()=>h(e),className:"px-6 py-3 rounded-lg font-semibold transition-colors ".concat(o===e?"hunting"===e?"bg-hunting-accent text-white":"bg-photo-accent text-white":"bg-earth-200 text-earth-700 hover:bg-earth-300"),children:"hunting"===e?"Hunting Safari":"Photo Safari"},e))}),(0,a.jsxs)("div",{className:"bg-earth-50 p-4 rounded-lg",children:[(0,a.jsxs)("h4",{className:"font-semibold text-earth-900 mb-2",children:["hunting"===o?"Hunting Safari":"photo_safari"===o?"Photo Safari":"Combined Safari"," Experience"]}),(0,a.jsx)("p",{className:"text-earth-700",children:"hunting"===o?"Professional guided hunting with experienced trackers. All equipment provided. Trophy preparation available.":"photo_safari"===o?"Guided photography tours with professional wildlife photographers. Best spots for wildlife viewing and photography.":"Experience both hunting and photography opportunities with our expert guides."})]})]})]}),(0,a.jsxs)(c.Zp,{children:[(0,a.jsx)(c.aR,{children:(0,a.jsxs)(c.ZB,{children:["About ",t.name]})}),(0,a.jsx)(c.Wu,{children:(0,a.jsx)("p",{className:"text-earth-700 leading-relaxed",children:t.description})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)(c.Zp,{children:[(0,a.jsx)(c.aR,{children:(0,a.jsx)(c.ZB,{children:"Available Species"})}),(0,a.jsx)(c.Wu,{children:(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:t.species.length>0?t.species.map(e=>(0,a.jsx)(d.E,{variant:"default",children:e},e)):(0,a.jsx)("p",{className:"text-earth-600 text-sm",children:"Species information coming soon"})})})]}),(0,a.jsxs)(c.Zp,{children:[(0,a.jsx)(c.aR,{children:(0,a.jsx)(c.ZB,{children:"Amenities"})}),(0,a.jsx)(c.Wu,{children:(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:t.amenities.length>0?t.amenities.map(e=>(0,a.jsx)(d.E,{variant:"location",children:e.name},e.id)):(0,a.jsx)("p",{className:"text-earth-600 text-sm",children:"Amenities information coming soon"})})})]})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-earth-900",children:"Guest Reviews"}),(0,a.jsx)(j,{reviews:t.reviews}),(0,a.jsxs)("div",{className:"space-y-4",children:[t.reviews.length>0?t.reviews.slice(0,5).map(e=>(0,a.jsx)(p,{review:e},e.id)):(0,a.jsx)(c.Zp,{children:(0,a.jsxs)(c.Wu,{className:"text-center py-12",children:[(0,a.jsx)("div",{className:"text-6xl mb-4",children:"⭐"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-earth-900 mb-2",children:"No reviews yet"}),(0,a.jsx)("p",{className:"text-earth-600",children:"Be the first to review this farm after your visit!"})]})}),t.reviews.length>5&&(0,a.jsx)("div",{className:"text-center",children:(0,a.jsxs)(l.$,{variant:"outline",children:["View All ",t.reviews.length," Reviews"]})})]})]})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)(c.Zp,{className:"sticky top-24",children:[(0,a.jsx)(c.aR,{children:(0,a.jsx)(c.ZB,{children:"Book Your Safari"})}),(0,a.jsxs)(c.Wu,{children:[(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("p",{className:"text-2xl font-bold text-earth-900",children:t.pricingInfo||"Contact for pricing"}),(0,a.jsx)("p",{className:"text-earth-600 text-sm",children:"per person per day"})]}),(0,a.jsx)(l.$,{variant:"primary",size:"lg",className:"w-full mb-4",onClick:()=>f(!0),children:"Request Booking"}),(0,a.jsxs)("div",{className:"space-y-2 text-sm text-earth-700",children:[(0,a.jsx)("p",{children:"• Minimum 3 days for hunting"}),(0,a.jsx)("p",{children:"• Professional guide included"}),(0,a.jsx)("p",{children:"• Accommodation available"})]})]})]}),(0,a.jsxs)(c.Zp,{children:[(0,a.jsx)(c.aR,{children:(0,a.jsx)(c.ZB,{children:"Contact Information"})}),(0,a.jsxs)(c.Wu,{className:"space-y-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-earth-900",children:"Phone"}),(0,a.jsx)("p",{className:"text-earth-700",children:t.contactPhone||"Contact via email"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-earth-900",children:"Email"}),(0,a.jsx)("p",{className:"text-earth-700",children:t.contactEmail})]}),t.websiteUrl&&(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-earth-900",children:"Website"}),(0,a.jsx)("p",{className:"text-earth-700",children:t.websiteUrl})]})]})]}),(0,a.jsxs)(c.Zp,{children:[(0,a.jsx)(c.aR,{children:(0,a.jsx)(c.ZB,{children:"Important Information"})}),(0,a.jsx)(c.Wu,{children:(0,a.jsx)("div",{className:"text-sm text-earth-700",children:t.rules?(0,a.jsx)("div",{dangerouslySetInnerHTML:{__html:t.rules.replace(/\n/g,"<br>")}}):(0,a.jsx)("p",{children:"Please contact the farm for specific rules and requirements."})})})]})]})]})}),u&&(0,a.jsx)(x,{farm:t,activity:o,onClose:()=>f(!1)})]})}},71060:(e,t,s)=>{Promise.resolve().then(s.bind(s,23472))},84105:(e,t,s)=>{"use strict";s.d(t,{As:()=>o,AuthProvider:()=>d,Nu:()=>m});var a=s(95155),i=s(12115),r=s(16203),n=s(35317),l=s(28214);let c=(0,i.createContext)(void 0);function d(e){let{children:t}=e,[s,d]=(0,i.useState)(null),[o,m]=(0,i.useState)(null),[x,u]=(0,i.useState)(!0),[p,j]=(0,i.useState)(null),g=async e=>{try{let r=(0,n.H9)(l.db,"users",e),c=await (0,n.x7)(r);if(c.exists()){var t,s,a,i;let e=c.data();return{id:c.id,...e,createdAt:(null==(s=e.createdAt)||null==(t=s.toDate)?void 0:t.call(s))||e.createdAt,updatedAt:(null==(i=e.updatedAt)||null==(a=i.toDate)?void 0:a.call(i))||e.updatedAt}}return null}catch(e){return console.error("Error fetching user profile:",e),null}},f=async()=>{s&&m(await g(s.uid))},v=e=>new Promise(t=>{let s=(0,r.hg)(l.j2,a=>{e&&a&&a.uid===e.uid?(s(),t()):e||a||(s(),t())})});(0,i.useEffect)(()=>(0,r.hg)(l.j2,async e=>{if(d(e),e){try{let t=await e.getIdToken();document.cookie="firebase-token=".concat(t,"; path=/; max-age=3600; secure; samesite=strict")}catch(e){console.error("Error getting ID token:",e)}m(await g(e.uid))}else document.cookie="firebase-token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT",m(null);u(!1)}),[]);let N=async(e,t)=>{try{j(null),u(!0);let s=await (0,r.x9)(l.j2,e,t);await v(s.user),await new Promise(e=>setTimeout(e,100))}catch(e){throw j(h(e instanceof Error&&"code"in e?e.code:"unknown")),e}finally{u(!1)}},b=async(e,t,s)=>{try{j(null),u(!0);let a=(await (0,r.eJ)(l.j2,e,t)).user;await (0,r.r7)(a,{displayName:"".concat(s.firstName," ").concat(s.lastName)});let i=n.Dc.now(),c={email:e,fullName:"".concat(s.firstName," ").concat(s.lastName),firstName:s.firstName,lastName:s.lastName,phone:s.phone||null,role:s.role,languagePreference:"en",createdAt:i,updatedAt:i};await (0,n.BN)((0,n.H9)(l.db,"users",a.uid),c),m({id:a.uid,...c,phone:s.phone||void 0});try{let e=await a.getIdToken(),t=await fetch("/api/auth/set-role",{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(e)},body:JSON.stringify({uid:a.uid,role:s.role})});if(t.ok)console.log("User role set successfully");else{let e=await t.json();console.error("Failed to set user role:",e.error)}}catch(e){console.error("Error setting user role:",e)}await v(a),await new Promise(e=>setTimeout(e,100))}catch(e){throw j(h(e instanceof Error&&"code"in e?e.code:"unknown")),e}finally{u(!1)}},w=async()=>{try{j(null),await (0,r.CI)(l.j2),m(null)}catch(e){throw j(h(e instanceof Error&&"code"in e?e.code:"unknown")),e}},y=async e=>{try{j(null),await (0,r.J1)(l.j2,e)}catch(e){throw j(h(e instanceof Error&&"code"in e?e.code:"unknown")),e}};return(0,a.jsx)(c.Provider,{value:{user:s,userProfile:o,loading:x,error:p,signIn:N,signUp:b,signOut:w,resetPassword:y,clearError:()=>{j(null)},refreshProfile:f},children:t})}function o(){let e=(0,i.useContext)(c);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}function h(e){switch(e){case"auth/user-not-found":case"auth/wrong-password":case"auth/invalid-credential":return"Invalid email or password";case"auth/email-already-in-use":return"An account with this email already exists";case"auth/weak-password":return"Password should be at least 6 characters";case"auth/invalid-email":return"Invalid email address";case"auth/too-many-requests":return"Too many failed attempts. Please try again later";case"auth/network-request-failed":return"Network error. Please check your connection";default:return"An unexpected error occurred. Please try again"}}function m(){let{user:e,loading:t}=o();return{user:e,loading:t,isAuthenticated:!!e}}}},e=>{var t=t=>e(e.s=t);e.O(0,[992,965,277,288,63,752,441,684,358],()=>t(71060)),_N_E=e.O()}]);