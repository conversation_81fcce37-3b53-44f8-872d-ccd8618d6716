(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[239],{19946:(e,t,r)=>{"use strict";r.d(t,{A:()=>u});var a=r(12115);let s=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),n=e=>{let t=i(e);return t.charAt(0).toUpperCase()+t.slice(1)},l=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()},c=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var o={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,a.forwardRef)((e,t)=>{let{color:r="currentColor",size:s=24,strokeWidth:i=2,absoluteStrokeWidth:n,className:d="",children:u,iconNode:m,...h}=e;return(0,a.createElement)("svg",{ref:t,...o,width:s,height:s,stroke:r,strokeWidth:n?24*Number(i)/Number(s):i,className:l("lucide",d),...!u&&!c(h)&&{"aria-hidden":"true"},...h},[...m.map(e=>{let[t,r]=e;return(0,a.createElement)(t,r)}),...Array.isArray(u)?u:[u]])}),u=(e,t)=>{let r=(0,a.forwardRef)((r,i)=>{let{className:c,...o}=r;return(0,a.createElement)(d,{ref:i,iconNode:t,className:l("lucide-".concat(s(n(e))),"lucide-".concat(e),c),...o})});return r.displayName=n(e),r}},30884:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>b});var a=r(95155),s=r(12115),i=r(35695),n=r(13741),l=r(93915),c=r(17703),o=r(52814),d=r(28666),u=r(84105),m=r(91950),h=r(35169),p=r(92657);let x=(0,r(19946).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]]);var f=r(6874),v=r.n(f);function b(){let{user:e,loading:t}=(0,u.As)(),r=(0,i.useRouter)(),f=(0,i.useParams)().id,[b,g]=(0,s.useState)(null),[j,y]=(0,s.useState)(!0),[N,w]=(0,s.useState)(!1),[k,A]=(0,s.useState)(null),[C,P]=(0,s.useState)(null),[E,F]=(0,s.useState)({name:"",description:"",descriptionAfrikaans:"",location:"",province:"",sizeHectares:"",activityTypes:"both",contactEmail:"",contactPhone:"",websiteUrl:"",rules:"",rulesAfrikaans:"",pricingInfo:"",isActive:!0,featured:!1}),S=(0,s.useCallback)(async()=>{try{var t;y(!0),A(null);let r=await d.eg.get(f);if(!r)throw Error("Farm not found");if(r.ownerId!==e.uid)throw Error("Access denied - you do not own this farm");g(r),F({name:r.name,description:r.description||"",descriptionAfrikaans:r.descriptionAfrikaans||"",location:r.location,province:r.province,sizeHectares:(null==(t=r.sizeHectares)?void 0:t.toString())||"",activityTypes:r.activityTypes,contactEmail:r.contactEmail,contactPhone:r.contactPhone||"",websiteUrl:r.websiteUrl||"",rules:r.rules||"",rulesAfrikaans:r.rulesAfrikaans||"",pricingInfo:r.pricingInfo||"",isActive:r.isActive,featured:r.featured})}catch(e){console.error("Error fetching farm:",e),A("Failed to load farm details")}finally{y(!1)}},[f,e]);(0,s.useEffect)(()=>{!t&&e&&S()},[t,e,S]);let z=(e,t)=>{F(r=>({...r,[e]:t})),P(null)},I=async t=>{if(t.preventDefault(),e&&b)try{w(!0),A(null);let e={name:E.name,description:E.description||void 0,descriptionAfrikaans:E.descriptionAfrikaans||void 0,location:E.location,province:E.province,sizeHectares:E.sizeHectares?parseInt(E.sizeHectares):void 0,activityTypes:E.activityTypes,contactEmail:E.contactEmail,contactPhone:E.contactPhone||void 0,websiteUrl:E.websiteUrl||void 0,rules:E.rules||void 0,rulesAfrikaans:E.rulesAfrikaans||void 0,pricingInfo:E.pricingInfo||void 0,isActive:E.isActive,featured:E.featured};await d.eg.update(f,e),P("Farm details updated successfully!"),await S()}catch(e){console.error("Error updating farm:",e),A("Failed to update farm details")}finally{w(!1)}};return t||j?(0,a.jsx)("div",{className:"min-h-screen bg-earth-100 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-accent-600 mx-auto"}),(0,a.jsx)("p",{className:"mt-4 text-earth-600",children:"Loading farm details..."})]})}):e?k&&!b?(0,a.jsx)("div",{className:"min-h-screen bg-earth-100 py-8",children:(0,a.jsx)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-earth-900 mb-4",children:"Farm Not Found"}),(0,a.jsx)("p",{className:"text-earth-600 mb-6",children:k}),(0,a.jsx)(v(),{href:"/dashboard",children:(0,a.jsxs)(n.$,{variant:"primary",children:[(0,a.jsx)(h.A,{className:"w-4 h-4 mr-2"}),"Back to Dashboard"]})})]})})}):(0,a.jsx)("div",{className:"min-h-screen bg-earth-100 py-8",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsxs)("div",{className:"flex items-center gap-4 mb-4",children:[(0,a.jsx)(v(),{href:"/dashboard",children:(0,a.jsxs)(n.$,{variant:"outline",size:"sm",children:[(0,a.jsx)(h.A,{className:"w-4 h-4 mr-2"}),"Back to Dashboard"]})}),(0,a.jsx)(v(),{href:"/farms/".concat(f),children:(0,a.jsxs)(n.$,{variant:"outline",size:"sm",children:[(0,a.jsx)(p.A,{className:"w-4 h-4 mr-2"}),"View Public Page"]})})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-earth-900",children:"Edit Farm Listing"}),(0,a.jsx)("p",{className:"text-earth-600 mt-2",children:"Update your farm details and settings"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(o.E,{variant:E.isActive?"hunting":"default",children:E.isActive?"Active":"Inactive"}),E.featured&&(0,a.jsx)(o.E,{variant:"photo",children:"Featured"})]})]})]}),(0,a.jsxs)("form",{onSubmit:I,className:"space-y-8",children:[k&&(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-md p-3",children:(0,a.jsx)("p",{className:"text-red-600 text-sm",children:k})}),C&&(0,a.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-md p-3",children:(0,a.jsx)("p",{className:"text-green-600 text-sm",children:C})}),(0,a.jsxs)(c.Zp,{children:[(0,a.jsx)(c.aR,{children:(0,a.jsx)(c.ZB,{children:"Basic Information"})}),(0,a.jsxs)(c.Wu,{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-earth-700 mb-2",children:"Farm Name *"}),(0,a.jsx)(l.p,{id:"name",type:"text",value:E.name,onChange:e=>z("name",e.target.value),placeholder:"Enter your farm name",required:!0})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"location",className:"block text-sm font-medium text-earth-700 mb-2",children:"Location/City *"}),(0,a.jsx)(l.p,{id:"location",type:"text",value:E.location,onChange:e=>z("location",e.target.value),placeholder:"e.g., Lephalale",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"province",className:"block text-sm font-medium text-earth-700 mb-2",children:"Province *"}),(0,a.jsxs)("select",{id:"province",value:E.province,onChange:e=>z("province",e.target.value),className:"w-full px-3 py-2 border border-earth-300 rounded-md focus:outline-none focus:ring-2 focus:ring-accent-600 focus:border-transparent",required:!0,children:[(0,a.jsx)("option",{value:"",children:"Select Province"}),m.L0.map(e=>(0,a.jsx)("option",{value:e,children:e},e))]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"sizeHectares",className:"block text-sm font-medium text-earth-700 mb-2",children:"Farm Size (Hectares)"}),(0,a.jsx)(l.p,{id:"sizeHectares",type:"number",value:E.sizeHectares,onChange:e=>z("sizeHectares",e.target.value),placeholder:"e.g., 5000"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"activityTypes",className:"block text-sm font-medium text-earth-700 mb-2",children:"Activity Types *"}),(0,a.jsxs)("select",{id:"activityTypes",value:E.activityTypes,onChange:e=>z("activityTypes",e.target.value),className:"w-full px-3 py-2 border border-earth-300 rounded-md focus:outline-none focus:ring-2 focus:ring-accent-600 focus:border-transparent",required:!0,children:[(0,a.jsx)("option",{value:"hunting",children:"Hunting Only"}),(0,a.jsx)("option",{value:"photo_safari",children:"Photo Safari Only"}),(0,a.jsx)("option",{value:"both",children:"Both Hunting & Photo Safari"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"description",className:"block text-sm font-medium text-earth-700 mb-2",children:"Description"}),(0,a.jsx)("textarea",{id:"description",value:E.description,onChange:e=>z("description",e.target.value),placeholder:"Describe your farm, facilities, and what makes it special...",rows:4,className:"w-full px-3 py-2 border border-earth-300 rounded-md focus:outline-none focus:ring-2 focus:ring-accent-600 focus:border-transparent"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"pricingInfo",className:"block text-sm font-medium text-earth-700 mb-2",children:"Pricing Information"}),(0,a.jsx)(l.p,{id:"pricingInfo",type:"text",value:E.pricingInfo,onChange:e=>z("pricingInfo",e.target.value),placeholder:"e.g., From R2,500 per day"})]})]})]}),(0,a.jsxs)(c.Zp,{children:[(0,a.jsx)(c.aR,{children:(0,a.jsx)(c.ZB,{children:"Contact Information"})}),(0,a.jsxs)(c.Wu,{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"contactEmail",className:"block text-sm font-medium text-earth-700 mb-2",children:"Contact Email *"}),(0,a.jsx)(l.p,{id:"contactEmail",type:"email",value:E.contactEmail,onChange:e=>z("contactEmail",e.target.value),placeholder:"<EMAIL>",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"contactPhone",className:"block text-sm font-medium text-earth-700 mb-2",children:"Contact Phone"}),(0,a.jsx)(l.p,{id:"contactPhone",type:"tel",value:E.contactPhone,onChange:e=>z("contactPhone",e.target.value),placeholder:"+27 12 345 6789"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"websiteUrl",className:"block text-sm font-medium text-earth-700 mb-2",children:"Website URL"}),(0,a.jsx)(l.p,{id:"websiteUrl",type:"text",value:E.websiteUrl,onChange:e=>z("websiteUrl",e.target.value),placeholder:"www.yourfarm.com"})]})]})]}),(0,a.jsxs)(c.Zp,{children:[(0,a.jsx)(c.aR,{children:(0,a.jsx)(c.ZB,{children:"Farm Settings"})}),(0,a.jsxs)(c.Wu,{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-earth-700",children:"Active Status"}),(0,a.jsx)("p",{className:"text-sm text-earth-600",children:"Control whether your farm is visible to customers"})]}),(0,a.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:E.isActive,onChange:e=>z("isActive",e.target.checked),className:"sr-only peer"}),(0,a.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-accent-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-accent-600"})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-earth-700",children:"Featured Listing"}),(0,a.jsx)("p",{className:"text-sm text-earth-600",children:"Featured farms appear at the top of search results"})]}),(0,a.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:E.featured,onChange:e=>z("featured",e.target.checked),className:"sr-only peer"}),(0,a.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-accent-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-accent-600"})]})]})]})]}),(0,a.jsxs)("div",{className:"flex justify-end gap-4",children:[(0,a.jsx)(v(),{href:"/dashboard",children:(0,a.jsx)(n.$,{variant:"outline",type:"button",children:"Cancel"})}),(0,a.jsx)(n.$,{type:"submit",variant:"primary",disabled:N,className:"min-w-[120px]",children:N?(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Saving..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(x,{className:"w-4 h-4 mr-2"}),"Save Changes"]})})]})]})]})}):(r.push("/auth/login"),null)}},35169:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},35695:(e,t,r)=>{"use strict";var a=r(18999);r.o(a,"useParams")&&r.d(t,{useParams:function(){return a.useParams}}),r.o(a,"useRouter")&&r.d(t,{useRouter:function(){return a.useRouter}}),r.o(a,"useSearchParams")&&r.d(t,{useSearchParams:function(){return a.useSearchParams}})},52814:(e,t,r)=>{"use strict";r.d(t,{E:()=>n});var a=r(95155),s=r(12115),i=r(59434);let n=(0,s.forwardRef)((e,t)=>{let{className:r,variant:s="default",...n}=e;return(0,a.jsx)("span",{ref:t,className:(0,i.cn)("inline-flex items-center px-2 py-1 rounded-[var(--radius-sm)] text-sm font-semibold",{hunting:"bg-[var(--hunting-accent)] text-white",photo:"bg-[var(--photo-accent)] text-white",location:"bg-[var(--secondary-stone)] text-white",default:"bg-[var(--medium-gray)] text-white"}[s],r),...n})});n.displayName="Badge"},66766:(e,t,r)=>{"use strict";r.d(t,{default:()=>s.a});var a=r(71469),s=r.n(a)},71469:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return c},getImageProps:function(){return l}});let a=r(88229),s=r(38883),i=r(33063),n=a._(r(51193));function l(e){let{props:t}=(0,s.getImgProps)(e,{defaultLoader:n.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,r]of Object.entries(t))void 0===r&&delete t[e];return{props:t}}let c=i.Image},79298:(e,t,r)=>{Promise.resolve().then(r.bind(r,30884))},91950:(e,t,r)=>{"use strict";r.d(t,{L0:()=>a,QC:()=>i,lc:()=>s});let a=["Eastern Cape","Free State","Gauteng","KwaZulu-Natal","Limpopo","Mpumalanga","Northern Cape","North West","Western Cape"],s=["Luxury Lodge","Basic Accommodation","Restaurant","Bar","Swimming Pool","Spa","WiFi","Airport Transfer","Professional Guide","Trophy Preparation","Taxidermy"],i=["Lion","Leopard","Elephant","Buffalo","Rhino","Kudu","Impala","Springbok","Eland","Sable","Gemsbok","Waterbuck","Bushbuck","Warthog"]},92657:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},93915:(e,t,r)=>{"use strict";r.d(t,{p:()=>n});var a=r(95155),s=r(12115),i=r(59434);let n=(0,s.forwardRef)((e,t)=>{let{className:r,type:s,label:n,error:l,...c}=e;return(0,a.jsxs)("div",{className:"w-full",children:[n&&(0,a.jsx)("label",{className:"block mb-2 font-semibold text-[var(--primary-brown)] font-[var(--font-ui)]",children:n}),(0,a.jsx)("input",{type:s,className:(0,i.cn)("\n            w-full px-4 py-2 border-2 border-[var(--medium-gray)] \n            rounded-[var(--radius-md)] font-[var(--font-ui)] \n            transition-colors duration-300\n            focus:outline-none focus:border-[var(--primary-brown)] \n            focus:ring-0 focus:shadow-[0_0_0_3px_rgba(139,69,19,0.1)]\n            placeholder:text-[var(--medium-gray)]\n            ",l&&"border-red-500 focus:border-red-500",r),ref:t,...c}),l&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600",children:l})]})});n.displayName="Input"}},e=>{var t=t=>e(e.s=t);e.O(0,[992,965,277,288,63,874,228,441,684,358],()=>t(79298)),_N_E=e.O()}]);