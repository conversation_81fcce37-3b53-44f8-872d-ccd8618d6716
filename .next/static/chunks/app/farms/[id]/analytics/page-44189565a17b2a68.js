(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[673],{75167:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>z});var a=s(95155),l=s(12115),r=s(35695),n=s(13741),i=s(17703),c=s(28666),d=s(84105),o=s(35169),x=s(92657),h=s(69074),m=s(55868),u=s(33109),j=s(38564),g=s(83540),p=s(93504),f=s(94754),v=s(96025),b=s(16238),y=s(94517),N=s(21374),w=s(3401),k=s(83394),A=s(8782),B=s(34e3),D=s(54811),R=s(6874),S=s.n(R),Z=s(59434);let E={primary:"#A0522D",hunting:"#8B4513",photo:"#228B22",pending:"#FFA500",confirmed:"#32CD32",completed:"#4169E1",cancelled:"#DC143C"};function z(){let{user:e,loading:t}=(0,d.As)(),s=(0,r.useRouter)(),R=(0,r.useParams)().id,[z,F]=(0,l.useState)(null),[C,W]=(0,l.useState)(null),[T,L]=(0,l.useState)(!0),[P,K]=(0,l.useState)(null),[_,M]=(0,l.useState)("90d"),V=(0,l.useCallback)(async()=>{try{L(!0),K(null);let t=await c.eg.get(R);if(!t)throw Error("Farm not found");if(t.ownerId!==e.uid)throw Error("Access denied - you do not own this farm");F(t);let s=new Date,a=new Date;switch(_){case"30d":a.setDate(s.getDate()-30);break;case"90d":a.setDate(s.getDate()-90);break;case"1y":a.setFullYear(s.getFullYear()-1);break;case"all":a=new Date("2020-01-01")}let l=(await c.zi.getAll({farmId:R})).filter(e=>e.createdAt.toDate()>=a),r=await c.C2.getByFarm(R),n=l||[],i=r||[],d=n.length,o=n.filter(e=>"completed"===e.status).reduce((e,t)=>e+(t.totalPrice||0),0),x=i.length>0?i.reduce((e,t)=>e+t.rating,0)/i.length:0,h=new Map;n.forEach(e=>{let t=e.createdAt.toDate().toLocaleDateString("en-US",{year:"numeric",month:"short"}),s=h.get(t)||{bookings:0,revenue:0};h.set(t,{bookings:s.bookings+1,revenue:s.revenue+("completed"===e.status&&e.totalPrice||0)})});let m=Array.from(h.entries()).map(e=>{let[t,s]=e;return{month:t,...s}}).sort((e,t)=>new Date(e.month).getTime()-new Date(t.month).getTime()),u=new Map;n.forEach(e=>{u.set(e.status,(u.get(e.status)||0)+1)});let j=Array.from(u.entries()).map(e=>{let[t,s]=e;return{status:t.charAt(0).toUpperCase()+t.slice(1),count:s,color:E[t]||E.primary}}),g=new Map;n.forEach(e=>{let t="hunting"===e.activityType?"Hunting":"Photo Safari";g.set(t,(g.get(t)||0)+1)});let p=Array.from(g.entries()).map(e=>{let[t,s]=e;return{activity:t,count:s,color:"Hunting"===t?E.hunting:E.photo}});W({totalBookings:d,totalRevenue:o,averageBookingValue:d>0?o/d:0,totalViews:0,conversionRate:0,averageRating:x,reviewCount:i.length,bookingsByMonth:m,bookingsByStatus:j,activityBreakdown:p})}catch(e){console.error("Error fetching analytics:",e),K("Failed to load analytics data")}finally{L(!1)}},[R,e,_]);return((0,l.useEffect)(()=>{!t&&e&&V()},[t,e,V]),t||T)?(0,a.jsx)("div",{className:"min-h-screen bg-earth-100 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-accent-600 mx-auto"}),(0,a.jsx)("p",{className:"mt-4 text-earth-600",children:"Loading analytics..."})]})}):e?P&&!z?(0,a.jsx)("div",{className:"min-h-screen bg-earth-100 py-8",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-earth-900 mb-4",children:"Analytics Not Available"}),(0,a.jsx)("p",{className:"text-earth-600 mb-6",children:P}),(0,a.jsx)(S(),{href:"/dashboard",children:(0,a.jsxs)(n.$,{variant:"primary",children:[(0,a.jsx)(o.A,{className:"w-4 h-4 mr-2"}),"Back to Dashboard"]})})]})})}):(0,a.jsx)("div",{className:"min-h-screen bg-earth-100 py-8",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsxs)("div",{className:"flex items-center gap-4 mb-4",children:[(0,a.jsx)(S(),{href:"/dashboard",children:(0,a.jsxs)(n.$,{variant:"outline",size:"sm",children:[(0,a.jsx)(o.A,{className:"w-4 h-4 mr-2"}),"Back to Dashboard"]})}),(0,a.jsx)(S(),{href:"/farms/".concat(R),children:(0,a.jsxs)(n.$,{variant:"outline",size:"sm",children:[(0,a.jsx)(x.A,{className:"w-4 h-4 mr-2"}),"View Public Page"]})}),(0,a.jsx)(S(),{href:"/farms/".concat(R,"/edit"),children:(0,a.jsx)(n.$,{variant:"outline",size:"sm",children:"Edit Farm"})})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-earth-900",children:"Farm Analytics"}),(0,a.jsxs)("p",{className:"text-earth-600 mt-2",children:[null==z?void 0:z.name," - Performance insights and metrics"]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("span",{className:"text-sm text-earth-600",children:"Time Range:"}),(0,a.jsxs)("select",{value:_,onChange:e=>M(e.target.value),className:"px-3 py-2 border border-earth-300 rounded-md focus:outline-none focus:ring-2 focus:ring-accent-600 focus:border-transparent",children:[(0,a.jsx)("option",{value:"30d",children:"Last 30 Days"}),(0,a.jsx)("option",{value:"90d",children:"Last 90 Days"}),(0,a.jsx)("option",{value:"1y",children:"Last Year"}),(0,a.jsx)("option",{value:"all",children:"All Time"})]})]})]})]}),C&&(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,a.jsx)(i.Zp,{children:(0,a.jsx)(i.Wu,{className:"text-center py-6",children:(0,a.jsxs)("div",{className:"flex flex-col items-center",children:[(0,a.jsx)(h.A,{className:"text-accent-600 mb-2",size:24}),(0,a.jsx)("div",{className:"text-3xl font-bold text-accent-600 mb-1",children:C.totalBookings}),(0,a.jsx)("p",{className:"text-earth-600 text-sm",children:"Total Bookings"})]})})}),(0,a.jsx)(i.Zp,{children:(0,a.jsx)(i.Wu,{className:"text-center py-6",children:(0,a.jsxs)("div",{className:"flex flex-col items-center",children:[(0,a.jsx)(m.A,{className:"text-hunting-accent mb-2",size:24}),(0,a.jsxs)("div",{className:"text-3xl font-bold text-hunting-accent mb-1",children:["R",(0,Z.Z)(C.totalRevenue)]}),(0,a.jsx)("p",{className:"text-earth-600 text-sm",children:"Total Revenue"})]})})}),(0,a.jsx)(i.Zp,{children:(0,a.jsx)(i.Wu,{className:"text-center py-6",children:(0,a.jsxs)("div",{className:"flex flex-col items-center",children:[(0,a.jsx)(u.A,{className:"text-photo-accent mb-2",size:24}),(0,a.jsxs)("div",{className:"text-3xl font-bold text-photo-accent mb-1",children:["R",(0,Z.Z)(C.averageBookingValue)]}),(0,a.jsx)("p",{className:"text-earth-600 text-sm",children:"Avg. Booking Value"})]})})}),(0,a.jsx)(i.Zp,{children:(0,a.jsx)(i.Wu,{className:"text-center py-6",children:(0,a.jsxs)("div",{className:"flex flex-col items-center",children:[(0,a.jsx)(j.A,{className:"text-accent-600 mb-2",size:24}),(0,a.jsx)("div",{className:"text-3xl font-bold text-accent-600 mb-1",children:C.averageRating>0?C.averageRating.toFixed(1):"N/A"}),(0,a.jsxs)("p",{className:"text-earth-600 text-sm",children:["Avg. Rating (",C.reviewCount," reviews)"]})]})})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)(i.Zp,{children:[(0,a.jsx)(i.aR,{children:(0,a.jsx)(i.ZB,{children:"Bookings Over Time"})}),(0,a.jsx)(i.Wu,{children:(0,a.jsx)(g.u,{width:"100%",height:300,children:(0,a.jsxs)(p.b,{data:C.bookingsByMonth,children:[(0,a.jsx)(f.d,{strokeDasharray:"3 3"}),(0,a.jsx)(v.W,{dataKey:"month",tick:{fontSize:12}}),(0,a.jsx)(b.h,{tick:{fontSize:12}}),(0,a.jsx)(y.m,{}),(0,a.jsx)(N.N,{type:"monotone",dataKey:"bookings",stroke:E.primary,strokeWidth:2,dot:{fill:E.primary}})]})})})]}),(0,a.jsxs)(i.Zp,{children:[(0,a.jsx)(i.aR,{children:(0,a.jsx)(i.ZB,{children:"Revenue Over Time"})}),(0,a.jsx)(i.Wu,{children:(0,a.jsx)(g.u,{width:"100%",height:300,children:(0,a.jsxs)(w.E,{data:C.bookingsByMonth,children:[(0,a.jsx)(f.d,{strokeDasharray:"3 3"}),(0,a.jsx)(v.W,{dataKey:"month",tick:{fontSize:12}}),(0,a.jsx)(b.h,{tick:{fontSize:12}}),(0,a.jsx)(y.m,{formatter:e=>"R".concat(Number(e).toLocaleString())}),(0,a.jsx)(k.y,{dataKey:"revenue",fill:E.hunting,radius:[4,4,0,0]})]})})})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)(i.Zp,{children:[(0,a.jsx)(i.aR,{children:(0,a.jsx)(i.ZB,{children:"Booking Status Distribution"})}),(0,a.jsx)(i.Wu,{children:(0,a.jsx)(g.u,{width:"100%",height:300,children:(0,a.jsxs)(A.r,{children:[(0,a.jsx)(B.F,{data:C.bookingsByStatus,cx:"50%",cy:"50%",labelLine:!1,label:e=>{let{status:t,count:s}=e;return"".concat(t,": ").concat(s)},outerRadius:80,fill:"#8884d8",dataKey:"count",children:C.bookingsByStatus.map((e,t)=>(0,a.jsx)(D.f,{fill:e.color},"cell-".concat(t)))}),(0,a.jsx)(y.m,{})]})})})]}),(0,a.jsxs)(i.Zp,{children:[(0,a.jsx)(i.aR,{children:(0,a.jsx)(i.ZB,{children:"Activity Type Breakdown"})}),(0,a.jsx)(i.Wu,{children:(0,a.jsx)(g.u,{width:"100%",height:300,children:(0,a.jsxs)(A.r,{children:[(0,a.jsx)(B.F,{data:C.activityBreakdown,cx:"50%",cy:"50%",labelLine:!1,label:e=>{let{activity:t,count:s}=e;return"".concat(t,": ").concat(s)},outerRadius:80,fill:"#8884d8",dataKey:"count",children:C.activityBreakdown.map((e,t)=>(0,a.jsx)(D.f,{fill:e.color},"cell-".concat(t)))}),(0,a.jsx)(y.m,{})]})})})]})]})]})]})}):(s.push("/auth/login"),null)}},91467:(e,t,s)=>{Promise.resolve().then(s.bind(s,75167))}},e=>{var t=t=>e(e.s=t);e.O(0,[992,965,277,288,63,874,871,228,441,684,358],()=>t(91467)),_N_E=e.O()}]);