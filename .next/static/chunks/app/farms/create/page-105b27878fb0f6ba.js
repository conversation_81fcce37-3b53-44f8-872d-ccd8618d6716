(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1,228],{13741:(e,r,t)=>{"use strict";t.d(r,{$:()=>o});var a=t(95155),s=t(12115),n=t(59434);let o=(0,s.forwardRef)((e,r)=>{let{className:t,variant:s="primary",size:o="md",isLoading:i,children:l,disabled:c,...d}=e;return(0,a.jsxs)("button",{className:(0,n.cn)("\n      inline-flex items-center justify-center rounded-[var(--radius-md)] \n      font-[var(--font-ui)] font-semibold transition-all duration-300 \n      focus:outline-none focus:ring-2 focus:ring-offset-2\n      disabled:opacity-50 disabled:cursor-not-allowed\n      hover:transform hover:-translate-y-0.5 hover:shadow-[var(--shadow-lg)]\n    ",{primary:"\n        bg-[var(--primary-brown)] text-white \n        hover:bg-[#A0522D] focus:ring-[var(--primary-brown)]\n      ",secondary:"\n        bg-[var(--secondary-sky)] text-white \n        hover:bg-[#357AE0] focus:ring-[var(--secondary-sky)]\n      ",outline:"\n        bg-transparent border-2 border-[var(--primary-brown)] text-[var(--primary-brown)]\n        hover:bg-[var(--primary-brown)] hover:text-white focus:ring-[var(--primary-brown)]\n      ",hunting:"\n        bg-[var(--hunting-accent)] text-white \n        hover:bg-[#A67C00] focus:ring-[var(--hunting-accent)]\n      ",photo:"\n        bg-[var(--photo-accent)] text-white \n        hover:bg-[#3557C7] focus:ring-[var(--photo-accent)]\n      "}[s],{sm:"px-3 py-1.5 text-sm",md:"px-6 py-2 text-base",lg:"px-8 py-3 text-lg"}[o],i&&"cursor-wait",t),disabled:c||i,ref:r,...d,children:[i&&(0,a.jsxs)("svg",{className:"mr-2 h-4 w-4 animate-spin",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),l]})});o.displayName="Button"},17703:(e,r,t)=>{"use strict";t.d(r,{MH:()=>u,Wu:()=>l,ZB:()=>d,Zp:()=>i,aR:()=>c});var a=t(95155),s=t(12115),n=t(66766),o=t(59434);let i=(0,s.forwardRef)((e,r)=>{let{className:t,hover:s=!0,children:n,...i}=e;return(0,a.jsx)("div",{ref:r,className:(0,o.cn)("\n          bg-white rounded-[var(--radius-lg)] shadow-[var(--shadow-md)] \n          overflow-hidden transition-all duration-300\n          ",s&&"hover:transform hover:-translate-y-1 hover:shadow-[var(--shadow-xl)]",t),...i,children:n})});i.displayName="Card";let l=(0,s.forwardRef)((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("div",{ref:r,className:(0,o.cn)("p-[var(--space-lg)]",t),...s})});l.displayName="CardContent";let c=(0,s.forwardRef)((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("div",{ref:r,className:(0,o.cn)("px-[var(--space-lg)] pt-[var(--space-lg)]",t),...s})});c.displayName="CardHeader";let d=(0,s.forwardRef)((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("h3",{ref:r,className:(0,o.cn)("text-xl font-bold mb-2 text-[var(--primary-brown)] font-[var(--font-display)]",t),...s})});d.displayName="CardTitle";let u=(0,s.forwardRef)((e,r)=>{let{className:t,src:s,alt:i,children:l,...c}=e;return(0,a.jsx)("div",{ref:r,className:(0,o.cn)("relative w-full h-48 bg-gradient-to-br from-[var(--primary-green)] to-[var(--secondary-sunset)] flex items-center justify-center text-white text-5xl",t),...c,children:s?(0,a.jsx)(n.default,{src:s,alt:i||"",fill:!0,className:"object-cover"}):l})});u.displayName="CardImage"},28214:(e,r,t)=>{"use strict";t.d(r,{IG:()=>a.IG,db:()=>a.db,j2:()=>a.j2});var a=t(67039)},28666:(e,r,t)=>{"use strict";t.d(r,{C2:()=>c,QQ:()=>o,eg:()=>i,zi:()=>l});var a=t(35317),s=t(28214);function n(e){var r,t,a,s;if(!e.exists())return null;let n=e.data();return{id:e.id,...n,createdAt:(null==n||null==(t=n.createdAt)||null==(r=t.toDate)?void 0:r.call(t))||(null==n?void 0:n.createdAt),updatedAt:(null==n||null==(s=n.updatedAt)||null==(a=s.toDate)?void 0:a.call(s))||(null==n?void 0:n.updatedAt)}}let o={async get(e){let r=(0,a.H9)(s.db,"users",e);return n(await (0,a.x7)(r))},async create(e,r){let t=(0,a.H9)(s.db,"users",e),n=new Date;await (0,a.BN)(t,{...r,createdAt:a.Dc.fromDate(n),updatedAt:a.Dc.fromDate(n)})},async update(e,r){let t=(0,a.H9)(s.db,"users",e);await (0,a.mZ)(t,{...r,updatedAt:a.Dc.now()})}},i={async getAll(e){let r=(0,a.rJ)(s.db,"farms");return(null==e?void 0:e.isActive)!==void 0&&(r=(0,a.P)(r,(0,a._M)("isActive","==",e.isActive))),(null==e?void 0:e.featured)!==void 0&&(r=(0,a.P)(r,(0,a._M)("featured","==",e.featured))),(null==e?void 0:e.province)&&(r=(0,a.P)(r,(0,a._M)("province","==",e.province))),(null==e?void 0:e.activityType)&&(r=(0,a.P)(r,(0,a._M)("activityTypes","==",e.activityType))),r=(0,a.P)(r,(0,a.My)("createdAt","desc")),(null==e?void 0:e.limit)&&(r=(0,a.P)(r,(0,a.AB)(e.limit))),(await (0,a.GG)(r)).docs.map(e=>n(e)).filter(Boolean)},async get(e){let r=(0,a.H9)(s.db,"farms",e);return n(await (0,a.x7)(r))},async getByOwner(e){let r=(0,a.P)((0,a.rJ)(s.db,"farms"),(0,a._M)("ownerId","==",e),(0,a.My)("createdAt","desc"));return(await (0,a.GG)(r)).docs.map(e=>n(e)).filter(Boolean)},async getActive(e){let r=(0,a.P)((0,a.rJ)(s.db,"farms"),(0,a._M)("isActive","==",!0),(0,a.My)("createdAt","desc"));return e&&(r=(0,a.P)(r,(0,a.AB)(e))),(await (0,a.GG)(r)).docs.map(e=>n(e)).filter(Boolean)},async create(e){let r=new Date;return(await (0,a.gS)((0,a.rJ)(s.db,"farms"),{...e,createdAt:a.Dc.fromDate(r),updatedAt:a.Dc.fromDate(r)})).id},async update(e,r){let t=(0,a.H9)(s.db,"farms",e);await (0,a.mZ)(t,{...r,updatedAt:a.Dc.now()})},async delete(e){let r=(0,a.H9)(s.db,"farms",e);await (0,a.kd)(r)},async addImage(e,r){let t=new Date;return(await (0,a.gS)((0,a.rJ)(s.db,"farms",e,"images"),{...r,createdAt:a.Dc.fromDate(t),updatedAt:a.Dc.fromDate(t)})).id},async getImages(e){let r=(0,a.P)((0,a.rJ)(s.db,"farms",e,"images"),(0,a.My)("displayOrder","asc"),(0,a.My)("createdAt","asc"));return(await (0,a.GG)(r)).docs.map(e=>n(e)).filter(Boolean)},async deleteImage(e,r){let t=(0,a.H9)(s.db,"farms",e,"images",r);await (0,a.kd)(t)}},l={async getAll(e){let r=(0,a.rJ)(s.db,"bookings");return(null==e?void 0:e.hunterId)&&(r=(0,a.P)(r,(0,a._M)("hunterId","==",e.hunterId))),(null==e?void 0:e.farmId)&&(r=(0,a.P)(r,(0,a._M)("farmId","==",e.farmId))),(null==e?void 0:e.status)&&(r=(0,a.P)(r,(0,a._M)("status","==",e.status))),r=(0,a.P)(r,(0,a.My)("createdAt","desc")),(null==e?void 0:e.limit)&&(r=(0,a.P)(r,(0,a.AB)(e.limit))),(await (0,a.GG)(r)).docs.map(e=>n(e)).filter(Boolean)},async get(e){let r=(0,a.H9)(s.db,"bookings",e);return n(await (0,a.x7)(r))},async create(e){let r=new Date,t="BVR-".concat(r.getFullYear()).concat((r.getMonth()+1).toString().padStart(2,"0")).concat(r.getDate().toString().padStart(2,"0"),"-").concat(Math.random().toString(36).substring(2,8).toUpperCase());return(await (0,a.gS)((0,a.rJ)(s.db,"bookings"),{...e,bookingReference:t,createdAt:a.Dc.fromDate(r),updatedAt:a.Dc.fromDate(r)})).id},async update(e,r){let t=(0,a.H9)(s.db,"bookings",e);await (0,a.mZ)(t,{...r,updatedAt:a.Dc.now()})}},c={async getByFarm(e){let r=(0,a.P)((0,a.rJ)(s.db,"farms",e,"reviews"),(0,a._M)("isPublic","==",!0),(0,a.My)("createdAt","desc"));return(await (0,a.GG)(r)).docs.map(e=>n(e)).filter(Boolean)},async create(e,r){let t=new Date;return(await (0,a.gS)((0,a.rJ)(s.db,"farms",e,"reviews"),{...r,createdAt:a.Dc.fromDate(t),updatedAt:a.Dc.fromDate(t)})).id},async update(e,r,t){let n=(0,a.H9)(s.db,"farms",e,"reviews",r);await (0,a.mZ)(n,{...t,updatedAt:a.Dc.now()})}}},35072:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>j});var a=t(95155),s=t(12115),n=t(35695),o=t(66766),i=t(84105),l=t(13741),c=t(93915),d=t(17703),u=t(90858),m=t(28214),h=t(67039);async function f(e,r,t,a,s){var n,o,i,l;try{let l=h.j2.currentUser;if(!l)throw Error("User must be authenticated to upload files");let c=await l.getIdToken(),d=new FormData;d.append("file",e),d.append("bucket",r),t&&d.append("farmId",t),a&&d.append("userId",a);let u=(()=>{if(!(null==s?void 0:s.onProgress))return;let r=0,t=setInterval(()=>{var a;if((r+=20*Math.random())>=90)return void clearInterval(t);null==(a=s.onProgress)||a.call(s,{bytesTransferred:Math.floor(r/100*e.size),totalBytes:e.size,progress:r})},200);return t})();try{let r=await fetch(function(){let e="rvbsafaris";if(!e)throw Error("NEXT_PUBLIC_FIREBASE_PROJECT_ID environment variable is required");return"https://us-central1-".concat(e,".cloudfunctions.net/uploadImage")}(),{method:"POST",headers:{Authorization:"Bearer ".concat(c)},body:d});if(u&&clearInterval(u),!r.ok){let e=await r.json().catch(()=>({}));throw Error(e.error||"Upload failed with status ".concat(r.status))}let t=await r.json();if(!t.success||!t.downloadURL)throw Error(t.error||"Upload failed");return null==s||null==(n=s.onProgress)||n.call(s,{bytesTransferred:e.size,totalBytes:e.size,progress:100}),null==s||null==(o=s.onComplete)||o.call(s,t.downloadURL),t.downloadURL}catch(r){u&&clearInterval(u);let e=r instanceof Error?r:Error("Upload failed");throw null==s||null==(i=s.onError)||i.call(s,e),e}}catch(r){console.error("Error uploading file via Cloud Function:",r);let e=r instanceof Error?r:Error("Upload failed");throw null==s||null==(l=s.onError)||l.call(s,e),e}}let p={async deleteFile(e){try{let r=(0,u.KR)(m.IG,e);await (0,u.XR)(r)}catch(e){throw console.error("Error deleting file:",e),e}},async getDownloadURL(e){try{let r=(0,u.KR)(m.IG,e);return await (0,u.qk)(r)}catch(e){throw console.error("Error getting download URL:",e),e}},async listFiles(e){try{let r=(0,u.KR)(m.IG,e);return(await (0,u._w)(r)).items}catch(e){throw console.error("Error listing files:",e),e}},async getMetadata(e){try{let r=(0,u.KR)(m.IG,e);return await (0,u.yb)(r)}catch(e){throw console.error("Error getting metadata:",e),e}},async updateMetadata(e,r){try{let t=(0,u.KR)(m.IG,e);return await (0,u.D5)(t,r)}catch(e){throw console.error("Error updating metadata:",e),e}}},g={uploadFarmImage:async(e,r,t)=>(console.log("Using Cloud Function for farm image upload"),await f(r,"farm-images",e,void 0,t)),async deleteFarmImage(e,r){try{let t=x(r);t&&t.startsWith("farm-images/".concat(e,"/"))&&await p.deleteFile(t)}catch(e){throw console.error("Error deleting farm image:",e),e}},async listFarmImages(e){try{let r=await p.listFiles("farm-images/".concat(e));return await Promise.all(r.map(e=>(0,u.qk)(e)))}catch(e){throw console.error("Error listing farm images:",e),e}}},v={uploadProfileImage:async(e,r,t)=>(console.log("Using Cloud Function for profile image upload"),await f(r,"profile-images",void 0,e,t)),async deleteProfileImage(e,r){try{let t=x(r);t&&t.startsWith("profile-images/".concat(e,"/"))&&await p.deleteFile(t)}catch(e){throw console.error("Error deleting profile image:",e),e}}};function x(e){try{let r=new URL(e);if("firebasestorage.googleapis.com"===r.hostname){let e=r.pathname.match(/\/o\/(.+)\?/);if(e)return decodeURIComponent(e[1])}return null}catch(e){return console.error("Error extracting path from URL:",e),null}}function b(e){let{currentImage:r,alt:t="Upload preview",onUpload:n,bucket:i,farmId:l,userId:c,maxSize:d=5,className:u=""}=e,[m,h]=(0,s.useState)(!1),[f,p]=(0,s.useState)(0),[x,b]=(0,s.useState)(null),[w,y]=(0,s.useState)(r||null),j=(0,s.useRef)(null),N=async e=>{var t;let a=null==(t=e.target.files)?void 0:t[0];if(!a)return;if(b(null),p(0),a.size>1024*d*1024)return void b("File size must be less than ".concat(d,"MB"));if(!a.type.startsWith("image/"))return void b("Please select an image file");if("farm-images"===i&&!l)return void b("Farm ID is required for farm image uploads");if("profile-images"===i&&!c)return void b("User ID is required for profile image uploads");let s=new FileReader;s.onload=e=>{var r;y(null==(r=e.target)?void 0:r.result)},s.readAsDataURL(a),h(!0);try{let e;if("farm-images"===i&&l)console.log("Starting farm image upload for farmId:",l),e=await g.uploadFarmImage(l,a,{onProgress:e=>{console.log("Upload progress:",e.progress),p(e.progress)},onError:e=>{console.error("Upload error:",e),b(e.message)}});else if("profile-images"===i&&c)console.log("Starting profile image upload for userId:",c),e=await v.uploadProfileImage(c,a,{onProgress:e=>{console.log("Upload progress:",e.progress),p(e.progress)},onError:e=>{console.error("Upload error:",e),b(e.message)}});else throw Error("Invalid bucket or missing required parameters");y(e),n(e),p(100)}catch(e){console.error("Upload error:",e),b(e instanceof Error?e.message:"Upload failed"),y(r||null)}finally{h(!1),setTimeout(()=>p(0),2e3)}};return(0,a.jsxs)("div",{className:u,children:[(0,a.jsx)("input",{ref:j,type:"file",accept:"image/*",onChange:N,className:"hidden"}),(0,a.jsx)("div",{onClick:()=>{var e;null==(e=j.current)||e.click()},className:"relative border-2 border-dashed border-earth-300 rounded-lg p-6 hover:border-accent-600 transition-colors cursor-pointer group",children:w?(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"relative w-full h-48 rounded-lg overflow-hidden",children:(0,a.jsx)(o.default,{src:w,alt:t,fill:!0,className:"object-cover",sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"})}),(0,a.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all rounded-lg flex items-center justify-center",children:(0,a.jsx)("div",{className:"opacity-0 group-hover:opacity-100 transition-opacity",children:(0,a.jsxs)("svg",{className:"w-8 h-8 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"}),(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 13a3 3 0 11-6 0 3 3 0 016 0z"})]})})}),m&&(0,a.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-50 rounded-lg flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center text-white",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-2"}),(0,a.jsxs)("p",{className:"text-sm",children:[Math.round(f),"%"]})]})})]}):(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("svg",{className:"mx-auto h-12 w-12 text-earth-400",stroke:"currentColor",fill:"none",viewBox:"0 0 48 48",children:(0,a.jsx)("path",{d:"M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"})}),(0,a.jsxs)("div",{className:"mt-4",children:[(0,a.jsx)("p",{className:"text-sm text-earth-600",children:m?"Uploading... ".concat(Math.round(f),"%"):"Click to upload an image"}),(0,a.jsxs)("p",{className:"text-xs text-earth-500 mt-1",children:["PNG, JPG, GIF up to ",d,"MB"]})]})]})}),x&&(0,a.jsx)("p",{className:"text-red-600 text-sm mt-2",children:x})]})}var w=t(28666),y=t(91950);function j(){let{user:e,loading:r}=(0,i.As)(),t=(0,n.useRouter)(),[u,m]=(0,s.useState)(!1),[h,f]=(0,s.useState)(null),[p,g]=(0,s.useState)([]),[v,x]=(0,s.useState)({name:"",description:"",descriptionAfrikaans:"",location:"",province:"",sizeHectares:"",activityTypes:"both",contactEmail:"",contactPhone:"",websiteUrl:"",rules:"",rulesAfrikaans:"",pricingInfo:""});(0,s.useEffect)(()=>{if(!r&&!e)return void t.push("/auth/login");(null==e?void 0:e.email)&&x(r=>({...r,contactEmail:e.email}))},[e,r,t]);let j=(e,r)=>{x(t=>({...t,[e]:r})),f(null)},N=e=>{g(r=>r.filter(r=>r!==e))},k=async r=>{r.preventDefault(),m(!0),f(null);try{if(!v.name||!v.location||!v.province||!v.contactEmail){f("Please fill in all required fields"),m(!1);return}if(v.websiteUrl&&v.websiteUrl.trim()&&!/^(https?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$/.test(v.websiteUrl.trim())){f("Please enter a valid website URL"),m(!1);return}let r={ownerId:e.uid,name:v.name,description:v.description||void 0,descriptionAfrikaans:v.descriptionAfrikaans||void 0,location:v.location,province:v.province,sizeHectares:v.sizeHectares?parseInt(v.sizeHectares):void 0,activityTypes:v.activityTypes,contactEmail:v.contactEmail,contactPhone:v.contactPhone||void 0,websiteUrl:v.websiteUrl||void 0,rules:v.rules||void 0,rulesAfrikaans:v.rulesAfrikaans||void 0,pricingInfo:v.pricingInfo||void 0,isActive:!0,featured:!1},a=await w.eg.create(r);p.length>0&&console.log("Farm created with ".concat(p.length," images uploaded to Firebase Storage")),t.push("/farms/".concat(a))}catch(e){f("An unexpected error occurred"),console.error("Farm creation error:",e)}finally{m(!1)}};return r?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-earth-100",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-accent-600"})}):e?(0,a.jsx)("div",{className:"min-h-screen bg-earth-100 py-8",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-earth-900",children:"Create Farm Listing"}),(0,a.jsx)("p",{className:"text-earth-600 mt-2",children:"Add your game farm to BvR Safaris and start receiving bookings"})]}),(0,a.jsxs)("form",{onSubmit:k,className:"space-y-8",children:[h&&(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-md p-3",children:(0,a.jsx)("p",{className:"text-red-600 text-sm",children:h})}),(0,a.jsxs)(d.Zp,{children:[(0,a.jsx)(d.aR,{children:(0,a.jsx)(d.ZB,{children:"Basic Information"})}),(0,a.jsxs)(d.Wu,{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-earth-700 mb-2",children:"Farm Name *"}),(0,a.jsx)(c.p,{id:"name",type:"text",value:v.name,onChange:e=>j("name",e.target.value),placeholder:"Enter your farm name",required:!0})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"location",className:"block text-sm font-medium text-earth-700 mb-2",children:"Location/City *"}),(0,a.jsx)(c.p,{id:"location",type:"text",value:v.location,onChange:e=>j("location",e.target.value),placeholder:"e.g., Lephalale",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"province",className:"block text-sm font-medium text-earth-700 mb-2",children:"Province *"}),(0,a.jsxs)("select",{id:"province",value:v.province,onChange:e=>j("province",e.target.value),className:"w-full px-3 py-2 border border-earth-300 rounded-md focus:outline-none focus:ring-2 focus:ring-accent-600 focus:border-transparent",required:!0,children:[(0,a.jsx)("option",{value:"",children:"Select Province"}),y.L0.map(e=>(0,a.jsx)("option",{value:e,children:e},e))]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"sizeHectares",className:"block text-sm font-medium text-earth-700 mb-2",children:"Size (Hectares)"}),(0,a.jsx)(c.p,{id:"sizeHectares",type:"number",value:v.sizeHectares,onChange:e=>j("sizeHectares",e.target.value),placeholder:"e.g., 5000"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"activityTypes",className:"block text-sm font-medium text-earth-700 mb-2",children:"Activity Types *"}),(0,a.jsxs)("select",{id:"activityTypes",value:v.activityTypes,onChange:e=>j("activityTypes",e.target.value),className:"w-full px-3 py-2 border border-earth-300 rounded-md focus:outline-none focus:ring-2 focus:ring-accent-600 focus:border-transparent",required:!0,children:[(0,a.jsx)("option",{value:"hunting",children:"Hunting Only"}),(0,a.jsx)("option",{value:"photo_safari",children:"Photo Safari Only"}),(0,a.jsx)("option",{value:"both",children:"Both Hunting & Photo Safari"})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"description",className:"block text-sm font-medium text-earth-700 mb-2",children:"Description (English)"}),(0,a.jsx)("textarea",{id:"description",value:v.description,onChange:e=>j("description",e.target.value),placeholder:"Describe your farm, facilities, and what makes it special...",rows:4,className:"w-full px-3 py-2 border border-earth-300 rounded-md focus:outline-none focus:ring-2 focus:ring-accent-600 focus:border-transparent"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"descriptionAfrikaans",className:"block text-sm font-medium text-earth-700 mb-2",children:"Description (Afrikaans)"}),(0,a.jsx)("textarea",{id:"descriptionAfrikaans",value:v.descriptionAfrikaans,onChange:e=>j("descriptionAfrikaans",e.target.value),placeholder:"Beskryf jou plaas, fasiliteite, en wat dit spesiaal maak...",rows:4,className:"w-full px-3 py-2 border border-earth-300 rounded-md focus:outline-none focus:ring-2 focus:ring-accent-600 focus:border-transparent"})]})]})]}),(0,a.jsxs)(d.Zp,{children:[(0,a.jsx)(d.aR,{children:(0,a.jsx)(d.ZB,{children:"Contact Information"})}),(0,a.jsxs)(d.Wu,{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"contactEmail",className:"block text-sm font-medium text-earth-700 mb-2",children:"Contact Email *"}),(0,a.jsx)(c.p,{id:"contactEmail",type:"email",value:v.contactEmail,onChange:e=>j("contactEmail",e.target.value),placeholder:"<EMAIL>",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"contactPhone",className:"block text-sm font-medium text-earth-700 mb-2",children:"Contact Phone"}),(0,a.jsx)(c.p,{id:"contactPhone",type:"tel",value:v.contactPhone,onChange:e=>j("contactPhone",e.target.value),placeholder:"+27 12 345 6789"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"websiteUrl",className:"block text-sm font-medium text-earth-700 mb-2",children:"Website URL"}),(0,a.jsx)(c.p,{id:"websiteUrl",type:"text",value:v.websiteUrl,onChange:e=>j("websiteUrl",e.target.value),placeholder:"www.yourfarm.com"})]})]})]}),(0,a.jsxs)(d.Zp,{children:[(0,a.jsx)(d.aR,{children:(0,a.jsx)(d.ZB,{children:"Additional Information"})}),(0,a.jsxs)(d.Wu,{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"pricingInfo",className:"block text-sm font-medium text-earth-700 mb-2",children:"Pricing Information"}),(0,a.jsx)("textarea",{id:"pricingInfo",value:v.pricingInfo,onChange:e=>j("pricingInfo",e.target.value),placeholder:"Provide general pricing information or mention 'Contact for pricing'...",rows:3,className:"w-full px-3 py-2 border border-earth-300 rounded-md focus:outline-none focus:ring-2 focus:ring-accent-600 focus:border-transparent"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"rules",className:"block text-sm font-medium text-earth-700 mb-2",children:"Rules & Regulations (English)"}),(0,a.jsx)("textarea",{id:"rules",value:v.rules,onChange:e=>j("rules",e.target.value),placeholder:"List important rules, safety requirements, what to bring, etc...",rows:4,className:"w-full px-3 py-2 border border-earth-300 rounded-md focus:outline-none focus:ring-2 focus:ring-accent-600 focus:border-transparent"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"rulesAfrikaans",className:"block text-sm font-medium text-earth-700 mb-2",children:"Rules & Regulations (Afrikaans)"}),(0,a.jsx)("textarea",{id:"rulesAfrikaans",value:v.rulesAfrikaans,onChange:e=>j("rulesAfrikaans",e.target.value),placeholder:"Lys belangrike re\xebls, veiligheidsvereistes, wat om te bring, ens...",rows:4,className:"w-full px-3 py-2 border border-earth-300 rounded-md focus:outline-none focus:ring-2 focus:ring-accent-600 focus:border-transparent"})]})]})]}),(0,a.jsxs)(d.Zp,{children:[(0,a.jsx)(d.aR,{children:(0,a.jsx)(d.ZB,{children:"Farm Images"})}),(0,a.jsx)(d.Wu,{className:"space-y-6",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-earth-700 mb-4",children:"Upload Farm Photos"}),(0,a.jsx)("p",{className:"text-sm text-earth-600 mb-4",children:"Add photos of your farm, facilities, wildlife, and accommodations. The first image will be used as the main photo."}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[p.map((e,r)=>(0,a.jsxs)("div",{className:"relative group",children:[(0,a.jsx)("div",{className:"relative w-full h-48 rounded-lg border overflow-hidden",children:(0,a.jsx)(o.default,{src:e,alt:"Farm image ".concat(r+1),fill:!0,className:"object-cover",sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"})}),(0,a.jsx)("div",{className:"absolute top-2 left-2",children:0===r&&(0,a.jsx)("span",{className:"bg-accent-600 text-white text-xs px-2 py-1 rounded",children:"Main Photo"})}),(0,a.jsx)("button",{type:"button",onClick:()=>N(e),className:"absolute top-2 right-2 bg-red-600 text-white rounded-full w-6 h-6 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity",children:"\xd7"})]},e)),p.length<10&&e&&(0,a.jsx)(b,{bucket:"farm-images",farmId:e.uid,onUpload:e=>{g(r=>[...r,e])},maxSize:10,className:"h-48"})]}),p.length>=10&&(0,a.jsx)("p",{className:"text-sm text-earth-500 mt-2",children:"Maximum of 10 images allowed. Remove an image to add more."})]})})]}),(0,a.jsxs)("div",{className:"flex gap-4",children:[(0,a.jsx)(l.$,{type:"submit",variant:"primary",isLoading:u,className:"flex-1",children:u?"Creating Farm...":"Create Farm Listing"}),(0,a.jsx)(l.$,{type:"button",variant:"outline",onClick:()=>t.push("/dashboard"),className:"flex-1",children:"Cancel"})]})]})]})}):(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-earth-100",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-earth-600 mb-4",children:"Please log in to create a farm listing."}),(0,a.jsx)(l.$,{onClick:()=>t.push("/auth/login"),children:"Go to Login"})]})})}},35695:(e,r,t)=>{"use strict";var a=t(18999);t.o(a,"useParams")&&t.d(r,{useParams:function(){return a.useParams}}),t.o(a,"useRouter")&&t.d(r,{useRouter:function(){return a.useRouter}}),t.o(a,"useSearchParams")&&t.d(r,{useSearchParams:function(){return a.useSearchParams}})},59434:(e,r,t)=>{"use strict";t.d(r,{Y:()=>i,Z:()=>o,cn:()=>n});var a=t(52596),s=t(39688);function n(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,s.QP)((0,a.$)(r))}function o(e){return e.toString().replace(/\B(?=(\d{3})+(?!\d))/g," ")}function i(e){let r=new Date(e);return"".concat(r.getDate()," ").concat(["January","February","March","April","May","June","July","August","September","October","November","December"][r.getMonth()]," ").concat(r.getFullYear())}},66766:(e,r,t)=>{"use strict";t.d(r,{default:()=>s.a});var a=t(71469),s=t.n(a)},67039:(e,r,t)=>{"use strict";t.d(r,{IG:()=>d,db:()=>c,j2:()=>l});var a=t(23915),s=t(16203),n=t(35317),o=t(90858);let i=(0,a.Wp)({apiKey:"AIzaSyDdsTm8eifLrb1W0WLK0DwBk4p4XLFZKMw",authDomain:"rvbsafaris.firebaseapp.com",projectId:"rvbsafaris",storageBucket:"rvbsafaris.firebasestorage.app",messagingSenderId:"593781635754",appId:"1:593781635754:web:7a39a3b4451f34f85feed3"}),l=(0,s.xI)(i),c=(0,n.aU)(i),d=(0,o.c7)(i);console.log("Firebase Storage initialized with bucket:","rvbsafaris.firebasestorage.app")},71469:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(r,{default:function(){return l},getImageProps:function(){return i}});let a=t(88229),s=t(38883),n=t(33063),o=a._(t(51193));function i(e){let{props:r}=(0,s.getImgProps)(e,{defaultLoader:o.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,t]of Object.entries(r))void 0===t&&delete r[e];return{props:r}}let l=n.Image},84105:(e,r,t)=>{"use strict";t.d(r,{As:()=>d,AuthProvider:()=>c,Nu:()=>m});var a=t(95155),s=t(12115),n=t(16203),o=t(35317),i=t(28214);let l=(0,s.createContext)(void 0);function c(e){let{children:r}=e,[t,c]=(0,s.useState)(null),[d,m]=(0,s.useState)(null),[h,f]=(0,s.useState)(!0),[p,g]=(0,s.useState)(null),v=async e=>{try{let n=(0,o.H9)(i.db,"users",e),l=await (0,o.x7)(n);if(l.exists()){var r,t,a,s;let e=l.data();return{id:l.id,...e,createdAt:(null==(t=e.createdAt)||null==(r=t.toDate)?void 0:r.call(t))||e.createdAt,updatedAt:(null==(s=e.updatedAt)||null==(a=s.toDate)?void 0:a.call(s))||e.updatedAt}}return null}catch(e){return console.error("Error fetching user profile:",e),null}},x=async()=>{t&&m(await v(t.uid))},b=e=>new Promise(r=>{let t=(0,n.hg)(i.j2,a=>{e&&a&&a.uid===e.uid?(t(),r()):e||a||(t(),r())})});(0,s.useEffect)(()=>(0,n.hg)(i.j2,async e=>{if(c(e),e){try{let r=await e.getIdToken();document.cookie="firebase-token=".concat(r,"; path=/; max-age=3600; secure; samesite=strict")}catch(e){console.error("Error getting ID token:",e)}m(await v(e.uid))}else document.cookie="firebase-token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT",m(null);f(!1)}),[]);let w=async(e,r)=>{try{g(null),f(!0);let t=await (0,n.x9)(i.j2,e,r);await b(t.user),await new Promise(e=>setTimeout(e,100))}catch(e){throw g(u(e instanceof Error&&"code"in e?e.code:"unknown")),e}finally{f(!1)}},y=async(e,r,t)=>{try{g(null),f(!0);let a=(await (0,n.eJ)(i.j2,e,r)).user;await (0,n.r7)(a,{displayName:"".concat(t.firstName," ").concat(t.lastName)});let s=o.Dc.now(),l={email:e,fullName:"".concat(t.firstName," ").concat(t.lastName),firstName:t.firstName,lastName:t.lastName,phone:t.phone||null,role:t.role,languagePreference:"en",createdAt:s,updatedAt:s};await (0,o.BN)((0,o.H9)(i.db,"users",a.uid),l),m({id:a.uid,...l,phone:t.phone||void 0});try{let e=await a.getIdToken(),r=await fetch("/api/auth/set-role",{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(e)},body:JSON.stringify({uid:a.uid,role:t.role})});if(r.ok)console.log("User role set successfully");else{let e=await r.json();console.error("Failed to set user role:",e.error)}}catch(e){console.error("Error setting user role:",e)}await b(a),await new Promise(e=>setTimeout(e,100))}catch(e){throw g(u(e instanceof Error&&"code"in e?e.code:"unknown")),e}finally{f(!1)}},j=async()=>{try{g(null),await (0,n.CI)(i.j2),m(null)}catch(e){throw g(u(e instanceof Error&&"code"in e?e.code:"unknown")),e}},N=async e=>{try{g(null),await (0,n.J1)(i.j2,e)}catch(e){throw g(u(e instanceof Error&&"code"in e?e.code:"unknown")),e}};return(0,a.jsx)(l.Provider,{value:{user:t,userProfile:d,loading:h,error:p,signIn:w,signUp:y,signOut:j,resetPassword:N,clearError:()=>{g(null)},refreshProfile:x},children:r})}function d(){let e=(0,s.useContext)(l);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}function u(e){switch(e){case"auth/user-not-found":case"auth/wrong-password":case"auth/invalid-credential":return"Invalid email or password";case"auth/email-already-in-use":return"An account with this email already exists";case"auth/weak-password":return"Password should be at least 6 characters";case"auth/invalid-email":return"Invalid email address";case"auth/too-many-requests":return"Too many failed attempts. Please try again later";case"auth/network-request-failed":return"Network error. Please check your connection";default:return"An unexpected error occurred. Please try again"}}function m(){let{user:e,loading:r}=d();return{user:e,loading:r,isAuthenticated:!!e}}},85163:(e,r,t)=>{Promise.resolve().then(t.bind(t,35072))},91950:(e,r,t)=>{"use strict";t.d(r,{L0:()=>a,QC:()=>n,lc:()=>s});let a=["Eastern Cape","Free State","Gauteng","KwaZulu-Natal","Limpopo","Mpumalanga","Northern Cape","North West","Western Cape"],s=["Luxury Lodge","Basic Accommodation","Restaurant","Bar","Swimming Pool","Spa","WiFi","Airport Transfer","Professional Guide","Trophy Preparation","Taxidermy"],n=["Lion","Leopard","Elephant","Buffalo","Rhino","Kudu","Impala","Springbok","Eland","Sable","Gemsbok","Waterbuck","Bushbuck","Warthog"]},93915:(e,r,t)=>{"use strict";t.d(r,{p:()=>o});var a=t(95155),s=t(12115),n=t(59434);let o=(0,s.forwardRef)((e,r)=>{let{className:t,type:s,label:o,error:i,...l}=e;return(0,a.jsxs)("div",{className:"w-full",children:[o&&(0,a.jsx)("label",{className:"block mb-2 font-semibold text-[var(--primary-brown)] font-[var(--font-ui)]",children:o}),(0,a.jsx)("input",{type:s,className:(0,n.cn)("\n            w-full px-4 py-2 border-2 border-[var(--medium-gray)] \n            rounded-[var(--radius-md)] font-[var(--font-ui)] \n            transition-colors duration-300\n            focus:outline-none focus:border-[var(--primary-brown)] \n            focus:ring-0 focus:shadow-[0_0_0_3px_rgba(139,69,19,0.1)]\n            placeholder:text-[var(--medium-gray)]\n            ",i&&"border-red-500 focus:border-red-500",t),ref:r,...l}),i&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600",children:i})]})});o.displayName="Input"}},e=>{var r=r=>e(e.s=r);e.O(0,[992,965,277,288,63,441,684,358],()=>r(85163)),_N_E=e.O()}]);