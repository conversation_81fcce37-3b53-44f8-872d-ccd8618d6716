(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[412],{14395:(e,t,s)=>{"use strict";s.d(t,{I:()=>c});var a=s(95155),i=s(12115),n=s(93915),r=s(13741);function c(e){let{onSearch:t,placeholder:s="Search farms, activities, or species...",className:c}=e,[l,o]=(0,i.useState)(""),[d,h]=(0,i.useState)("");return(0,a.jsx)("form",{onSubmit:e=>{e.preventDefault(),null==t||t(l,d)},className:c,children:(0,a.jsxs)("div",{className:"flex flex-col md:flex-row gap-4 p-6 bg-white rounded-lg shadow-lg",children:[(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsx)(n.p,{type:"text",placeholder:s,value:l,onChange:e=>o(e.target.value),className:"border-earth-300 focus:border-accent-600"})}),(0,a.jsx)("div",{className:"flex-1 md:max-w-xs",children:(0,a.jsx)(n.p,{type:"text",placeholder:"Location (province, city)",value:d,onChange:e=>h(e.target.value),className:"border-earth-300 focus:border-accent-600"})}),(0,a.jsx)(r.$,{type:"submit",variant:"primary",size:"lg",className:"md:px-8",children:"\uD83D\uDD0D Search"})]})})}},18010:(e,t,s)=>{Promise.resolve().then(s.bind(s,71848))},55922:(e,t,s)=>{"use strict";s.d(t,{f:()=>l});var a=s(95155),i=s(6874),n=s.n(i),r=s(17703),c=s(52814);function l(e){let{id:t,name:s,location:i,province:l,description:o,imageUrl:d,activities:h,priceRange:x,rating:m,reviewCount:p}=e;return(0,a.jsx)(n(),{href:"/farms/".concat(t),children:(0,a.jsxs)(r.Zp,{className:"h-full cursor-pointer",children:[(0,a.jsx)(r.MH,{src:d,alt:"".concat(s," farm")}),(0,a.jsxs)(r.Wu,{className:"flex flex-col h-full",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between mb-3",children:[(0,a.jsx)(r.ZB,{className:"text-lg leading-tight",children:s}),m&&(0,a.jsxs)("div",{className:"flex items-center text-sm text-earth-600",children:[(0,a.jsx)("span",{className:"text-accent-600 mr-1",children:"★"}),(0,a.jsx)("span",{children:m}),p&&(0,a.jsxs)("span",{className:"ml-1",children:["(",p,")"]})]})]}),(0,a.jsxs)("div",{className:"text-earth-600 text-sm mb-3",children:[i,", ",l]}),(0,a.jsx)("p",{className:"text-earth-700 text-sm mb-4 line-clamp-3",children:o}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2 mb-4",children:h.map(e=>(0,a.jsx)(c.E,{variant:"hunting"===e?"hunting":"photo",children:"hunting"===e?"Hunting":"Photo Safari"},e))})]}),x&&(0,a.jsx)("div",{className:"text-earth-900 font-semibold text-lg",children:x})]})]})})}},71848:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>f});var a=s(95155),i=s(12115),n=s(35695),r=s(14395),c=s(55922),l=s(13741),o=s(52814),d=s(17703),h=s(59434),x=s(91950);function m(e){let{filters:t,onFiltersChange:s,className:n}=e,[r,c]=(0,i.useState)(!1),m=e=>{let a=t.activities.includes(e)?t.activities.filter(t=>t!==e):[...t.activities,e];s({...t,activities:a})},p=e=>{let a=t.provinces.includes(e)?t.provinces.filter(t=>t!==e):[...t.provinces,e];s({...t,provinces:a})},u=e=>{let a=t.amenities.includes(e)?t.amenities.filter(t=>t!==e):[...t.amenities,e];s({...t,amenities:a})},v=e=>{let a=t.species.includes(e)?t.species.filter(t=>t!==e):[...t.species,e];s({...t,species:a})},f=t.activities.length>0||t.provinces.length>0||t.amenities.length>0||t.species.length>0;return(0,a.jsxs)(d.Zp,{className:n,children:[(0,a.jsx)(d.aR,{children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(d.ZB,{children:"Filters"}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[f&&(0,a.jsx)(l.$,{variant:"outline",size:"sm",onClick:()=>{s({activities:[],provinces:[],priceRange:[0,2e4],amenities:[],species:[]})},children:"Clear All"}),(0,a.jsxs)(l.$,{variant:"outline",size:"sm",onClick:()=>c(!r),children:[r?"Less":"More"," Filters"]})]})]})}),(0,a.jsxs)(d.Wu,{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-semibold mb-3 text-earth-900",children:"Activity Type"}),(0,a.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,a.jsx)("button",{onClick:()=>m("hunting"),className:"px-3 py-2 rounded-md text-sm font-medium transition-colors ".concat(t.activities.includes("hunting")?"bg-hunting-accent text-white":"bg-earth-200 text-earth-700 hover:bg-earth-300"),children:"Hunting"}),(0,a.jsx)("button",{onClick:()=>m("photo_safari"),className:"px-3 py-2 rounded-md text-sm font-medium transition-colors ".concat(t.activities.includes("photo_safari")?"bg-photo-accent text-white":"bg-earth-200 text-earth-700 hover:bg-earth-300"),children:"Photo Safari"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-semibold mb-3 text-earth-900",children:"Price Range (per day)"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("input",{type:"range",min:"0",max:"20000",step:"500",value:t.priceRange[1],onChange:e=>s({...t,priceRange:[t.priceRange[0],parseInt(e.target.value)]}),className:"w-full"}),(0,a.jsxs)("div",{className:"flex justify-between text-sm text-earth-600",children:[(0,a.jsx)("span",{children:"R0"}),(0,a.jsxs)("span",{children:["R",(0,h.Z)(t.priceRange[1])]})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-semibold mb-3 text-earth-900",children:"Provinces"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:x.L0.map(e=>(0,a.jsx)("button",{onClick:()=>p(e),className:"px-3 py-2 rounded-md text-sm font-medium transition-colors ".concat(t.provinces.includes(e)?"bg-accent-600 text-white":"bg-earth-200 text-earth-700 hover:bg-earth-300"),children:e},e))})]}),r&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-semibold mb-3 text-earth-900",children:"Amenities"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:x.lc.map(e=>(0,a.jsx)("button",{onClick:()=>u(e),className:"px-3 py-2 rounded-md text-sm font-medium transition-colors ".concat(t.amenities.includes(e)?"bg-accent-600 text-white":"bg-earth-200 text-earth-700 hover:bg-earth-300"),children:e},e))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-semibold mb-3 text-earth-900",children:"Available Species"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:x.QC.map(e=>(0,a.jsx)("button",{onClick:()=>v(e),className:"px-3 py-2 rounded-md text-sm font-medium transition-colors ".concat(t.species.includes(e)?"bg-accent-600 text-white":"bg-earth-200 text-earth-700 hover:bg-earth-300"),children:e},e))})]})]}),f&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-semibold mb-3 text-earth-900",children:"Active Filters"}),(0,a.jsxs)("div",{className:"flex flex-wrap gap-2",children:[t.activities.map(e=>(0,a.jsx)(o.E,{variant:"hunting"===e?"hunting":"photo",children:"hunting"===e?"Hunting":"Photo Safari"},e)),t.provinces.map(e=>(0,a.jsxs)(o.E,{variant:"location",children:["\uD83D\uDCCD ",e]},e)),t.amenities.slice(0,3).map(e=>(0,a.jsx)(o.E,{variant:"default",children:e},e)),t.amenities.length>3&&(0,a.jsxs)(o.E,{variant:"default",children:["+",t.amenities.length-3," more"]})]})]})]})]})}var p=s(17154),u=s(28666);function v(){let e=(0,n.useSearchParams)(),[t,s]=(0,i.useState)(""),[l,o]=(0,i.useState)(""),[d,h]=(0,i.useState)({activities:[],provinces:[],priceRange:[0,2e4],amenities:[],species:[]}),[x,v]=(0,i.useState)([]),[f,g]=(0,i.useState)(!0),[j,N]=(0,i.useState)(null);(0,i.useEffect)(()=>{let t=e.get("activity");("hunting"===t||"photo_safari"===t)&&h(e=>({...e,activities:[t]}))},[e]);let b=(0,i.useCallback)(async()=>{try{g(!0),N(null);let e=(await u.eg.getAll({isActive:!0})).map(e=>{let t=[];return"hunting"===e.activityTypes?t.push("hunting"):"photo_safari"===e.activityTypes?t.push("photo_safari"):"both"===e.activityTypes&&t.push("hunting","photo_safari"),{...e,activities:t,rating:void 0,reviewCount:0}});v(e)}catch(e){console.error("Error fetching farms:",e),N("Failed to load farms")}finally{g(!1)}},[]);(0,i.useEffect)(()=>{b()},[b]);let y=x.filter(e=>{let s=!t||e.name.toLowerCase().includes(t.toLowerCase())||e.description&&e.description.toLowerCase().includes(t.toLowerCase()),a=!l||e.location.toLowerCase().includes(l.toLowerCase())||e.province.toLowerCase().includes(l.toLowerCase()),i=0===d.activities.length||d.activities.some(t=>e.activities.includes(t)),n=0===d.provinces.length||d.provinces.includes(e.province);return s&&a&&i&&n});return(0,a.jsxs)("div",{className:"min-h-screen bg-earth-100",children:[(0,a.jsx)("div",{className:"relative text-white h-96",children:(0,a.jsx)(p.t,{className:"absolute inset-0",children:(0,a.jsxs)("div",{className:"relative z-20 container mx-auto px-4 h-full flex items-center",children:[(0,a.jsxs)("div",{className:"text-center mb-8",children:[(0,a.jsx)("h1",{className:"text-4xl text-earth-200 md:text-5xl font-bold mb-4 font-display",children:"Find Your Perfect Safari Experience"}),(0,a.jsx)("p",{className:"text-xl text-earth-200 max-w-2xl mx-auto",children:"Discover exceptional hunting and photo safari opportunities across South Africa"})]}),(0,a.jsx)("div",{className:"max-w-4xl mx-auto",children:(0,a.jsx)(r.I,{onSearch:(e,t)=>{s(e),o(t)}})})]})})}),(0,a.jsx)("div",{className:"container mx-auto px-4 py-12",children:(0,a.jsxs)("div",{className:"flex flex-col lg:flex-row gap-8",children:[(0,a.jsx)("div",{className:"lg:w-1/4",children:(0,a.jsx)(m,{filters:d,onFiltersChange:h,className:"sticky top-24"})}),(0,a.jsxs)("div",{className:"lg:w-3/4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-8",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-earth-900",children:f?"Loading...":"".concat(y.length," Farms Found")}),(t||l)&&(0,a.jsxs)("div",{className:"text-earth-600",children:[t&&(0,a.jsxs)("span",{children:["Search: “",t,"” "]}),l&&(0,a.jsxs)("span",{children:["Location: “",l,"”"]})]})]}),f?(0,a.jsx)("div",{className:"text-center py-16",children:(0,a.jsx)("p",{className:"text-earth-600 text-lg",children:"Loading farms..."})}):j?(0,a.jsxs)("div",{className:"text-center py-16",children:[(0,a.jsx)("p",{className:"text-red-600 text-lg",children:j}),(0,a.jsx)("button",{onClick:b,className:"mt-4 px-4 py-2 bg-accent-600 text-white rounded-lg hover:bg-accent-700",children:"Try Again"})]}):0===y.length?(0,a.jsx)("div",{className:"text-center py-16",children:(0,a.jsx)("p",{className:"text-earth-600 text-lg",children:"No farms found matching your criteria. Try adjusting your search or filters."})}):(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:y.map(e=>(0,a.jsx)(c.f,{id:e.id,name:e.name,location:e.location,province:e.province,description:e.description||"",activities:e.activities,priceRange:e.pricingInfo||"Contact for pricing",rating:e.rating,reviewCount:e.reviewCount},e.id))})]})]})})]})}function f(){return(0,a.jsx)(i.Suspense,{fallback:(0,a.jsx)("div",{className:"min-h-screen bg-earth-100 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-accent-600 mx-auto"}),(0,a.jsx)("p",{className:"mt-4 text-earth-600",children:"Loading farms..."})]})}),children:(0,a.jsx)(v,{})})}},91950:(e,t,s)=>{"use strict";s.d(t,{L0:()=>a,QC:()=>n,lc:()=>i});let a=["Eastern Cape","Free State","Gauteng","KwaZulu-Natal","Limpopo","Mpumalanga","Northern Cape","North West","Western Cape"],i=["Luxury Lodge","Basic Accommodation","Restaurant","Bar","Swimming Pool","Spa","WiFi","Airport Transfer","Professional Guide","Trophy Preparation","Taxidermy"],n=["Lion","Leopard","Elephant","Buffalo","Rhino","Kudu","Impala","Springbok","Eland","Sable","Gemsbok","Waterbuck","Bushbuck","Warthog"]}},e=>{var t=t=>e(e.s=t);e.O(0,[992,965,277,288,63,874,752,441,684,358],()=>t(18010)),_N_E=e.O()}]);