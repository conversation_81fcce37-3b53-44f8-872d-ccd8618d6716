(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[228,636],{13741:(e,t,a)=>{"use strict";a.d(t,{$:()=>l});var r=a(95155),n=a(12115),s=a(59434);let l=(0,n.forwardRef)((e,t)=>{let{className:a,variant:n="primary",size:l="md",isLoading:o,children:i,disabled:d,...c}=e;return(0,r.jsxs)("button",{className:(0,s.cn)("\n      inline-flex items-center justify-center rounded-[var(--radius-md)] \n      font-[var(--font-ui)] font-semibold transition-all duration-300 \n      focus:outline-none focus:ring-2 focus:ring-offset-2\n      disabled:opacity-50 disabled:cursor-not-allowed\n      hover:transform hover:-translate-y-0.5 hover:shadow-[var(--shadow-lg)]\n    ",{primary:"\n        bg-[var(--primary-brown)] text-white \n        hover:bg-[#A0522D] focus:ring-[var(--primary-brown)]\n      ",secondary:"\n        bg-[var(--secondary-sky)] text-white \n        hover:bg-[#357AE0] focus:ring-[var(--secondary-sky)]\n      ",outline:"\n        bg-transparent border-2 border-[var(--primary-brown)] text-[var(--primary-brown)]\n        hover:bg-[var(--primary-brown)] hover:text-white focus:ring-[var(--primary-brown)]\n      ",hunting:"\n        bg-[var(--hunting-accent)] text-white \n        hover:bg-[#A67C00] focus:ring-[var(--hunting-accent)]\n      ",photo:"\n        bg-[var(--photo-accent)] text-white \n        hover:bg-[#3557C7] focus:ring-[var(--photo-accent)]\n      "}[n],{sm:"px-3 py-1.5 text-sm",md:"px-6 py-2 text-base",lg:"px-8 py-3 text-lg"}[l],o&&"cursor-wait",a),disabled:d||o,ref:t,...c,children:[o&&(0,r.jsxs)("svg",{className:"mr-2 h-4 w-4 animate-spin",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,r.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,r.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),i]})});l.displayName="Button"},17703:(e,t,a)=>{"use strict";a.d(t,{MH:()=>u,Wu:()=>i,ZB:()=>c,Zp:()=>o,aR:()=>d});var r=a(95155),n=a(12115),s=a(66766),l=a(59434);let o=(0,n.forwardRef)((e,t)=>{let{className:a,hover:n=!0,children:s,...o}=e;return(0,r.jsx)("div",{ref:t,className:(0,l.cn)("\n          bg-white rounded-[var(--radius-lg)] shadow-[var(--shadow-md)] \n          overflow-hidden transition-all duration-300\n          ",n&&"hover:transform hover:-translate-y-1 hover:shadow-[var(--shadow-xl)]",a),...o,children:s})});o.displayName="Card";let i=(0,n.forwardRef)((e,t)=>{let{className:a,...n}=e;return(0,r.jsx)("div",{ref:t,className:(0,l.cn)("p-[var(--space-lg)]",a),...n})});i.displayName="CardContent";let d=(0,n.forwardRef)((e,t)=>{let{className:a,...n}=e;return(0,r.jsx)("div",{ref:t,className:(0,l.cn)("px-[var(--space-lg)] pt-[var(--space-lg)]",a),...n})});d.displayName="CardHeader";let c=(0,n.forwardRef)((e,t)=>{let{className:a,...n}=e;return(0,r.jsx)("h3",{ref:t,className:(0,l.cn)("text-xl font-bold mb-2 text-[var(--primary-brown)] font-[var(--font-display)]",a),...n})});c.displayName="CardTitle";let u=(0,n.forwardRef)((e,t)=>{let{className:a,src:n,alt:o,children:i,...d}=e;return(0,r.jsx)("div",{ref:t,className:(0,l.cn)("relative w-full h-48 bg-gradient-to-br from-[var(--primary-green)] to-[var(--secondary-sunset)] flex items-center justify-center text-white text-5xl",a),...d,children:n?(0,r.jsx)(s.default,{src:n,alt:o||"",fill:!0,className:"object-cover"}):i})});u.displayName="CardImage"},28214:(e,t,a)=>{"use strict";a.d(t,{IG:()=>r.IG,db:()=>r.db,j2:()=>r.j2});var r=a(67039)},28666:(e,t,a)=>{"use strict";a.d(t,{C2:()=>d,QQ:()=>l,eg:()=>o,zi:()=>i});var r=a(35317),n=a(28214);function s(e){var t,a,r,n;if(!e.exists())return null;let s=e.data();return{id:e.id,...s,createdAt:(null==s||null==(a=s.createdAt)||null==(t=a.toDate)?void 0:t.call(a))||(null==s?void 0:s.createdAt),updatedAt:(null==s||null==(n=s.updatedAt)||null==(r=n.toDate)?void 0:r.call(n))||(null==s?void 0:s.updatedAt)}}let l={async get(e){let t=(0,r.H9)(n.db,"users",e);return s(await (0,r.x7)(t))},async create(e,t){let a=(0,r.H9)(n.db,"users",e),s=new Date;await (0,r.BN)(a,{...t,createdAt:r.Dc.fromDate(s),updatedAt:r.Dc.fromDate(s)})},async update(e,t){let a=(0,r.H9)(n.db,"users",e);await (0,r.mZ)(a,{...t,updatedAt:r.Dc.now()})}},o={async getAll(e){let t=(0,r.rJ)(n.db,"farms");return(null==e?void 0:e.isActive)!==void 0&&(t=(0,r.P)(t,(0,r._M)("isActive","==",e.isActive))),(null==e?void 0:e.featured)!==void 0&&(t=(0,r.P)(t,(0,r._M)("featured","==",e.featured))),(null==e?void 0:e.province)&&(t=(0,r.P)(t,(0,r._M)("province","==",e.province))),(null==e?void 0:e.activityType)&&(t=(0,r.P)(t,(0,r._M)("activityTypes","==",e.activityType))),t=(0,r.P)(t,(0,r.My)("createdAt","desc")),(null==e?void 0:e.limit)&&(t=(0,r.P)(t,(0,r.AB)(e.limit))),(await (0,r.GG)(t)).docs.map(e=>s(e)).filter(Boolean)},async get(e){let t=(0,r.H9)(n.db,"farms",e);return s(await (0,r.x7)(t))},async getByOwner(e){let t=(0,r.P)((0,r.rJ)(n.db,"farms"),(0,r._M)("ownerId","==",e),(0,r.My)("createdAt","desc"));return(await (0,r.GG)(t)).docs.map(e=>s(e)).filter(Boolean)},async getActive(e){let t=(0,r.P)((0,r.rJ)(n.db,"farms"),(0,r._M)("isActive","==",!0),(0,r.My)("createdAt","desc"));return e&&(t=(0,r.P)(t,(0,r.AB)(e))),(await (0,r.GG)(t)).docs.map(e=>s(e)).filter(Boolean)},async create(e){let t=new Date;return(await (0,r.gS)((0,r.rJ)(n.db,"farms"),{...e,createdAt:r.Dc.fromDate(t),updatedAt:r.Dc.fromDate(t)})).id},async update(e,t){let a=(0,r.H9)(n.db,"farms",e);await (0,r.mZ)(a,{...t,updatedAt:r.Dc.now()})},async delete(e){let t=(0,r.H9)(n.db,"farms",e);await (0,r.kd)(t)},async addImage(e,t){let a=new Date;return(await (0,r.gS)((0,r.rJ)(n.db,"farms",e,"images"),{...t,createdAt:r.Dc.fromDate(a),updatedAt:r.Dc.fromDate(a)})).id},async getImages(e){let t=(0,r.P)((0,r.rJ)(n.db,"farms",e,"images"),(0,r.My)("displayOrder","asc"),(0,r.My)("createdAt","asc"));return(await (0,r.GG)(t)).docs.map(e=>s(e)).filter(Boolean)},async deleteImage(e,t){let a=(0,r.H9)(n.db,"farms",e,"images",t);await (0,r.kd)(a)}},i={async getAll(e){let t=(0,r.rJ)(n.db,"bookings");return(null==e?void 0:e.hunterId)&&(t=(0,r.P)(t,(0,r._M)("hunterId","==",e.hunterId))),(null==e?void 0:e.farmId)&&(t=(0,r.P)(t,(0,r._M)("farmId","==",e.farmId))),(null==e?void 0:e.status)&&(t=(0,r.P)(t,(0,r._M)("status","==",e.status))),t=(0,r.P)(t,(0,r.My)("createdAt","desc")),(null==e?void 0:e.limit)&&(t=(0,r.P)(t,(0,r.AB)(e.limit))),(await (0,r.GG)(t)).docs.map(e=>s(e)).filter(Boolean)},async get(e){let t=(0,r.H9)(n.db,"bookings",e);return s(await (0,r.x7)(t))},async create(e){let t=new Date,a="BVR-".concat(t.getFullYear()).concat((t.getMonth()+1).toString().padStart(2,"0")).concat(t.getDate().toString().padStart(2,"0"),"-").concat(Math.random().toString(36).substring(2,8).toUpperCase());return(await (0,r.gS)((0,r.rJ)(n.db,"bookings"),{...e,bookingReference:a,createdAt:r.Dc.fromDate(t),updatedAt:r.Dc.fromDate(t)})).id},async update(e,t){let a=(0,r.H9)(n.db,"bookings",e);await (0,r.mZ)(a,{...t,updatedAt:r.Dc.now()})}},d={async getByFarm(e){let t=(0,r.P)((0,r.rJ)(n.db,"farms",e,"reviews"),(0,r._M)("isPublic","==",!0),(0,r.My)("createdAt","desc"));return(await (0,r.GG)(t)).docs.map(e=>s(e)).filter(Boolean)},async create(e,t){let a=new Date;return(await (0,r.gS)((0,r.rJ)(n.db,"farms",e,"reviews"),{...t,createdAt:r.Dc.fromDate(a),updatedAt:r.Dc.fromDate(a)})).id},async update(e,t,a){let s=(0,r.H9)(n.db,"farms",e,"reviews",t);await (0,r.mZ)(s,{...a,updatedAt:r.Dc.now()})}}},33246:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>u});var r=a(95155),n=a(12115),s=a(35695),l=a(84105),o=a(13741),i=a(93915),d=a(17703),c=a(28666);function u(){let{user:e,loading:t}=(0,l.As)(),a=(0,s.useRouter)(),[u,m]=(0,n.useState)(null),[f,h]=(0,n.useState)(!0),[p,g]=(0,n.useState)(!1),[b,v]=(0,n.useState)(null),[x,y]=(0,n.useState)(!1),[w,N]=(0,n.useState)({fullName:"",phone:"",bio:"",languagePreference:"en"}),j=(0,n.useCallback)(async()=>{try{h(!0);let t=await c.QQ.get(e.uid);t?(m(t),N({fullName:t.fullName||"",phone:t.phone||"",bio:t.bio||"",languagePreference:t.languagePreference||"en"})):(console.error("Profile not found"),v("Failed to load profile"))}catch(e){console.error("Unexpected error:",e),v("An unexpected error occurred")}finally{h(!1)}},[e]);(0,n.useEffect)(()=>{if(!t&&!e)return void a.push("/auth/login");e&&j()},[e,t,a,j]);let A=(e,t)=>{N(a=>({...a,[e]:t})),y(!1),v(null)},P=async t=>{t.preventDefault(),g(!0),v(null);try{await c.QQ.update(e.uid,{fullName:w.fullName,phone:w.phone||void 0,bio:w.bio||void 0,languagePreference:w.languagePreference}),y(!0),await j()}catch(e){v("An unexpected error occurred"),console.error("Profile update error:",e)}finally{g(!1)}};return t||f?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-earth-100",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-accent-600"})}):e&&u?(0,r.jsx)("div",{className:"min-h-screen bg-earth-100 py-8",children:(0,r.jsxs)("div",{className:"max-w-2xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-earth-900",children:"Profile Settings"}),(0,r.jsx)("p",{className:"text-earth-600 mt-2",children:"Manage your account information and preferences"})]}),(0,r.jsxs)(d.Zp,{children:[(0,r.jsx)(d.aR,{children:(0,r.jsx)(d.ZB,{children:"Personal Information"})}),(0,r.jsx)(d.Wu,{children:(0,r.jsxs)("form",{onSubmit:P,className:"space-y-6",children:[b&&(0,r.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-md p-3",children:(0,r.jsx)("p",{className:"text-red-600 text-sm",children:b})}),x&&(0,r.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-md p-3",children:(0,r.jsx)("p",{className:"text-green-600 text-sm",children:"Profile updated successfully!"})}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-earth-700 mb-2",children:"Email Address"}),(0,r.jsx)(i.p,{type:"email",value:u.email,disabled:!0,className:"bg-earth-50"}),(0,r.jsx)("p",{className:"text-xs text-earth-500 mt-1",children:"Email cannot be changed. Contact support if needed."})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-earth-700 mb-2",children:"Account Type"}),(0,r.jsx)(i.p,{type:"text",value:u.role.replace("_"," ").replace(/\b\w/g,e=>e.toUpperCase()),disabled:!0,className:"bg-earth-50"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"fullName",className:"block text-sm font-medium text-earth-700 mb-2",children:"Full Name *"}),(0,r.jsx)(i.p,{id:"fullName",type:"text",value:w.fullName,onChange:e=>A("fullName",e.target.value),placeholder:"Enter your full name",required:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"phone",className:"block text-sm font-medium text-earth-700 mb-2",children:"Phone Number"}),(0,r.jsx)(i.p,{id:"phone",type:"tel",value:w.phone,onChange:e=>A("phone",e.target.value),placeholder:"Enter your phone number"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"bio",className:"block text-sm font-medium text-earth-700 mb-2",children:"Bio"}),(0,r.jsx)("textarea",{id:"bio",value:w.bio,onChange:e=>A("bio",e.target.value),placeholder:"Tell us about yourself...",rows:4,className:"w-full px-3 py-2 border border-earth-300 rounded-md focus:outline-none focus:ring-2 focus:ring-accent-600 focus:border-transparent"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"language",className:"block text-sm font-medium text-earth-700 mb-2",children:"Language Preference"}),(0,r.jsxs)("select",{id:"language",value:w.languagePreference,onChange:e=>A("languagePreference",e.target.value),className:"w-full px-3 py-2 border border-earth-300 rounded-md focus:outline-none focus:ring-2 focus:ring-accent-600 focus:border-transparent",children:[(0,r.jsx)("option",{value:"en",children:"English"}),(0,r.jsx)("option",{value:"af",children:"Afrikaans"})]})]}),(0,r.jsxs)("div",{className:"flex gap-4",children:[(0,r.jsx)(o.$,{type:"submit",variant:"primary",isLoading:p,className:"flex-1",children:p?"Saving...":"Save Changes"}),(0,r.jsx)(o.$,{type:"button",variant:"outline",onClick:()=>a.push("/dashboard"),className:"flex-1",children:"Cancel"})]})]})})]})]})}):(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-earth-100",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("p",{className:"text-earth-600 mb-4",children:"Profile not found"}),(0,r.jsx)(o.$,{onClick:()=>a.push("/dashboard"),children:"Go to Dashboard"})]})})}},35695:(e,t,a)=>{"use strict";var r=a(18999);a.o(r,"useParams")&&a.d(t,{useParams:function(){return r.useParams}}),a.o(r,"useRouter")&&a.d(t,{useRouter:function(){return r.useRouter}}),a.o(r,"useSearchParams")&&a.d(t,{useSearchParams:function(){return r.useSearchParams}})},59434:(e,t,a)=>{"use strict";a.d(t,{Y:()=>o,Z:()=>l,cn:()=>s});var r=a(52596),n=a(39688);function s(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,n.QP)((0,r.$)(t))}function l(e){return e.toString().replace(/\B(?=(\d{3})+(?!\d))/g," ")}function o(e){let t=new Date(e);return"".concat(t.getDate()," ").concat(["January","February","March","April","May","June","July","August","September","October","November","December"][t.getMonth()]," ").concat(t.getFullYear())}},66766:(e,t,a)=>{"use strict";a.d(t,{default:()=>n.a});var r=a(71469),n=a.n(r)},67039:(e,t,a)=>{"use strict";a.d(t,{IG:()=>c,db:()=>d,j2:()=>i});var r=a(23915),n=a(16203),s=a(35317),l=a(90858);let o=(0,r.Wp)({apiKey:"AIzaSyDdsTm8eifLrb1W0WLK0DwBk4p4XLFZKMw",authDomain:"rvbsafaris.firebaseapp.com",projectId:"rvbsafaris",storageBucket:"rvbsafaris.firebasestorage.app",messagingSenderId:"593781635754",appId:"1:593781635754:web:7a39a3b4451f34f85feed3"}),i=(0,n.xI)(o),d=(0,s.aU)(o),c=(0,l.c7)(o);console.log("Firebase Storage initialized with bucket:","rvbsafaris.firebasestorage.app")},71469:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var a in t)Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}(t,{default:function(){return i},getImageProps:function(){return o}});let r=a(88229),n=a(38883),s=a(33063),l=r._(a(51193));function o(e){let{props:t}=(0,n.getImgProps)(e,{defaultLoader:l.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,a]of Object.entries(t))void 0===a&&delete t[e];return{props:t}}let i=s.Image},76410:(e,t,a)=>{Promise.resolve().then(a.bind(a,33246))},84105:(e,t,a)=>{"use strict";a.d(t,{As:()=>c,AuthProvider:()=>d,Nu:()=>m});var r=a(95155),n=a(12115),s=a(16203),l=a(35317),o=a(28214);let i=(0,n.createContext)(void 0);function d(e){let{children:t}=e,[a,d]=(0,n.useState)(null),[c,m]=(0,n.useState)(null),[f,h]=(0,n.useState)(!0),[p,g]=(0,n.useState)(null),b=async e=>{try{let s=(0,l.H9)(o.db,"users",e),i=await (0,l.x7)(s);if(i.exists()){var t,a,r,n;let e=i.data();return{id:i.id,...e,createdAt:(null==(a=e.createdAt)||null==(t=a.toDate)?void 0:t.call(a))||e.createdAt,updatedAt:(null==(n=e.updatedAt)||null==(r=n.toDate)?void 0:r.call(n))||e.updatedAt}}return null}catch(e){return console.error("Error fetching user profile:",e),null}},v=async()=>{a&&m(await b(a.uid))},x=e=>new Promise(t=>{let a=(0,s.hg)(o.j2,r=>{e&&r&&r.uid===e.uid?(a(),t()):e||r||(a(),t())})});(0,n.useEffect)(()=>(0,s.hg)(o.j2,async e=>{if(d(e),e){try{let t=await e.getIdToken();document.cookie="firebase-token=".concat(t,"; path=/; max-age=3600; secure; samesite=strict")}catch(e){console.error("Error getting ID token:",e)}m(await b(e.uid))}else document.cookie="firebase-token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT",m(null);h(!1)}),[]);let y=async(e,t)=>{try{g(null),h(!0);let a=await (0,s.x9)(o.j2,e,t);await x(a.user),await new Promise(e=>setTimeout(e,100))}catch(e){throw g(u(e instanceof Error&&"code"in e?e.code:"unknown")),e}finally{h(!1)}},w=async(e,t,a)=>{try{g(null),h(!0);let r=(await (0,s.eJ)(o.j2,e,t)).user;await (0,s.r7)(r,{displayName:"".concat(a.firstName," ").concat(a.lastName)});let n=l.Dc.now(),i={email:e,fullName:"".concat(a.firstName," ").concat(a.lastName),firstName:a.firstName,lastName:a.lastName,phone:a.phone||null,role:a.role,languagePreference:"en",createdAt:n,updatedAt:n};await (0,l.BN)((0,l.H9)(o.db,"users",r.uid),i),m({id:r.uid,...i,phone:a.phone||void 0});try{let e=await r.getIdToken(),t=await fetch("/api/auth/set-role",{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(e)},body:JSON.stringify({uid:r.uid,role:a.role})});if(t.ok)console.log("User role set successfully");else{let e=await t.json();console.error("Failed to set user role:",e.error)}}catch(e){console.error("Error setting user role:",e)}await x(r),await new Promise(e=>setTimeout(e,100))}catch(e){throw g(u(e instanceof Error&&"code"in e?e.code:"unknown")),e}finally{h(!1)}},N=async()=>{try{g(null),await (0,s.CI)(o.j2),m(null)}catch(e){throw g(u(e instanceof Error&&"code"in e?e.code:"unknown")),e}},j=async e=>{try{g(null),await (0,s.J1)(o.j2,e)}catch(e){throw g(u(e instanceof Error&&"code"in e?e.code:"unknown")),e}};return(0,r.jsx)(i.Provider,{value:{user:a,userProfile:c,loading:f,error:p,signIn:y,signUp:w,signOut:N,resetPassword:j,clearError:()=>{g(null)},refreshProfile:v},children:t})}function c(){let e=(0,n.useContext)(i);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}function u(e){switch(e){case"auth/user-not-found":case"auth/wrong-password":case"auth/invalid-credential":return"Invalid email or password";case"auth/email-already-in-use":return"An account with this email already exists";case"auth/weak-password":return"Password should be at least 6 characters";case"auth/invalid-email":return"Invalid email address";case"auth/too-many-requests":return"Too many failed attempts. Please try again later";case"auth/network-request-failed":return"Network error. Please check your connection";default:return"An unexpected error occurred. Please try again"}}function m(){let{user:e,loading:t}=c();return{user:e,loading:t,isAuthenticated:!!e}}},93915:(e,t,a)=>{"use strict";a.d(t,{p:()=>l});var r=a(95155),n=a(12115),s=a(59434);let l=(0,n.forwardRef)((e,t)=>{let{className:a,type:n,label:l,error:o,...i}=e;return(0,r.jsxs)("div",{className:"w-full",children:[l&&(0,r.jsx)("label",{className:"block mb-2 font-semibold text-[var(--primary-brown)] font-[var(--font-ui)]",children:l}),(0,r.jsx)("input",{type:n,className:(0,s.cn)("\n            w-full px-4 py-2 border-2 border-[var(--medium-gray)] \n            rounded-[var(--radius-md)] font-[var(--font-ui)] \n            transition-colors duration-300\n            focus:outline-none focus:border-[var(--primary-brown)] \n            focus:ring-0 focus:shadow-[0_0_0_3px_rgba(139,69,19,0.1)]\n            placeholder:text-[var(--medium-gray)]\n            ",o&&"border-red-500 focus:border-red-500",a),ref:t,...i}),o&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-600",children:o})]})});l.displayName="Input"}},e=>{var t=t=>e(e.s=t);e.O(0,[992,965,277,288,63,441,684,358],()=>t(76410)),_N_E=e.O()}]);