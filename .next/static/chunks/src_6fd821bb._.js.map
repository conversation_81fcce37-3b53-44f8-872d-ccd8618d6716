{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/src/personal/bvr-safaris/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\n/**\n * Format a number with consistent thousand separators for SSR compatibility\n * Uses spaces as thousand separators to match South African conventions\n */\nexport function formatNumber(num: number): string {\n  return num.toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, ' ')\n}\n\n/**\n * Format a date string consistently for SSR compatibility\n * Uses a fixed format to avoid locale-based differences between server and client\n */\nexport function formatDate(dateString: string): string {\n  const date = new Date(dateString)\n  const months = [\n    'January', 'February', 'March', 'April', 'May', 'June',\n    'July', 'August', 'September', 'October', 'November', 'December'\n  ]\n\n  return `${date.getDate()} ${months[date.getMonth()]} ${date.getFullYear()}`\n}"], "names": [], "mappings": ";;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAMO,SAAS,aAAa,GAAW;IACtC,OAAO,IAAI,QAAQ,GAAG,OAAO,CAAC,yBAAyB;AACzD;AAMO,SAAS,WAAW,UAAkB;IAC3C,MAAM,OAAO,IAAI,KAAK;IACtB,MAAM,SAAS;QACb;QAAW;QAAY;QAAS;QAAS;QAAO;QAChD;QAAQ;QAAU;QAAa;QAAW;QAAY;KACvD;IAED,OAAO,GAAG,KAAK,OAAO,GAAG,CAAC,EAAE,MAAM,CAAC,KAAK,QAAQ,GAAG,CAAC,CAAC,EAAE,KAAK,WAAW,IAAI;AAC7E", "debugId": null}}, {"offset": {"line": 49, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/src/personal/bvr-safaris/src/components/ui/Button.tsx"], "sourcesContent": ["import { ButtonHTMLAttributes, forwardRef } from 'react'\nimport { cn } from '@/lib/utils'\n\nexport interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'outline' | 'hunting' | 'photo'\n  size?: 'sm' | 'md' | 'lg'\n  isLoading?: boolean\n}\n\nconst Button = forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = 'primary', size = 'md', isLoading, children, disabled, ...props }, ref) => {\n    const baseStyles = `\n      inline-flex items-center justify-center rounded-[var(--radius-md)] \n      font-[var(--font-ui)] font-semibold transition-all duration-300 \n      focus:outline-none focus:ring-2 focus:ring-offset-2\n      disabled:opacity-50 disabled:cursor-not-allowed\n      hover:transform hover:-translate-y-0.5 hover:shadow-[var(--shadow-lg)]\n    `\n\n    const variants = {\n      primary: `\n        bg-[var(--primary-brown)] text-white \n        hover:bg-[#A0522D] focus:ring-[var(--primary-brown)]\n      `,\n      secondary: `\n        bg-[var(--secondary-sky)] text-white \n        hover:bg-[#357AE0] focus:ring-[var(--secondary-sky)]\n      `,\n      outline: `\n        bg-transparent border-2 border-[var(--primary-brown)] text-[var(--primary-brown)]\n        hover:bg-[var(--primary-brown)] hover:text-white focus:ring-[var(--primary-brown)]\n      `,\n      hunting: `\n        bg-[var(--hunting-accent)] text-white \n        hover:bg-[#A67C00] focus:ring-[var(--hunting-accent)]\n      `,\n      photo: `\n        bg-[var(--photo-accent)] text-white \n        hover:bg-[#3557C7] focus:ring-[var(--photo-accent)]\n      `\n    }\n\n    const sizes = {\n      sm: 'px-3 py-1.5 text-sm',\n      md: 'px-6 py-2 text-base',\n      lg: 'px-8 py-3 text-lg'\n    }\n\n    return (\n      <button\n        className={cn(\n          baseStyles,\n          variants[variant],\n          sizes[size],\n          isLoading && 'cursor-wait',\n          className\n        )}\n        disabled={disabled || isLoading}\n        ref={ref}\n        {...props}\n      >\n        {isLoading && (\n          <svg\n            className=\"mr-2 h-4 w-4 animate-spin\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            fill=\"none\"\n            viewBox=\"0 0 24 24\"\n          >\n            <circle\n              className=\"opacity-25\"\n              cx=\"12\"\n              cy=\"12\"\n              r=\"10\"\n              stroke=\"currentColor\"\n              strokeWidth=\"4\"\n            />\n            <path\n              className=\"opacity-75\"\n              fill=\"currentColor\"\n              d=\"m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n            />\n          </svg>\n        )}\n        {children}\n      </button>\n    )\n  }\n)\n\nButton.displayName = 'Button'\n\nexport { Button }"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAQA,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,OACtB,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IACzF,MAAM,aAAa,CAAC;;;;;;IAMpB,CAAC;IAED,MAAM,WAAW;QACf,SAAS,CAAC;;;MAGV,CAAC;QACD,WAAW,CAAC;;;MAGZ,CAAC;QACD,SAAS,CAAC;;;MAGV,CAAC;QACD,SAAS,CAAC;;;MAGV,CAAC;QACD,OAAO,CAAC;;;MAGR,CAAC;IACH;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,YACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK,EACX,aAAa,eACb;QAEF,UAAU,YAAY;QACtB,KAAK;QACJ,GAAG,KAAK;;YAER,2BACC,6LAAC;gBACC,WAAU;gBACV,OAAM;gBACN,MAAK;gBACL,SAAQ;;kCAER,6LAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,6LAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;YAIP;;;;;;;AAGP;;AAGF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 155, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/src/personal/bvr-safaris/src/lib/firebase/config.ts"], "sourcesContent": ["import { initializeApp } from 'firebase/app'\nimport { getAuth, connectAuthEmulator } from 'firebase/auth'\nimport { getFirestore, connectFirestoreEmulator } from 'firebase/firestore'\nimport { getStorage, connectStorageEmulator } from 'firebase/storage'\n\nconst firebaseConfig = {\n  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,\n  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,\n  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,\n  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,\n  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,\n  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID\n}\n\n// Initialize Firebase\nconst app = initializeApp(firebaseConfig)\n\n// Initialize Firebase services\nexport const auth = getAuth(app)\nexport const db = getFirestore(app)\nexport const storage = getStorage(app)\n\n// Connect to emulators in development (only if explicitly enabled)\nif (typeof window !== 'undefined' &&\n    process.env.NODE_ENV === 'development' &&\n    process.env.NEXT_PUBLIC_USE_FIREBASE_EMULATORS === 'true') {\n  // Check if we're already connected to avoid reconnection errors\n  try {\n    // Connect to emulators - Firebase will handle duplicate connections gracefully\n    connectAuthEmulator(auth, 'http://localhost:9099')\n    connectFirestoreEmulator(db, 'localhost', 8080)\n    connectStorageEmulator(storage, 'localhost', 9199)\n\n    console.log('Connected to Firebase emulators')\n  } catch (error) {\n    console.log('Emulators already connected or not available:', error instanceof Error ? error.message : 'Unknown error')\n  }\n}\n\n// Debug storage configuration\nconsole.log('Firebase Storage initialized with bucket:', process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET)\n\n// Export the app instance for other uses\nexport default app\n"], "names": [], "mappings": ";;;;;;AAMU;AANV;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AACA;AAAA;;;;;AAEA,MAAM,iBAAiB;IACrB,MAAM;IACN,UAAU;IACV,SAAS;IACT,aAAa;IACb,iBAAiB;IACjB,KAAK;AACP;AAEA,sBAAsB;AACtB,MAAM,MAAM,CAAA,GAAA,uLAAA,CAAA,gBAAa,AAAD,EAAE;AAGnB,MAAM,OAAO,CAAA,GAAA,6MAAA,CAAA,UAAO,AAAD,EAAE;AACrB,MAAM,KAAK,CAAA,GAAA,sKAAA,CAAA,eAAY,AAAD,EAAE;AACxB,MAAM,UAAU,CAAA,GAAA,oKAAA,CAAA,aAAU,AAAD,EAAE;AAElC,mEAAmE;AACnE,IAAI,aAAkB,eAClB,oDAAyB,iBACzB,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,kCAAkC,KAAK,QAAQ;IAC7D,gEAAgE;IAChE,IAAI;QACF,+EAA+E;QAC/E,CAAA,GAAA,yNAAA,CAAA,sBAAmB,AAAD,EAAE,MAAM;QAC1B,CAAA,GAAA,sKAAA,CAAA,2BAAwB,AAAD,EAAE,IAAI,aAAa;QAC1C,CAAA,GAAA,oKAAA,CAAA,yBAAsB,AAAD,EAAE,SAAS,aAAa;QAE7C,QAAQ,GAAG,CAAC;IACd,EAAE,OAAO,OAAO;QACd,QAAQ,GAAG,CAAC,iDAAiD,iBAAiB,QAAQ,MAAM,OAAO,GAAG;IACxG;AACF;AAEA,8BAA8B;AAC9B,QAAQ,GAAG,CAAC;uCAGG", "debugId": null}}, {"offset": {"line": 213, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/src/personal/bvr-safaris/src/lib/firebase/client.ts"], "sourcesContent": ["import { auth, db, storage } from './config'\n\n// Re-export Firebase services for client-side use\nexport { auth, db, storage }\n\n// Helper function to get current user\nexport const getCurrentUser = () => {\n  return auth.currentUser\n}\n\n// Helper function to check if user is authenticated\nexport const isAuthenticated = () => {\n  return !!auth.currentUser\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAMO,MAAM,iBAAiB;IAC5B,OAAO,mIAAA,CAAA,OAAI,CAAC,WAAW;AACzB;AAGO,MAAM,kBAAkB;IAC7B,OAAO,CAAC,CAAC,mIAAA,CAAA,OAAI,CAAC,WAAW;AAC3B", "debugId": null}}, {"offset": {"line": 244, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/src/personal/bvr-safaris/src/hooks/useAuth.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect, createContext, useContext, ReactNode } from 'react'\nimport {\n  User,\n  onAuthStateChanged,\n  signOut as firebaseSignOut,\n  signInWithEmailAndPassword,\n  createUserWithEmailAndPassword,\n  updateProfile,\n  sendPasswordResetEmail\n} from 'firebase/auth'\nimport { doc, setDoc, getDoc, Timestamp } from 'firebase/firestore'\nimport { auth, db } from '@/lib/firebase/client'\nimport { UserProfile, UserRole } from '@/lib/types/firestore'\n\ninterface FirebaseError extends Error {\n  code: string\n}\n\ninterface AuthContextType {\n  user: User | null\n  userProfile: UserProfile | null\n  loading: boolean\n  error: string | null\n  signIn: (email: string, password: string) => Promise<void>\n  signUp: (email: string, password: string, userData: SignUpData) => Promise<void>\n  signOut: () => Promise<void>\n  resetPassword: (email: string) => Promise<void>\n  clearError: () => void\n  refreshProfile: () => Promise<void>\n}\n\ninterface SignUpData {\n  firstName: string\n  lastName: string\n  phone?: string\n  role: UserRole\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined)\n\nexport function AuthProvider({ children }: { children: ReactNode }) {\n  const [user, setUser] = useState<User | null>(null)\n  const [userProfile, setUserProfile] = useState<UserProfile | null>(null)\n  const [loading, setLoading] = useState(true)\n  const [error, setError] = useState<string | null>(null)\n\n  // Fetch user profile from Firestore\n  const fetchUserProfile = async (userId: string): Promise<UserProfile | null> => {\n    try {\n      const docRef = doc(db, 'users', userId)\n      const docSnap = await getDoc(docRef)\n\n      if (docSnap.exists()) {\n        const data = docSnap.data()\n        return {\n          id: docSnap.id,\n          ...data,\n          createdAt: data.createdAt?.toDate?.() || data.createdAt,\n          updatedAt: data.updatedAt?.toDate?.() || data.updatedAt,\n        } as UserProfile\n      }\n      return null\n    } catch (err) {\n      console.error('Error fetching user profile:', err)\n      return null\n    }\n  }\n\n  // Refresh user profile\n  const refreshProfile = async () => {\n    if (user) {\n      const profile = await fetchUserProfile(user.uid)\n      setUserProfile(profile)\n    }\n  }\n\n  // Helper function to wait for auth state to be updated\n  const waitForAuthState = (expectedUser: User | null): Promise<void> => {\n    return new Promise((resolve) => {\n      const unsubscribe = onAuthStateChanged(auth, (currentUser) => {\n        if (expectedUser && currentUser && currentUser.uid === expectedUser.uid) {\n          unsubscribe()\n          resolve()\n        } else if (!expectedUser && !currentUser) {\n          unsubscribe()\n          resolve()\n        }\n      })\n    })\n  }\n\n  // Listen to auth state changes\n  useEffect(() => {\n    const unsubscribe = onAuthStateChanged(auth, async (user) => {\n      setUser(user)\n\n      if (user) {\n        // Get the ID token and set it as a cookie for middleware\n        try {\n          const idToken = await user.getIdToken()\n          // Set the token as a cookie that expires in 1 hour\n          document.cookie = `firebase-token=${idToken}; path=/; max-age=3600; secure; samesite=strict`\n        } catch (error) {\n          console.error('Error getting ID token:', error)\n        }\n\n        // Fetch user profile when user is authenticated\n        const profile = await fetchUserProfile(user.uid)\n        setUserProfile(profile)\n      } else {\n        // Clear the cookie when user signs out\n        document.cookie = 'firebase-token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT'\n        setUserProfile(null)\n      }\n\n      setLoading(false)\n    })\n\n    return unsubscribe\n  }, [])\n\n  // Sign in function\n  const signIn = async (email: string, password: string) => {\n    try {\n      setError(null)\n      setLoading(true)\n      const userCredential = await signInWithEmailAndPassword(auth, email, password)\n\n      // Wait for the auth state to be properly updated\n      await waitForAuthState(userCredential.user)\n\n      // Small additional delay to ensure cookie is set\n      await new Promise(resolve => setTimeout(resolve, 100))\n\n    } catch (err: unknown) {\n      const errorCode = err instanceof Error && 'code' in err ? (err as FirebaseError).code : 'unknown'\n      setError(getAuthErrorMessage(errorCode))\n      throw err\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  // Sign up function\n  const signUp = async (email: string, password: string, userData: SignUpData) => {\n    try {\n      setError(null)\n      setLoading(true)\n\n      // Create user with Firebase Auth\n      const userCredential = await createUserWithEmailAndPassword(auth, email, password)\n      const user = userCredential.user\n\n      // Update user profile with display name\n      await updateProfile(user, {\n        displayName: `${userData.firstName} ${userData.lastName}`\n      })\n\n      // Create user profile document in Firestore\n      const now = Timestamp.now()\n      const profileData = {\n        email: email,\n        fullName: `${userData.firstName} ${userData.lastName}`,\n        firstName: userData.firstName,\n        lastName: userData.lastName,\n        phone: userData.phone || null,\n        role: userData.role,\n        languagePreference: 'en',\n        createdAt: now,\n        updatedAt: now\n      }\n\n      await setDoc(doc(db, 'users', user.uid), profileData)\n\n      // Set the profile in state\n      setUserProfile({\n        id: user.uid,\n        ...profileData,\n        phone: userData.phone || undefined\n      })\n\n      // Set custom claims for role-based access control\n      try {\n        const idToken = await user.getIdToken()\n        const response = await fetch('/api/auth/set-role', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${idToken}`,\n          },\n          body: JSON.stringify({\n            uid: user.uid,\n            role: userData.role\n          }),\n        })\n\n        if (!response.ok) {\n          const errorData = await response.json()\n          console.error('Failed to set user role:', errorData.error)\n          // Don't throw here - user is created, just log the error\n          // The user can still use the app, but role-based permissions might not work correctly\n        } else {\n          console.log('User role set successfully')\n        }\n      } catch (roleError) {\n        console.error('Error setting user role:', roleError)\n        // Don't throw here - user is created, just log the error\n      }\n\n      // Wait for the auth state to be properly updated\n      await waitForAuthState(user)\n\n      // Small additional delay to ensure cookie is set\n      await new Promise(resolve => setTimeout(resolve, 100))\n\n    } catch (err: unknown) {\n      const errorCode = err instanceof Error && 'code' in err ? (err as FirebaseError).code : 'unknown'\n      setError(getAuthErrorMessage(errorCode))\n      throw err\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  // Sign out function\n  const signOut = async () => {\n    try {\n      setError(null)\n      await firebaseSignOut(auth)\n      setUserProfile(null)\n    } catch (err: unknown) {\n      const errorCode = err instanceof Error && 'code' in err ? (err as FirebaseError).code : 'unknown'\n      setError(getAuthErrorMessage(errorCode))\n      throw err\n    }\n  }\n\n  // Reset password function\n  const resetPassword = async (email: string) => {\n    try {\n      setError(null)\n      await sendPasswordResetEmail(auth, email)\n    } catch (err: unknown) {\n      const errorCode = err instanceof Error && 'code' in err ? (err as FirebaseError).code : 'unknown'\n      setError(getAuthErrorMessage(errorCode))\n      throw err\n    }\n  }\n\n  // Clear error function\n  const clearError = () => {\n    setError(null)\n  }\n\n  const value: AuthContextType = {\n    user,\n    userProfile,\n    loading,\n    error,\n    signIn,\n    signUp,\n    signOut,\n    resetPassword,\n    clearError,\n    refreshProfile\n  }\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  )\n}\n\n// Hook to use auth context\nexport function useAuth() {\n  const context = useContext(AuthContext)\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider')\n  }\n  return context\n}\n\n// Helper function to get user-friendly error messages\nfunction getAuthErrorMessage(errorCode: string): string {\n  switch (errorCode) {\n    case 'auth/user-not-found':\n    case 'auth/wrong-password':\n    case 'auth/invalid-credential':\n      return 'Invalid email or password'\n    case 'auth/email-already-in-use':\n      return 'An account with this email already exists'\n    case 'auth/weak-password':\n      return 'Password should be at least 6 characters'\n    case 'auth/invalid-email':\n      return 'Invalid email address'\n    case 'auth/too-many-requests':\n      return 'Too many failed attempts. Please try again later'\n    case 'auth/network-request-failed':\n      return 'Network error. Please check your connection'\n    default:\n      return 'An unexpected error occurred. Please try again'\n  }\n}\n\n// Helper hook for checking if user is authenticated\nexport function useRequireAuth() {\n  const { user, loading } = useAuth()\n  return { user, loading, isAuthenticated: !!user }\n}\n\n// Helper hook for checking user role\nexport function useUserRole(): UserRole | null {\n  const { userProfile } = useAuth()\n  return userProfile?.role || null\n}"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AAAA;AACA;AAAA;;;AAbA;;;;;AAwCA,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS,aAAa,EAAE,QAAQ,EAA2B;;IAChE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB;IACnE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,oCAAoC;IACpC,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,MAAM,SAAS,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,mIAAA,CAAA,KAAE,EAAE,SAAS;YAChC,MAAM,UAAU,MAAM,CAAA,GAAA,sKAAA,CAAA,SAAM,AAAD,EAAE;YAE7B,IAAI,QAAQ,MAAM,IAAI;gBACpB,MAAM,OAAO,QAAQ,IAAI;gBACzB,OAAO;oBACL,IAAI,QAAQ,EAAE;oBACd,GAAG,IAAI;oBACP,WAAW,KAAK,SAAS,EAAE,cAAc,KAAK,SAAS;oBACvD,WAAW,KAAK,SAAS,EAAE,cAAc,KAAK,SAAS;gBACzD;YACF;YACA,OAAO;QACT,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,OAAO;QACT;IACF;IAEA,uBAAuB;IACvB,MAAM,iBAAiB;QACrB,IAAI,MAAM;YACR,MAAM,UAAU,MAAM,iBAAiB,KAAK,GAAG;YAC/C,eAAe;QACjB;IACF;IAEA,uDAAuD;IACvD,MAAM,mBAAmB,CAAC;QACxB,OAAO,IAAI,QAAQ,CAAC;YAClB,MAAM,cAAc,CAAA,GAAA,wNAAA,CAAA,qBAAkB,AAAD,EAAE,mIAAA,CAAA,OAAI,EAAE,CAAC;gBAC5C,IAAI,gBAAgB,eAAe,YAAY,GAAG,KAAK,aAAa,GAAG,EAAE;oBACvE;oBACA;gBACF,OAAO,IAAI,CAAC,gBAAgB,CAAC,aAAa;oBACxC;oBACA;gBACF;YACF;QACF;IACF;IAEA,+BAA+B;IAC/B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM,cAAc,CAAA,GAAA,wNAAA,CAAA,qBAAkB,AAAD,EAAE,mIAAA,CAAA,OAAI;sDAAE,OAAO;oBAClD,QAAQ;oBAER,IAAI,MAAM;wBACR,yDAAyD;wBACzD,IAAI;4BACF,MAAM,UAAU,MAAM,KAAK,UAAU;4BACrC,mDAAmD;4BACnD,SAAS,MAAM,GAAG,CAAC,eAAe,EAAE,QAAQ,+CAA+C,CAAC;wBAC9F,EAAE,OAAO,OAAO;4BACd,QAAQ,KAAK,CAAC,2BAA2B;wBAC3C;wBAEA,gDAAgD;wBAChD,MAAM,UAAU,MAAM,iBAAiB,KAAK,GAAG;wBAC/C,eAAe;oBACjB,OAAO;wBACL,uCAAuC;wBACvC,SAAS,MAAM,GAAG;wBAClB,eAAe;oBACjB;oBAEA,WAAW;gBACb;;YAEA,OAAO;QACT;iCAAG,EAAE;IAEL,mBAAmB;IACnB,MAAM,SAAS,OAAO,OAAe;QACnC,IAAI;YACF,SAAS;YACT,WAAW;YACX,MAAM,iBAAiB,MAAM,CAAA,GAAA,iOAAA,CAAA,6BAA0B,AAAD,EAAE,mIAAA,CAAA,OAAI,EAAE,OAAO;YAErE,iDAAiD;YACjD,MAAM,iBAAiB,eAAe,IAAI;YAE1C,iDAAiD;YACjD,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QAEnD,EAAE,OAAO,KAAc;YACrB,MAAM,YAAY,eAAe,SAAS,UAAU,MAAM,AAAC,IAAsB,IAAI,GAAG;YACxF,SAAS,oBAAoB;YAC7B,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,mBAAmB;IACnB,MAAM,SAAS,OAAO,OAAe,UAAkB;QACrD,IAAI;YACF,SAAS;YACT,WAAW;YAEX,iCAAiC;YACjC,MAAM,iBAAiB,MAAM,CAAA,GAAA,qOAAA,CAAA,iCAA8B,AAAD,EAAE,mIAAA,CAAA,OAAI,EAAE,OAAO;YACzE,MAAM,OAAO,eAAe,IAAI;YAEhC,wCAAwC;YACxC,MAAM,CAAA,GAAA,oNAAA,CAAA,gBAAa,AAAD,EAAE,MAAM;gBACxB,aAAa,GAAG,SAAS,SAAS,CAAC,CAAC,EAAE,SAAS,QAAQ,EAAE;YAC3D;YAEA,4CAA4C;YAC5C,MAAM,MAAM,sKAAA,CAAA,YAAS,CAAC,GAAG;YACzB,MAAM,cAAc;gBAClB,OAAO;gBACP,UAAU,GAAG,SAAS,SAAS,CAAC,CAAC,EAAE,SAAS,QAAQ,EAAE;gBACtD,WAAW,SAAS,SAAS;gBAC7B,UAAU,SAAS,QAAQ;gBAC3B,OAAO,SAAS,KAAK,IAAI;gBACzB,MAAM,SAAS,IAAI;gBACnB,oBAAoB;gBACpB,WAAW;gBACX,WAAW;YACb;YAEA,MAAM,CAAA,GAAA,sKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,mIAAA,CAAA,KAAE,EAAE,SAAS,KAAK,GAAG,GAAG;YAEzC,2BAA2B;YAC3B,eAAe;gBACb,IAAI,KAAK,GAAG;gBACZ,GAAG,WAAW;gBACd,OAAO,SAAS,KAAK,IAAI;YAC3B;YAEA,kDAAkD;YAClD,IAAI;gBACF,MAAM,UAAU,MAAM,KAAK,UAAU;gBACrC,MAAM,WAAW,MAAM,MAAM,sBAAsB;oBACjD,QAAQ;oBACR,SAAS;wBACP,gBAAgB;wBAChB,iBAAiB,CAAC,OAAO,EAAE,SAAS;oBACtC;oBACA,MAAM,KAAK,SAAS,CAAC;wBACnB,KAAK,KAAK,GAAG;wBACb,MAAM,SAAS,IAAI;oBACrB;gBACF;gBAEA,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;oBACrC,QAAQ,KAAK,CAAC,4BAA4B,UAAU,KAAK;gBACzD,yDAAyD;gBACzD,sFAAsF;gBACxF,OAAO;oBACL,QAAQ,GAAG,CAAC;gBACd;YACF,EAAE,OAAO,WAAW;gBAClB,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,yDAAyD;YAC3D;YAEA,iDAAiD;YACjD,MAAM,iBAAiB;YAEvB,iDAAiD;YACjD,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QAEnD,EAAE,OAAO,KAAc;YACrB,MAAM,YAAY,eAAe,SAAS,UAAU,MAAM,AAAC,IAAsB,IAAI,GAAG;YACxF,SAAS,oBAAoB;YAC7B,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,oBAAoB;IACpB,MAAM,UAAU;QACd,IAAI;YACF,SAAS;YACT,MAAM,CAAA,GAAA,6MAAA,CAAA,UAAe,AAAD,EAAE,mIAAA,CAAA,OAAI;YAC1B,eAAe;QACjB,EAAE,OAAO,KAAc;YACrB,MAAM,YAAY,eAAe,SAAS,UAAU,MAAM,AAAC,IAAsB,IAAI,GAAG;YACxF,SAAS,oBAAoB;YAC7B,MAAM;QACR;IACF;IAEA,0BAA0B;IAC1B,MAAM,gBAAgB,OAAO;QAC3B,IAAI;YACF,SAAS;YACT,MAAM,CAAA,GAAA,6NAAA,CAAA,yBAAsB,AAAD,EAAE,mIAAA,CAAA,OAAI,EAAE;QACrC,EAAE,OAAO,KAAc;YACrB,MAAM,YAAY,eAAe,SAAS,UAAU,MAAM,AAAC,IAAsB,IAAI,GAAG;YACxF,SAAS,oBAAoB;YAC7B,MAAM;QACR;IACF;IAEA,uBAAuB;IACvB,MAAM,aAAa;QACjB,SAAS;IACX;IAEA,MAAM,QAAyB;QAC7B;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,6LAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP;GAxOgB;KAAA;AA2OT,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB;AAQhB,sDAAsD;AACtD,SAAS,oBAAoB,SAAiB;IAC5C,OAAQ;QACN,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAGO,SAAS;;IACd,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG;IAC1B,OAAO;QAAE;QAAM;QAAS,iBAAiB,CAAC,CAAC;IAAK;AAClD;IAHgB;;QACY;;;AAKrB,SAAS;;IACd,MAAM,EAAE,WAAW,EAAE,GAAG;IACxB,OAAO,aAAa,QAAQ;AAC9B;IAHgB;;QACU", "debugId": null}}, {"offset": {"line": 550, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/src/personal/bvr-safaris/src/components/layout/Navbar.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport Link from 'next/link'\nimport { useRouter } from 'next/navigation'\nimport { Button } from '@/components/ui/Button'\nimport { cn } from '@/lib/utils'\nimport { useAuth } from '@/hooks/useAuth'\n\nexport function Navbar() {\n  const [isMenuOpen, setIsMenuOpen] = useState(false)\n  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false)\n  const { user, loading, signOut } = useAuth()\n  const router = useRouter()\n\n  const handleSignOut = async () => {\n    await signOut()\n    router.push('/')\n  }\n\n  return (\n    <nav className=\"bg-white shadow-[var(--shadow-sm)] sticky top-0 z-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* Logo - Enhanced with Design Guide Typography */}\n          <Link\n            href=\"/\"\n            className=\"flex items-center font-bold\"\n            style={{\n              fontFamily: 'var(--font-display)',\n              fontSize: '28px',\n              fontWeight: '700',\n              color: 'var(--deep-brown)',\n              textDecoration: 'none'\n            }}\n          >\n            BvR Safaris\n          </Link>\n\n          {/* Desktop Navigation - Enhanced with Design Guide Styling */}\n          <div className=\"hidden md:flex items-center space-x-8\">\n            <Link\n              href=\"/farms\"\n              className=\"nav-text transition-colors hover:opacity-80\"\n              style={{\n                fontFamily: 'var(--font-body)',\n                fontSize: 'var(--text-secondary-body)',\n                fontWeight: '400',\n                color: 'var(--dark-text)',\n                textDecoration: 'none'\n              }}\n            >\n              Farms\n            </Link>\n            <Link\n              href=\"/farms?activity=hunting\"\n              className=\"nav-text transition-colors hover:opacity-80\"\n              style={{\n                fontFamily: 'var(--font-body)',\n                fontSize: 'var(--text-secondary-body)',\n                fontWeight: '400',\n                color: 'var(--dark-text)',\n                textDecoration: 'none'\n              }}\n            >\n              Hunt\n            </Link>\n            <Link\n              href=\"/farms?activity=photo_safari\"\n              className=\"nav-text transition-colors hover:opacity-80\"\n              style={{\n                fontFamily: 'var(--font-body)',\n                fontSize: 'var(--text-secondary-body)',\n                fontWeight: '400',\n                color: 'var(--dark-text)',\n                textDecoration: 'none'\n              }}\n            >\n              Photo Safari\n            </Link>\n            <Link\n              href=\"/about\"\n              className=\"nav-text transition-colors hover:opacity-80\"\n              style={{\n                fontFamily: 'var(--font-body)',\n                fontSize: 'var(--text-secondary-body)',\n                fontWeight: '400',\n                color: 'var(--dark-text)',\n                textDecoration: 'none'\n              }}\n            >\n              About\n            </Link>\n          </div>\n\n          {/* Auth Buttons */}\n          <div className=\"hidden md:flex items-center space-x-4\">\n            {loading ? (\n              <div className=\"animate-pulse bg-gray-200 h-8 w-20 rounded\"></div>\n            ) : user ? (\n              <div className=\"relative\">\n                <button\n                  onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}\n                  className=\"flex items-center space-x-2 text-[var(--dark-gray)] hover:text-[var(--primary-brown)] font-medium transition-colors\"\n                >\n                  <div className=\"w-8 h-8 bg-[var(--primary-brown)] text-white rounded-full flex items-center justify-center text-sm font-semibold\">\n                    {user.displayName?.[0] || user.email?.[0]?.toUpperCase() || 'U'}\n                  </div>\n                  <span className=\"hidden lg:block\">\n                    {user.displayName || 'Account'}\n                  </span>\n                  <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\n                  </svg>\n                </button>\n\n                {isUserMenuOpen && (\n                  <div className=\"absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 border\">\n                    <Link\n                      href=\"/dashboard\"\n                      className=\"block px-4 py-2 text-sm text-[var(--dark-gray)] hover:bg-[var(--earth-100)] hover:text-[var(--primary-brown)]\"\n                      onClick={() => setIsUserMenuOpen(false)}\n                    >\n                      Dashboard\n                    </Link>\n                    <Link\n                      href=\"/profile\"\n                      className=\"block px-4 py-2 text-sm text-[var(--dark-gray)] hover:bg-[var(--earth-100)] hover:text-[var(--primary-brown)]\"\n                      onClick={() => setIsUserMenuOpen(false)}\n                    >\n                      Profile Settings\n                    </Link>\n                    <hr className=\"my-1\" />\n                    <button\n                      onClick={() => {\n                        setIsUserMenuOpen(false)\n                        handleSignOut()\n                      }}\n                      className=\"block w-full text-left px-4 py-2 text-sm text-[var(--dark-gray)] hover:bg-[var(--earth-100)] hover:text-[var(--primary-brown)]\"\n                    >\n                      Sign Out\n                    </button>\n                  </div>\n                )}\n              </div>\n            ) : (\n              <>\n                <Link href=\"/auth/login\">\n                  <Button variant=\"outline\" size=\"sm\" style={{\n                    fontFamily: 'var(--font-body)',\n                    fontSize: 'var(--text-labels)',\n                    borderColor: 'var(--deep-brown)',\n                    color: 'var(--deep-brown)',\n                    borderRadius: '8px'\n                  }}>\n                    Sign In\n                  </Button>\n                </Link>\n                <Link href=\"/auth/register\">\n                  <Button variant=\"primary\" size=\"sm\" style={{\n                    fontFamily: 'var(--font-body)',\n                    fontSize: 'var(--text-labels)',\n                    backgroundColor: 'var(--deep-brown)',\n                    color: 'var(--cream)',\n                    borderRadius: '8px'\n                  }}>\n                    Sign Up\n                  </Button>\n                </Link>\n              </>\n            )}\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden\">\n            <button\n              onClick={() => setIsMenuOpen(!isMenuOpen)}\n              className=\"text-[var(--dark-gray)] hover:text-[var(--primary-brown)] p-2\"\n            >\n              <svg\n                className=\"h-6 w-6\"\n                fill=\"none\"\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                strokeWidth=\"2\"\n                viewBox=\"0 0 24 24\"\n                stroke=\"currentColor\"\n              >\n                {isMenuOpen ? (\n                  <path d=\"M6 18L18 6M6 6l12 12\" />\n                ) : (\n                  <path d=\"M4 6h16M4 12h16M4 18h16\" />\n                )}\n              </svg>\n            </button>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        <div\n          className={cn(\n            'md:hidden transition-all duration-300 overflow-hidden',\n            isMenuOpen ? 'max-h-96 pb-4' : 'max-h-0'\n          )}\n        >\n          <div className=\"px-2 pt-2 pb-3 space-y-1\">\n            <Link\n              href=\"/farms\"\n              className=\"block px-3 py-2 text-[var(--dark-gray)] hover:text-[var(--primary-brown)] font-medium\"\n            >\n              Farms\n            </Link>\n            <Link\n              href=\"/farms?activity=hunting\"\n              className=\"block px-3 py-2 text-[var(--dark-gray)] hover:text-[var(--primary-brown)] font-medium\"\n            >\n              Hunt\n            </Link>\n            <Link\n              href=\"/farms?activity=photo_safari\"\n              className=\"block px-3 py-2 text-[var(--dark-gray)] hover:text-[var(--primary-brown)] font-medium\"\n            >\n              Photo Safari\n            </Link>\n            <Link\n              href=\"/about\"\n              className=\"block px-3 py-2 text-[var(--dark-gray)] hover:text-[var(--primary-brown)] font-medium\"\n            >\n              About\n            </Link>\n            <div className=\"pt-4 space-y-2\">\n              {loading ? (\n                <div className=\"animate-pulse bg-gray-200 h-8 w-full rounded\"></div>\n              ) : user ? (\n                <>\n                  <Link href=\"/dashboard\" className=\"block\">\n                    <Button variant=\"outline\" size=\"sm\" className=\"w-full\">\n                      Dashboard\n                    </Button>\n                  </Link>\n                  <Link href=\"/profile\" className=\"block\">\n                    <Button variant=\"outline\" size=\"sm\" className=\"w-full\">\n                      Profile\n                    </Button>\n                  </Link>\n                  <Button variant=\"primary\" size=\"sm\" className=\"w-full\" onClick={handleSignOut}>\n                    Sign Out\n                  </Button>\n                </>\n              ) : (\n                <>\n                  <Link href=\"/auth/login\" className=\"block\">\n                    <Button variant=\"outline\" size=\"sm\" className=\"w-full\">\n                      Sign In\n                    </Button>\n                  </Link>\n                  <Link href=\"/auth/register\" className=\"block\">\n                    <Button variant=\"primary\" size=\"sm\" className=\"w-full\">\n                      Sign Up\n                    </Button>\n                  </Link>\n                </>\n              )}\n            </div>\n          </div>\n        </div>\n      </div>\n    </nav>\n  )\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;AASO,SAAS;;IACd,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,UAAO,AAAD;IACzC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,gBAAgB;QACpB,MAAM;QACN,OAAO,IAAI,CAAC;IACd;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,+JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;4BACV,OAAO;gCACL,YAAY;gCACZ,UAAU;gCACV,YAAY;gCACZ,OAAO;gCACP,gBAAgB;4BAClB;sCACD;;;;;;sCAKD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;oCACV,OAAO;wCACL,YAAY;wCACZ,UAAU;wCACV,YAAY;wCACZ,OAAO;wCACP,gBAAgB;oCAClB;8CACD;;;;;;8CAGD,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;oCACV,OAAO;wCACL,YAAY;wCACZ,UAAU;wCACV,YAAY;wCACZ,OAAO;wCACP,gBAAgB;oCAClB;8CACD;;;;;;8CAGD,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;oCACV,OAAO;wCACL,YAAY;wCACZ,UAAU;wCACV,YAAY;wCACZ,OAAO;wCACP,gBAAgB;oCAClB;8CACD;;;;;;8CAGD,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;oCACV,OAAO;wCACL,YAAY;wCACZ,UAAU;wCACV,YAAY;wCACZ,OAAO;wCACP,gBAAgB;oCAClB;8CACD;;;;;;;;;;;;sCAMH,6LAAC;4BAAI,WAAU;sCACZ,wBACC,6LAAC;gCAAI,WAAU;;;;;uCACb,qBACF,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS,IAAM,kBAAkB,CAAC;wCAClC,WAAU;;0DAEV,6LAAC;gDAAI,WAAU;0DACZ,KAAK,WAAW,EAAE,CAAC,EAAE,IAAI,KAAK,KAAK,EAAE,CAAC,EAAE,EAAE,iBAAiB;;;;;;0DAE9D,6LAAC;gDAAK,WAAU;0DACb,KAAK,WAAW,IAAI;;;;;;0DAEvB,6LAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACjE,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;;oCAIxE,gCACC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,kBAAkB;0DAClC;;;;;;0DAGD,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,kBAAkB;0DAClC;;;;;;0DAGD,6LAAC;gDAAG,WAAU;;;;;;0DACd,6LAAC;gDACC,SAAS;oDACP,kBAAkB;oDAClB;gDACF;gDACA,WAAU;0DACX;;;;;;;;;;;;;;;;;qDAOP;;kDACE,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;4CAAK,OAAO;gDACzC,YAAY;gDACZ,UAAU;gDACV,aAAa;gDACb,OAAO;gDACP,cAAc;4CAChB;sDAAG;;;;;;;;;;;kDAIL,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;4CAAK,OAAO;gDACzC,YAAY;gDACZ,UAAU;gDACV,iBAAiB;gDACjB,OAAO;gDACP,cAAc;4CAChB;sDAAG;;;;;;;;;;;;;;;;;;sCASX,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,SAAS,IAAM,cAAc,CAAC;gCAC9B,WAAU;0CAEV,cAAA,6LAAC;oCACC,WAAU;oCACV,MAAK;oCACL,eAAc;oCACd,gBAAe;oCACf,aAAY;oCACZ,SAAQ;oCACR,QAAO;8CAEN,2BACC,6LAAC;wCAAK,GAAE;;;;;6DAER,6LAAC;wCAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQlB,6LAAC;oBACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,yDACA,aAAa,kBAAkB;8BAGjC,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;0CAGD,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;0CAGD,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;0CAGD,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;0CAGD,6LAAC;gCAAI,WAAU;0CACZ,wBACC,6LAAC;oCAAI,WAAU;;;;;2CACb,qBACF;;sDACE,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAa,WAAU;sDAChC,cAAA,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAU,MAAK;gDAAK,WAAU;0DAAS;;;;;;;;;;;sDAIzD,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAW,WAAU;sDAC9B,cAAA,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAU,MAAK;gDAAK,WAAU;0DAAS;;;;;;;;;;;sDAIzD,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;4CAAK,WAAU;4CAAS,SAAS;sDAAe;;;;;;;iEAKjF;;sDACE,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAc,WAAU;sDACjC,cAAA,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAU,MAAK;gDAAK,WAAU;0DAAS;;;;;;;;;;;sDAIzD,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAiB,WAAU;sDACpC,cAAA,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAU,MAAK;gDAAK,WAAU;0DAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAY3E;GApQgB;;QAGqB,2HAAA,CAAA,UAAO;QAC3B,qIAAA,CAAA,YAAS;;;KAJV", "debugId": null}}]}