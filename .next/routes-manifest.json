{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "dynamicRoutes": [{"page": "/farms/[id]", "regex": "^/farms/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/farms/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/farms/[id]/analytics", "regex": "^/farms/([^/]+?)/analytics(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/farms/(?<nxtPid>[^/]+?)/analytics(?:/)?$"}, {"page": "/farms/[id]/edit", "regex": "^/farms/([^/]+?)/edit(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/farms/(?<nxtPid>[^/]+?)/edit(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/about", "regex": "^/about(?:/)?$", "routeKeys": {}, "namedRegex": "^/about(?:/)?$"}, {"page": "/auth/forgot-password", "regex": "^/auth/forgot\\-password(?:/)?$", "routeKeys": {}, "namedRegex": "^/auth/forgot\\-password(?:/)?$"}, {"page": "/auth/login", "regex": "^/auth/login(?:/)?$", "routeKeys": {}, "namedRegex": "^/auth/login(?:/)?$"}, {"page": "/auth/register", "regex": "^/auth/register(?:/)?$", "routeKeys": {}, "namedRegex": "^/auth/register(?:/)?$"}, {"page": "/auth/reset-password", "regex": "^/auth/reset\\-password(?:/)?$", "routeKeys": {}, "namedRegex": "^/auth/reset\\-password(?:/)?$"}, {"page": "/auth/verify-email", "regex": "^/auth/verify\\-email(?:/)?$", "routeKeys": {}, "namedRegex": "^/auth/verify\\-email(?:/)?$"}, {"page": "/contact", "regex": "^/contact(?:/)?$", "routeKeys": {}, "namedRegex": "^/contact(?:/)?$"}, {"page": "/dashboard", "regex": "^/dashboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard(?:/)?$"}, {"page": "/dashboard/bookings", "regex": "^/dashboard/bookings(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/bookings(?:/)?$"}, {"page": "/dashboard/farms", "regex": "^/dashboard/farms(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/farms(?:/)?$"}, {"page": "/faq", "regex": "^/faq(?:/)?$", "routeKeys": {}, "namedRegex": "^/faq(?:/)?$"}, {"page": "/farm-owners", "regex": "^/farm\\-owners(?:/)?$", "routeKeys": {}, "namedRegex": "^/farm\\-owners(?:/)?$"}, {"page": "/farms", "regex": "^/farms(?:/)?$", "routeKeys": {}, "namedRegex": "^/farms(?:/)?$"}, {"page": "/farms/create", "regex": "^/farms/create(?:/)?$", "routeKeys": {}, "namedRegex": "^/farms/create(?:/)?$"}, {"page": "/favicon.ico", "regex": "^/favicon\\.ico(?:/)?$", "routeKeys": {}, "namedRegex": "^/favicon\\.ico(?:/)?$"}, {"page": "/help", "regex": "^/help(?:/)?$", "routeKeys": {}, "namedRegex": "^/help(?:/)?$"}, {"page": "/how-it-works", "regex": "^/how\\-it\\-works(?:/)?$", "routeKeys": {}, "namedRegex": "^/how\\-it\\-works(?:/)?$"}, {"page": "/privacy", "regex": "^/privacy(?:/)?$", "routeKeys": {}, "namedRegex": "^/privacy(?:/)?$"}, {"page": "/profile", "regex": "^/profile(?:/)?$", "routeKeys": {}, "namedRegex": "^/profile(?:/)?$"}, {"page": "/safety", "regex": "^/safety(?:/)?$", "routeKeys": {}, "namedRegex": "^/safety(?:/)?$"}, {"page": "/terms", "regex": "^/terms(?:/)?$", "routeKeys": {}, "namedRegex": "^/terms(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc", "prefetchSegmentHeader": "Next-Router-Segment-Prefetch", "prefetchSegmentSuffix": ".segment.rsc", "prefetchSegmentDirSuffix": ".segments"}, "rewriteHeaders": {"pathHeader": "x-nextjs-rewritten-path", "queryHeader": "x-nextjs-rewritten-query"}, "rewrites": []}