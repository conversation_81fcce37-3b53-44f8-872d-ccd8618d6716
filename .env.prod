# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://lfotdsflgqtmqwldkgzx.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imxmb3Rkc2ZsZ3F0bXF3bGRrZ3p4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgxOTE1MDEsImV4cCI6MjA2Mzc2NzUwMX0.WawjyIygsGCBrG1fkZVindQAcTEor2oXQwfBezrhWeo

NEXT_PUBLIC_PIXABAY_API_KEY=**********************************
# Optional: For local development with Supabase CLI
# NEXT_PUBLIC_SUPABASE_URL=http://localhost:54321
# NEXT_PUBLIC_SUPABASE_ANON_KEY=your_local_anon_key

# Firebase Client Configuration
NEXT_PUBLIC_FIREBASE_API_KEY=AIzaSyC1234567890abcdefghijklmnopqrstuvwxyz
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=bvr-safaris-12345.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=bvr-safaris-12345
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=bvr-safaris-12345.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=************
NEXT_PUBLIC_FIREBASE_APP_ID=1:************:web:abcdef1234567890

# Firebase Admin SDK (keep this secret!)
FIREBASE_SERVICE_ACCOUNT_KEY='{"type":"service_account","project_id":"bvr-safaris-12345","private_key_id":"abc123","private_key":"-----BEGIN PRIVATE KEY-----\nMIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC...\n-----END PRIVATE KEY-----\n","client_email":"*******","client_id":"************345678901","auth_uri":"https://accounts.google.com/o/oauth2/auth","token_uri":"https://oauth2.googleapis.com/token","auth_provider_x509_cert_url":"https://www.googleapis.com/oauth2/v1/certs","client_x509_cert_url":"https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk-xyz%40bvr-safaris-12345.iam.gserviceaccount.com"}'
